// Types for monitoring and business metrics endpoints

export interface HealthResponse {
  status: string;
  uptime: number;
  timestamp: number;
}

export interface ReadyResponse {
  status: string;
  db: string;
  timestamp: number;
}

export interface InfoResponse {
  name: string;
  version: string;
  environment: string;
  buildTime: string | null;
  hostname: string;
  timestamp: number;
}

export interface StatusResponse {
  uptime: number;
  memory: Record<string, number>;
  cpu: Record<string, number>;
  pid: number;
  platform: string;
  nodeVersion: string;
  eventLoopLag: number | null;
  hostname: string;
  timestamp: number;
}

export interface BusinessMetricsResponse {
  activeUsers: number;
  totalBookings: number;
  failedPayments: number;
  timestamp: number;
}

export const defaultBusinessMetrics: BusinessMetricsResponse = {
  activeUsers: 0,
  totalBookings: 0,
  failedPayments: 0,
  timestamp: 0,
};