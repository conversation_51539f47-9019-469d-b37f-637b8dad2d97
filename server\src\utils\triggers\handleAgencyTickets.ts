import { prisma } from "../../prisma";
import { Prisma } from "@prisma/client";

type TicketStatus =
  | "pending"
  | "available"
  | "hold"
  | "unavailable"
  | "updated"
  | "rejected"
  | "blocked";

const allowedStatuses: TicketStatus[] = [
  "pending",
  "available",
  "hold",
  "unavailable",
  "updated",
  "rejected",
  "blocked",
];

export const updateAgencyTicketsToHold = async (
  agencyId: string,
  reason: "disabled" | "deactivated" | "suspended" = "disabled"
) => {
  try {
    await prisma.$transaction(async (transaction: any) => {
      // 1. Handle tickets that were in "updated" status
      const updatedTickets = await transaction.flightTicket.findMany({
        where: {
          ownerId: agencyId,
          ticketStatus: "available",
          updated: true,
        },
        select: { id: true },
      });

      if (updatedTickets.length > 0) {
        await transaction.flightTicket.updateMany({
          where: { id: { in: updatedTickets.map((t: any) => t.id) } },
          data: {
            ticketStatus: "available",
            updated: false,
          },
        });

        // Create withdrawal logs
        await transaction.ticketHistoryLog.createMany({
          data: updatedTickets.map((ticket: any) => ({
            ticketId: ticket.id,
            changeType: "withdraw request",
            changeDetails: JSON.stringify({
              comment: `Ticket status: updated -> available.\nUpdate automatically withdrawn during restoration.`,
            }),
            agencyId: agencyId,
          })),
        });
      }

      const batchSize = 1000; // Adjust based on your environment
      let skip = 0;
      let hasMore = true;

      while (hasMore) {
        // Get a batch of ticket IDs to update
        const batch = await transaction.flightTicket.findMany({
          where: {
            ownerId: agencyId,
            ticketStatus: {
              in: allowedStatuses.filter((status) => status !== "hold"), // Only select tickets that aren't already on hold
            },
          },
          select: { id: true },
          skip,
          take: batchSize,
        });

        if (batch.length === 0) {
          hasMore = false;
          break;
        }

        // Use raw SQL for column-to-column update
        // await transaction.$executeRaw(Prisma.sql`
        //   UPDATE "flightTickets"
        //   SET "previousStatus" = "ticketStatus",
        //       "ticketStatus" = 'hold'
        //   WHERE "id" IN (${Prisma.join(batch.map((t: any) => t.id))})
        // `);
        await transaction.$executeRaw`\
          UPDATE "flightTickets"\
          SET "previousStatus" = "ticketStatus",\
              "ticketStatus" = 'hold'\
          WHERE "id" IN (${Prisma.join(batch.map((t: any) => t.id))})\
        `;

        skip += batchSize;
      }

      // Fetch updated tickets to create logs
      const holdTickets = await transaction.flightTicket.findMany({
        where: {
          ownerId: agencyId,
          ticketStatus: "hold",
        },
        select: {
          id: true,
          previousStatus: true,
          ticketStatus: true,
        },
      });

      // Create history logs
      await Promise.all(
        holdTickets.map((ticket: any) =>
          transaction.ticketHistoryLog.create({
            data: {
              ticketId: ticket.id,
              changeType: "hold",
              changeDetails: JSON.stringify({
                comment:
                  reason === "disabled"
                    ? `Ticket status: ${ticket.previousStatus} -> ${ticket.ticketStatus}.\nAgency account has been disabled.`
                    : reason === "deactivated"
                      ? `Ticket status: ${ticket.previousStatus} -> ${ticket.ticketStatus}.\nAgency account has been deactivated.`
                      : `Ticket status: ${ticket.previousStatus} -> ${ticket.ticketStatus}.\nAgency account has been suspended.`,
              }),
            },
          })
        )
      );
    });
  } catch (error) {
    console.error("Error updating agency tickets to hold:", error);
    throw error;
  }
};

export const restoreAgencyTickets = async (agencyId: string) => {
  try {
    await prisma.$transaction(async (transaction: any) => {
      // 1. Handle tickets that were in "updated" status
      const updatedTickets = await transaction.flightTicket.findMany({
        where: {
          ownerId: agencyId,
          ticketStatus: "hold",
          previousStatus: "updated",
        },
        select: { id: true },
      });

      if (updatedTickets.length > 0) {
        // Bulk update to available and reset flags
        // update the ticket
        await transaction.flightTicket.updateMany({
          where: { id: { in: updatedTickets.map((t: any) => t.id) } },
          data: {
            updated: false,
          },
        });

        // Log detailed changes to the ticket
        await transaction.ticketHistoryLog.createMany({
          data: updatedTickets.map((ticket: any) => ({
            ticketId: ticket.id,
            changeType: "withdraw request",
            changeDetails: JSON.stringify({
              comment: "Update automatically withdrawn during restoration",
              previousStatus: "updated",
              newStatus: "available",
            }),
            agencyId: agencyId,
          })),
        });

        await transaction.flightTicket.updateMany({
          where: { id: { in: updatedTickets.map((t: any) => t.id) } },
          data: {
            ticketStatus: "available",
            previousStatus: null,
            updated: false,
          },
        });

        // Create withdrawal logs
        await transaction.ticketHistoryLog.createMany({
          data: updatedTickets.map((ticket: any) => ({
            ticketId: ticket.id,
            changeType: "withdraw request",
            changeDetails: JSON.stringify({
              comment: "Update automatically withdrawn during restoration",
              previousStatus: "updated",
              newStatus: "available",
            }),
            agencyId: agencyId,
          })),
        });
      }

      // 2. Handle remaining tickets with valid previous statuses
      const allowedStatuses: TicketStatus[] = [
        "pending",
        "available",
        "hold",
        "unavailable",
        "rejected",
        "blocked",
      ];
      const batchSize = 1000;
      let skip = 0;

      while (true) {
        const batch = await transaction.flightTicket.findMany({
          where: {
            ownerId: agencyId,
            ticketStatus: "hold",
            previousStatus: { not: null },
          },
          select: { id: true, previousStatus: true },
          skip,
          take: batchSize,
        });

        if (batch.length === 0) break;
        skip += batchSize;

        // Prepare status updates
        const updates = batch.map((ticket: any) => ({
          id: ticket.id,
          newStatus: allowedStatuses.includes(
            ticket.previousStatus as TicketStatus
          )
            ? ticket.previousStatus
            : "available",
        }));

        // Bulk update using raw SQL for performance
        // await transaction.$executeRaw`
        //   UPDATE "flightTickets" AS ft
        //   SET
        //     "ticketStatus" = v.new_status,
        //     "previousStatus" = NULL
        //   FROM (VALUES ${Prisma.join(
        //     updates.map((u: any) => Prisma.sql`(${u.id}, ${u.newStatus})`)
        //   )}) AS v(id, new_status)
        //   WHERE ft.id = v.id
        // `;
        await transaction.$executeRaw`
          UPDATE "flightTickets" AS ft
          SET 
            "ticketStatus" = v.new_status,
            "previousStatus" = NULL
          FROM (VALUES ${Prisma.join(
            updates.map((u: { id: unknown; newStatus: unknown; }) => Prisma.sql`(${u.id}, ${u.newStatus})`)
          )}) AS v(id, new_status)
          WHERE ft.id = v.id
        `;

        // Create restoration logs
        await transaction.ticketHistoryLog.createMany({
          data: updates.map((u: any) => ({
            ticketId: u.id,
            changeType: u.newStatus as string,
            changeDetails: JSON.stringify({
              comment: `Ticket status: hold -> ${u.newStatus}.\nAgency account restored.`,
            }),
            agencyId: agencyId,
          })),
        });
      }
    });
  } catch (error) {
    console.error("Error restoring agency tickets:", error);
    throw error;
  }
};
