import Jo<PERSON> from "joi";
import { TravelerType } from "@/utils/definitions/blockSeatsDefinitions";

// Define validation schema for traveler form
export const travelerFormValidation = Joi.object({
  title: Joi.string().valid("Mr", "Mrs", "Ms").required().messages({
    "string.empty": "Title is required",
    "any.required": "Title is required",
    "any.only": "Please select a valid title",
  }),

  firstName: Joi.string()
    .trim()
    .min(2)
    .max(50)
    .pattern(/^[a-zA-Z\s-']+$/)
    .required()
    .messages({
      "string.empty": "First name is required",
      "string.min": "First name must be at least {#limit} characters",
      "string.max": "First name cannot exceed {#limit} characters",
      "string.pattern.base":
        "First name can only contain letters, spaces, hyphens, and apostrophes",
      "any.required": "First name is required",
    }),

  lastName: Joi.string()
    .trim()
    .min(2)
    .max(50)
    .pattern(/^[a-zA-Z\s-']+$/)
    .required()
    .messages({
      "string.empty": "Last name is required",
      "string.min": "Last name must be at least {#limit} characters",
      "string.max": "Last name cannot exceed {#limit} characters",
      "string.pattern.base":
        "Last name can only contain letters, spaces, hyphens, and apostrophes",
      "any.required": "Last name is required",
    }),

  dateOfBirth: Joi.date().max("now").required().messages({
    "date.base": "Please enter a valid date of birth",
    "date.max": "Date of birth cannot be in the future",
    "any.required": "Date of birth is required",
  }),

  gender: Joi.string().valid("male", "female").required().messages({
    "string.empty": "Gender is required",
    "any.only": "Please select a valid gender",
    "any.required": "Gender is required",
  }),

  nationality: Joi.string().required().messages({
    "string.empty": "Nationality is required",
    "any.required": "Nationality is required",
  }),

  // Traveler type field
  type: Joi.string()
    .valid(...Object.values(TravelerType))
    .required()
    .messages({
      "string.empty": "Traveler type is required",
      "any.only": "Please select a valid traveler type",
      "any.required": "Traveler type is required",
    }),

  passportNumber: Joi.string()
    .pattern(/^[A-Z0-9<]+$/)
    .min(5)
    .max(20)
    .required()
    .messages({
      "string.empty": "Passport number is required",
      "string.pattern.base":
        "Passport number can only contain uppercase letters and numbers",
      "string.min": "Passport number must be at least {#limit} characters",
      "string.max": "Passport number cannot exceed {#limit} characters",
      "any.required": "Passport number is required",
    }),

  issuingCountry: Joi.string().required().messages({
    "string.empty": "Passport issuing country is required",
    "any.required": "Passport issuing country is required",
  }),

  passportExpiry: Joi.date().min("now").required().messages({
    "date.base": "Please enter a valid expiry date",
    "date.min": "Passport must be valid (not expired)",
    "any.required": "Passport expiry date is required",
  }),

  contactEmail: Joi.string()
    .email({ tlds: { allow: false } })
    .optional()
    .allow("")
    .empty("")
    .messages({
      "string.email": "Please enter a valid email address",
    }),
  
  contactPhone: Joi.string()
    .pattern(/^[0-9+\s-]+$/)
    .min(8)
    .max(20)
    .optional()
    .allow("")
    .empty("")
    .messages({
      "string.pattern.base": "Please enter a valid phone number",
      "string.min": "Phone number must be at least {#limit} digits",
      "string.max": "Phone number cannot exceed {#limit} characters",
    }),
});

// Export individual field validations
const fieldValidations: Record<string, any> = {
  title: travelerFormValidation.extract("title"),
  firstName: travelerFormValidation.extract("firstName"),
  lastName: travelerFormValidation.extract("lastName"),
  dateOfBirth: travelerFormValidation.extract("dateOfBirth"),
  gender: travelerFormValidation.extract("gender"),
  nationality: travelerFormValidation.extract("nationality"),
  type: travelerFormValidation.extract("type"),
  passportNumber: travelerFormValidation.extract("passportNumber"),
  issuingCountry: travelerFormValidation.extract(
    "issuingCountry"
  ),
  passportExpiry: travelerFormValidation.extract("passportExpiry"),
  contactEmail: travelerFormValidation.extract("contactEmail"),
  contactPhone: travelerFormValidation.extract("contactPhone"),
};

export const getFieldValidation = (field: string) => {
  return fieldValidations[field] || Joi.any();
};

// Export the validation schema with extract method
export default {
  ...travelerFormValidation,
  extract: (field: string) => getFieldValidation(field),
};
