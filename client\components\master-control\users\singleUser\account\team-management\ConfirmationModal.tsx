import { X, Trash2, Ban, CheckCircle } from "lucide-react";

type ActionType = "delete" | "status-toggle";

interface ConfirmationModalProps {
  isOpen: boolean;
  actionType: ActionType;
  currentStatus?: "active" | "inactive";
  onConfirm: () => void;
  onCancel: () => void;
  title: string;
  description: string;
}

const ConfirmationModal = ({
  isOpen,
  actionType,
  currentStatus = "active",
  onConfirm,
  onCancel,
  title,
  description,
}: ConfirmationModalProps) => {
  if (!isOpen) return null;

  const getActionConfig = () => {
    if (actionType === "delete") {
      return {
        icon: <Trash2 size={32} className="text-white" />,
        iconBg: "bg-red-500",
        confirmColor: "bg-red-500 hover:bg-red-600",
        confirmIcon: <Trash2 size={16} />,
        confirmText: "Delete",
      };
    }

    const isActivating = currentStatus === "inactive";
    return {
      icon: isActivating ? <CheckCircle size={32} className="text-white"  /> : <Ban size={32} className="text-white"  />,
      iconBg: isActivating ? "bg-green-500" : "bg-orange-500",
      confirmColor: isActivating
        ? "bg-green-500 hover:bg-green-600"
        : "bg-orange-500 hover:bg-orange-600",
      confirmIcon: isActivating ? <CheckCircle size={16} className="text-white" /> : <Ban size={16} className="text-white" />,
      confirmText: isActivating ? "Enable" : "Disable",
    };
  };

  const { icon, iconBg, confirmColor, confirmIcon, confirmText } =
    getActionConfig();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]">
      <div className="dark:bg-gray-800 bg-gray-100 rounded-2xl shadow-lg max-w-sm w-full p-6 text-center">
        <div className="mb-4">
          <div
            className={`w-12 h-12 rounded-full ${iconBg} mx-auto flex items-center justify-center`}
          >
            {icon}
          </div>
        </div>
        <h2 className="text-xl font-bold dark:text-white text-gray-800 mb-2">
          {title}
        </h2>
        <p className="dark:text-gray-300 text-gray-700 mb-6">{description}</p>
        <div className="flex justify-center space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <X size={16} />
            <span>Cancel</span>
          </button>
          <button
            type="button"
            onClick={onConfirm}
            className={`px-6 py-2 ${confirmColor} text-white rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2`}
          >
            {confirmIcon}
            <span>{confirmText}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
