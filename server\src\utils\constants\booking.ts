// Centralized enums and constants for booking logic

export enum BookingHistoryChangeType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  TIMEOUT = 'TIMEOUT',
  CANCEL = 'CANCEL',
}

export enum ReceiptType {
  THIRD_PARTY = 'THIRD_PARTY',
  INTERNAL = 'INTERNAL',
}

export enum ReceiptStatus {
  ISSUED = 'ISSUED',
  CANCELLED = 'CANCELLED',
}

export enum ActorType {
  CUSTOMER = 'CUSTOMER',
  AGENT = 'AGENT',
  ADMIN = 'ADMIN',
}

export enum CreditTransactionType {
  TRANSFER = 'transfer',
  REFUND = 'refund',
}

export enum BookedFlightSeatStatus {
  BOOKED = 'booked',
  CANCELLED = 'cancelled',
}
