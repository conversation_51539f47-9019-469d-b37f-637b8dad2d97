import {
  AgentR<PERSON>,
  PrismaClient,
  RoleType as PrismaRoleType,
  Role,
  TeamMemberRole,
} from "@prisma/client";

// Helper function to generate roleType
// export const generateRoleType = (role: Role, subRole: SubRole): RoleType => {
//   return `${role}_${subRole}`;
// };

export const generateRoleType = (
  role: Role,
  subRole: TeamMemberRole | AgentRole
): PrismaRoleType => {
  switch (role) {
    case "master":
      switch (subRole) {
        case "admin":
          return "master_admin";
        case "moderator":
          return "master_moderator";
        case "accountant":
          return "master_accountant";
      }
      break;
    case "agency":
      switch (subRole) {
        case "admin":
          return "agency_admin";
        case "accountant":
          return "agency_accountant";
        case "operation":
          return "agency_operation";
        case "sales":
          return "agency_sales";
      }
      break;
  }

  throw new Error("Invalid role or subRole");
};
