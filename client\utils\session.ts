export const SESSION_CHANNEL = 'airvilla-session-channel';

export const setupSessionBroadcast = (onSessionExpired: () => void) => {
  // Only run in browser
  if (typeof window === 'undefined') return;

  const channel = new BroadcastChannel(SESSION_CHANNEL);

  // Listen for session expiration messages from other tabs
  const handleMessage = (event: MessageEvent) => {
    if (event.data?.type === 'SESSION_EXPIRED') {
      onSessionExpired();
    }
  };

  channel.addEventListener('message', handleMessage);

  // Cleanup
  return () => {
    channel.removeEventListener('message', handleMessage);
    channel.close();
  };
};

export const broadcastSessionExpiration = () => {
  if (typeof window === 'undefined') return;
  
  const channel = new BroadcastChannel(SESSION_CHANNEL);
  channel.postMessage({ type: 'SESSION_EXPIRED' });
  channel.close();
};