import { prisma } from "../../prisma";
import { clearSessionExpiration } from "../schedule/trackSessionExpiration";
import { restoreAgencyTickets } from "./handleAgencyTickets";

/**
 * Updates the access status of all agency agents when an agency owner's account is accepted
 * @param agencyId - The ID of the agency owner whose account is accepted
 */
export const handleAgencyAcceptation = async (agencyId: string) => {
  try {
    await prisma.$transaction(async (transaction: any) => {
      // Update all agency agents' access status
      await transaction.agencyAgent.updateMany({
        where: {
          agencyId: agencyId,
        },
        data: {
          accountStatus: "accepted",
          status: "active",
          deactivationDate: null,
        },
      });

      // Restore tickets to their previous status
      await restoreAgencyTickets(agencyId);
    });

    // Send session expiration event to all agency agents
    const agents = await prisma.agencyAgent.findMany({
      where: { agencyId: agencyId },
    });
    agents.forEach((agent: any) => {
      clearSessionExpiration(agent.id);
    });
  } catch (error) {
    console.error("Error in handleAgencyAcceptation:", error);
    throw error;
  }
};
