import { prisma } from "../../prisma";
import { getIO } from "../../socket";
import { updateAgencyTicketsToHold } from "./handleAgencyTickets";

/**
 * Updates the access status of all agency agents when an agency owner's account is disabled
 * @param agencyId - The ID of the agency owner whose account is disabled
 */
export const handleAgencyDisablement = async (agencyId: string) => {
  try {
    // Update all agency agents' access status
    await prisma.agencyAgent.updateMany({
      where: {
        agencyId: agencyId,
      },
      data: {
        accountStatus: "disabled",
        status: "inactive",
        deactivationDate: new Date(),
      },
    });
    // Update tickets to HOLD
    await updateAgencyTicketsToHold(agencyId, "disabled");

    // Send session expiration event to all agency agents
    const agents = await prisma.agencyAgent.findMany({
      where: { agencyId: agencyId },
    });
    agents.forEach((agent: any) => {
      const io = getIO();
      io.to(agent.id).emit("sessionExpiration", {
        message: "Your agency account has been disabled.",
      });
    });
  } catch (error) {
    console.error("Error in handleAgencyDisablement:", error);
    throw error;
  }
};
