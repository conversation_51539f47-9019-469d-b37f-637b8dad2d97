import { Response, NextFunction } from "express";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
import { TeamMemberRole } from "@prisma/client";

export const checkTeamMemberAccess = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // Skip if already authenticated as a user
    if (req.accountType === "masterUser") {
      return next();
    }

    const teamMemberId = req.userId;
    if (!teamMemberId) {
      return next();
    }

    const teamMember = await prisma.teamMember.findUnique({
      where: { id: teamMemberId },
    });

    if (!teamMember) {
      return next();
    }

    // Check if team member has appropriate role
    const allowedRoles = [
      TeamMemberRole.admin,
      TeamMemberRole.moderator,
      TeamMemberRole.accountant,
    ];

    if (!teamMember.subRole || !allowedRoles.includes(teamMember.subRole)) {
      return res.status(403).json({
        success: false,
        message: "Insufficient permissions for this operation",
      });
    }

    // Convert null to undefined for properties that might be null from database
    if (teamMember.refId === null) teamMember.refId = undefined as any;
    if (teamMember.agencyName === null) teamMember.agencyName = undefined as any;

    // Add team member info to request
    req.teamMember = teamMember as any;
    return next();
  } catch (error) {
    console.error("Team member auth error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error during authentication",
    });
  }
};
