import React, { useState } from "react";
import { formatPrice } from "@/utils/functions/functions";
import { EyeIcon, Loader } from "lucide-react";
import { PriceBookingSectionProps } from "@/utils/definitions/blockSeatsDefinitions";
import { useSearchState } from "@/components/hooks/useSearchState";
import { useDispatch } from "react-redux";
import { setBookingConfirmationData } from "@/redux/features/BookingConfirmationSlice";

import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";
import { setMsg } from "@/redux/features/ActionMsgSlice";

export const PriceBookingSection = ({
  flightClass,
  searchState,
  ticket,
  combinedId,
  onSelect,
  selectedDeparture,
  selectedReturn,
  getTicketState,
  setFeedbackModalOpen,
  selectedTicketType, // <-- pass from parent
  isReturn,
}: PriceBookingSectionProps & {
  selectedTicketType?: "internal" | "third-party";
}) => {
  const { updateUrlParams } = useSearchState();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);

  // Infer userId for ticket type logic
  const user = useAppSelector(selectUser);
  const userId = typeof user === "object" && "id" in user ? user.id : undefined;
  const userAgencyId =
    typeof user === "object" && "agencyId" in user ? user.agencyId : undefined;
  const userRole =
    typeof user === "object" && "role" in user ? user.role : undefined;
  const userRoleType =
    typeof user === "object" && "roleType" in user ? user.roleType : undefined;

  const getTicketType = (ticket: any) => {
    // A ticket is "internal" if:
    // 1. It's owned by the user directly (for individual users), OR
    // 2. It's owned by the user's agency (for agency users like agents/team members)
    const isInternal =
      ticket.ownerId === userId ||
      (ticket.owner?.agencyName &&
        "agencyName" in user &&
        user?.agencyName &&
        ticket.owner.agencyName.toLowerCase() ===
          user.agencyName.toLowerCase()) ||
      (userAgencyId && ticket.ownerId === userAgencyId) ||
      (userRoleType &&
        [
          "agency_admin",
          "agency_sales",
          "agency_accountant",
          "agency_operation",
          "agency_member",
          "agency_owner",
          "team_member",
        ].includes(userRoleType) &&
        ticket.ownerId === userAgencyId);

    return isInternal ? "internal" : "third-party";
  };

  const ticketType = getTicketType(ticket);
  const isTypeMismatch =
    selectedTicketType && ticketType !== selectedTicketType;

  // Get passenger counts from URL or searchState
  const getPassengerCounts = () => {
    // First try to get from URL parameters
    const urlParams = new URLSearchParams(window.location.search);

    // If URL has parameters, use them
    if (urlParams.has("adults")) {
      const adults = parseInt(urlParams.get("adults") || "1", 10);
      const children = parseInt(urlParams.get("children") || "0", 10);
      const infants = parseInt(urlParams.get("infants") || "0", 10);
      const travelers = adults + children + infants;
      const travelClass = urlParams.get("travelClass") || "Economy";
      return {
        adults,
        children,
        infants,
        total: travelers,
        travelClass,
      };
    }

    // Fall back to searchState if URL params not available
    if (searchState?.passengers) {
      const { adults = 1, children = 0, infants = 0 } = searchState.passengers;
      const travelers = adults + children + infants;
      return {
        adults,
        children,
        infants,
        total: travelers,
        travelClass: searchState.travelClass || "Economy",
      };
    }

    // Default values if nothing else is available
    return {
      adults: 1,
      children: 0,
      infants: 0,
      total: 1,
      travelClass: "Economy",
    };
  };

  // Determine if this ticket is for departure or return
  const isReturnTicket = isReturn;
  const isDepartureTicket = !isReturn;

  // Get the ticket state
  const ticketState = getTicketState(ticket.id, isReturnTicket);

  // Determine button state and text
  let buttonState = "select";
  let buttonDisplayText = ticketState === "selected" ? "Selected" : "Select";

  if (searchState.itinerary === "round trip") {
    if (
      (isDepartureTicket && selectedDeparture === ticket.id) ||
      (isReturnTicket && selectedReturn === ticket.id)
    ) {
      if (selectedDeparture && selectedReturn) {
        buttonState = "book now";
        buttonDisplayText = "Book Now";
      } else {
        buttonState = "selected";
        buttonDisplayText = "Selected";
      }
    }
  } else if (searchState.itinerary === "one way") {
    buttonState = "book now";
    buttonDisplayText = "Book Now";
  }

  // Generate link URL
  const linkUrl =
    buttonState === "book now"
      ? updateUrlParams(
          `/blockseats/list/${
            searchState.itinerary === "one way" ? ticket.id : combinedId
          }`,
          {
            ...searchState,
          }
        )
      : "#";

  // Check if there are enough seats available
  const hasEnoughSeats = () => {
    const passengerCounts = getPassengerCounts();
    const totalTravelers = passengerCounts.total;
    const availableSeats = ticket.remainingSeats || 0;

    // If we don't have seat information, assume it's okay to proceed
    if (availableSeats === null || availableSeats === undefined) {
      console.warn("No seat information available for ticket", ticket.id);
      return true;
    }

    return totalTravelers <= availableSeats;
  };

  // Show error message
  const showSeatError = () => {
    const passengerCounts = getPassengerCounts();
    const totalTravelers = passengerCounts.total;
    const availableSeats = ticket.remainingSeats || 0;

    const message = `The number of selected travelers (${totalTravelers}) exceeds the available seats (${availableSeats}) for this flight.`;

    // Show alert with formatted message
    dispatch(setMsg({ message, success: false }));
  };

  // Handle button click
  const handleButtonClick = () => {
    // Prevent action if there's a type mismatch
    if (isTypeMismatch) {
      return;
    }

    if (buttonState === "book now" && linkUrl !== "#") {
      // First check if we have enough seats
      if (!hasEnoughSeats()) {
        showSeatError();
        return;
      }

      // Set loading state while processing
      setIsLoading(true);

      const passengerCounts = getPassengerCounts();
      // Save full ticket to Redux before navigation
      dispatch(
        setBookingConfirmationData({
          bookingResult: null,
          travelerData: null,
          ticket,
          fullTicket: ticket,
          passengerCounts,
        })
      );

      // Navigate to booking page
      window.location.href = linkUrl;
    } else if (searchState.itinerary === "round trip") {
      // Use the isReturn prop to determine if this is a return ticket
      onSelect(ticket, isReturn);
    }
  };

  // Render button
  const renderButton = () => (
    <div className="relative flex flex-col">
      <button
        className={`btn min-w-32 py-2 px-4 rounded-lg mr-2 ${
          buttonState === "book now"
            ? "bg-red-500 hover:bg-red-700 text-white"
            : buttonState === "selected"
            ? "bg-blue-500 hover:bg-blue-700 text-white"
            : "bg-slate-950 hover:bg-slate-700 text-white"
        } ${isTypeMismatch ? "opacity-50 cursor-not-allowed" : ""}`}
        onClick={isTypeMismatch ? undefined : handleButtonClick}
        disabled={isTypeMismatch}
        title={
          isTypeMismatch
            ? `Internal and Third-Party tickets can’t be combined.`
            : ""
        }
      >
        {isLoading ? (
          <Loader className="animate-spin mr-2 h-4 w-4" />
        ) : (
          buttonDisplayText
        )}
      </button>
      {isTypeMismatch && (
        <span className="text-xs text-red-500 block mt-1 text-left">
          Internal and Third-Party tickets can’t be combined.
        </span>
      )}
    </div>
  );

  return (
    <div className="mt-4 text-blue-500 dark:text-blue-400 text-sm text-end w-full md:w-1/5 flex flex-col md:items-end">
      <div className="flex flex-col items-start mb-4">
        {/* Price */}
        <div className="text-justify text-xl md:text-2xl font-bold text-gray-950 dark:text-gray-50  transition-colors duration-300 group-hover:text-green-400">
          {formatPrice(
            typeof flightClass.price.adult === "number"
              ? flightClass.price.adult
              : 0,
            flightClass.price.currency
          )}
          <div className="flex flex-col sm:flex-row xl:justify-end items-start xl:items-center font-light text-sm list-inline text-center justify-content-sm-between mb-0 pb-2 text-gray-800 dark:text-gray-400">
            per traveler
          </div>
        </div>
        <div className="flex flex-col xs:flex-row sm:flex-col md:items-start items-center justify-between">
          {renderButton()}
          <div className="mt-2 flex items-start text-white dark:text-slate-800">
            <EyeIcon
              style={{
                fill: "rgb(239, 68, 68)",
              }}
            />
            <button
              className="text-red-500 underline offset-1"
              aria-controls="feedback-modal"
              onClick={() => setFeedbackModalOpen(true)}
            >
              <span className="underline-offset-1 ml-1">Flight Details</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
