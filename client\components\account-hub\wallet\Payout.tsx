// "use client";

// import React, { useState, useEffect, InputHTMLAttributes, ButtonHTMLAttributes, LabelHTMLAttributes, ReactNode } from 'react';
// // Custom form hook to replace react-hook-form
// const useForm = <T extends Record<string, any>>(options: {
//   defaultValues: T;
//   onSubmit: (data: T) => void | Promise<void>;
// }) => {
//   const [formData, setFormData] = useState<T>(options.defaultValues);
//   const [errors, setErrors] = useState<Record<keyof T, string>>({} as any);
//   const [isSubmitting, setIsSubmitting] = useState(false);

//   const register = (name: keyof T, rules?: { required?: boolean | string; validate?: (value: any) => boolean | string }) => {
//     return {
//       name,
//       value: formData[name],
//       onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
//         const value = e.target.type === 'number' ? parseFloat(e.target.value) || '' : e.target.value;
//         setFormData(prev => ({ ...prev, [name]: value }));
        
//         // Clear error when user types
//         if (errors[name as keyof typeof errors]) {
//           setErrors(prev => ({ ...prev, [name]: '' }));
//         }
//       },
//       onBlur: () => {
//         // Validate on blur
//         if (rules?.required) {
//           const value = formData[name];
//           if (!value && value !== 0) {
//             setErrors(prev => ({
//               ...prev,
//               [name]: typeof rules.required === 'string' ? rules.required : 'This field is required',
//             }));
//           }
//         }
//       },
//     };
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
    
//     // Run validations
//     let hasErrors = false;
//     const newErrors = { ...errors };
    
//     Object.keys(formData).forEach(key => {
//       const value = formData[key as keyof T];
//       // @ts-ignore
//       const rules = options.defaultValues[key]?.rules;
      
//       if (rules?.required && !value && value !== 0) {
//         newErrors[key as keyof T] = typeof rules.required === 'string' ? rules.required : 'This field is required';
//         hasErrors = true;
//       } else if (rules?.validate) {
//         const validationResult = rules.validate(value);
//         if (validationResult !== true) {
//           newErrors[key as keyof T] = validationResult || 'Invalid value';
//           hasErrors = true;
//         }
//       } else {
//         newErrors[key as keyof T] = '';
//       }
//     });
    
//     setErrors(newErrors as any);
    
//     if (!hasErrors) {
//       setIsSubmitting(true);
//       Promise.resolve(options.onSubmit(formData))
//         .finally(() => setIsSubmitting(false));
//     }
//   };

//   const setValue = (name: keyof T, value: any) => {
//     setFormData(prev => ({ ...prev, [name]: value }));
//   };

//   const reset = (values: T = options.defaultValues) => {
//     setFormData(values);
//     setErrors({} as any);
//   };

//   const watch = (name?: keyof T) => {
//     return name ? formData[name] : formData;
//   };

//   return {
//     register,
//     handleSubmit,
//     formState: { errors, isSubmitting },
//     setValue,
//     reset,
//     watch,
//     getValues: () => formData,
//     setError: (name: keyof T, error: { type: string; message: string }) => {
//       setErrors(prev => ({
//         ...prev,
//         [name]: error.message,
//       }));
//     },
//     clearErrors: () => setErrors({} as any),
//   };
// };
// import { ArrowLeft, Building2, Clock, AlertCircle, Plus } from 'lucide-react';

// // Local Button Component
// interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
//   variant?: 'default' | 'ghost' | 'outline' | 'destructive';
//   size?: 'default' | 'sm' | 'lg' | 'icon';
//   isLoading?: boolean;
//   children: ReactNode;
// }

// const Button = ({
//   variant = 'default',
//   size = 'default',
//   className = '',
//   isLoading = false,
//   children,
//   disabled,
//   ...props
// }: ButtonProps) => {
//   const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  
//   const variantStyles = {
//     default: 'bg-red-500 text-white hover:bg-red-600',
//     ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800',
//     outline: 'border border-gray-300 dark:border-gray-600 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800',
//     destructive: 'bg-red-500 text-white hover:bg-red-600',
//   };

//   const sizeStyles = {
//     default: 'h-10 py-2 px-4',
//     sm: 'h-9 px-3 rounded-md',
//     lg: 'h-11 px-8 rounded-md',
//     icon: 'h-10 w-10',
//   };

//   return (
//     <button
//       className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`}
//       disabled={disabled || isLoading}
//       {...props}
//     >
//       {isLoading ? (
//         <>
//           <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
//             <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//             <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//           </svg>
//           {children}
//         </>
//       ) : (
//         children
//       )}
//     </button>
//   );
// };

// // Local Input Component
// interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
//   label?: string;
//   error?: string;
// }

// const Input = ({ className = '', label, error, ...props }: InputProps) => {
//   const inputClass = `flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`;
  
//   return (
//     <div className="w-full">
//       {label && <Label>{label}</Label>}
//       <input className={inputClass} {...props} />
//       {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
//     </div>
//   );
// };

// // Local Label Component
// interface LabelProps extends LabelHTMLAttributes<HTMLLabelElement> {}

// const Label = ({ className = '', ...props }: LabelProps) => {
//   return (
//     <label
//       className={`block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300 ${className}`}
//       {...props}
//     />
//   );
// };

// // Local Toast Hook
// const useToast = () => {
//   return {
//     toast: (options: { title: string; description: string; variant?: 'default' | 'destructive' }) => {
//       console.log('Toast:', options);
//       // This is a simple implementation that logs to console
//       // In a real app, you might want to use a proper toast library
//       const toast = document.createElement('div');
//       toast.className = `fixed top-4 right-4 p-4 rounded-md ${
//         options.variant === 'destructive' ? 'bg-red-500' : 'bg-gray-800'
//       } text-white shadow-lg z-50 max-w-sm`;
//       toast.innerHTML = `
//         <h3 class="font-medium">${options.title}</h3>
//         <p class="text-sm opacity-90">${options.description}</p>
//       `;
//       document.body.appendChild(toast);
//       setTimeout(() => {
//         document.body.removeChild(toast);
//       }, 5000);
//     },
//   };
// };

// // Local cn utility
// const cn = (...classes: (string | undefined)[]) => {
//   return classes.filter(Boolean).join(' ');
// };

// interface FormData {
//   amount: string;
//   currency: string;
//   note?: string;
// }

// const Payout = () => {
//   const { toast } = useToast();
//   const [isLoading, setIsLoading] = useState(false);
//   const [hasBankAccount, setHasBankAccount] = useState(false);
//   const [characterCount, setCharacterCount] = useState(0);
  
//   const onSubmit = async (data: FormData) => {
//     try {
//       setIsLoading(true);
//       // Simulate API call
//       await new Promise(resolve => setTimeout(resolve, 1000));
      
//       toast({
//         title: 'Payout Requested',
//         description: `Your payout of ${data.amount} ${data.currency} is being processed.`,
//       });
      
//       // Reset form after successful submission
//       reset();
//     } catch (error) {
//       toast({
//         title: 'Error',
//         description: 'Failed to process payout. Please try again.',
//         variant: 'destructive',
//       });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const {
//     register,
//     handleSubmit: handleFormSubmit,
//     formState: { errors, isSubmitting },
//     watch,
//     setValue,
//     reset
//   } = useForm<FormData>({
//     defaultValues: {
//       amount: '0.00',
//       currency: 'USD',
//       note: ''
//     },
//     onSubmit,
//   });

//   const amount = watch('amount');
//   const note = watch('note') as string || '';

//   useEffect(() => {
//     // Simulate checking for bank accounts
//     const checkBankAccounts = async () => {
//       try {
//         // const response = await fetch('/api/bank-accounts');
//         // const data = await response.json();
//         // setHasBankAccount(data.accounts.length > 0);
//         setHasBankAccount(false); // For demo purposes
//       } catch (error) {
//         console.error('Error checking bank accounts:', error);
//         toast({
//           title: 'Error',
//           description: 'Failed to load bank accounts',
//           variant: 'destructive',
//         });
//       }
//     };

//     checkBankAccounts();
//   }, [toast]);

//   useEffect(() => {
//     setCharacterCount(note?.length || 0);
//   }, [note]);

//   const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const value = e.target.value;
//     // Allow only numbers and one decimal point
//     if (/^\d*\.?\d*$/.test(value) || value === '') {
//       setValue('amount', value as any);
//     }
//   };

//   const handleNoteChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
//     const value = e.target.value;
//     if (value.length <= 200) {
//       setValue('note', value as any);
//       setCharacterCount(value.length);
//     }
//   };

//   const handleAddBankAccount = () => {
//     // Navigate to add bank account page or open modal
//     console.log('Navigate to add bank account');
//   };

//   const handleFormSubmission = (e: React.FormEvent) => {
//     e.preventDefault();
//     handleFormSubmit(e);
//   };

//   return (
//     <div className="fixed inset-0 z-50 overflow-y-auto">
//       <div className="fixed inset-0 bg-black/50 transition-opacity" />
//       <div className="flex min-h-full items-center justify-center p-4">
//         <div className="relative w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 shadow-xl transition-all animate-modal">
//           <div className="p-6">
//             <div className="flex items-center justify-between mb-6">
//               <div className="flex items-center space-x-4">
//                 <Button
//                   variant="ghost"
//                   size="icon"
//                   className="text-gray-400 hover:text-gray-900 dark:hover:text-white"
//                   onClick={() => window.history.back()}
//                 >
//                   <ArrowLeft className="h-5 w-5" />
//                   <span className="sr-only">Back</span>
//                 </Button>
//                 <h2 className="text-xl font-bold text-gray-900 dark:text-white">
//                   Request Payout
//                 </h2>
//               </div>
//             </div>

//             <form onSubmit={handleFormSubmission} className="space-y-6">
//               {/* Balance Card */}
//               <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-6">
//                 <div className="flex items-center space-x-3">
//                   <div className="p-2 rounded-lg bg-gray-200 dark:bg-gray-600">
//                     <Building2 className="h-6 w-6 text-gray-600 dark:text-gray-300" />
//                   </div>
//                   <div>
//                     <p className="text-sm text-gray-500 dark:text-gray-400">Current Balance</p>
//                     <p className="text-2xl font-bold text-gray-900 dark:text-white">JOD 0.00</p>
//                   </div>
//                 </div>
//               </div>

//               {/* Payout Amount */}
//               <div>
//                 <Label htmlFor="amount" className="mb-2 block">
//                   Payout Amount <span className="text-red-500">*</span>
//                 </Label>
//                 <div className="relative">
//                   <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                     <span className="text-gray-500 dark:text-gray-400 text-sm">JOD</span>
//                   </div>
//                   <Input
//                     id="amount"
//                     type="text"
//                     className={cn(
//                       'pl-16 py-6 text-base',
//                       errors.amount && 'border-red-500',
//                       !hasBankAccount && 'opacity-50 cursor-not-allowed'
//                     )}
//                     disabled={!hasBankAccount}
//                     {...register('amount', {
//                       required: 'Amount is required',
//                       validate: (value: string) => {
//                         const numValue = parseFloat(value);
//                         return numValue > 0 || 'Amount must be greater than 0';
//                       }
//                     })}
//                     onChange={handleAmountChange}
//                     aria-invalid={errors.amount ? 'true' : 'false'}
//                   />
//                 </div>
//                 {errors.amount && (
//                   <p className="mt-1 text-sm text-red-500">{errors.amount.message}</p>
//                 )}
//               </div>

//               {/* Bank Account Selection */}
//               <div>
//                 <Label className="mb-2 block">
//                   Select Bank Account <span className="text-red-500">*</span>
//                 </Label>
                
//                 {!hasBankAccount ? (
//                   <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-6 text-center">
//                     <div className="flex justify-center mb-4">
//                       <Building2 className="h-12 w-12 text-gray-400" />
//                     </div>
//                     <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
//                       No Bank Account Found
//                     </h3>
//                     <p className="text-gray-500 dark:text-gray-400 mb-6">
//                       You need to add a bank account before requesting a payout.
//                     </p>
//                     <Button
//                       type="button"
//                       onClick={handleAddBankAccount}
//                       className="bg-red-500 hover:bg-red-600 text-white"
//                     >
//                       <Plus className="mr-2 h-4 w-4" />
//                       Add Bank Account
//                     </Button>
//                   </div>
//                 ) : (
//                   <div className="border rounded-lg p-4">
//                     {/* Bank account selection would go here */}
//                     <p className="text-gray-500 dark:text-gray-400">Bank account selection will appear here</p>
//                   </div>
//                 )}
                
//                 {!hasBankAccount && (
//                   <div className="mt-2 flex items-center space-x-2 text-yellow-500 text-sm">
//                     <AlertCircle className="h-4 w-4 flex-shrink-0" />
//                     <p>Please add a bank account to proceed with the payout request</p>
//                   </div>
//                 )}
//               </div>

//               {/* Note */}
//               <div>
//                 <Label htmlFor="note" className="mb-2 block">
//                   Note <span className="text-gray-500">(Optional)</span>
//                 </Label>
//                 <textarea
//                   id="note"
//                   className={cn(
//                     'flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
//                     'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
//                     !hasBankAccount && 'opacity-50 cursor-not-allowed'
//                   )}
//                   maxLength={200}
//                   disabled={!hasBankAccount}
//                   {...register('note')}
//                 />
//                 <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
//                   {characterCount}/200 characters
//                 </p>
//               </div>

//               {/* Info Box */}
//               <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
//                 <div className="flex items-start space-x-3">
//                   <Clock className="h-5 w-5 text-blue-500 dark:text-blue-400 flex-shrink-0 mt-0.5" />
//                   <p className="text-sm text-blue-700 dark:text-blue-300">
//                     Payouts are typically processed within 3-5 business days. The funds will be transferred to your selected bank account.
//                   </p>
//                 </div>
//               </div>

//               {/* Submit Button */}
//               <Button
//                 type="submit"
//                 className="w-full py-6 text-base font-medium"
//                 disabled={!hasBankAccount || isLoading}
//                 isLoading={isLoading}
//               >
//                 {isLoading ? 'Processing...' : 'Request Payout'}
//               </Button>
//             </form>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Payout;