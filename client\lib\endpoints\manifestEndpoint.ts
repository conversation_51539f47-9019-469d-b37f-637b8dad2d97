// server base url
import { API_VERSION } from "../constants/apiVersion";
const SERVER_URL = process.env.SERVER_URL;
// BASE URL
const BASE_URL = SERVER_URL + API_VERSION + "/manifest";

// AGENCY ENDPOINT
const manifestUrl = {
  getManifestTickets: (page: number, pageSize: number) => `${BASE_URL}/?page=${page}&pageSize=${pageSize}`,
  sendManifestEmail: (): string => `${BASE_URL}/send-email`,
  downloadPdf: (id: string) => `${BASE_URL}/download/${id}`,
};

export default manifestUrl;
