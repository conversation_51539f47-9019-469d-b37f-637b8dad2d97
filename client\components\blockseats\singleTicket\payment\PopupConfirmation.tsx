import React, { useState, useCallback, useEffect } from "react";
import { useDebouncedCallback } from "use-debounce";
import { CheckCircle, AlertCircle, Loader } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import {
  selectPassengerCounts,
  setBookingConfirmationData,
} from "@/redux/features/BookingConfirmationSlice";
import {
  createInternalBooking,
  createThirdPartyBooking,
} from "@/lib/data/bookingData";
import PaymentDialog from "./PopupPaymentForm";
import { CreateInternalBookingDto } from "@/utils/types/booking.types";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { useSelector } from "react-redux";
import { selectFullTicket } from "@/redux/features/BookingConfirmationSlice";
import { validateBookingData } from "@/utils/validation/bookingValidation";
import { RootState } from "@/redux/store";
import { redirectWithParams } from "@/utils/functions/redirectWithParams";
import PopupConfirmationThirdParty from "./PopupConfirmationThirdParty";
import ReactDOM from "react-dom";

// Utility to remove undefined fields from an object
function removeUndefinedFields<T extends object>(obj: T): T {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, v]) => v !== undefined)
  ) as T;
}

/**
 * Popup Confirmation Component Props
 */
interface PopupConfirmationProps {
  /**
   * Booking data for internal booking
   */
  bookingData: CreateInternalBookingDto;
  /**
   * Callback function to propagate booking result upwards
   */
  onBookingResult?: (result: any) => void;

  /**
   * Callback function when user confirms the action
   * @default undefined
   */
  onConfirm?: () => void;

  /**
   * Callback function when user cancels the action
   * @default undefined
   */
  onCancel?: () => void;

  /**
   * Type of action being confirmed
   * @default undefined
   */
  actionType?: "quickHold" | "reserveSeat" | null;

  /**
   * Custom confirmation message
   * @default "Are you sure you want to perform this action? This cannot be undone."
   */
  message?: string;

  /**
   * Custom confirmation title
   * @default "Confirm Action"
   */
  title?: string;

  /**
   * Callback for timer expiry (optional)
   */
  onTimerExpired?: () => void;
}

/**
 * Popup Confirmation Component
 * Displays a modal dialog for action confirmation
 */
// --- Type guards for Booking vs ThirdPartyBookingResponse ---
function isThirdPartyBookingResponse(
  obj: any
): obj is import("@/utils/types/booking.types").ThirdPartyBookingResponse {
  return (
    obj &&
    typeof obj === "object" &&
    "booking" in obj &&
    "eTicket" in obj &&
    "receipt" in obj
  );
}
function isBooking(
  obj: any
): obj is import("@/utils/types/booking.types").Booking {
  return obj && typeof obj === "object" && "id" in obj && "ticketId" in obj;
}

const PopupConfirmation: React.FC<PopupConfirmationProps> = ({
  onConfirm,
  onCancel,
  onBookingResult,
  actionType,
  message = "Are you sure you want to perform this action? This cannot be undone.",
  title = "Confirm Action",
  bookingData,
  onTimerExpired,
}) => {
  // Get the original full ticket from Redux, fallback to bookingData
  const fullTicket = useSelector(selectFullTicket) || bookingData;
  const itinerary = useSelector(
    (state: RootState) => state.ticketSearchForm.value.itinerary
  );
  const bookingType = useSelector(
    (state: RootState) => state.bookingConfirmation.bookingType
  );
  const passengerCounts = useSelector(selectPassengerCounts);

  // State to control popup visibility, loading state, and success state
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [isFailure, setIsFailure] = useState<boolean>(false);
  const [customError, setCustomError] = useState<string | null>(null);
  const [showLoading, setShowLoading] = useState<boolean>(false);
  const [showResult, setShowResult] = useState<boolean>(false);
  const [modalState, setModalState] = useState<
    "confirmation" | "payment" | "confirmAction" | null
  >("confirmation");
  const [pendingPaymentData, setPendingPaymentData] = useState<any>(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const [showThirdPartyConfirmation, setShowThirdPartyConfirmation] =
    useState<boolean>(false);
  const [confirmedBooking, setConfirmedBooking] = useState<any | null>(null);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(
    null
  );

  useEffect(() => {
    // Create a dedicated container for the portal
    const container = document.createElement("div");
    container.id = "third-party-portal";
    document.body.appendChild(container);
    setPortalContainer(container);

    return () => {
      if (container) document.body.removeChild(container);
    };
  }, []);

  useEffect(() => {
    // Debug third-party modal state
    if (showThirdPartyConfirmation) {
      console.log("Third-party confirmation modal is active");
    }
  }, [showThirdPartyConfirmation]);
  // Throttled handler for confirmation action (robust, deduplicated)
  // Throttle booking confirmation to prevent rapid repeated requests
  const handleConfirm = useDebouncedCallback(() => {
    setIsLoading(true);
    setIsFailure(false);
    setIsSuccess(false);
    setShowLoading(true);
    setShowResult(false);

    // Block back navigation while processing
    const blockBackNavigation = (e: PopStateEvent) => {
      e.preventDefault();
      window.history.pushState(null, "", window.location.href);
    };
    window.history.pushState(null, "", window.location.href);
    window.addEventListener("popstate", blockBackNavigation);

    let currentUrl = window.location.pathname + window.location.search;

    // This regex matches "/blockseats/list/<dynamicId>/checkout"
    const pattern = /\/blockseats\/list\/[^/]+\/checkout/;
    currentUrl = currentUrl.replace(pattern, "/blockseats/list");
    // Show loading for 5s, then process booking
    setTimeout(() => {
      (async () => {
        try {
          let booking;
          // Validate bookingData

          const isBookingSuccess = (
            b: any
          ): b is {
            success: boolean;
            requestId?: string;
            id?: string;
            error?: any;
          } => {
            return !!(
              b &&
              typeof b === "object" &&
              "success" in b &&
              (b.requestId || b.id)
            );
          };
          if (!bookingData || !bookingData.source) {
            setIsSuccess(false);
            setIsFailure(true);
            setCustomError("Missing or invalid booking data.");
            setShowLoading(false);
            setShowResult(true);

            onBookingResult?.({
              success: false,
              error: "Missing or invalid booking data.",
            });
            return;
          }
          if (bookingData.source === "THIRD_PARTY") {
            booking = await createThirdPartyBooking(bookingData);

            if (booking) {
              setIsSuccess(true);
              // setIsFailure(false);
              setShowLoading(false);
              // setIsVisible(false);
              // setShowResult(true);
              // // Redirect to ManageBooking
              // router.push(
              //   `/blockseats/list/${booking.booking.id}/checkout/booking-confirmation`
              // );
              setShowThirdPartyConfirmation(true);
              setConfirmedBooking(booking.booking);
              // Store data in Redux for potential later use
              if (isThirdPartyBookingResponse(booking)) {
                dispatch(
                  setBookingConfirmationData({
                    bookingResult: {
                      success: true,
                      booking: booking.booking,
                      eTicket: booking.eTicket,
                      receipt: booking.receipt,
                      message: booking.message || undefined,
                    },
                    travelerData: bookingData?.travelers || null,
                    ticket: bookingData || null,
                    fullTicket: fullTicket,
                    passengerCounts: passengerCounts,
                  })
                );
              }
            } else {
              setIsSuccess(false);
              setIsFailure(true);
              setCustomError("Failed to create booking");
              setShowLoading(false);
              setShowResult(true);
              onBookingResult?.({
                success: false,
                error: "Failed to create booking",
              });
            }
          } else if (bookingData.source === "INTERNAL") {
            const validationError = validateBookingData(bookingData);
            if (validationError) {
              setIsSuccess(false);
              setIsFailure(true);
              setCustomError(validationError);
              setShowLoading(false);
              setShowResult(true);
              onBookingResult?.({ success: false, error: validationError });
              return;
            }
            try {
              // Call the internal booking API
              const response = await createInternalBooking(bookingData);

              // Extract the nested booking data
              const bookingDataFromResponse = response.booking.booking;

              // Determine if this is a round-trip (array of bookings) or one-way (single booking)
              const isRoundTrip = Array.isArray(bookingDataFromResponse);
              const mainBooking = isRoundTrip
                ? bookingDataFromResponse[0]
                : bookingDataFromResponse;

              // Store result in Redux
              dispatch(
                setBookingConfirmationData({
                  bookingResult: bookingDataFromResponse,
                  travelerData: bookingData.travelers,
                  // ticket: [bookingData.ticketId || null,
                  // bookingData.returnTicketId || null],
                  ticket: [
                    mainBooking.ticketId || null,
                    isRoundTrip
                      ? bookingDataFromResponse[1]?.ticketId || null
                      : bookingData.returnTicketId || null,
                  ],
                  fullTicket: fullTicket || null,
                  passengerCounts: passengerCounts || {
                    adults: 1,
                    children: 0,
                    infants: 0,
                    travelClass: "Economy",
                  },
                })
              );

              setIsSuccess(true);
              setShowLoading(false);
              setIsVisible(false);

              // Redirect to ManageBooking with the booking ID
              if (isRoundTrip) {
                router.push(
                  `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingDataFromResponse[0].id}&returnBookingId=${bookingDataFromResponse[1].id}&fromConfirmation=true`
                );
              } else {
                router.push(
                  `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingDataFromResponse.id}&fromConfirmation=true`
                );
              }

              onBookingResult?.({
                success: true,
                booking: bookingDataFromResponse,
              });
              return;
            } catch (err: any) {
              setIsSuccess(false);
              setIsFailure(true);
              setCustomError(
                err?.message || "Booking failed. Please try again."
              );
              setShowLoading(false);
              setShowResult(true);
              onBookingResult?.({ success: false, error: err });
              return;
            }
          } else {
            setIsSuccess(false);
            setIsFailure(true);
            setCustomError("Unknown booking source.");
            setShowLoading(false);
            setShowResult(true);

            onBookingResult?.({
              success: false,
              error: "Unknown booking source.",
            });
            return;
          }

          // --- Normalize booking result for consumer ---

          let normalizedResult: any = null;
          if (
            bookingData.source === "THIRD_PARTY" &&
            isThirdPartyBookingResponse(booking)
          ) {
            normalizedResult = {
              success: true,
              booking: booking.booking,
              eTicket: booking.eTicket,
              receipt: booking.receipt,
              message: booking.message || undefined,
            };
          } else if (isBooking(booking)) {
            normalizedResult = {
              success: true,
              booking: booking,
            };
          }
          if (normalizedResult && normalizedResult.success) {
            setIsSuccess(true);
            setIsFailure(false);
            setCustomError(null);
            // Propagate booking result upwards
            onBookingResult?.(normalizedResult);
            // Show success message
            dispatch(setMsg({ success: true, message: "Booking successful!" }));
            // Store booking and traveler data in Redux for confirmation page
            dispatch(
              setBookingConfirmationData({
                bookingResult: normalizedResult,
                travelerData: bookingData?.travelers || null,
                ticket: bookingData || null,
                fullTicket: fullTicket,
                passengerCounts: passengerCounts,
              })
            );
            // --- REDIRECT LOGIC ---
            if (typeof window !== "undefined") {
              const currentUrl =
                window.location.pathname + window.location.search;
              const checkoutIndex = currentUrl.indexOf("/checkout");
              if (checkoutIndex !== -1) {
                const before = currentUrl.substring(
                  0,
                  checkoutIndex + "/checkout".length
                );
                // const after = currentUrl.substring(checkoutIndex + "/checkout".length);
                // newPath = `${before}/booking-confirmation${after}`;
                // Prefer bookingId from normalizedResult if available
                const bookingId =
                  normalizedResult.booking?.id ||
                  normalizedResult.booking?.bookingID ||
                  normalizedResult.bookingId ||
                  normalizedResult.id;
                let newPath = `${before}/booking-confirmation`;
                if (bookingId) {
                  newPath += `?bookingId=${bookingId}`;
                } else {
                  // fallback to current search params if no bookingId
                  newPath += window.location.search;
                }
                // router.push(newPath);
              }
            }
          } else {
            // Robust error extraction
            let errorMsg =
              "Booking failed. Please check your details and try again.";
            if (booking && typeof booking === "object") {
              if ("error" in booking) {
                if (typeof booking.error === "string") errorMsg = booking.error;
                else if (
                  booking.error &&
                  typeof (booking.error as any)?.message === "string"
                )
                  errorMsg = (booking.error as any).message;
              }
            }
            setIsSuccess(false);
            setIsFailure(true);
            setCustomError(errorMsg);
            setShowLoading(false);
            setShowResult(true);

            onBookingResult?.({ success: false, error: errorMsg });
            dispatch(setMsg({ success: false, message: errorMsg }));
            // Redirect to SearchTicketsList page after 2 seconds
            redirectWithParams("/blockseats/list", 2000);
          }
        } catch (err: any) {
          setIsSuccess(false);
          setIsFailure(true);
          setShowLoading(false);
          setShowResult(true);
          let errorMessage =
            err?.response?.data?.message ||
            "Oops! Your booking seems to have taken a detour. Time for a Plan B.";
          // For legacy cases, still check for INSUFFICIENT_WALLET
          if (
            err?.response?.data?.code === "INSUFFICIENT_WALLET" &&
            !err?.response?.data?.message
          ) {
            errorMessage =
              "Insufficient wallet balance. Please top up your wallet to proceed.";
          }
          if (onBookingResult) onBookingResult({ success: false, error: err });
          setIsSuccess(false);
          setIsFailure(true);
          setCustomError(errorMessage);
          setShowLoading(false);
          setShowResult(true);

          // Redirect to SearchTicketsList page after 2 seconds
          redirectWithParams('/blockseats/list', 2000); // 2 second delay to show the error message
        } finally {
          setIsLoading(false);
          window.removeEventListener("popstate", blockBackNavigation);

          // Only redirect if there was an error (not on success)
          if (isFailure && !isSuccess) {
            // Show error for 2 seconds before redirecting
            setTimeout(() => {
              if (onTimerExpired) {
                onTimerExpired();
              } else {
                // Use the utility function to redirect with preserved parameters
                redirectWithParams("/blockseats/list");
              }
            }, 2000);
          }
        }
      })();
    }, 5000); // 5 seconds loading
  }); // 5 seconds loading

  // Handler for cancellation action
  const handleCancel = useCallback(() => {
    setIsVisible(false);
    if (onCancel) {
      onCancel();
    }
  }, [onCancel]);

  // If popup is not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  // Payment form submit handler
  const handlePaymentSubmit = async (paymentData: any) => {
    setModalState("confirmation");
    setShowLoading(true);
    try {
      // Merge paymentData with bookingData if needed
      const cleanedBookingData = removeUndefinedFields({
        ...pendingPaymentData,
        payment: paymentData,
      });
      // const booking = await createInternalBooking(cleanedBookingData);
      setIsSuccess(true);
      setShowResult(true);
      // onBookingResult?.({ success: true, booking });
    } catch (error) {
      setIsFailure(true);
      setCustomError("Failed to create booking after payment.");
      setShowResult(true);
    } finally {
      setShowLoading(false);
    }
  };

  return (
    <div className="p-8">
      {/* Unified Modal Overlay: only one modal rendered at a time */}
      {modalState === "payment" ? (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {/* Payment Modal (no confirmation modal content) */}
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
          <div className="relative rounded-xl shadow-lg max-w-md w-full mx-4 p-6 z-10 bg-gray-100 dark:bg-gray-800">
            <PaymentDialog
              initialVisibility={true}
              bookingPrice={pendingPaymentData?.price?.toString() || ""}
              systemCurrency={pendingPaymentData?.currency || "JOD"}
              onClose={() => setModalState("confirmation")}
              onSubmit={handlePaymentSubmit}
            />
          </div>
        </div>
      ) : (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {/* Background overlay - click events blocked during loading/success */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            // onClick={isLoading || isSuccess ? undefined : handleCancel}
          ></div>

          {/* Popup content */}
          <div className="relative rounded-xl shadow-lg max-w-md w-full mx-4 p-6 z-10 bg-white dark:bg-gray-800">
            {showLoading ? (
              /* Loading state overlay */
              <div className="flex flex-col items-center text-center py-8 h-64 justify-center">
                {/* Loading spinner */}
                <div className="mb-4">
                  <Loader className="h-12 w-12 text-red-500 animate-spin" />
                </div>

                {/* Loading message */}
                <p className="text-gray-700 dark:text-gray-300 text-lg font-medium">
                  Please remain seated while we prepare your booking for
                  takeoff...
                </p>
                <p className="text-gray-700 dark:text-gray-300 mt-2">
                  This may take a few seconds.
                </p>
              </div>
            ) : showResult && isSuccess ? (
              <>
                /* Success state overlay */
                <div className="flex flex-col items-center text-center py-8 h-64 justify-center">
                  <div className="mb-4">
                    {/* Success icon */}
                    <CheckCircle className="h-12 w-12 text-green-500" />
                  </div>

                  {/* Success message */}
                  <p className="text-gray-700 dark:text-gray-300 text-lg font-medium">
                    Awesome! Your booking has been processed.
                  </p>
                  {/** Show the request ID if available */}
                  {(() => {
                    let bookingId = null;
                    if (typeof window !== "undefined") {
                      const stateBooking = JSON.parse(
                        window.localStorage.getItem("bookingResult") || "{}"
                      );
                      bookingId =
                        stateBooking?.booking?.id ||
                        stateBooking?.booking?.bookingID ||
                        stateBooking?.bookingId ||
                        stateBooking?.id;
                    }
                    if (!bookingId && typeof window !== "undefined") {
                      // fallback: try to get from Redux or other state
                      bookingId = (window as any)?.bookingId;
                    }
                    return (
                      <p className="text-gray-700 dark:text-gray-300 mt-2">
                        <span className="font-semibold">Request ID:</span>{" "}
                        {bookingId || "N/A"}
                      </p>
                    );
                  })()}

                  {/* Navigation buttons */}
                  <div className="flex flex-row gap-4 mt-6">
                    <button
                      className="py-2 px-4 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700"
                      onClick={() => {
                        // Navigate to My Bookings
                        if (typeof window !== "undefined") {
                          window.location.href = "/blockseats/myBookings";
                        }
                      }}
                    >
                      Go to My Bookings
                    </button>
                    <button
                      className="py-2 px-4 rounded-lg bg-green-600 text-white font-semibold hover:bg-green-700"
                      onClick={() => {
                        // Navigate to Manage Internal Booking
                        let bookingId = null;
                        if (typeof window !== "undefined") {
                          const stateBooking = JSON.parse(
                            window.localStorage.getItem("bookingResult") || "{}"
                          );
                          bookingId =
                            stateBooking?.booking?.id ||
                            stateBooking?.booking?.bookingID ||
                            stateBooking?.bookingId ||
                            stateBooking?.id;
                        }
                        if (bookingId) {
                          window.location.href = `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingId}`;
                        } else {
                          window.location.href =
                            "/blockseats/singleTicket/payment/ManageBooking";
                        }
                      }}
                    >
                      Manage Internal Booking
                    </button>
                  </div>
                </div>
                {showThirdPartyConfirmation && portalContainer && (
                  <PopupConfirmationThirdParty
                    onConfirm={() => {
                      setIsVisible(false);
                      router.push("/ticket-hub/myBookings");
                      if (onConfirm) {
                        onConfirm();
                      }
                    }}
                    onCancel={() => {
                      setIsVisible(false);
                      setShowThirdPartyConfirmation(false);
                      router.push("/blockseats");
                      if (onCancel) {
                        onCancel();
                      }
                    }}
                  />
                )}
              </>
            ) : showResult && isFailure ? (
              /* Failure content */
              <div className="flex flex-col items-center text-center h-64 justify-center">
                {/* Icon */}
                <div className="h-16 w-16 bg-red-500/10 rounded-full flex items-center justify-center mb-4">
                  {/* Warning icon */}
                  <AlertCircle className="h-10 w-10 text-red-500" />
                </div>

                {/* Failure message */}
                <p className="text-gray-700 dark:text-gray-300 text-lg font-medium">
                  {customError ||
                    "Oops! Your booking seems to have taken a detour. Time for a Plan B."}
                </p>
                <p className="text-gray-700 dark:text-gray-300 mt-2">
                  Resetting to your booking journey...
                </p>
              </div>
            ) : (
              /* Confirmation content */
              <div className="flex flex-col items-center text-center h-64 justify-center">
                {/* Icon */}
                <div className="h-16 w-16 bg-red-500/10 rounded-full flex items-center justify-center mb-4">
                  {/* Warning icon */}
                  {actionType === "quickHold" && (
                    <AlertCircle className="h-10 w-10 text-red-500" />
                  )}
                  {/* Success icon */}
                  {actionType === "reserveSeat" && (
                    <CheckCircle className="h-10 w-10 text-red-500" />
                  )}
                </div>

                {/* Title */}
                <h2 className="text-2xl font-bold text-gray-700 dark:text-gray-300 mb-2">
                  {title}
                </h2>

                {/* Message */}
                <p className="text-gray-700 dark:text-gray-300 mb-6">
                  {message}
                </p>

                {/* Action buttons */}
                <div className="flex w-full space-x-4">
                  <button
                    className="w-1/2 py-3 px-6 rounded-lg font-semibold bg-gray-700 dark:bg-gray-600 text-white dark:text-gray-300 hover:bg-gray-600 dark:hover:bg-gray-700"
                    onClick={handleCancel}
                    disabled={isLoading || isSuccess}
                  >
                    Cancel
                  </button>
                  <button
                    className="w-1/2 py-3 px-6 rounded-lg font-semibold bg-red-500 dark:bg-red-600 text-white dark:text-gray-300 hover:bg-red-600 dark:hover:bg-red-700"
                    onClick={handleConfirm}
                    disabled={isLoading || isSuccess}
                  >
                    Confirm
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PopupConfirmation;
