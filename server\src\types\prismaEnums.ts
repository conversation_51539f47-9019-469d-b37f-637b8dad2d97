// // Local Prisma enums for use instead of @prisma/client
// // Keep these in sync with your schema.prisma

// export interface User {
//   id: string;
//   firstName: string;
//   lastName: string;
//   username: string;
//   email: string;
//   hashedPassword: string;
//   role?: Role;
//   roleType?: RoleType;
//   accountStatus?: AccountStatus;
//   createdAt?: Date;
//   updatedAt?: Date;
// }

// export interface TeamMember {
//   id: string;
//   refId?: string;
//   username?: string;
//   firstName?: string;
//   lastName?: string;
//   email?: string;
//   hashedPassword?: string;
//   agencyName?: string;
//   nationality?: string;
//   dateOfBirth?: string;
//   gender?: string;
//   logo?: string;
//   website?: string;
//   role?: Role;
//   subRole?: TeamMemberRole;
//   department?: Department;
//   accountStatus?: AccountStatus;
//   createdAt?: Date;
//   updatedAt?: Date;
// }

// export interface AgencyAgent {
//   id: string;
//   firstName?: string;
//   lastName?: string;
//   agencyName?: string;
//   username?: string;
//   email?: string;
//   agencyId: string;
//   nationality?: string;
//   dateOfBirth?: string;
//   hashedPassword?: string;
//   role?: Role;
//   subRole?: AgentRole;
//   roleType?: RoleType;
//   department?: Department;
//   status?: TeamMemberStatus;
//   agency?: any; // Reference to the agency
//   createdAt?: Date;
//   updatedAt?: Date;
// }

// export interface FlightTicket {
//   id: string;
//   refId: string;
//   ticketStatus: string;
//   previousStatus?: string;
//   description?: string;
//   seats: number;
//   remainingSeats: number;
//   departureId?: string;
//   arrivalId?: string;
//   flightDate: string;
//   departureTime?: string;
//   arrivalTime?: string;
//   duration?: string;
//   stops: number;
//   ownerId: string;
//   agencyAgentId?: string;
//   updated: boolean;
//   createdBy?: string;
//   archived: boolean;
//   archivedAt?: Date;
//   createdAt: Date;
//   updatedAt: Date;

//   // Relations (optional)
//   owner?: User;
//   agencyAgent?: AgencyAgent;
//   ticketAccess?: TicketAccess[];
//   departure?: any; // FlightLocation
//   arrival?: any; // FlightLocation
//   bookedSeats?: any[]; // BookedFlightSeat[]
//   flightClasses?: any[]; // FlightClass[]
//   segments?: any[]; // FlightSegment[]
//   purchasedSeats?: any; // PurchasedFlightTicket
//   ticketHistoryLogs?: any[]; // TicketHistoryLog[]
//   notifications?: any[]; // Notification[]
// }

// export interface TicketAccess {
//   id: string;
//   ticketId: string;
//   teamMemberId: string;
//   accessLevel: AccessLevel;
//   createdById: string;
//   createdAt: Date;
//   updatedAt: Date;

//   // Relations (optional)
//   ticket?: FlightTicket;
//   createdBy?: User;
// }

// export interface Booking {
//   id: string;
//   ticketId?: string;
//   userId: string;
//   agencyAgentId?: string;
//   teamMemberId?: string;
//   sellerAgencyId?: string;
//   buyerAgencyId?: string;
//   requestId: string;
//   source: BookingSource;
//   type: BookingType;
//   status: BookingStatus;
//   totalSeats: number;
//   totalAmount: number;
//   initialHoldType?: BookingType;
//   timerDuration: number;
//   timerStartedAt: Date;
//   expiresAt: Date;
//   timedOutAt?: Date;
//   cancellationReason?: string;
//   statusReason?: string;
//   transactionId?: string;
//   transactionDate?: Date;
//   paymentCompletedAt?: Date;
//   meta?: any;
//   createdAt?: Date;
//   updatedAt?: Date;

//   // Relations (optional)
//   user?: User;
//   ticket?: FlightTicket;
//   agencyAgent?: AgencyAgent;
//   teamMember?: TeamMember;
//   payment?: Payment;
//   eTickets?: ETicket[];
//   notifications?: Notification[];
//   travelers?: BookingTraveler[];
//   bookedSeats?: any[]; // BookedFlightSeat[]
//   webPushSubscriptions?: WebPushSubscription[];
//   bookingHistoryLogs?: BookingHistory[];
//   receipts?: Receipt[];
// }
// export interface Payment {
//   id: string;
//   bookingId: string;
//   amount: number;
//   paymentMethod?: string;
//   paymentMeta?: any;
//   paidByUserId?: string;
//   currency: string;
//   paymentStatus: PaymentStatus;
//   paymentReference?: string;
//   idempotencyKey?: string;
//   refundedAmount?: number;
//   refundReason?: string;
//   retryCount: number;
//   lastAttempt?: Date;
//   errorMessage?: string;
//   createdAt: Date;
//   updatedAt: Date;

//   // Relations (optional)
//   booking?: Booking;
//   paidByUser?: User;
// }

// export interface BookingTraveler {
//   id: string;
//   bookingId: string;
//   travelerId: string;
//   infoStatus?: TravelerInfoStatus;

//   // Relations (optional)
//   booking?: Booking;
//   traveler?: Traveler;
// }

// export interface Traveler {
//   id: string;
//   title?: CustomerInfoTitle;
//   firstName?: string;
//   lastName?: string;
//   nationality?: string;
//   dateOfBirth?: Date;
//   gender?: string;
//   documentType?: CustomerDocumentType;
//   documentNumber?: string;
//   issuingCountry?: string;
//   expirationDate?: Date;
//   contactEmail?: string;
//   contactPhone?: string;
//   primaryContact?: boolean;
//   createdAt: Date;
//   updatedAt: Date;

//   // Relations (optional)
//   bookings?: BookingTraveler[];
//   bookedSeats?: any[]; // BookedFlightSeat[]
// }

// export interface ETicket {
//   id: string;
//   bookingId: string;
//   eTicketNumber: string;
//   fileUrl?: string;
//   meta?: any;
//   issuedAt: Date;
//   issuedBy?: string;
//   ticketType: TicketType;
//   status: ETicketStatus;
//   voidedAt?: Date;
//   voidReason?: string;
//   reissuedFromId?: string;
//   createdAt: Date;
//   updatedAt: Date;

//   // Relations (optional)
//   booking?: Booking;
//   reissuedFrom?: ETicket;
//   reissues?: ETicket[];
//   receipt?: Receipt;
// }

// export interface WebPushSubscription {
//   id: string;
//   endpoint: string;
//   userId: string;
//   createdAt: Date;
//   updatedAt: Date;
//   teamMemberId?: string;
//   agencyAgentId?: string;
//   bookingId?: string;

//   // Relations (optional)
//   user?: User;
//   teamMember?: TeamMember;
//   agencyAgent?: AgencyAgent;
//   booking?: Booking;
// }

// export interface Notification {
//   id: string;
//   userId: string;
//   type: string;
//   title: string;
//   message: string;
//   read: boolean;
//   createdAt: Date;
//   relatedId?: string;
//   link?: string;
//   priority: number;

//   // Relations (optional)
//   user: User;
//   ticket?: FlightTicket;
//   agencyAgent?: AgencyAgent;
//   teamMember?: TeamMember;
//   booking?: Booking;
// }
// export interface BookingHistory {
//   id: string;
//   bookingId: string;
//   changeType: BookingHistoryChangeType;
//   actorType: ActorType;
//   actorId: string;
//   previousStatus?: BookingStatus;
//   newStatus?: BookingStatus;
//   reason?: string;
//   metadata?: any;
//   createdAt: Date;

//   // Relations (optional)
//   booking?: Booking;
// }
// export interface Receipt {
//   id: string;
//   bookingId: string;
//   eTicketId?: string;
//   receiptNumber: string;
//   fileUrl?: string;
//   issuedAt: Date;
//   issuedBy?: string;
//   receiptType: ReceiptType;
//   status: ReceiptStatus;
//   voidedAt?: Date;
//   voidReason?: string;
//   createdAt: Date;
//   updatedAt: Date;

//   // Relations (optional)
//   booking?: Booking;
//   eTicket?: ETicket;
// }
// export enum CreditTransactionType {
//   TRANSFER = "transfer",
//   REFUND = "refund",
// }

// export enum PaymentStatus {
//   PENDING,
//   COMPLETED,
//   FAILED,
//   REFUNDED,
//   PARTIALLY_REFUNDED,
// }

// export enum TravelerInfoStatus {
//   PLACEHOLDER,
//   COMPLETED,
// }
// export enum TicketType {
//   INTERNAL,
//   THIRD_PARTY,
// }

// export enum ETicketStatus {
//   ISSUED,
//   VOIDED,
//   REISSUED,
//   CANCELLED,
// }
// export enum ReceiptStatus {
//   ISSUED = "ISSUED",
//   VOIDED = "VOIDED",
//   CANCELLED = "CANCELLED",
// }
// export enum ReceiptType {
//   INTERNAL = "INTERNAL",
//   THIRD_PARTY = "THIRD_PARTY",
// }
// export enum BookingHistoryChangeType {
//   CREATE = "CREATE",
//   UPDATE = "UPDATE",
//   TIMEOUT = "TIMEOUT",
//   CANCEL = "CANCEL",
// }

// export enum Role {
//   master = "master",
//   agency = "agency",
//   affiliate = "affiliate",
// }

// export enum RoleType {
//   master_owner = "master_owner",
//   master_admin = "master_admin",
//   master_moderator = "master_moderator",
//   master_accountant = "master_accountant",
//   agency_owner = "agency_owner",
//   agency_admin = "agency_admin",
//   agency_accountant = "agency_accountant",
//   agency_operation = "agency_operation",
//   agency_sales = "agency_sales",
//   affiliate = "affiliate",
// }

// export enum AgentRole {
//   admin = "admin",
//   accountant = "accountant",
//   operation = "operation",
//   sales = "sales",
// }

// export enum TeamMemberRole {
//   admin = "admin",
//   moderator = "moderator",
//   accountant = "accountant",
// }

// export enum AccountStatus {
//   accepted = "accepted",
//   pending = "pending",
//   rejected = "rejected",
//   suspended = "suspended",
//   deactivated = "deactivated",
//   disabled = "disabled",
// }

// export enum SubscriptionStatus {
//   active = "active",
//   inactive = "inactive",
// }

// export enum TeamMemberStatus {
//   active = "active",
//   inactive = "inactive",
// }

// export enum Department {
//   customer_support = "customer_support",
//   management = "management",
//   finance = "finance",
//   marketing = "marketing",
//   sales = "sales",
//   it = "it",
//   operations = "operations",
// }

// export enum InvitationStatus {
//   pending = "pending",
//   accepted = "accepted",
//   expired = "expired",
//   rejected = "rejected",
//   revoked = "revoked",
// }

// export enum BookedFlightSeatStatus {
//   booked = "booked",
//   onHold = "onHold",
//   canceled = "canceled",
// }

// export enum CustomerInfoTitle {
//   MR = "MR",
//   MRS = "MRS",
// }

// export enum CustomerDocumentType {
//   passport = "passport",
//   id_card = "id_card",
// }

// export enum BookingStatus {
//   QUICK_HOLD, // Initial 1hr hold
//   TIMED_OUT, // Hold expired before action
//   PENDING_APPROVAL, // After payment completed, waiting for admin
//   BOOKING_CONFIRMED, // Approved by admin
//   BOOKING_REJECTED, // Rejected by admin
//   CANCELLED_BY_USER, // Cancelled by user
//   CANCELLED_BY_SYSTEM, // e.g., if ticket becomes unavailable
// }

// export enum BookingType {
//   QUICK_HOLD,
//   SUBMIT_BOOKING,
// }

// export enum BookingSource {
//   INTERNAL,
//   THIRD_PARTY,
// }

// export enum ActorType {
//   AGENT,
//   ADMIN,
//   SYSTEM,
//   CUSTOMER,
// }

// export enum AccessLevel {
//   READ,
//   WRITE,
//   ADMIN,
// }
