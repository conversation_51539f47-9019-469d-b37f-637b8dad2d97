// import { Department } from "@prisma/client";

// /**
//  * Formats a department name for display by converting from snake_case and capitalizing each word
//  * @param department - The department name in snake_case
//  * @returns The formatted department name with proper capitalization
//  */
// export const formatDepartmentForDisplay = (department: string): string => {
//   // Replace underscores with spaces and split into words
//   return department
//     .split('_')
//     .map(word => {
//       // Special handling for "it"
//       if (word.toLowerCase() === 'it') return 'IT';
//       return word.charAt(0).toUpperCase() + word.slice(1);
//     })
//     .join(' ');
// };

// /**
//  * Formats a department name for backend storage by converting to snake_case
//  * @param department - The department name in any format
//  * @returns The formatted department name in snake_case
//  */
// export const formatDepartmentForStorage = (department: string): string => {
//   // If it's an enum key (e.g., CUSTOMER_SUPPORT), convert it to the enum value
//   const enumKey = department.toUpperCase() as keyof typeof Department;
//   if (enumKey in Department) {
//     return Department[enumKey].toLowerCase();
//   }

//   // Otherwise, format the string as snake_case
//   return department
//     .toLowerCase()
//     .replace(/\s+/g, '_')
//     .replace(/&/g, 'and');
// };
