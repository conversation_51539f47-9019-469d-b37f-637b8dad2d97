// Server base URL
import { API_VERSION } from "../constants/apiVersion";
const SERVER_URL = process.env.SERVER_URL;

// Base URL for booking endpoints
const BASE_URL = `${SERVER_URL}${API_VERSION}/booking`;

/**
 * Booking endpoint URLs
 *
 * This object contains all the endpoint URLs related to bookings.
 * Each endpoint is documented with a clear description of its purpose.
 */
const bookingUrl = {
  // 1. Create internal booking
  createInternalBooking: `${BASE_URL}/`,

  // 2. Get a booking by ID
  getBookingById: (id: string) => `${BASE_URL}/${id}`,

  // 3. Get bookings by user
  getBookingsByUser: (userId: string) => `${BASE_URL}/user/${userId}`,

  // 4. Get bookings by status
  getBookingsByStatus: (status: string) => `${BASE_URL}/status/${status}`,

  // 4a. Get status of a specific booking
  getBookingStatus: (id: string) => `${BASE_URL}/${id}/status`,

  // 5. Get all bookings (admin, paginated)
  getAllBookings: (page = 1, pageSize = 20) =>
    `${BASE_URL}/all?page=${page}&pageSize=${pageSize}`,

  // 5a. Get all bookings (admin, paginated)
  getSalesAgentNames: () => `${BASE_URL}/sales-agents`,
  getBookingAgentNames: () => `${BASE_URL}/booking-agents`,
  getGlobalAgentNames: () => `${BASE_URL}/global-agents`,
  getCarrierNames: () => `${BASE_URL}/carriers`,
  getAgencyNames: () => `${BASE_URL}/agencies`,

  // 6. Approve booking (admin)
  approveBooking: (id: string) => `${BASE_URL}/${id}/approve`,

  // 7. Reject booking (admin)
  rejectBooking: (id: string) => `${BASE_URL}/${id}/reject`,

  // 8. Cancel booking (user/agent)
  cancelBooking: (id: string) => `${BASE_URL}/${id}/cancel`,

  // 9. Confirm booking (agent action)
  confirmBooking: (id: string) => `${BASE_URL}/${id}/confirm`,

  // 10. Timeout booking (system/admin)
  timeoutBooking: (id: string) => `${BASE_URL}/${id}/timeout`,

  // 11. Release seat (agent/system)
  releaseSeat: (id: string) => `${BASE_URL}/${id}/release-seat`,

  // 12. Complete payment
  completePayment: (id: string) => `${BASE_URL}/${id}/complete-payment`,

  // 13. Identify booking type
  identifyBookingType: `${BASE_URL}/type`,

  // 14. Create third-party booking
  createThirdPartyBooking: `${BASE_URL}/third-party`,

  // 15. Reschedule booking
  rescheduleBooking: (id: string) => `${BASE_URL}/${id}/reschedule`,

  // Get ticket information for rescheduling
  getTicketForReschedule: (id: string) => `${BASE_URL}/${id}/reschedule-info`,

  // 16. Get global bookings (no filters for master users)
  getAllGlobalBookings: (page = 1, pageSize = 20) =>
    `${BASE_URL}/global?page=${page}&pageSize=${pageSize}`,

  // 17. Get all sales
  getAllSales: (page = 1, pageSize = 20) =>
    `${BASE_URL}/sales?page=${page}&pageSize=${pageSize}`,

  // 18. Refund booking
  refundBooking: (id: string) => `${BASE_URL}/${id}/refund`,

  // 19. Update traveler information for a booking
  updateTravelerInfo: (id: string) => `${BASE_URL}/${id}/update-traveler`,
};
export default bookingUrl;
