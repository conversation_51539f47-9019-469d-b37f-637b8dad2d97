import { <PERSON><PERSON><PERSON>, Department, Role } from "@prisma/client";
export interface AgentMemberValidationError {
  field: string;
  message: string;
}

export interface AgentMemberInput {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: Role;
  department: Department;
  subRole?: AgentRole;
}

export interface SanitizedAgentMember {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: string;
  subRole?: string;
  department: string;
  roleType?: string;
  errors: AgentMemberValidationError[];
}
