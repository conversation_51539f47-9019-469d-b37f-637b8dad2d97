import {
  Bell,
  Building,
  CreditCard,
  DollarSign,
  FileText,
  HelpCircle,
  Shield,
  Users,
} from "lucide-react";
import { AgentRole, Department } from "../definitions/agentsDefinitions";

export const NAVIGATION_ITEMS = [
  { id: "agency-details", label: "Agency Overview", icon: Building },
  { id: "organization-settings", label: "Team Management", icon: Users },
  { id: "credit-balance", label: "Credit Balance", icon: DollarSign },
  { id: "privacy-settings", label: "Privacy & Security", icon: Shield },
  { id: "notifications", label: "Notifications", icon: Bell },
];

export const TABLE_HEADERS = [
  { key: "username", label: "User name" },
  { key: "role", label: "Role / Department" },
  { key: "status", label: "Status" },
  { key: "lastLogin", label: "Last login" },
  { key: "createdAt", label: "Date Added" },
  { key: "actions", label: "Actions" },
];

export const statusStyles: {
  [key: string]: { style: string; dot: string };
} = {
  inactive: { style: "bg-red-200 text-red-800", dot: "bg-red-500" }, // Neutral for inactive
  active: { style: "bg-green-100 text-green-800", dot: "bg-green-400" }, // Positive for active
  "customer support": {
    style: "bg-blue-100 text-blue-800",
    dot: "",
  }, // Trust and support
  management: { style: "bg-indigo-100 text-indigo-800", dot: "" }, // Leadership
  finance: { style: "bg-yellow-100 text-yellow-800", dot: "" }, // Money/gold
  marketing: { style: "bg-pink-100 text-pink-800", dot: "" }, // Vibrant and creative
  sales: { style: "bg-purple-100 text-purple-800", dot: "" }, // Associated with strategy
  it: { style: "bg-teal-100 text-teal-800", dot: "" }, // Calm and technological
  operations: { style: "bg-cyan-100 text-cyan-800", dot: "" }, // Efficient and systematic
  admin: { style: "bg-red-100 text-red-800", dot: "" }, // Authority and importance
  operation: {
    style: "bg-emerald-100 text-emerald-800",
    dot: "",
  }, // Balanced and fair
  accountant: { style: "bg-orange-100 text-orange-800", dot: "" }, // Associated with alertness
  default: { style: "bg-gray-100 text-gray-800", dot: "" }, // Neutral fallback
};

export const DEPARTMENTS = Object.values(Department);

export const ROLES = Object.values(AgentRole);
