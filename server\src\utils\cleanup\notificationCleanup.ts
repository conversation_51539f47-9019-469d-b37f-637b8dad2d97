import { prisma } from "../../prisma";
import { acquireLock, releaseLock } from "../locks/jobLock";
import logger from "../logger";

const JOB_NAME = "notificationCleanup";

// Configuration
const CONFIG = {
  // Production mode: 7 days retention
  PROD_RETENTION_DAYS: 7,
  // Testing mode: 1 hour retention
  TEST_RETENTION_HOURS: 1,
  // Batch size for deletion
  BATCH_SIZE: 1000,
} as const;

export type CleanupMode = 'production' | 'testing';

export interface CleanupOptions {
  mode?: CleanupMode;
  dryRun?: boolean;
  batchSize?: number;
}

export interface CleanupResult {
  success: boolean;
  deletedCount: number;
  message: string;
  mode: CleanupMode;
  dryRun: boolean;
  cutoffDate: string;
  error?: string;
}

interface NotificationBatch {
  id: string;
}

/** 
 * Cleans up read notifications older than the specified retention period
 * @param options Configuration options for the cleanup
 */
export async function cleanupOldNotifications(
  options: CleanupOptions = {}
): Promise<CleanupResult> {
  const { 
    mode = 'testing', 
    dryRun = false, 
    batchSize = CONFIG.BATCH_SIZE 
  } = options;
  
  const isTesting = mode === 'testing';
  let deletedCount = 0;
  let lastProcessedId: string | null = null;
  
  if (!(await acquireLock(JOB_NAME))) {
    const message = `[${JOB_NAME}] Lock not acquired, skipping this run.`;
    logger.info(message);
    return { 
      success: false, 
      message,
      mode,
      dryRun,
      deletedCount: 0,
      cutoffDate: new Date().toISOString()
    };
  }

  try {
    const cutoffDate = new Date();
    const retentionPeriod = isTesting 
      ? CONFIG.TEST_RETENTION_HOURS * 60 * 60 * 1000 // hours to ms
      : CONFIG.PROD_RETENTION_DAYS * 24 * 60 * 60 * 1000; // days to ms
    cutoffDate.setTime(cutoffDate.getTime() - retentionPeriod);

    logger.info(`[${JOB_NAME}] Starting cleanup in ${mode} mode${dryRun ? ' (dry run)' : ''}`, {
      mode,
      dryRun,
      cutoffDate: cutoffDate.toISOString(),
      retention: isTesting ? `${CONFIG.TEST_RETENTION_HOURS} hours` : `${CONFIG.PROD_RETENTION_DAYS} days`,
      batchSize
    });
    
    logger.debug(`[${JOB_NAME}] Cleanup will process notifications older than: ${cutoffDate.toISOString()}`);

    // Process in batches
    while (true) {
      // Find a batch of notifications to delete
      const batch: NotificationBatch[] = await prisma.notification.findMany({
        where: {
          read: true,
          createdAt: {
            lt: cutoffDate
          },
          ...(lastProcessedId ? { id: { gt: lastProcessedId } } : {})
        },
        orderBy: { id: 'asc' },
        take: batchSize,
        select: { id: true }
      });

      if (batch.length === 0) {
        logger.debug(`[${JOB_NAME}] No more notifications to process`);
        break;
      }

      const batchIds = batch.map((n: NotificationBatch) => n.id);
      lastProcessedId = batch[batch.length - 1].id;
      
      logger.debug(`[${JOB_NAME}] Processing batch of ${batch.length} notifications`, {
        batchStartId: batch[0].id,
        batchEndId: lastProcessedId
      });

      if (!dryRun) {
        // Delete the batch
        const startTime = Date.now();
        const result = await prisma.notification.deleteMany({
          where: { id: { in: batchIds } }
        });
        const endTime = Date.now();
        deletedCount += result.count;
        logger.debug(`[${JOB_NAME}] Deleted batch of ${result.count} notifications in ${endTime - startTime}ms`, {
          batchSize: batchIds.length,
          deletedCount: result.count,
          durationMs: endTime - startTime
        });
      } else {
        // Just count for dry run
        deletedCount += batchIds.length;
      }

      // Small delay to prevent database overload
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const message = dryRun 
      ? `Would delete ${deletedCount} notifications older than ${cutoffDate.toISOString()}`
      : `Successfully deleted ${deletedCount} old notifications`;

    logger.info(`[${JOB_NAME}] ${message}`, {
      totalDeleted: deletedCount,
      cutoffDate: cutoffDate.toISOString(),
      mode,
      dryRun,
      batchSize,
      retentionPeriod: isTesting ? `${CONFIG.TEST_RETENTION_HOURS} hours` : `${CONFIG.PROD_RETENTION_DAYS} days`
    });
    
    return {
      success: true,
      deletedCount,
      message,
      mode,
      dryRun,
      cutoffDate: cutoffDate.toISOString()
    };
  } catch (error) {
    const errorMessage = `Error cleaning up notifications: ${error instanceof Error ? error.message : 'Unknown error'}`;
    logger.error(`[${JOB_NAME}] ${errorMessage}`, { 
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      deletedCount,
      lastProcessedId,
      mode,
      dryRun
    });
    return {
      success: false,
      deletedCount: 0,
      error: errorMessage,
      mode,
      dryRun,
      cutoffDate: new Date().toISOString(),
      message: errorMessage
    };
  } finally {
    await releaseLock(JOB_NAME).catch(err => 
      logger.error(`[${JOB_NAME}] Failed to release lock:`, err)
    );
    await prisma.$disconnect();
  }
}

// For direct script execution
if (require.main === module) {
  // Default to testing mode with dry run for safety
  const options: CleanupOptions = {
    mode: 'testing',
    dryRun: true,
    batchSize: CONFIG.BATCH_SIZE
  };
  
  logger.info(`Running notification cleanup in ${options.mode} mode${options.dryRun ? ' (dry run)' : ''}`);
  
  cleanupOldNotifications(options)
    .then((result) => {
      console.log(JSON.stringify(result, null, 2));
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}