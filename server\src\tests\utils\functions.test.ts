import {
  capitalize,
  combineDateAndTime,
  convertTimeToISOString,
  generateInvitationToken,
  getCreatedTimeRange,
  getRoleType,
  timeDif,
  trimPhoneNumber,
} from "../../utils/functions";
import { describe, test, expect, jest } from "@jest/globals";
import { RoleType } from "@prisma/client";
import crypto from "crypto";

describe("capitalize", () => {
  test("capitalizes first letter of a single word", () => {
    expect(capitalize("hello")).toBe("Hello");
    expect(capitalize("WORLD")).toBe("World");
    expect(capitalize("tEsT")).toBe("Test");
  });

  test("capitalizes first letter of each word in a multi-word string", () => {
    expect(capitalize("hello world")).toBe("Hello World");
    expect(capitalize("THE QUICK BROWN FOX")).toBe("The Quick Brown Fox");
    expect(capitalize("john doe smith")).toBe("<PERSON>");
  });

  test("handles extra spaces correctly", () => {
    expect(capitalize("  hello   world  ")).toBe("Hello World");
    expect(capitalize("multiple   spaces   here")).toBe("Multiple Spaces Here");
  });

  test("handles empty string", () => {
    expect(capitalize("")).toBe("");
  });

  test("handles single character", () => {
    expect(capitalize("a")).toBe("A");
    expect(capitalize("Z")).toBe("Z");
  });
});

describe("trimPhoneNumber", () => {
  test("removes spaces from phone number", () => {
    expect(trimPhoneNumber("123 456 789")).toBe("123456789");
    expect(trimPhoneNumber("555 0123 4567")).toBe("55501234567");
  });

  test("handles leading and trailing spaces", () => {
    expect(trimPhoneNumber("  123 456 789  ")).toBe("123456789");
  });

  test("handles phone number with no spaces", () => {
    expect(trimPhoneNumber("123456789")).toBe("123456789");
  });

  test("handles empty string", () => {
    expect(trimPhoneNumber("")).toBe("");
  });

  test("handles string with only spaces", () => {
    expect(trimPhoneNumber("   ")).toBe("");
  });
});

describe("convertTimeToISOString", () => {
  test("converts valid time string to ISO format", () => {
    const result = convertTimeToISOString("12:34");
    expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T12:34:00\.000$/);
  });

  test("handles single digit hours and minutes", () => {
    const result = convertTimeToISOString("09:05");
    expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T09:05:00\.000$/);
  });

  test("handles 24-hour time format", () => {
    const result = convertTimeToISOString("23:59");
    expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T23:59:00\.000$/);
  });

  test("handles midnight time", () => {
    const result = convertTimeToISOString("00:00");
    expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T00:00:00\.000$/);
  });

  test("throws error for invalid time format", () => {
    expect(() => convertTimeToISOString("25:00")).toThrow();
    expect(() => convertTimeToISOString("12:60")).toThrow();
    expect(() => convertTimeToISOString("1234")).toThrow();
    expect(() => convertTimeToISOString("12-34")).toThrow();
  });

  test("handles edge cases", () => {
    const result = convertTimeToISOString("23:59");
    expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T23:59:00\.000$/);

    const midnight = convertTimeToISOString("00:00");
    expect(midnight).toMatch(/^\d{4}-\d{2}-\d{2}T00:00:00\.000$/);
  });

  test("maintains consistent output format", () => {
    const result = convertTimeToISOString("15:30");
    expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T15:30:00\.000$/);
    expect(result.length).toBe(23);
  });

  test("throws error for non-string input", () => {
    expect(() => convertTimeToISOString(null)).toThrow();
    expect(() => convertTimeToISOString(undefined)).toThrow();
    expect(() => convertTimeToISOString(123)).toThrow();
    expect(() => convertTimeToISOString({})).toThrow();
  });
});

describe("timeDif", () => {
  test("calculates correct time difference for same day times", () => {
    expect(timeDif("2023-01-01T10:00:00.000", "2023-01-01T12:30:00.000")).toBe(
      "2h 30m"
    );
    expect(timeDif("2023-01-01T08:15:00.000", "2023-01-01T09:45:00.000")).toBe(
      "1h 30m"
    );
  });

  test("calculates correct time difference across days", () => {
    expect(timeDif("2023-01-01T22:00:00.000", "2023-01-02T01:30:00.000")).toBe(
      "3h 30m"
    );
    expect(timeDif("2023-01-01T23:45:00.000", "2023-01-02T00:15:00.000")).toBe(
      "0h 30m"
    );
  });

  test("handles zero duration", () => {
    expect(timeDif("2023-01-01T10:00:00.000", "2023-01-01T10:00:00.000")).toBe(
      "0h 0m"
    );
  });

  test("handles full hours", () => {
    expect(timeDif("2023-01-01T10:00:00.000", "2023-01-01T13:00:00.000")).toBe(
      "3h 0m"
    );
  });

  test("handles minutes only", () => {
    expect(timeDif("2023-01-01T10:00:00.000", "2023-01-01T10:45:00.000")).toBe(
      "0h 45m"
    );
  });

  test("handles invalid departure time", () => {
    expect(() => timeDif("invalid", "2023-01-01T10:00:00.000")).toThrow();
  });

  test("handles invalid arrival time", () => {
    expect(() => timeDif("2023-01-01T10:00:00.000", "invalid")).toThrow();
  });
});

describe("getCreatedTimeRange", () => {
  test("returns correct range for today", () => {
    const result = getCreatedTimeRange("today");
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    expect(result).toEqual({ gte: now });
  });

  test("returns correct range for last 7 days", () => {
    const result = getCreatedTimeRange("last 7 days");
    const now = new Date();
    now.setDate(now.getDate() - 7);
    expect(result).toEqual({ gte: now });
  });

  test("returns correct range for last month", () => {
    const result = getCreatedTimeRange("last month");
    const now = new Date();
    now.setMonth(now.getMonth() - 1);
    expect(result.gte).toBeInstanceOf(Date);
    expect(result.gte).toBeDefined();
    expect(
      Math.abs((result.gte as Date).getTime() - now.getTime())
    ).toBeLessThan(10);
  });

  test("returns correct range for last 12 months", () => {
    const result = getCreatedTimeRange("last 12 months");
    const now = new Date();
    now.setMonth(now.getMonth() - 12);
    expect(result.gte).toBeInstanceOf(Date);
    expect(
      Math.abs((result.gte as Date).getTime() - now.getTime())
    ).toBeLessThan(10);
  });

  test("returns empty object for all time", () => {
    const result = getCreatedTimeRange("all time");
    expect(result).toEqual({});
  });

  test("returns empty object for invalid filter", () => {
    const result = getCreatedTimeRange("invalid filter");
    expect(result).toEqual({});
  });
});

describe("combineDateAndTime", () => {
  test("combines date and time correctly", () => {
    expect(
      combineDateAndTime("2023-01-01T00:00:00.000", "2023-12-31T15:30:45.123")
    ).toBe("2023-01-01T15:30:45.123");
  });

  test("preserves date from first parameter and time from second parameter", () => {
    expect(
      combineDateAndTime("2023-06-15T12:00:00.000", "2000-01-01T16:45:30.456")
    ).toBe("2023-06-15T16:45:30.456");
  });

  test("handles midnight time correctly", () => {
    expect(
      combineDateAndTime("2023-12-31T12:34:56.789", "2023-01-01T00:00:00.000")
    ).toBe("2023-12-31T00:00:00.000");
  });

  test("handles invalid date format", () => {
    expect(() =>
      combineDateAndTime("invalid", "2023-01-01T00:00:00.000")
    ).toThrow();
  });

  test("handles invalid time format", () => {
    expect(() =>
      combineDateAndTime("2023-01-01T00:00:00.000", "invalid")
    ).toThrow();
  });
});

describe("generateInvitationToken", () => {
  test("generates valid invitation token", async () => {
    const token = await generateInvitationToken();
    expect(typeof token).toBe("string");
    expect(token).toHaveLength(64); // 32 bytes = 64 hex characters
    expect(token).toMatch(/^[0-9a-f]+$/); // Should be hex string
  });

  test("generates unique tokens", async () => {
    const token1 = await generateInvitationToken();
    const token2 = await generateInvitationToken();
    expect(token1).not.toBe(token2);
  });

  test("handles crypto.randomBytes errors", async () => {
    // Mock crypto.randomBytes to simulate an error
    const mockRandomBytes = jest.spyOn(crypto, "randomBytes");
    mockRandomBytes.mockImplementation((size, callback) => {
      callback(new Error("Random bytes generation failed"), Buffer.alloc(0));
    });

    await expect(generateInvitationToken()).rejects.toThrow(
      "Invitation token generation failed: Failed to generate invitation token: Random bytes generation failed"
    );

    mockRandomBytes.mockRestore();
  });

  test("handles invalid buffer length", async () => {
    const mockRandomBytes = jest.spyOn(crypto, "randomBytes");
    mockRandomBytes.mockImplementation((size, callback) => {
      callback(null, Buffer.alloc(16)); // Wrong buffer length
    });

    await expect(generateInvitationToken()).rejects.toThrow(
      "Generated token does not meet length requirements"
    );

    mockRandomBytes.mockRestore();
  });

  test("handles null buffer", async () => {
    const mockRandomBytes = jest.spyOn(crypto, "randomBytes");
    mockRandomBytes.mockImplementation((size, callback) => {
      callback(null, Buffer.alloc(0));
    });

    await expect(generateInvitationToken()).rejects.toThrow(
      "Generated token does not meet length requirements"
    );

    mockRandomBytes.mockRestore();
  });
});

describe("getRoleType", () => {
  test("returns correct RoleType for valid roles", () => {
    const testCases = [
      { input: "admin", expected: RoleType.master_admin },
      { input: "MASTER", expected: RoleType.master_owner },
      { input: "moderator", expected: RoleType.master_moderator },
      { input: "accountant", expected: RoleType.master_accountant },
      { input: "agency", expected: RoleType.agency_admin },
      { input: "affiliate", expected: RoleType.affiliate },
      { input: "  admin  ", expected: RoleType.master_admin }, // Tests trimming
    ];

    testCases.forEach(({ input, expected }) => {
      expect(getRoleType(input)).toBe(expected);
    });
  });

  test("throws error for invalid roles", () => {
    const invalidRoles = ["", "invalid", "unknown", "   "];

    invalidRoles.forEach((role) => {
      expect(() => getRoleType(role)).toThrow();
    });
  });

  test("throws specific error messages", () => {
    expect(() => getRoleType("")).toThrow("Role cannot be empty");
    expect(() => getRoleType("invalid")).toThrow(
      "Invalid role: 'invalid'. Valid roles are: master, admin, moderator, accountant, agency, affiliate"
    );
  });

  test("handles edge cases", () => {
    // @ts-expect-error - Testing invalid input type
    expect(() => getRoleType(null)).toThrow(
      "Invalid role type: expected string, got object"
    );
    // @ts-expect-error - Testing invalid input type
    expect(() => getRoleType(undefined)).toThrow(
      "Invalid role type: expected string, got undefined"
    );
  });
});
