import { Router } from "express";
import {
  createInter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  getBooking<PERSON>y<PERSON>d<PERSON><PERSON><PERSON>,
  getBookingsBy<PERSON>ser<PERSON><PERSON><PERSON>,
  getBookingsByStatus<PERSON><PERSON><PERSON>,
  cancelBooking<PERSON><PERSON><PERSON>,
  identifyBooking<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getAllBookings<PERSON>andler,
  approveBook<PERSON><PERSON><PERSON><PERSON>,
  rejectBook<PERSON><PERSON><PERSON><PERSON>,
  timeoutBooking<PERSON><PERSON><PERSON>,
  release<PERSON><PERSON><PERSON><PERSON><PERSON>,
  completePaymentHandler,
  confirmBookingHandler,
  createThirdPartyBookingHandler,
  reschedule<PERSON><PERSON>ing<PERSON><PERSON><PERSON>,
  getTicketForRescheduleHandler,
  getBookingStatusByIdHandler,
  getAllGlobalBookingsHandler,
  getAllSalesHandler,
  refundBooking<PERSON>andler,
  updateTravelerInfoHandler,
  getCarrierNamesHandler,
  getAgencyNamesHandler,
  getMySalesAgentNamesHandler,
  getMyBookingsAgentNamesHandler,
  getGlobalBookingsAgentNamesHandler,
} from "../controllers/booking.controller";
import userAuth from "../middlewares/userAuth";
import { authorizeAgent } from "../middlewares/authorizeAgent";

const router = Router();

// POST /api/bookings - Create a new internal booking request
// Apply authentication (userAuth) then authorization (authorizeAgent)
/**
 * @openapi
 * /booking:
 *   post:
 *     tags:
 *       - Booking
 *     summary: Create a new internal booking request
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Booking created
 */

/**
 * @openapi
 * /booking/update-traveler:
 *   post:
 *     tags:
 *       - Booking
 *     summary: Update traveler information for a booking
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Traveler information updated
 */
router.post("/", userAuth, authorizeAgent, createInternalBookingHandler);

// GET /api/bookings/all - Get all bookings (admin, paginated)
router.get("/all", userAuth, getAllBookingsHandler);

// GET /api/bookings/global - Get all bookings (no filters for master users)
router.get("/global", userAuth, getAllGlobalBookingsHandler);

// GET /api/bookings/all-sales - Get all sales bookings (admin, paginated)
router.get("/sales", userAuth, getAllSalesHandler);

// GET /api/bookings/status/:status - Get bookings by status
router.get("/status/:status", userAuth, getBookingsByStatusHandler);

// GET /api/bookings/user/:userId - Get bookings by user
router.get("/user/:userId", userAuth, getBookingsByUserHandler);

// POST /api/bookings/type - Identify booking type
router.post("/type", userAuth, identifyBookingTypeHandler);

// POST /api/bookings/third-party - Create third party booking
router.post("/third-party", userAuth, createThirdPartyBookingHandler);

// GET /api/bookings/agents - Get unique agent names
router.get("/sales-agents", userAuth, getMySalesAgentNamesHandler);
router.get("/booking-agents", userAuth, getMyBookingsAgentNamesHandler);
router.get("/global-agents", userAuth, getGlobalBookingsAgentNamesHandler);
router.get("/carriers", userAuth, getCarrierNamesHandler);
router.get("/agencies", userAuth, getAgencyNamesHandler);
// ===== Routes with :id parameter (keep these at the end) =====

// GET /api/bookings/:id - Get booking by ID
router.get("/:id", userAuth, getBookingByIdHandler);

// GET /api/bookings/:id/status - Get status of a specific booking
router.get("/:id/status", userAuth, getBookingStatusByIdHandler);

// GET /api/bookings/:id/reschedule-info - Get ticket information for rescheduling
router.get("/:id/reschedule-info", userAuth, getTicketForRescheduleHandler);

// POST /api/bookings/:id/approve - Approve a booking (admin)
router.post("/:id/approve", userAuth, approveBookingHandler);

// POST /api/bookings/:id/reject - Reject a booking (admin)
router.post("/:id/reject", userAuth, rejectBookingHandler);

// POST /api/bookings/:id/timeout - Timeout a booking (system/admin)
router.post("/:id/timeout", userAuth, timeoutBookingHandler);

// POST /api/bookings/:id/release-seat - Release seat for a booking
router.post("/:id/release-seat", userAuth, releaseSeatHandler);

// POST /api/bookings/:id/complete-payment - Complete payment for a booking
router.post("/:id/complete-payment", userAuth, completePaymentHandler);

// POST /api/bookings/:id/confirm - Confirm a booking (agent action)
router.post("/:id/confirm", userAuth, confirmBookingHandler);

// POST /api/bookings/:id/cancel - Cancel a booking
router.post("/:id/cancel", userAuth, cancelBookingHandler);

// POST /api/bookings/:id/reschedule - Reschedule a booking
router.post("/:id/reschedule", userAuth, rescheduleBookingHandler);

// POST /api/bookings/:id/refund - Refund a booking
router.post("/:id/refund", userAuth, refundBookingHandler);

// POST /api/bookings/:id/update-traveler - Update traveler information for a booking
router.put("/:id/update-traveler", userAuth, updateTravelerInfoHandler);

export default router;
