import {
  TeamMemberInput,
  SanitizedTeamMember,
  TeamMemberValidationError,
} from "../types/team-member";
import { teamMemberValidation } from "./teamMemberValidation";
import { agentValidation } from "./agentValidation";
import { ValidationService } from "../services/validation.service";
import { RoleValidations } from "./roleValidations";
import { AgentRole, Role, TeamMemberRole } from "@prisma/client";
import { getRoleType } from "../functions";
import {
  AgentMemberInput,
  AgentMemberValidationError,
  SanitizedAgentMember,
} from "../types/agent-member";
import {
  SanitizedUser,
  UserInput,
  UserValidationError,
} from "../types/user-member";
import { userMemberValidation } from "./userMemberValidation";

export function validateTeamMember(
  data: TeamMemberInput,
  currentUserRole?: TeamMemberRole | Role
): SanitizedTeamMember {
  // Joi validation
  const { error, value } = teamMemberValidation.validate(data, {
    allowUnknown: true,
    stripUnknown: true,
  });

  const errors: TeamMemberValidationError[] = [];
  if (error) {
    errors.push(...ValidationService.formatJoiErrors(error.details));
  }

  // Custom role validation
  const roleErrors = RoleValidations.validateTeamMemberRole(
    currentUserRole,
    data.subRole
  );
  roleErrors.forEach((message) => errors.push({ field: "role", message }));

  // Custom name validation
  try {
    value.firstName = ValidationService.validateName(
      data.firstName,
      "First name"
    );
    value.firstName = ValidationService.hasLeadingOrTrailingSpaces(
      data.firstName,
      "First name"
    );
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "firstName", message });
  }

  try {
    value.lastName = ValidationService.validateName(data.lastName, "Last name");
    value.lastName = ValidationService.hasLeadingOrTrailingSpaces(
      data.lastName,
      "Last name"
    );
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "lastName", message: message });
  }

  // Validate password
  if (data.password) {
    try {
      value.password = ValidationService.validatePassword(
        data.password,
        "Password"
      );
    } catch (error) {
      const message =
        error instanceof Error ? error.message : "An unknown error occurred";
      errors.push({ field: "password", message: message });
    }
  }

  // Validate email
  try {
    if (!data.email) {
      throw new Error("Email is required");
    }
    value.email = ValidationService.validateTeamMemberEmail(data.email);
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "email", message: message });
  }
  return {
    ...value,
    roleType: getRoleType(value.role),
    errors,
  };
}

export function validateAgentMember(
  data: AgentMemberInput,
  currentUserRole?: Role
): SanitizedAgentMember {
  // Joi validation
  const validationErrors: Record<string, string> = {};
  const { error, value } = agentValidation.validate(data, {
    abortEarly: false,
  });

  const errors: AgentMemberValidationError[] = [];
  if (error) {
    errors.push(...ValidationService.formatJoiErrors(error.details));
  }

  // Custom role validation
  const roleErrors = RoleValidations.validateAgentRole(
    currentUserRole,
    data.subRole
  );
  roleErrors.forEach((message) => errors.push({ field: "role", message }));

  // Custom name validation
  try {
    value.firstName = ValidationService.validateName(
      data.firstName,
      "First name"
    );
    value.firstName = ValidationService.hasLeadingOrTrailingSpaces(
      data.firstName,
      "First name"
    );
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "firstName", message });
  }

  // Validate last name
  try {
    value.lastName = ValidationService.validateName(data.lastName, "Last name");
    value.lastName = ValidationService.hasLeadingOrTrailingSpaces(
      data.lastName,
      "Last name"
    );
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "lastName", message: message });
  }

  // Validate password
  try {
    if (data.password) {
      value.password = ValidationService.validatePassword(
        data.password,
        "Password"
      );
    }
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "password", message: message });
  }

  // Validate email
  try {
    if (!data.email) {
      throw new Error("Email is required");
    }
    value.email = ValidationService.validateEmail(data.email, "Email");
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    errors.push({ field: "email", message: message });
  }

  return {
    ...value,
    roleType: getRoleType(value.role),
    errors,
  };
}

export function validateUsers(data: UserInput): SanitizedUser {
  // Joi validation
  const validationErrors: Record<string, string> = {};
  const { error } = userMemberValidation.validate(data, {
    abortEarly: false,
  });

  if (error) {
    error.details.forEach((detail) => {
      if (detail.context && detail.context.key) {
        validationErrors[detail.context.key] = detail.message;
      }
    });
  }

  // Return list of errors if validation fails
  if (error) {
    const errorDetails = error.details.reduce(
      (acc, detail) => {
        if (detail.context && detail.context.key) {
          acc[detail.context.key] = detail.message;
        }
        return acc;
      },
      {} as Record<string, string>
    );

    return {
      firstName: data.firstName || "",
      lastName: data.lastName || "",
      validationErrors,
    };
  }
  // Custom name validation
  try {
    data.firstName = ValidationService.validateName(
      data.firstName,
      "First name"
    );
    data.firstName = ValidationService.hasLeadingOrTrailingSpaces(
      data.firstName,
      "First name"
    );
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    validationErrors.firstName = message;
  }

  try {
    data.lastName = ValidationService.validateName(data.lastName, "Last name");
    data.lastName = ValidationService.hasLeadingOrTrailingSpaces(
      data.lastName,
      "Last name"
    );
  } catch (error) {
    const message =
      error instanceof Error ? error.message : "An unknown error occurred";
    validationErrors.lastName = message;
  }

  if (Object.keys(validationErrors).length > 0) {
    return {
      firstName: data.firstName || "",
      lastName: data.lastName || "",
      validationErrors,
    } as SanitizedUser;
  }

  return {
    ...data,
    validationErrors: {},
  } as SanitizedUser;
}
