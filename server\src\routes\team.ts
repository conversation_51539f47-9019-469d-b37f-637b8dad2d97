import { Router } from "express";
import {
  createTeamMember,
  getTeamMembers,
  removeTeamMember,
  resendInvitation,
  getTeamId,
  searchTeamMembers,
  updateTeamMember,
} from "../controllers/teamController";

const router = Router();

import userAuth from "../middlewares/userAuth";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

router.use(userAuth);
// router.use(enterpriseApiLimiter);

/**
 * @openapi
 * /team/id:
 *   get:
 *     tags:
 *       - Team
 *     summary: Get team ID
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Team ID
 *
 * /team/members:
 *   post:
 *     tags:
 *       - Team
 *     summary: Create a team member
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Team member created
 *   get:
 *     tags:
 *       - Team
 *     summary: Get all team members
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of team members
 *
 * /team/members/{id}:
 *   put:
 *     tags:
 *       - Team
 *     summary: Update a team member
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Team member updated
 *   delete:
 *     tags:
 *       - Team
 *     summary: Remove a team member
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Team member removed
 *
 * /team/members/search:
 *   get:
 *     tags:
 *       - Team
 *     summary: Search team members
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Team member search results
 *
 * /team/invitations/resend:
 *   post:
 *     tags:
 *       - Team
 *     summary: Resend team invitation
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Invitation resent
 */
router.get("/id", getTeamId);
router.post("/members", createTeamMember);
router.put("/members/:id", updateTeamMember);
router.get("/members", getTeamMembers);
router.get("/members/search", searchTeamMembers);
router.delete("/members/:id", removeTeamMember);
router.post("/invitations/resend", resendInvitation);

export default router;
