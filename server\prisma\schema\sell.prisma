// model PurchasedFlightTicket {
//   id        String       @id @default(cuid())
//   userId    String
//   ticketId  String       @unique
//   seats     Int
//   createdAt DateTime     @default(now())
//   updatedAt DateTime     @updatedAt
//   ticket    FlightTicket @relation(fields: [ticketId], references: [id], onDelete: Restrict)
//   user      User         @relation(fields: [userId], references: [id], onDelete: Restrict)

//   @@index([userId])
//   @@map("purchasedFlightTickets")
// }

// model BookedFlightSeat {
//   id             String                 @id @default(cuid())
//   flightTicketId String
//   seatStatus     BookedFlightSeatStatus @default(booked)
//   totalPrice     Decimal                @db.Decimal(10, 2)

//   // Travelers
//   traveler   Traveler? @relation(fields: [travelerId], references: [id])
//   travelerId String?   @unique

//   // Agency agents
//   bookedByAgency String
//   agencyAgent    AgencyAgent? @relation(fields: [bookedByAgent], references: [id])
//   bookedByAgent  String?

//   // Booking relationship
//   booking   Booking @relation(fields: [bookingId], references: [id])
//   bookingId String

//   // Add seat identifier (optional)
//   seatNumber  String?
//   flightClass String?

//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   flightTicket FlightTicket @relation(fields: [flightTicketId], references: [id], onDelete: Restrict)

//   // @@index([flightTicketId], name: "flight_ticket_idx")
//   // @@index([flightTicketId, seatStatus])
//   // @@index([bookedByAgent], name: "booked_by_agent_idx")
//   // @@index([travelerId], name: "customer_info_idx")
//   // @@index([createdAt], name: "booking_time_idx")
//   // @@index([bookingId], name: "booking_idx")
//   // @@map("bookedFlightSeats")

//   @@index([flightTicketId, seatStatus]) // Crucial for seat availability checks
//   @@index([bookedByAgent])
//   @@index([bookingId])
//   @@index([travelerId])
//   @@map("bookedFlightSeats")
// }

// // model CustomerInfo {
// //   id             String               @id @default(cuid())
// //   title          CustomerInfoTitle
// //   gender         String               @db.VarChar(50)
// //   firstName      String               @db.VarChar(50)
// //   lastName       String               @db.VarChar(50)
// //   nationality    String               @db.VarChar(50)
// //   dateOfBirth    DateTime             @map("date_of_birth")
// //   documentType   CustomerDocumentType 
// //   documentNumber String               @db.VarChar(50)
// //   issuingCountry String               @db.VarChar(50)
// //   expirationDate DateTime?            @map("expiration_date")
// //   phoneNumber    String?              @map("phone_number")
// //   email          String?              @map("email")

// //   createdAt  DateTime           @default(now())
// //   updatedAt  DateTime           @updatedAt
// //   bookedSeat BookedFlightSeat[]

// //   @@map("customersInfo")
// // }

// enum BookedFlightSeatStatus {
//   booked
//   onHold
//   canceled
// }

// enum CustomerInfoTitle {
//   MR
//   MRS
// }

// enum CustomerDocumentType {
//   passport
//   id_card
// }
