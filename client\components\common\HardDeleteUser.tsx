import React, { useState } from "react";
import { Trash2 } from "lucide-react";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/messageSlice";
import { setLoading } from "@/redux/features/LoadingSlice";
import { hardDeleteUser } from "@/lib/data/masterUsersData";
import { useRouter } from "next/navigation";
import { logoutUser } from "@/redux/features/AuthSlice";

/**
 * Custom AlertDialog Component System
 * A set of composable components for creating consistent alert dialogs
 */

// Main dialog container with overlay background
interface AlertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

const AlertDialog = ({ open, onOpenChange, children }: AlertDialogProps) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
        {children}
      </div>
    </div>
  );
};

// Content container with padding
const AlertDialogContent = ({ children }: { children: React.ReactNode }) => (
  <div className="p-6">{children}</div>
);

// Header section for the dialog
const AlertDialogHeader = ({ children }: { children: React.ReactNode }) => (
  <div className="mb-4">{children}</div>
);

// Footer section with action buttons
const AlertDialogFooter = ({ children }: { children: React.ReactNode }) => (
  <div className="mt-6 flex justify-end space-x-2">{children}</div>
);

// Title component for dialog
const AlertDialogTitle = ({ children }: { children: React.ReactNode }) => (
  <h3 className="text-2xl font-bold text-red-500 mb-2">{children}</h3>
);

// Description container for dialog body content
const AlertDialogDescription = ({
  children,
}: {
  children: React.ReactNode;
}) => <div className="text-gray-700 dark:text-gray-300">{children}</div>;

// Primary action button (destructive)
const AlertDialogAction = ({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}) => (
  <button
    onClick={onClick}
    className="bg-red-500 text-white hover:bg-red-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
  >
    {children}
  </button>
);

// Secondary action button (cancel)
const AlertDialogCancel = ({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}) => (
  <button
    onClick={onClick}
    className="bg-gray-300 text-gray-800 hover:bg-gray-400 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
  >
    {children}
  </button>
);

/**
 * HardDeleteUserButton - Component specifically for user hard deletion functionality
 * Displays a deletion button and confirmation dialog with detailed information
 */
interface HardDeleteUserButtonProps {
  userId?: string;
}

const HardDeleteUserButton = ({ userId }: HardDeleteUserButtonProps) => {
  // State management
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const dispatch = useAppDispatch();
  const router = useRouter();

  /**
   * Event Handlers
   */
  // Opens the deletion confirmation dialog
  const handleAccountDeletion = () => {
    setShowDeleteConfirmation(true);
  };

  // Processes the deletion confirmation
  const handleConfirmDeletion = async () => {
    if (!userId) return;

    if (deleteConfirmText.toUpperCase() === "DELETE") {
      dispatch(setLoading(true));
      const result = await hardDeleteUser(userId);
      dispatch(
        setMsg({
          type: result.success ? "success" : "error",
          message: result.message,
        })
      );
      dispatch(logoutUser());

      if (result.success) {
        setShowDeleteConfirmation(false);
        router.push("/master-control/users");
      }
      dispatch(setLoading(false));
    } else {
      dispatch(
        setMsg({
          type: "error",
          message: "Please type DELETE to confirm user deletion.",
        })
      );
    }
  };

  // Cancels the deletion process and resets state
  const handleCancelDeletion = () => {
    setShowDeleteConfirmation(false);
    setDeleteConfirmText("");
  };

  return (
    <div className="text-gray-800 dark:text-white">
      {/* Account Deletion Section */}
      <section>
        <button
          onClick={handleAccountDeletion}
          className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-300 flex items-center"
        >
          <Trash2 size={18} className="mr-2" />
          Hard Delete User
        </button>
      </section>

      {/* Deletion Confirmation Dialog */}
      <AlertDialog
        open={showDeleteConfirmation}
        onOpenChange={setShowDeleteConfirmation}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm User Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              {/* Warning Message */}
              <p className="mb-4 dark:text-gray-200">
                Are you sure you want to permanently delete this user's account
                and data?
              </p>

              {/* Consequences Information Box */}
              <div className="bg-gray-300 dark:bg-gray-700 p-4 rounded-lg mb-4">
                {/* Deletion Consequences Section */}
                <h4 className="text-base font-medium mb-3 text-gray-600 dark:text-gray-100">
                  What happens when you delete this user:
                </h4>
                <ul className="list-disc dark:text-gray-300 space-y-1 ml-6">
                  <li className="pl-2">
                    <span className="block ml-1">
                      This user's data will be deleted permanently
                    </span>
                  </li>
                  <li className="pl-2">
                    <span className="block ml-1">
                      The user will lose all access to services and
                      subscriptions
                    </span>
                  </li>
                  <li className="pl-2">
                    <span className="block ml-1">
                      Any outstanding balances or credits will be forfeited.
                    </span>
                  </li>
                  <li className="pl-2">
                    <span className="block ml-1">
                      All associated accounts linked to this user will also be
                      deleted
                    </span>
                  </li>
                  <li className="pl-2">
                    <span className="block ml-1">
                      This action cannot be undone
                    </span>
                  </li>
                </ul>
                <p className="mt-3 dark:text-gray-300">
                  <strong>
                    Consider suspending the user or rejecting access instead.
                  </strong>
                </p>

                {/* Liability Section */}
                <div className="border-t border-gray-600 mt-4 pt-3">
                  <h4 className="text-base font-medium mb-3 dark:text-gray-100">
                    Liability Reminder:
                  </h4>
                  <p className="dark:text-gray-300">
                    By proceeding, you acknowledge that you are fully
                    responsible for this action and any resulting consequences.
                  </p>
                </div>
              </div>

              {/* Confirmation Input Section */}
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                Please type "DELETE" in the box below to confirm:
              </p>
              <input
                type="text"
                className="w-full bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md px-3 py-2 mt-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 border-0"
                placeholder="Type DELETE here"
                value={deleteConfirmText}
                onChange={(e) => setDeleteConfirmText(e.target.value)}
              />
            </AlertDialogDescription>
          </AlertDialogHeader>

          {/* Action Buttons */}
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDeletion}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDeletion}>
              Delete User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default HardDeleteUserButton;
