import {
  Card,
  EmptyState,
  Tooltip,
} from "@/components/flight-tickets/myTickets/addTicket/AddTicketComponents";
import {
  CreateFlightClassesFormTypes,
  UserSegmentResultType,
  UserTicketResultType,
} from "@/utils/definitions/myTicketsDefinitions";
import {
  ArrowBigRight,
  Building,
  Coins,
  FileText,
  Gift,
  Plane,
  Plus,
  Ticket,
  Trash2,
  Users,
} from "lucide-react";
import React, { useEffect, useState, useRef } from "react";
import ProgressLoading from "@/components/utils/ProgressLoading";
import {
  fetchSingleTicketById,
  fetchUpdateValidTicketById,
} from "@/lib/data/userTicketData";
import {
  addExtraOffersToUpdateTicket,
  addFlightClassSectionToUpdateTicket,
  addSegmentToUpdateTicket,
  removeExtraOffersToUpdateTicket,
  removeFlightClassSectionToUpdateTicket,
  removeSegmentSectionToUpdateTicket,
  selectSingleTicket,
  setTicketUpdateData,
} from "@/redux/features/SingleTicketSlice";
import {
  selectSelectedTicket,
  setSelectedTicket,
} from "@/redux/features/SelectedTicketSlice";
import { selectIsLoggedIn } from "@/redux/features/AuthSlice";
import useAgencyUserAuth from "@/components/hooks/useAgencyUserAuth";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getFormatDate, getFormatTime } from "@/utils/functions/functions";
import { useRouter } from "next/navigation";
import MyTicketHistoryLogs from "@/components/flight-tickets/myTickets/ticketId/MyTicketHistoryLogs";
import FlightDateUpdateField from "./FlightDateUpdateField";
import { FaMinus } from "react-icons/fa";
import ReusableDropdown from "./ReusableDropdownUpdate";
import {
  flightClassOptions,
  carryOnAllowedOptions,
  carryOnWeightOptions,
  checkedAllowedOptions,
  checkedWeightOptions,
  extraOffersNameOptions,
  extraOffersAvailableOptions,
  statusOptions,
} from "@/components/flight-tickets/myTickets/addTicket/AddTicketData";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimeField } from "@mui/x-date-pickers/TimeField";
import dayjs from "dayjs";
import ReusableSearchUpdate from "./ReusableSearchUpdate";
import { updateTicket } from "@/redux/features/TicketSlice";
import OriginalFlatpickr from "react-flatpickr";
import moment from "moment";
const Flatpickr = OriginalFlatpickr as any;

// Helper to calculate duration between two times in HH:MM format
const calculateDuration = (start: string, end: string) => {
  if (!start || !end) return "0h 00m";
  const startTime = new Date(start).getTime();
  const endTime = new Date(end).getTime();
  const diffMs = endTime - startTime;
  const diffMins = Math.round(diffMs / 60000);
  const hours = Math.floor(diffMins / 60);
  const mins = diffMins % 60;
  return `${hours}h ${mins.toString().padStart(2, "0")}m`;
};

// Helper to update main card times/duration from segments
const updateMainCardTimesFromSegments = (ticket: UserTicketResultType) => {
  if (!ticket.segments?.length) return ticket;

  const firstSegment = ticket.segments[0];
  const lastSegment = ticket.segments[ticket.segments.length - 1];

  // Format duration as "2h 28m" format to match the expected format
  const formatDuration = (start: string, end: string) => {
    if (!start || !end) return "0h 00m";

    const startTime = new Date(start).getTime();
    const endTime = new Date(end).getTime();
    const diffMs = endTime - startTime;
    const diffMins = Math.round(diffMs / 60000);
    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    return `${hours}h ${mins.toString().padStart(2, "0")}m`;
  };

  const duration = formatDuration(
    firstSegment.departureTime,
    lastSegment.arrivalTime
  );

  return {
    ...ticket,
    departureTime: firstSegment.departureTime,
    arrivalTime: lastSegment.arrivalTime,
    duration,
  };
};

export default function UpdateSingleTicket({
  ticket,
  updatedTicket,
  editMode,
  updateReqMode,
  handleFormChange,
  validationError,
  handleCancelUpdateTicket,
  handleSubmitTicket,
  handleUpdateRequest,
  handleWithdrawUpdateRequest,
}: {
  ticket: UserTicketResultType;
  updatedTicket: UserTicketResultType;
  editMode: boolean;
  updateReqMode: boolean;
  handleFormChange: (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>,
    segIdx?: number,
    classIdx?: number
  ) => void;
  validationError: any;
  handleCancelUpdateTicket: () => void;
  handleSubmitTicket: (e: React.MouseEvent<HTMLButtonElement>) => Promise<void>;
  handleUpdateRequest: (
    e: React.MouseEvent<HTMLButtonElement>
  ) => Promise<void>;
  handleWithdrawUpdateRequest: (
    e: React.MouseEvent<HTMLButtonElement>
  ) => Promise<void>;
}) {
  // ########### STATES ##############
  const dispatch = useAppDispatch();
  const initFlightTicket: UserTicketResultType | {} =
    useAppSelector(selectSingleTicket);
  const flightTicket = initFlightTicket as UserTicketResultType;
  const formData = useAppSelector(selectSingleTicket);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [prevSeats, setPrevSeats] = useState<number>(
    ticket?.remainingSeats || 0
  );
  const initialRemainingSeats = useRef<number>(ticket?.remainingSeats || 0);
  const [isSoldOut, setIsSoldOut] = useState<boolean>(
    ticket?.remainingSeats === 0
  );
  const [isUpdatingFromZero, setIsUpdatingFromZero] = useState<boolean>(false);
  const isZeroToNonZeroUpdate = useRef<boolean>(false);
  const [isSeatUpdateInProgress, setIsSeatUpdateInProgress] =
    useState<boolean>(false);
  const initialSeatsBeforeUpdate = useRef<number | null>(null);

  const [updateTicket, setUpdateTicket] = useState<UserTicketResultType | null>(
    null
  );

  // Track when an update starts
  useEffect(() => {
    if (ticket.updated) {
      // When update starts, store the initial seats value if not already set
      if (initialSeatsBeforeUpdate.current === null) {
        initialSeatsBeforeUpdate.current = initialRemainingSeats.current;
        if (initialSeatsBeforeUpdate.current === 0) {
          isZeroToNonZeroUpdate.current = true;
        }
      }
    } else if (initialSeatsBeforeUpdate.current !== null) {
      // When update completes
      initialRemainingSeats.current = ticket.remainingSeats ?? 0;
      initialSeatsBeforeUpdate.current = null;
      isZeroToNonZeroUpdate.current = false;
    }
  }, [ticket.updated]);

  // Reset when ticket changes
  useEffect(() => {
    initialRemainingSeats.current = ticket.remainingSeats ?? 0;
    isZeroToNonZeroUpdate.current = false;
    initialSeatsBeforeUpdate.current = null;
  }, [ticket.id]);

  // Handle all four scenarios for status field:
  // 1. No update, seats > 0: Enable status (can toggle)
  // 2. Updating, seats > 0 → > 0: Enable status (can toggle)
  // 3. No update, seats = 0: Disable status
  // 4. Updating, seats = 0 → > 0: Disable status until update is complete
  const isStatusDisabled =
    // Case 3: No update and seats = 0
    (!ticket.updated && (ticket.remainingSeats ?? 0) <= 0) ||
    // Case 4: If we're in the middle of updating from 0 to > 0
    (isSeatUpdateInProgress && initialSeatsBeforeUpdate.current === 0) ||
    // Case 3 (alternative): If seats are 0 (regardless of update status)
    (ticket.remainingSeats ?? 0) <= 0 ||
    // Or if we have an update in progress that started from 0
    (ticket.updated && initialSeatsBeforeUpdate.current === 0);

  // Update sold out state when seats change
  useEffect(() => {
    const currentSeats = ticket?.remainingSeats || 0;

    // Check if we're starting an update from 0 to non-zero
    if (prevSeats === 0 && currentSeats > 0) {
      setIsSeatUpdateInProgress(true);
      // Don't update the status here, wait for the update to be confirmed
      setPrevSeats(currentSeats);
      setIsSoldOut(false);
      return;
    }

    // Only update status if this is not a pending update and seats have actually changed
    if (!ticket.updated) {
      // If seats increased from 0, set status to 'available' only after update is complete
      if (prevSeats === 0 && currentSeats > 0) {
        const updatedTicket = {
          ...ticket,
          ticketStatus: "available",
        };
        dispatch(setTicketUpdateData(updatedTicket));
        setPrevSeats(currentSeats);
        setIsSoldOut(false);
        // Reset the update in progress flag after a short delay
        setTimeout(() => setIsSeatUpdateInProgress(false), 1000);
        return;
      }
      // If seats decreased to 0, set status to 'unavailable'
      else if (currentSeats === 0 && ticket.ticketStatus !== "unavailable") {
        const updatedTicket = {
          ...ticket,
          ticketStatus: "unavailable",
        };
        dispatch(setTicketUpdateData(updatedTicket));
        setPrevSeats(currentSeats);
        setIsSoldOut(true);
        return;
      }
    }

    // Update previous seats if they've changed
    if (prevSeats !== currentSeats && !ticket.updated) {
      setPrevSeats(currentSeats);
      // Update sold out state based on current seats
      const currentlySoldOut = currentSeats === 0;
      if (isSoldOut !== currentlySoldOut) {
        setIsSoldOut(currentlySoldOut);
      }
    }
  }, [
    ticket.remainingSeats,
    ticket.updated,
    ticket.ticketStatus,
    dispatch,
    ticket,
    prevSeats,
    isSoldOut,
  ]);
  // Add this useEffect near your other useEffect hooks
  useEffect(() => {
    if (isSoldOut) {
      dispatch(
        setMsg({
          success: false,
          message:
            "This ticket is sold out. Ticket status cannot be changed while seat count is zero.",
        })
      );
    }
  }, [isSoldOut, dispatch]);

  // Handle status change when seats reach zero
  useEffect(() => {
    // Only run this check once when the component mounts or when remainingSeats changes to 0
    if (
      (ticket.remainingSeats ?? 0) <= 0 &&
      ticket.ticketStatus === "available"
    ) {
      // Use setTimeout to ensure this runs after the current render cycle
      const timer = setTimeout(() => {
        const updatedTicket = {
          ...ticket,
          ticketStatus: "unavailable",
        };
        dispatch(setTicketUpdateData(updatedTicket));

        dispatch(
          setMsg({
            success: false,
            message:
              "Status automatically changed to 'unavailable' because there are no seats available",
          })
        );
      }, 0);

      return () => clearTimeout(timer);
    }
  }, [ticket.remainingSeats, ticket.ticketStatus, dispatch, ticket]);
  // error res banner
  const selectedTicket = useAppSelector(selectSelectedTicket);
  const isLoggedIn = useAppSelector(selectIsLoggedIn);
  const router = useRouter();
  // ############ useEffect #############

  const fetchTicket = async () => {
    setIsLoading(true);

    const ticketData = await fetchSingleTicketById(ticket.id);
    // const ticketData = await fetchUpdateValidTicketById(ticket.refId, ticket);
    if (ticketData.success) {
      dispatch(setTicketUpdateData(ticketData.results));
    }

    setMsg({
      success: ticketData.success,
      message: ticketData.message,
    });

    setIsLoading(false);
  };

  // fetch tickets
  useEffect(() => {
    if (ticket.id) {
      fetchTicket();
    }

    // if the selected user is in update state, just activate edit mode
    if (selectedTicket && selectedTicket.status === "update") {
      //   setEditMode(true);
      dispatch(setSelectedTicket({ ticketId: "", status: "" }));
    }
  }, [dispatch, ticket.id, isLoggedIn, router]);

  const loadingAccess = useAgencyUserAuth();

  if (isLoading || loadingAccess) {
    return <ProgressLoading />;
  }

  // If ticket is not found, show 'No ticket found' message
  if (!ticket?.id) {
    return (
      <div className="w-full h-[80vh] flex justify-center items-center">
        <h1 className="text-red-700 text-xl font-bold">Ticket not found</h1>
      </div>
    );
  }

  // get flight location value
  const getFlightLocation = (act: string) => {
    if (act === "departure" || act === "arrival") {
      const { airportCode, country, city, airport } = ticket[act];
      return `${city}, ${country} - ${airportCode}`;
    } else {
      return "";
    }
  };

  // Sort the ticketHistoryLogs array by changedAt in descending order
  const sortedLogs = ticket.ticketHistoryLogs
    ? [...ticket.ticketHistoryLogs].sort(
        (a, b) =>
          new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()
      )
    : [];

  const getFlightLocationForSegment = (
    segment: {
      flightNumber: string;
      carrier: string;
      departure: {
        airportCode: string;
        country: string;
        city: string;
        airport: string;
      };
      arrival: {
        airportCode: string;
        country: string;
        city: string;
        airport: string;
      };
      departureTime: string;
      arrivalTime: string;
      duration: string;
    },

    act: "departure" | "arrival"
  ) => {
    if (segment[act]) {
      const { airportCode, country, city, airport } = segment[act];
      return `${city}, ${country} - ${airportCode} (${airport})`;
    }
    return "";
  };

  // update price change
  const handlePriceChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
    classIdx: number
  ) => {
    const { id, value } = e.target;
    const [field, _] = id.split("-");

    // Update the form data with the modified segment
    const updatedFormData = {
      ...ticket,
      flightClasses: ticket.flightClasses.map(
        (classes: CreateFlightClassesFormTypes, i: number) =>
          i === classIdx
            ? {
                ...classes,
                price: {
                  ...classes.price,
                  [field]: value,
                },
              }
            : classes
      ),
    };

    // Dispatch the updated form state
    dispatch(setTicketUpdateData(updatedFormData));
  };

  interface DropdownOption {
    id: number;
    value: string;
  }

  // Helper function to find initial selected ID
  const findInitialSelectedId = (
    options: DropdownOption[],
    currentValue: string | number | undefined
  ): number => {
    const foundOption = options.find(
      (option) => option.value === currentValue?.toString()
    );
    return foundOption ? foundOption.id : -1;
  };

  // Status field is only editable when:
  // 1. There are no pending updates (ticket.updated is false)
  // 2. Ticket status is either 'unavailable' or 'available'
  // 3. There are seats available (remainingSeats > 0)
  // 4. We're not in the middle of updating from 0 to non-zero seats
  const isStatusEditable =
    !ticket.updated &&
    ["unavailable", "available"].includes(ticket.ticketStatus) &&
    (ticket.remainingSeats ?? 0) > 0 &&
    !isZeroToNonZeroUpdate.current;

  // Get current status options
  const currentStatusOptions =
    (ticket.remainingSeats ?? 0) > 0
      ? statusOptions
      : statusOptions.filter((option) => option.value !== "available");

  const handleDateChange = (newDate: Date) => {
    if (!ticket) return;

    const newFlightDate = moment(newDate).format("YYYY-MM-DDTHH:mm:ss.SSS");

    // Update all segment times with the new date
    const updatedSegments = ticket.segments?.map((segment) => {
      const update: any = {};

      if (segment.departureTime) {
        const timePart = segment.departureTime.split("T")[1] || "00:00:00.000";
        update.departureTime = `${newFlightDate.split("T")[0]}T${timePart}`;
      }

      if (segment.arrivalTime) {
        const timePart = segment.arrivalTime.split("T")[1] || "00:00:00.000";
        update.arrivalTime = `${newFlightDate.split("T")[0]}T${timePart}`;
      }

      return { ...segment, ...update };
    });

    // Create the updated ticket
    const updatedTicket = {
      ...ticket,
      flightDate: newFlightDate,
      segments: updatedSegments,
    };

    // Update the main card times based on segments
    const finalTicket = updateMainCardTimesFromSegments(updatedTicket);

    // Update the state
    dispatch(setTicketUpdateData(finalTicket));
  };

  const handleTimeChange = (
    type: "departure" | "arrival",
    segIdx: number,
    segment: any,
    newTime: any
  ) => {
    if (!newTime) return;

    // Format the new time
    const timeString = dayjs(newTime).format("HH:mm:ss.SSS");
    const currentDate = dayjs(ticket.flightDate).format("YYYY-MM-DD");
    const newDateTime = `${currentDate}T${timeString}`;

    // Update the segments
    const updatedSegments = ticket.segments.map((seg, idx) => {
      if (idx !== segIdx) return seg;

      // Create updated segment with new time
      const updatedSegment = {
        ...seg,
        [type === "departure" ? "departureTime" : "arrivalTime"]: newDateTime,
      };

      // Recalculate duration if we have both times
      if (updatedSegment.departureTime && updatedSegment.arrivalTime) {
        updatedSegment.duration = calculateDuration(
          updatedSegment.departureTime,
          updatedSegment.arrivalTime
        );
      }

      return updatedSegment;
    });

    // Update the ticket
    const updatedTicket = {
      ...ticket,
      segments: updatedSegments,
    };

    // Update main card times from segments
    const finalTicket = updateMainCardTimesFromSegments(updatedTicket);

    // Update the state
    dispatch(setTicketUpdateData(finalTicket));
  };

  return (
    <div className="mb-4 sm:mb-0 w-full">
      <form>
        <div className="space-y-4">
          {/* Agency */}
          <Card icon={<Building color="#EE4544" />} title="Agency">
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
              {/* Agency Name */}
              <InputField
                id="agencyName"
                label="Agency Name"
                type="text"
                placeholder="Enter agency name"
                tooltip="This field displays the name of the agency associated with the ticket."
                value={ticket?.owner?.agencyName ?? ""}
                disabled={true}
              />
              {/* User Name */}
              <InputField
                id="userFullName"
                label="Submitted By"
                type="text"
                placeholder="Submitted By"
                tooltip="This field displays the name of the user associated with the ticket."
                value={`${
                  ticket?.agencyAgent?.firstName ?? ticket?.owner?.firstName
                } ${
                  ticket?.agencyAgent?.lastName ?? ticket?.owner?.lastName
                }  `}
                disabled={true}
              />
            </div>
          </Card>

          {/* Main */}
          <Card icon={<Ticket color="#EE4544" />} title="Main">
            {/* ROW 1 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
              {/* Ticket Status */}
              {["unavailable", "available"].includes(ticket.ticketStatus) ? (
                <>
                  <InputField
                    id="ticketStatus"
                    label="Ticket Status"
                    type="text"
                    placeholder="Ticket status"
                    tooltip={
                      isSoldOut
                        ? "This ticket is sold out. Ticket status cannot be changed while seat count is zero."
                        : "This field displays the current status of the ticket."
                    }
                    value={ticket?.ticketStatus ?? ""}
                    disabled={isStatusDisabled}
                    input={
                      <div>
                        <ReusableDropdown
                          options={currentStatusOptions}
                          initialSelectedId={findInitialSelectedId(
                            currentStatusOptions,
                            ticket.ticketStatus as string
                          )}
                          placeholder="Select status"
                          refId={ticket.refId}
                          isTicketStatus={true}
                          disabled={isStatusDisabled}
                          isSeatUpdateInProgress={isSeatUpdateInProgress}
                        />
                        {(ticket.remainingSeats ?? 0) <= 0 && (
                          <p className="text-red-500 text-xs mt-1 ml-1">
                            Cannot set status to "available" when seat count is
                            zero
                          </p>
                        )}
                      </div>
                    }
                  />
                </>
              ) : (
                <InputField
                  id="ticketStatus"
                  label="Ticket Status"
                  type="text"
                  placeholder="Ticket status"
                  tooltip="This field displays the current status of the ticket."
                  value={ticket?.ticketStatus ?? ""}
                  disabled={editMode ? !isStatusEditable : true}
                />
              )}
              {/* Seats */}
              {ticket.updated && updatedTicket ? (
                <UpdatedInputField
                  id="remainingSeats"
                  label="Seats"
                  tooltip="This field displays the number of seats available for the ticket."
                  value={ticket?.remainingSeats ?? ""}
                  newValue={updatedTicket?.remainingSeats ?? ""}
                />
              ) : (
                <InputField
                  id="remainingSeats"
                  label="Seats"
                  type="number"
                  placeholder="Number of seats"
                  tooltip="This field displays the number of seats available for the ticket."
                  value={ticket?.remainingSeats ?? ""}
                  disabled={!editMode && !updateReqMode}
                  onChange={(e) => handleFormChange(e)}
                  validationError={validationError?.remainingSeats}
                />
              )}
            </div>
            {/* ROW 2 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
              {/* Departure */}
              <InputField
                id="departure"
                label="Departure"
                type="text"
                placeholder="Departure"
                tooltip="This field displays the date when the ticket will depart."
                value={ticket.segments && getFlightLocation("departure")}
                disabled={true}
              />
              {/* Arrival */}
              <InputField
                id="arrival"
                label="Arrival"
                type="text"
                placeholder="Arrival"
                tooltip="This field displays the date when the ticket will arrival."
                value={ticket.segments && getFlightLocation("arrival")}
                disabled={true}
              />
            </div>

            {/* ROW 3 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
              {/* departureTime */}
              <InputField
                id="departureTime"
                label="Departure Time"
                type="text"
                placeholder="departure time"
                tooltip="This field displays the time when the ticket will departure."
                value={getFormatTime(ticket.departureTime) ?? ""}
                disabled={true}
              />
              {/* arrivalTime */}
              <InputField
                id="arrivalTime"
                label="Arrival Time"
                type="text"
                placeholder="arrival time"
                tooltip="This field displays the time when the ticket will arrival."
                value={getFormatTime(ticket.arrivalTime) ?? ""}
                disabled={true}
              />
            </div>
            {/* ROW 4 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
              {/* flightDate */}
               {(() => {
                // Scenario 1: Viewing pending updates
                if (ticket.updated && updatedTicket) {
                  return (
                    <UpdatedInputField
                      id="flightDate"
                      label="Flight Date"
                      tooltip="This field displays the date when the Flight will departure. Shows original and updated values."
                      value={getFormatDate(ticket.flightDate) ?? ""}
                      newValue={
                        getFormatDate(updatedTicket.flightDate) ?? ""
                      }
                    />
                  );
                }
                // Scenario 2: Editing the ticket
                else if (editMode || updateReqMode) {
                  return (
                    <InputField
                      id="flightDate"
                      label="Flight Date"
                      type="text"
                      placeholder="Flight Date"
                      tooltip="Select or modify the flight date."
                      value={getFormatDate(ticket.flightDate) ?? ""}
                      disabled={!(editMode || updateReqMode)}
                      input={
                        <FlightDateUpdateField
                          onDateChange={handleDateChange}
                          disabled={!(editMode || updateReqMode)}
                        />
                      }
                      validationError={validationError?.flightDate}
                    />
                  );
                }
                // Scenario 3: Default view
                else {
                  return (
                    <InputField
                      id="flightDate"
                      label="Flight Date"
                      type="text"
                      placeholder="Flight Date"
                      tooltip="This field displays the date when the Flight will departure."
                      value={getFormatDate(ticket.flightDate) ?? ""}
                      disabled={true}
                      validationError={validationError?.flightDate}
                    />
                  );
                }
              })()}
              {/* duration */}
              <InputField
                id="duration"
                label="duration"
                type="text"
                placeholder="duration"
                tooltip="This field displays the duration when the flight will take."
                value={ticket.duration}
                disabled={true}
              />
            </div>
            {/* ROW 5 */}
            {ticket?.description && (
              <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                {/* Description */}
                <InputField
                  id="description"
                  label="description"
                  type="textarea"
                  rows={4}
                  placeholder="Flight Ticket Description..."
                  tooltip="This field displays the description of the Flight."
                  value={ticket?.description ?? ""}
                  disabled={!editMode}
                  required={false}
                  onChange={(e) => handleFormChange(e)}
                  validationError={validationError?.description}
                />
              </div>
            )}
          </Card>

          {/* Segments */}
          <div style={{ margin: "1.5rem 0" }}>
            <Card icon={<Plane color="#EE4544" />} title="Segments">
              {ticket?.segments?.map((segment, segIdx) => {
                const currentSegment =
                  updatedTicket?.segments?.[segIdx] || segment;
                return (
                  <div key={segIdx} className="space-y-4 mt-4">
                    {/* Segment Title */}
                    <div className="text-slate-800 dark:text-slate-100 font-semibold">
                      {`Segment ${segIdx !== 0 ? segIdx + 1 : ""}`}
                    </div>

                    {/* ROW 1 */}
                    <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                      {/* Flight Number */}
                      {editMode ? (
                        <InputField
                          id={`flightNumber-${segIdx}`}
                          label={`Flight NO ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the flight number."
                          value={segment?.flightNumber ?? ""}
                          placeholder="Enter flight number"
                          disabled={!editMode}
                          onChange={(e) => handleFormChange(e, segIdx)}
                          validationError={
                            validationError &&
                            validationError[`segments.${segIdx}.flightNumber`]
                          }
                        />
                      ) : (
                        <InputField
                          id={`flightNumber-${segIdx}`}
                          label={`Flight NO ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the flight number."
                          value={segment.flightNumber ?? ""}
                          disabled={!editMode}
                        />
                      )}

                      {/* Carrier */}
                      {editMode ? (
                        <InputField
                          id={`carrier-${segIdx}`}
                          label={`Carrier ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the carrier name."
                          value={segment?.carrier ?? ""}
                          disabled={!editMode}
                          input={
                            <ReusableSearchUpdate
                              segmentIndex={segIdx}
                              fieldType="carrier"
                            />
                          }
                          validationError={
                            validationError &&
                            validationError[`segments.${segIdx}.carrier`]
                          }
                        />
                      ) : (
                        <InputField
                          id={`carrier-${segIdx}`}
                          label={`Carrier ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the carrier name."
                          value={segment.carrier ?? ""}
                          disabled={!editMode}
                        />
                      )}
                    </div>

                    {/* ROW 2 */}
                    <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                      {/* Departure Location */}
                      {editMode ? (
                        <InputField
                          id={`departure-${segIdx}`}
                          label={`Departure ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the departure location."
                          value={getFlightLocationForSegment(
                            segment,
                            "departure"
                          )}
                          disabled={!editMode}
                          input={
                            <ReusableSearchUpdate
                              segmentIndex={segIdx}
                              fieldType="departure"
                            />
                          }
                          validationError={
                            validationError &&
                            validationError[
                              `segments.${segIdx}.departure.airportCode`
                            ]
                          }
                        />
                      ) : (
                        <InputField
                          id={`departure-${segIdx}`}
                          label={`Departure ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the departure location."
                          value={getFlightLocationForSegment(
                            segment,
                            "departure"
                          )}
                          disabled={!editMode}
                        />
                      )}

                      {/* Arrival Location */}
                      {editMode ? (
                        <InputField
                          id={`arrival-${segIdx}`}
                          label={`Arrival ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the arrival location."
                          value={getFlightLocationForSegment(
                            segment,
                            "arrival"
                          )}
                          disabled={!editMode}
                          input={
                            <ReusableSearchUpdate
                              segmentIndex={segIdx}
                              fieldType="arrival"
                            />
                          }
                          validationError={
                            validationError &&
                            validationError[
                              `segments.${segIdx}.arrival.airportCode`
                            ]
                          }
                        />
                      ) : (
                        <InputField
                          id={`arrival-${segIdx}`}
                          label={`Arrival ${segIdx !== 0 ? segIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the arrival location."
                          value={getFlightLocationForSegment(
                            segment,
                            "arrival"
                          )}
                          disabled={!editMode}
                        />
                      )}
                    </div>

                    {/* ROW 3 */}
                    <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                      {/* Departure Time */}
                      {(() => {
                        // Scenario 1: Viewing pending updates (ticket.updated is true and updatedTicket has data)
                        if (
                          ticket.updated &&
                          updatedTicket &&
                          updatedTicket.segments &&
                          updatedTicket.segments[segIdx]
                        ) {
                          return (
                            <UpdatedInputField
                              id={`departureTime-${segIdx}`}
                              label={`Departure Time ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              tooltip="This field displays the departure time."
                              value={getFormatTime(segment.departureTime) ?? ""}
                              newValue={
                                getFormatTime(
                                  updatedTicket.segments[segIdx].departureTime
                                ) ?? ""
                              }
                            />
                          );
                        }
                        // Scenario 2: Editing the ticket (editMode or updateReqMode is true)
                        else if (editMode || updateReqMode) {
                          return (
                            <InputField
                              id={`departureTime-${segIdx}`}
                              label={`Departure Time ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              type="text"
                              tooltip="Enter or modify the departure time for this segment."
                              value={getFormatTime(segment.departureTime) ?? ""} // Display current formatted time
                              disabled={!(editMode || updateReqMode)}
                              input={
                                <LocalizationProvider
                                  dateAdapter={AdapterDayjs}
                                >
                                  <TimeField
                                    id={`departureTime-timefield-${segIdx}`}
                                    className="custom-time-field w-full"
                                    format="HH:mm"
                                    disabled={!(editMode || updateReqMode)}
                                    value={dayjs(segment.departureTime)}
                                    onChange={(newTime) =>
                                      handleTimeChange(
                                        "departure",
                                        segIdx,
                                        segment, // Pass current segment context
                                        newTime
                                      )
                                    }
                                    sx={{
                                      "& .MuiOutlinedInput-root": {
                                        border: "none",
                                        outline: "none",
                                        boxShadow: "none",
                                      },
                                      "& .MuiOutlinedInput-notchedOutline": {
                                        border: "none",
                                      },
                                      "& .MuiOutlinedInput-root.Mui-focused": {
                                        border: "2px solid #ef4444",
                                        outline: "none",
                                        boxShadow: "none",
                                      },
                                    }}
                                  />
                                </LocalizationProvider>
                              }
                              validationError={
                                validationError &&
                                validationError[
                                  `segments.${segIdx}.departureTime`
                                ]
                              }
                            />
                          );
                        }
                        // Scenario 3: Default view (not editing, no pending update)
                        else {
                          return (
                            <InputField
                              id={`departureTime-${segIdx}`}
                              label={`Departure Time ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              type="text"
                              tooltip="This field displays the departure time."
                              value={getFormatTime(currentSegment.departureTime) ?? ""}
                              disabled={true}
                            />
                          );
                        }
                      })()}

                      {/* Arrival Time */}
                      {(() => {
                        // Scenario 1: Viewing pending updates (ticket.updated is true and updatedTicket has data)
                        if (
                          ticket.updated &&
                          updatedTicket &&
                          updatedTicket.segments &&
                          updatedTicket.segments[segIdx]
                        ) {
                          return (
                            <UpdatedInputField
                              id={`arrivalTime-${segIdx}`}
                              label={`Arrival Time ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              tooltip="This field displays the arrival time."
                              value={getFormatTime(segment.arrivalTime) ?? ""}
                              newValue={
                                getFormatTime(
                                  updatedTicket.segments[segIdx].arrivalTime
                                ) ?? ""
                              }
                            />
                          );
                        }
                        // Scenario 2: Editing the ticket (editMode or updateReqMode is true)
                        else if (editMode || updateReqMode) {
                          return (
                            <InputField
                              id={`arrivalTime-${segIdx}`}
                              label={`Arrival Time ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              type="text"
                              tooltip="Enter or modify the arrival time for this segment."
                              value={getFormatTime(segment.arrivalTime) ?? ""} // Display current formatted time
                              disabled={!(editMode || updateReqMode)}
                              input={
                                <LocalizationProvider
                                  dateAdapter={AdapterDayjs}
                                >
                                  <TimeField
                                    id={`arrivalTime-timefield-${segIdx}`}
                                    className="custom-time-field w-full"
                                    format="HH:mm"
                                    disabled={!(editMode || updateReqMode)}
                                    value={dayjs(segment.arrivalTime)}
                                    onChange={(newTime) =>
                                      handleTimeChange(
                                        "arrival",
                                        segIdx,
                                        segment, // Pass current segment context
                                        newTime
                                      )
                                    }
                                    sx={{
                                      "& .MuiOutlinedInput-root": {
                                        border: "none",
                                        outline: "none",
                                        boxShadow: "none",
                                      },
                                      "& .MuiOutlinedInput-notchedOutline": {
                                        border: "none",
                                      },
                                      "& .MuiOutlinedInput-root.Mui-focused": {
                                        border: "2px solid #ef4444",
                                        outline: "none",
                                        boxShadow: "none",
                                      },
                                    }}
                                  />
                                </LocalizationProvider>
                              }
                              validationError={
                                validationError &&
                                validationError[
                                  `segments.${segIdx}.arrivalTime`
                                ]
                              }
                            />
                          );
                        }
                        // Scenario 3: Default view (not editing, no pending update)
                        else {
                          return (
                            <InputField
                              id={`arrivalTime-${segIdx}`}
                              label={`Arrival Time ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              type="text"
                              tooltip="This field displays the arrival time."
                              value={getFormatTime(currentSegment.arrivalTime) ?? ""}
                              disabled={true}
                            />
                          );
                        }
                      })()}

                      {/* Duration */}
                      {(() => {
                        // Scenario 1: Viewing pending updates (ticket.updated is true and updatedTicket has data)
                        if (
                          ticket.updated &&
                          updatedTicket &&
                          updatedTicket.segments &&
                          updatedTicket.segments[segIdx]
                        ) {
                          return (
                            <UpdatedInputField
                              id={`duration-${segIdx}`}
                              label={`Duration ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              tooltip="This field displays the flight duration."
                              value={segment.duration}
                              newValue={
                                updatedTicket?.segments?.[segIdx]?.duration
                                  ? updatedTicket.segments[segIdx].duration
                                  : ""
                              }
                            />
                          );
                        }
                        // Scenario 2: Editing the ticket (editMode or updateReqMode is true)
                        else if (editMode || updateReqMode) {
                          return (
                            <InputField
                              id={`duration-${segIdx}`}
                              label={`Duration ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              type="text"
                              tooltip="This field displays the flight duration."
                              value={segment.duration} // Display current formatted time
                              disabled={!(editMode || updateReqMode)}
                              validationError={
                                validationError &&
                                validationError[`segments.${segIdx}.duration`]
                              }
                            />
                          );
                        }
                        // Scenario 3: Default view (not editing, no pending update)
                        else {
                          return (
                            <InputField
                              id={`duration-${segIdx}`}
                              label={`Duration ${
                                segIdx !== 0 ? segIdx + 1 : ""
                              }`}
                              type="text"
                              tooltip="This field displays the flight duration."
                              value={currentSegment.duration}
                              disabled={true}
                            />
                          );
                        }
                      })()}
                    </div>
                    {/* Conditionally render <hr> if it's not the last segment */}
                    {segIdx < ticket.segments.length - 1 && (
                      <hr className="border-t border-gray-300 dark:border-gray-500 my-1"></hr>
                    )}
                  </div>
                );
              })}

              {/* BUTTONS */}
              {editMode && (
                <div className="w-full text-right mt-4">
                  <div className="flex justify-between items-center">
                    <button
                      type="button"
                      onClick={() => dispatch(addSegmentToUpdateTicket())}
                      className="bg-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 "
                    >
                      <Plus size={20} className="mr-2" />
                      Add Segment
                    </button>
                    {ticket?.segments.length > 1 && (
                      <button
                        type="button"
                        onClick={() =>
                          dispatch(removeSegmentSectionToUpdateTicket())
                        }
                        className="bg-red-500 text-white px-2 py-1  md:py-2 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 hover:bg-red-600"
                      >
                        <FaMinus />
                      </button>
                    )}
                  </div>
                </div>
              )}
            </Card>
          </div>

          {/* FLIGHT CLASSES  */}
          {ticket.flightClasses.map((flightClass, classIdx) => (
            <div
              key={classIdx}
              id={`Flight Classes ${classIdx !== 0 ? classIdx + 1 : ""}`}
            >
              {/* flightClass */}
              <Card
                icon={<Users color="#EE4544" />}
                title={`Flight Classes ${classIdx !== 0 ? classIdx + 1 : ""}`}
              >
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
                  {/* FLIGHT CLASSES */}
                  {editMode ? (
                    <InputField
                      id={`flightClass-${classIdx}`}
                      label={`Flight Class ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the Flight Class of the Flight."
                      value={flightClass?.type ?? ""}
                      disabled={editMode}
                      validationError={
                        validationError &&
                        validationError[`flightClasses.${classIdx}.type`]
                      }
                      input={
                        <ReusableDropdown
                          options={flightClassOptions}
                          initialSelectedId={findInitialSelectedId(
                            flightClassOptions,
                            flightClass.type as string
                          )}
                          placeholder="Select flight class"
                          classIdx={classIdx}
                          isTicketStatus={false}
                        />
                      }
                    />
                  ) : (
                    <InputField
                      id={`flightClass-${classIdx}`}
                      label={`Flight Class ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the Flight Class of the Flight."
                      value={flightClass?.type ?? ""}
                      disabled={!editMode}
                    />
                  )}
                </div>
              </Card>

              {/* Baggage */}
              <Card
                icon={<Users color="#EE4544" />}
                title={`Baggage ${classIdx !== 0 ? classIdx + 1 : ""}`}
              >
                {/* ROW 1 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
                  {/* carryOnAllowed */}
                  {editMode ? (
                    <InputField
                      id={`carryOnAllowed-${classIdx}`}
                      label={`Carry On Allowed ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the maximum number of carry-on bags allowed."
                      value={flightClass?.carryOnAllowed ?? ""}
                      disabled={!editMode}
                      input={
                        <ReusableDropdown
                          options={carryOnAllowedOptions}
                          initialSelectedId={findInitialSelectedId(
                            carryOnAllowedOptions,
                            flightClass.carryOnAllowed as number
                          )}
                          placeholder="Select carry-on allowed"
                          classIdx={classIdx}
                          isBaggageDropdown={true}
                          baggage="carryOnAllowed"
                        />
                      }
                      validationError={
                        validationError &&
                        validationError[
                          `flightClasses.${classIdx}.carryOnAllowed`
                        ]
                      }
                    />
                  ) : (
                    <InputField
                      id={`carryOnAllowed-${classIdx}`}
                      label={`Carry On Allowed ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the maximum number of carry-on bags allowed."
                      value={flightClass?.carryOnAllowed ?? ""}
                      disabled={!editMode}
                    />
                  )}

                  {/* carryOnWeight */}
                  {editMode ? (
                    <InputField
                      id={`carryOnWeight-${classIdx}`}
                      label={`Carry On Weight ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the Maximum weight allowed for each carry-on bag."
                      value={flightClass?.carryOnWeight ?? ""}
                      disabled={!editMode}
                      input={
                        <ReusableDropdown
                          options={carryOnWeightOptions}
                          initialSelectedId={findInitialSelectedId(
                            carryOnWeightOptions,
                            flightClass.carryOnWeight as number
                          )}
                          placeholder="Max Weight Per Bag"
                          classIdx={classIdx}
                          isBaggageDropdown={true}
                          baggage="carryOnWeight"
                        />
                      }
                      validationError={
                        validationError &&
                        validationError[
                          `flightClasses.${classIdx}.carryOnWeight`
                        ]
                      }
                    />
                  ) : (
                    <InputField
                      id={`carryOnWeigh-${classIdx}`}
                      label={`Carry On Weight ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the Maximum weight allowed for each carry-on bag."
                      value={flightClass?.carryOnWeight ?? ""}
                      disabled={!editMode}
                    />
                  )}
                </div>

                {/* ROW 2 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* checkedAllowed */}
                  {editMode ? (
                    <InputField
                      id={`checkedAllowed-${classIdx}`}
                      label={`Checked Bags Allowed ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the maximum number of checked bags allowed."
                      value={flightClass?.checkedAllowed ?? ""}
                      disabled={!editMode}
                      input={
                        <ReusableDropdown
                          options={checkedAllowedOptions}
                          initialSelectedId={findInitialSelectedId(
                            checkedAllowedOptions,
                            flightClass.checkedAllowed as number
                          )}
                          placeholder="Select checked allowed"
                          classIdx={classIdx}
                          isBaggageDropdown={true}
                          baggage="checkedAllowed"
                        />
                      }
                      validationError={
                        validationError &&
                        validationError[
                          `flightClasses.${classIdx}.carryOnWeight`
                        ]
                      }
                    />
                  ) : (
                    <InputField
                      id={`checkedAllowed-${classIdx}`}
                      label={`Checked Bags Allowed ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the maximum number of checked bags allowed."
                      value={flightClass?.checkedAllowed ?? ""}
                      disabled={!editMode}
                    />
                  )}

                  {/* checkedWeight */}
                  {editMode ? (
                    <InputField
                      id={`checkedWeight-${classIdx}`}
                      label={`Checked Bags Weight ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the Maximum weight allowed for each Checked bag."
                      value={flightClass?.checkedWeight ?? ""}
                      disabled={!editMode}
                      input={
                        <ReusableDropdown
                          options={checkedWeightOptions}
                          initialSelectedId={findInitialSelectedId(
                            checkedWeightOptions,
                            flightClass.checkedWeight as number
                          )}
                          placeholder="Max Weight Per Bag"
                          classIdx={classIdx}
                          isBaggageDropdown={true}
                          baggage="checkedWeight"
                        />
                      }
                      validationError={
                        validationError &&
                        validationError[
                          `flightClasses.${classIdx}.checkedWeight`
                        ]
                      }
                    />
                  ) : (
                    <InputField
                      id={`checkedWeight-${classIdx}`}
                      label={`Checked Bags Weight ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="text"
                      tooltip="This field displays the Maximum weight allowed for each Checked bag."
                      value={flightClass?.checkedWeight ?? ""}
                      disabled={!editMode}
                    />
                  )}
                </div>

                {/* ROW 3 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* checkedFee */}
                  <InputField
                    id={`checkedFee-${classIdx}`}
                    label={`Checked Bag Fee ${
                      classIdx !== 0 ? classIdx + 1 : ""
                    }`}
                    type="text"
                    tooltip="This field displays the fee for each checked bag."
                    placeholder="Fee for first checked bag"
                    value={flightClass?.checkedFee ?? ""}
                    min={0}
                    disabled={!editMode}
                    onChange={(e) => handleFormChange(e, undefined, classIdx)}
                    validationError={
                      validationError &&
                      validationError[`flightClasses.${classIdx}.checkedFee`]
                    }
                  />

                  {/* additionalFee */}
                  <InputField
                    id={`additionalFee-${classIdx}`}
                    label={`Additional Bag Fee ${
                      classIdx !== 0 ? classIdx + 1 : ""
                    }`}
                    type="text"
                    tooltip="This field displays the fee for any additional bags beyond the allowed limit."
                    placeholder="Fee for additional bags"
                    value={flightClass?.additionalFee ?? ""}
                    min={0}
                    disabled={!editMode}
                    onChange={(e) => handleFormChange(e, undefined, classIdx)}
                    validationError={
                      validationError &&
                      validationError[`flightClasses.${classIdx}.additionalFee`]
                    }
                  />
                </div>
              </Card>

              {/* Price */}
              <Card
                icon={<Coins color="#EE4544" />}
                title={`Price ${classIdx !== 0 ? classIdx + 1 : ""}`}
              >
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* adult */}
                  {ticket.updated &&
                  updatedTicket &&
                  updatedTicket.flightClasses &&
                  updatedTicket.flightClasses[classIdx] ? (
                    <UpdatedInputField
                      id={`adult-${classIdx}`}
                      label={`Adult (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      tooltip="This field displays the price for an adult ticket in Jordanian Dinars."
                      value={flightClass?.price?.adult ?? ""}
                      newValue={
                        updatedTicket.flightClasses[classIdx]?.price?.adult ??
                        ""
                      }
                    />
                  ) : (
                    <InputField
                      id={`adult-${classIdx}`}
                      label={`Adult (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="number"
                      min={0}
                      tooltip="This field displays the price for an adult ticket in Jordanian Dinars."
                      placeholder="Adult price"
                      value={flightClass?.price?.adult ?? ""}
                      disabled={!editMode && !updateReqMode}
                      onChange={(e) => handlePriceChange(e, classIdx)}
                      validationError={
                        validationError &&
                        validationError[`flightClasses.${classIdx}.price.adult`]
                      }
                    />
                  )}

                  {/* child */}
                  {ticket.updated &&
                  updatedTicket &&
                  updatedTicket.flightClasses &&
                  updatedTicket.flightClasses[classIdx] ? (
                    <UpdatedInputField
                      id={`child-${classIdx}`}
                      label={`Child (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      tooltip="This field displays the price for a child ticket."
                      value={flightClass?.price.child ?? ""}
                      newValue={
                        updatedTicket.flightClasses[classIdx]?.price?.child ??
                        ""
                      }
                    />
                  ) : (
                    <InputField
                      id={`child-${classIdx}`}
                      label={`Child (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="number"
                      min={0}
                      tooltip="This field displays the price for a child ticket."
                      placeholder="Child price"
                      value={flightClass?.price.child ?? ""}
                      disabled={!editMode && !updateReqMode}
                      onChange={(e) => handlePriceChange(e, classIdx)}
                      validationError={
                        validationError &&
                        validationError[`flightClasses.${classIdx}.price.child`]
                      }
                    />
                  )}
                </div>
                {/* row 2 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
                  {/* infant */}
                  {ticket.updated &&
                  updatedTicket &&
                  updatedTicket.flightClasses &&
                  updatedTicket.flightClasses[classIdx] ? (
                    <UpdatedInputField
                      id={`infant-${classIdx}`}
                      label={`Infant (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      tooltip="This field displays the price for an infant ticket."
                      value={flightClass?.price.infant ?? ""}
                      newValue={
                        updatedTicket.flightClasses[classIdx]?.price?.infant ??
                        ""
                      }
                    />
                  ) : (
                    <InputField
                      id={`infant-${classIdx}`}
                      label={`Infant (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="number"
                      min={0}
                      tooltip="This field displays the price for an infant ticket."
                      placeholder="Infant price"
                      value={flightClass?.price.infant ?? ""}
                      disabled={!editMode && !updateReqMode}
                      onChange={(e) => handlePriceChange(e, classIdx)}
                      validationError={
                        validationError &&
                        validationError[
                          `flightClasses.${classIdx}.price.infant`
                        ]
                      }
                    />
                  )}

                  {/* tax */}
                  {ticket.updated &&
                  updatedTicket &&
                  updatedTicket.flightClasses &&
                  updatedTicket.flightClasses[classIdx] ? (
                    <UpdatedInputField
                      id={`tax-${classIdx}`}
                      label={`Tax % (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      tooltip="This field displays the tax amount for the ticket in percentage."
                      value={flightClass.price.tax ?? ""}
                      newValue={
                        updatedTicket.flightClasses[classIdx]?.price?.tax ?? ""
                      }
                    />
                  ) : (
                    <InputField
                      id={`tax-${classIdx}`}
                      label={`Tax % (JOD) ${
                        classIdx !== 0 ? classIdx + 1 : ""
                      }`}
                      type="number"
                      min={0}
                      tooltip="This field displays the tax amount for the ticket in percentage."
                      placeholder="Tax price"
                      value={flightClass.price.tax ?? ""}
                      disabled={!editMode && !updateReqMode}
                      onChange={(e) => handlePriceChange(e, classIdx)}
                      validationError={
                        validationError &&
                        validationError[`flightClasses.${classIdx}.price.tax`]
                      }
                    />
                  )}
                </div>
              </Card>

              {/* extraOffers */}
              {flightClass.extraOffers &&
                flightClass.extraOffers?.length > 0 && (
                  <Card
                    icon={<Gift color="#EE4544" />}
                    title={`Extra Offers ${classIdx !== 0 ? classIdx + 1 : ""}`}
                  >
                    {(!flightClass.extraOffers ||
                      flightClass.extraOffers.length === 0) &&
                    editMode ? (
                      <EmptyState
                        icon={<Gift size={48} color="#EE4544" />}
                        title="No Extra Offers Yet"
                        description="Click the button below to add special offers or upgrades for this flight."
                      />
                    ) : (
                      <>
                        {(flightClass.extraOffers || []).map(
                          (offer, offerIdx) => (
                            <div
                              key={offerIdx + (offer.name || "")}
                              className="md:flex md:items-center space-y-4 md:space-y-0 md:space-x-4"
                            >
                              {/* Offer Name */}
                              {editMode ? (
                                <InputField
                                  id={`name-${classIdx}-${offerIdx}`}
                                  label={`Name ${
                                    offerIdx !== 0 ? offerIdx + 1 : ""
                                  }`}
                                  type="text"
                                  tooltip="This field displays the name of the extra offer."
                                  value={offer.name}
                                  disabled={!editMode}
                                  input={
                                    <ReusableDropdown
                                      options={extraOffersNameOptions}
                                      initialSelectedId={
                                        findInitialSelectedId(
                                          extraOffersNameOptions,
                                          offer.name
                                        )!
                                      }
                                      placeholder="Select an extra offer"
                                      classIdx={classIdx}
                                      offerIdx={offerIdx}
                                      value={offer.name}
                                      field="name"
                                    />
                                  }
                                  validationError={
                                    validationError &&
                                    validationError[
                                      `flightClasses.${classIdx}.extraOffers.${offerIdx}.name`
                                    ]
                                  }
                                />
                              ) : (
                                <InputField
                                  id={`name-${classIdx}-${offerIdx}`}
                                  label={`Name ${
                                    offerIdx !== 0 ? offerIdx + 1 : ""
                                  }`}
                                  type="text"
                                  tooltip="This field displays the name of the extra offer."
                                  value={offer.name}
                                  disabled={!editMode}
                                />
                              )}

                              {/* Offer Availability */}
                              {editMode ? (
                                <InputField
                                  id={`available-${classIdx}-${offerIdx}`}
                                  label={`Available ${
                                    offerIdx !== 0 ? offerIdx + 1 : ""
                                  }`}
                                  type="text"
                                  tooltip="This field displays the availability of the extra offer."
                                  value={offer.available}
                                  disabled={!editMode}
                                  input={
                                    <ReusableDropdown
                                      options={extraOffersAvailableOptions}
                                      initialSelectedId={findInitialSelectedId(
                                        extraOffersAvailableOptions,
                                        offer.available
                                      )}
                                      placeholder="Select availability"
                                      classIdx={classIdx}
                                      offerIdx={offerIdx}
                                      value={offer.available}
                                      field="available"
                                    />
                                  }
                                  validationError={
                                    validationError &&
                                    validationError[
                                      `flightClasses.${classIdx}.extraOffers.${offerIdx}.available`
                                    ]
                                  }
                                />
                              ) : (
                                <InputField
                                  id={`available-${classIdx}-${offerIdx}`}
                                  label={`Available ${
                                    offerIdx !== 0 ? offerIdx + 1 : ""
                                  }`}
                                  type="text"
                                  tooltip="This field displays the availability of the extra offer."
                                  value={offer.available}
                                  disabled={!editMode}
                                />
                              )}

                              {editMode && (
                                <button
                                  type="button"
                                  onClick={() =>
                                    dispatch(
                                      removeExtraOffersToUpdateTicket({
                                        classIdx,
                                        offerIdx,
                                      })
                                    )
                                  }
                                  className="mt-6 p-2 text-red-500 hover:text-red-600 transition-colors duration-300"
                                  title="Delete offer"
                                >
                                  <Trash2 size={20} />
                                </button>
                              )}
                            </div>
                          )
                        )}
                      </>
                    )}

                    {editMode && (
                      <div className="w-full text-right pb-5">
                        <div className="flex justify-between items-center">
                          <button
                            className="bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center mt-6 shadow-md hover:shadow-lg transition-all duration-300 "
                            type="button"
                            onClick={() =>
                              dispatch(
                                addExtraOffersToUpdateTicket({ classIdx })
                              )
                            }
                          >
                            <Plus size={20} className="mr-2" />
                            Add Extra Offer
                          </button>
                        </div>
                      </div>
                    )}
                  </Card>
                )}
            </div>
          ))}
          {/* {editMode && (
            <div className="w-full text-right pb-5">
              <div className="flex justify-between items-center">
                <button
                  type="button"
                  onClick={() => {
                    dispatch(addFlightClassSectionToUpdateTicket());
                  }}
                  className="bg-blue-500 text-white px-2 py-1 md:px-4 md:py-2 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 "
                >
                  <Plus size={20} className="mr-2" />
                  Add Flight Class
                </button>
                {flightTicket.flightClasses.length > 1 && (
                  <button
                    type="button"
                    onClick={() => {
                      dispatch(removeFlightClassSectionToUpdateTicket());
                    }}
                    className="bg-red-500 opacity-70 text-white px-2 py-1  md:py-2 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 "
                  >
                    <FaMinus />
                  </button>
                )}
              </div>
            </div>
          )} */}

          {/* withdraw update request button  */}
          {ticket.updated && (
            <div className="text-right space-x-5 border-t-2 py-5 border-slate-500 dark:border-slate-400">
              <button
                type="button"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                  handleWithdrawUpdateRequest(e)
                }
                className={`btn text-white capitalize px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-base font-semibold ${"bg-red-500 hover:bg-red-600"}`}
              >
                Withdraw Update Request
                <Tooltip
                  className="text-gray-200"
                  text={`${"Withdraw the update request."}`}
                />
              </button>
            </div>
          )}

          {/* Buttons */}
          {(editMode || updateReqMode) && (
            <div className="text-right space-x-0 md:space-x-5 border-t-2 py-5 border-slate-500 dark:border-slate-400 flex md:flex-row flex-col-reverse justify-end">
              {/* invalid ticket update btns */}
              {editMode &&
                ["cancel", "update"].map((action, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                      action === "update"
                        ? handleSubmitTicket(e)
                        : handleCancelUpdateTicket()
                    }
                    className={`btn text-white capitalize px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-base font-semibold ${
                      action === "update"
                        ? "bg-red-500 hover:bg-red-600"
                        : "bg-blue-500 hover:bg-blue-600 mt-5 md:mt-0"
                    }`}
                  >
                    {action.charAt(0).toUpperCase() + action.slice(1)}
                    <Tooltip
                      className="text-gray-200"
                      text={`${
                        action === "update"
                          ? "Submit changes to update the ticket details."
                          : "Discard changes and cancel the update process."
                      }`}
                    />
                  </button>
                ))}

              {/* valid update request btns  */}
              {updateReqMode &&
                ["cancel", "update"].map((action, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                      action === "update"
                        ? handleUpdateRequest(e)
                        : handleCancelUpdateTicket()
                    }
                    className={`btn text-white capitalize px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-base font-semibold ${
                      action === "update"
                        ? "bg-red-500 hover:bg-red-600"
                        : "bg-blue-500 hover:bg-blue-600"
                    }`}
                  >
                    {action.charAt(0).toUpperCase() + action.slice(1)}
                    <Tooltip
                      className="text-gray-200"
                      text={`${
                        action === "update"
                          ? "Send Update request, the changes apply upon admin user approval."
                          : "Discard changes and cancel the update process."
                      }`}
                    />
                  </button>
                ))}
            </div>
          )}
          {/* Ticket Logs */}
          {/* Content container */}
          <Card icon={<FileText color="#EE4544" />} title="Ticket Logs">
            {/* Logs section */}
            <div className="bg-gray-50 dark:bg-gray-600 rounded-lg p-6 mb-6 shadow-md">
              {/* Section header with icon and tooltip */}
              <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-white flex items-center">
                <FileText className="mr-2 text-red-500" />
                Recent Activity
                <Tooltip
                  text="This section displays the most recent ticket-related
                              actions, sorted by date with the latest updates shown
                              first."
                />
              </h2>
              {/* Scrollable log entries container */}
              <div className="max-h-96 overflow-y-auto custom-scrollbar-logs">
                {/* Map through log entries and render each one */}
                {sortedLogs &&
                  sortedLogs.length > 0 &&
                  sortedLogs.map((ticketLogs, idx) => (
                    <MyTicketHistoryLogs
                      key={idx}
                      ticketLogs={ticketLogs}
                      index={sortedLogs.length - idx}
                    />
                  ))}
              </div>
            </div>
          </Card>
        </div>
      </form>
    </div>
  );
}

export const InputField = ({
  id,
  label,
  placeholder,
  icon,
  tooltip,
  value,
  onChange,
  type,
  min,
  max,
  validationError,
  input,
  rows,
  disabled,
  required = true,
}: {
  id?: string;
  label: string;
  placeholder?: string;
  icon?: any;
  tooltip?: string;
  value?: string | number | undefined;
  onChange?: (
    e:
      | React.ChangeEvent<HTMLTextAreaElement>
      | React.ChangeEvent<HTMLInputElement>
  ) => void;
  type?: string;
  min?: number;
  max?: number;
  validationError?: any;
  input?: any;
  rows?: number;
  disabled?: boolean;
  required?: boolean;
}) => (
  <div className="flex-1 min-h-[5.6rem]">
    <label
      className="text-sm text-gray-800 dark:text-white font-medium mb-2 flex items-center capitalize space-x-2"
      htmlFor={id}
    >
      <p>{label}</p> {required && <span className="text-red-500">*</span>}
      {tooltip && <Tooltip text={tooltip} />}
    </label>
    {input ? (
      input
    ) : type === "textarea" ? (
      <textarea
        id={id}
        style={{ resize: "none" }}
        className="w-full border-0 text-opacity-70 dark:text-gray-200 dark:placeholder:text-gray-200 placeholder:text-opacity-70 disabled:text-gray-600 dark:disabled:text-gray-400 bg-gray-50 dark:bg-gray-600 disabled:bg-gray-400/50 disabled:dark:bg-gray-800/50 rounded-lg py-2 px-3 shadow focus:ring-2 focus:ring-red-500 disabled:cursor-not-allowed"
        rows={rows}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
      />
    ) : (
      <div className="relative">
        <input
          placeholder={placeholder}
          className="w-full border-0 
              text-opacity-70 dark:text-gray-200 disabled:text-gray-600 dark:disabled:text-gray-400 dark:placeholder:text-gray-200 placeholder:text-opacity-70 bg-white dark:bg-gray-600 rounded-lg py-2 px-3 shadow dark:shadow-inner border-none outline-none ring-0 focus:ring-2 focus:ring-red-500 focus:outline-none transition-all duration-300 no-arrows disabled:cursor-not-allowed disabled:bg-gray-400/50 disabled:dark:bg-gray-800/50 capitalize"
          id={id}
          value={value ?? ""}
          min={min}
          max={max}
          type={type}
          onChange={onChange}
          inputMode={type === "number" ? "numeric" : "text"}
          pattern={type === "number" ? "[0-9]*" : undefined}
          onWheel={(e) => (e.target as HTMLInputElement).blur()}
          disabled={disabled}
          readOnly={!onChange}
        />
        {icon && (
          <span className="absolute right-3 top-2.5 text-gray-400">{icon}</span>
        )}
      </div>
    )}
    <div className="text-sm mt-1 text-red-500">{validationError}</div>
  </div>
);

export const UpdatedInputField = ({
  id,
  label,
  tooltip,
  icon,
  value,
  newValue,
}: {
  id?: string;
  label: string;
  tooltip?: string;
  icon?: any;
  value?: string | number | undefined;
  newValue?: string | number | undefined;
}) => (
  <div className="flex-1 min-h-[5.6rem]">
    <label
      className="text-sm text-gray-800 dark:text-white font-medium mb-2 flex items-center capitalize"
      htmlFor={id}
    >
      {label}
      {tooltip && <Tooltip text={tooltip} />}
    </label>

    <div className="relative">
      <div
        className={`w-full ${
          value === newValue ? "" : "border-2 border-green-500"
        }
              text-opacity-70 dark:text-gray-200 dark:placeholder:text-gray-200 placeholder:text-opacity-70  dark:bg-gray-600 rounded-lg py-2 px-3 shadow  dark:shadow-inner focus:ring-2 focus:ring-red-500 focus:outline-none transition-all duration-300 no-arrows disabled:cursor-not-allowed bg-gray-100 dark:bg-gray-800/50`}
      >
        {value === newValue ? (
          value
        ) : (
          <div className="flex items-center">
            <span className="text-red-500">{value}</span>
            <ArrowBigRight className="ml-2 text-green-500" />
            <span className="text-green-500">{newValue}</span>
          </div>
        )}
      </div>
      {icon && (
        <span className="absolute right-3 top-2.5 text-gray-400">{icon}</span>
      )}
    </div>
  </div>
);
