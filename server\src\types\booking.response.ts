import { Booking, BookingSource, BookingStatus, BookingType, TripType, User, Prisma } from "@prisma/client";

type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
interface JsonObject { [key: string]: JsonValue; }
interface JsonArray extends Array<JsonValue> {}

type BookingMeta = {
  bookedByAgentName?: string;
  ownerName?: string;
  agentName?: string;
  agencyName?: string | null;
  [key: string]: any; // For any additional meta fields
};

type BookingWithMeta = Omit<Booking, 'meta'> & {
  meta: JsonValue | BookingMeta | null;
  createdById: string; // Add createdById to the base type
};

export interface BookingWithUser extends BookingWithMeta {
  user?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
    role?: string;
    roleType?: string;
    agencyName?: string | null;
    agencyAgent?: Array<{
      agency: {
        id: string;
        agencyName: string | null;
      };
    }>;
  };
  agent?: string; // This will be added by the controller
  agencyName?: string | null;
  ticket?: any;
  travelers?: Array<{ traveler: any }>;
  bookedSeats?: any[];
  payment?: any;
  bookingHistoryLogs?: any[];
  notifications?: any[];
}

export interface BookingsResponse {
  success: boolean;
  results: {
    bookings: BookingWithUser[];
    bookingsTotal: number;
    nextCursor: string | null;
  };
}
