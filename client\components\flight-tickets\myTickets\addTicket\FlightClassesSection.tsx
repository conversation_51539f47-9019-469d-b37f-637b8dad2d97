"use client";
import {
  addExtraOffers,
  removeExtraOffers,
  selectTicketForm,
  updateTicketForm,
} from "@/redux/features/TicketFormSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";

import React, { useEffect } from "react";
import ExtraOffersSection from "./ExtraOffersSection";
import AddTicketDropdown from "./AddTicketDropdown";

import {
  CreateExtraOffersTypes,
  CreateFlightClassesFormTypes,
} from "@/utils/definitions/myTicketsDefinitions";
import {
  carryOnAllowedOptions,
  carryOnWeightOptions,
  checkedAllowedOptions,
  checkedWeightOptions,
  flightClassOptions,
} from "./AddTicketData";
import { Card, EmptyState, InputField } from "./AddTicketComponents";
import { CreditCard, Gift, Luggage, Plus, Trash2, Users } from "lucide-react";

const FlightClassesSection = ({
  classIdx,
  validationError,
}: {
  classIdx: number;
  validationError: any;
}) => {
  // ############### STATES ##############
  // segment from data
  const formData = useAppSelector(selectTicketForm);
  const flightClass = formData.flightClasses[classIdx];
  const dispatch = useAppDispatch();

  // Check initial state of baggage fields
  useEffect(() => {
    let needsUpdate = false;
    let updatedClass = { ...flightClass };

    // If Carry-On Allowed is 0, ensure Carry-On Weight is also 0
    if (flightClass.carryOnAllowed === 0 && flightClass.carryOnWeight !== 0) {
      updatedClass.carryOnWeight = 0;
      needsUpdate = true;
    }

    // If Checked Allowed is 0, ensure Checked Weight is also 0
    if (flightClass.checkedAllowed === 0 && flightClass.checkedWeight !== 0) {
      updatedClass.checkedWeight = 0;
      needsUpdate = true;
    }

    // Only dispatch if we need to update
    if (needsUpdate) {
      const updatedFormData = {
        ...formData,
        flightClasses: formData.flightClasses.map(
          (classes: CreateFlightClassesFormTypes, i: number) =>
            i === classIdx ? updatedClass : classes
        ),
      };
      dispatch(updateTicketForm(updatedFormData));
    }
  }, [
    flightClass.carryOnAllowed,
    flightClass.checkedAllowed,
    flightClass.carryOnWeight,
    flightClass.checkedWeight,
    dispatch,
    formData,
    classIdx,
  ]);

  // ########## functions ############

  // Handle flight class field changes
  const handleFlightClassesChange = (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLSelectElement>
  ) => {
    const { id, value } = e.target;
    const [field, _] = id.split("-");

    // Update the form data with the modified segment
    const updatedFormData = {
      ...formData,
      flightClasses: formData.flightClasses.map(
        (classes: CreateFlightClassesFormTypes, i: number) =>
          i === classIdx
            ? {
                ...classes,
                [field]: value,
              }
            : classes
      ),
    };

    // Dispatch the updated form state
    dispatch(updateTicketForm(updatedFormData));
  };

  // handle dropdown changes
  const handleDropdownChanges = (field: string, value: string) => {
    let updatedClass = {
      ...formData.flightClasses[classIdx],
    } as CreateFlightClassesFormTypes;

    // Set the field value
    const numValue = parseInt(value);
    if (field === "type") {
      updatedClass.type = value;
    } else if (field === "carryOnAllowed") {
      updatedClass.carryOnAllowed = numValue;
      // If Carry-On Allowed is 0, set Carry-On Weight to 0
      if (numValue === 0) {
        updatedClass.carryOnWeight = 0;
      } else if (updatedClass.carryOnWeight === 0) {
        // If changing from 0 to a positive value, set a default weight
        updatedClass.carryOnWeight = parseInt(carryOnWeightOptions[0].value);
      }
    } else if (field === "carryOnWeight") {
      // Only allow setting weight if Carry-On Allowed is not 0
      if (updatedClass.carryOnAllowed !== 0) {
        updatedClass.carryOnWeight = numValue;
      } else {
        // Force weight to 0 if Carry-On Allowed is 0
        updatedClass.carryOnWeight = 0;
      }
    } else if (field === "checkedAllowed") {
      updatedClass.checkedAllowed = numValue;
      // If Checked Allowed is 0, set Checked Weight to 0
      if (numValue === 0) {
        updatedClass.checkedWeight = 0;
      } else if (updatedClass.checkedWeight === 0) {
        // If changing from 0 to a positive value, set a default weight
        updatedClass.checkedWeight = parseInt(checkedWeightOptions[0].value);
      }
    } else if (field === "checkedWeight") {
      // Only allow setting weight if Checked Allowed is not 0
      if (updatedClass.checkedAllowed !== 0) {
        updatedClass.checkedWeight = numValue;
      } else {
        // Force weight to 0 if Checked Allowed is 0
        updatedClass.checkedWeight = 0;
      }
    }

    const updatedFormData = {
      ...formData,
      flightClasses: formData.flightClasses.map(
        (classes: CreateFlightClassesFormTypes, i: number) =>
          i === classIdx ? updatedClass : classes
      ),
    };

    // Dispatch the updated form state
    dispatch(updateTicketForm(updatedFormData));
  };
  // update price change
  const handlePriceChange = (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLSelectElement>
  ) => {
    const { id, value } = e.target;
    const [field, _] = id.split("-");

    // Update the form data with the modified segment
    const updatedFormData = {
      ...formData,
      flightClasses: formData.flightClasses.map(
        (classes: CreateFlightClassesFormTypes, i: number) =>
          i === classIdx
            ? {
                ...classes,
                price: {
                  ...classes.price,
                  [field]: value,
                },
              }
            : classes
      ),
    };

    // Dispatch the updated form state
    dispatch(updateTicketForm(updatedFormData));
  };

  return (
    <div className="space-y-6 pt-2.5">
      {/* FLight Class Type */}
      <Card
        icon={<Users color="#EE4544" />}
        title={`Flight Class ${classIdx !== 0 ? classIdx + 1 : ""}`}
      >
        <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
          {/* Type */}
          <InputField
            label={`Flight Class ${classIdx !== 0 ? classIdx + 1 : ""}`}
            tooltip="Choose the class of service (e.g., Economy, Business, First Class)"
            validationError={
              validationError &&
              validationError[`flightClasses.${classIdx}.type`]
            }
            input={
              <AddTicketDropdown
                options={flightClassOptions}
                placeholder="Select Flight Class"
                value={flightClass.type}
                handleFlightClassChange={(value: string) =>
                  handleDropdownChanges("type", value)
                }
              />
            }
          />
        </div>
      </Card>

      {/* Baggage */}
      <Card
        icon={<Luggage color="#EE4544" />}
        title={`Baggage ${classIdx !== 0 ? classIdx + 1 : ""}`}
      >
        <div className="space-y-5 my-4">
          <div className="md:flex justify-center items-center space-y-5 md:space-y-0 md:space-x-4">
            {/* Carry On Allowed */}
            <InputField
              label={`Carry On Allowed ${classIdx !== 0 ? classIdx + 1 : ""}`}
              tooltip="Enter the number of carry-on bags allowed per passenger"
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.carryOnAllowed`]
              }
              input={
                <AddTicketDropdown
                  options={carryOnAllowedOptions}
                  placeholder="Enter carry-on bag allowance"
                  value={flightClass.carryOnAllowed || undefined}
                  handleFlightClassChange={(value: string) =>
                    handleDropdownChanges("carryOnAllowed", value)
                  }
                />
              }
            />

            {/* Carry On Weight */}
            <InputField
              label={`Carry-On Weight (kg) ${
                classIdx !== 0 ? classIdx + 1 : ""
              }`}
              tooltip="Enter the maximum weight allowed for each carry-on bag (in Kg)"
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.carryOnWeight`]
              }
              input={
                <AddTicketDropdown
                  options={
                    flightClass.carryOnAllowed === 0
                      ? [{ id: 0, value: "0" }]
                      : carryOnWeightOptions
                  }
                  placeholder="Max carry-on bag weight"
                  value={
                    flightClass.carryOnAllowed === 0
                      ? 0
                      : flightClass.carryOnWeight || undefined
                  }
                  handleFlightClassChange={(value: string) =>
                    handleDropdownChanges("carryOnWeight", value)
                  }
                  disabled={flightClass.carryOnAllowed === 0}
                />
              }
            />
          </div>

          {/* ROW 3 */}
          <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
            {/* Checked Allowed */}
            <InputField
              label={`Checked Allowed ${classIdx !== 0 ? classIdx + 1 : ""}`}
              tooltip="Enter the number of checked bags included per passenger without extra fees"
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.checkedAllowed`]
              }
              input={
                <AddTicketDropdown
                  options={checkedAllowedOptions}
                  placeholder="Enter checked bag allowance"
                  value={flightClass.checkedAllowed || undefined}
                  handleFlightClassChange={(value: string) =>
                    handleDropdownChanges("checkedAllowed", value)
                  }
                />
              }
            />

            {/* Checked Weight */}
            <InputField
              label={`Checked Weight (kg)${classIdx !== 0 ? classIdx + 1 : ""}`}
              tooltip="Enter the maximum weight allowed for each checked bag (in kg)"
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.checkedWeight`]
              }
              input={
                <AddTicketDropdown
                  options={
                    flightClass.checkedAllowed === 0
                      ? [{ id: 0, value: "0" }]
                      : checkedWeightOptions
                  }
                  placeholder="Max checked bag weight"
                  value={
                    flightClass.checkedAllowed === 0
                      ? 0
                      : flightClass.checkedWeight || undefined
                  }
                  handleFlightClassChange={(value: string) =>
                    handleDropdownChanges("checkedWeight", value)
                  }
                  disabled={flightClass.checkedAllowed === 0}
                />
              }
            />
          </div>

          {/* ROW 4 */}
          <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
            {/* checkedFee */}
            <InputField
              id={`checkedFee-${classIdx}`}
              label={`Checked Fee (1kg/JOD) ${
                classIdx !== 0 ? classIdx + 1 : ""
              }`}
              type="number"
              min={0}
              placeholder="Enter first checked bag fee"
              tooltip="Enter the fee for the first checked bag in JOD. If not applicable, enter 0."
              defaultValue={0}
              value={flightClass.checkedFee ?? ""}
              onChange={handleFlightClassesChange}
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.checkedFee`]
              }
            />

            {/* additionalFee */}
            <InputField
              id={`additionalFee-${classIdx}`}
              label={`Additional Checked Fee (1kg/JOD) ${
                classIdx !== 0 ? classIdx + 1 : ""
              }`}
              type="number"
              min={0}
              placeholder="Enter additional checked bag fee"
              tooltip="Enter the fee for each additional checked bag beyond the included allowance. If not applicable, enter 0."
              defaultValue={0}
              value={flightClass.additionalFee ?? ""}
              onChange={handleFlightClassesChange}
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.additionalFee`]
              }
            />
          </div>
        </div>
      </Card>

      {/* Price */}
      <Card
        icon={<CreditCard color="#EE4544" />}
        title={`Price ${classIdx !== 0 ? classIdx + 1 : ""}`}
      >
        <div className="space-y-4 my-4">
          {/* row 1 */}
          <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
            {/* adult */}
            <InputField
              id={`adult-${classIdx}`}
              label={`Adult (JOD) ${classIdx !== 0 ? classIdx + 1 : ""}`}
              type="number"
              min={0}
              max={50000}
              placeholder="Enter adult ticket price"
              tooltip="Enter the ticket price for adult passengers in JOD"
              value={flightClass.price.adult ?? ""}
              onChange={handlePriceChange}
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.price.adult`]
              }
            />

            {/* child */}
            <InputField
              id={`child-${classIdx}`}
              label={`Child (JOD) ${classIdx !== 0 ? classIdx + 1 : ""}`}
              type="number"
              min={0}
              max={50000}
              placeholder="Enter child ticket price"
              tooltip="Enter the ticket price for child passengers (2-12 years old) in JOD"
              value={flightClass.price.child ?? ""}
              onChange={handlePriceChange}
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.price.child`]
              }
            />
          </div>

          {/* row 2 */}
          <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
            {/* infant */}
            <InputField
              id={`infant-${classIdx}`}
              label={`Infant (JOD) ${classIdx !== 0 ? classIdx + 1 : ""}`}
              type="number"
              min={0}
              max={50000}
              placeholder="Enter infant ticket price"
              tooltip="Enter the ticket price for infant passengers (0-2 years old) in JOD"
              value={flightClass.price.infant ?? ""}
              onChange={handlePriceChange}
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.price.infant`]
              }
            />
            {/* tax */}
            <InputField
              id={`tax-${classIdx}`}
              label={`tax (JOD) ${classIdx !== 0 ? classIdx + 1 : ""}`}
              type="number"
              min={0}
              max={100}
              placeholder="Enter tax percentage"
              tooltip="Enter the tax percentage to apply to the ticket price (0-100%)"
              value={flightClass.price.tax ?? ""}
              onChange={handlePriceChange}
              validationError={
                validationError &&
                validationError[`flightClasses.${classIdx}.price.tax`]
              }
            />
          </div>
        </div>
      </Card>

      {/* Extra Offers Card */}
      <Card
        icon={<Gift color="#EE4544" />}
        title={`Extra Offers ${classIdx !== 0 ? classIdx + 1 : ""}`}
      >
        {flightClass.extraOffers.length === 0 ? (
          <EmptyState
            icon={<Gift size={48} color="#EE4544" />}
            title="No Extra Offers Yet"
            description="Click the button below to add special offers or upgrades for this flight."
          />
        ) : (
          <div className="space-y-4">
            {flightClass.extraOffers.map(
              (offer: CreateExtraOffersTypes, offerIdx: number) => (
                <div
                  key={offerIdx + offer.name}
                  className="flex justify-between items-center"
                >
                  <ExtraOffersSection
                    classIdx={classIdx}
                    offerIdx={offerIdx}
                    validationError={validationError}
                  />
                  <button
                    type="button"
                    onClick={() =>
                      dispatch(removeExtraOffers({ classIdx, offerIdx }))
                    }
                    className="mt-6 p-2 text-red-500 hover:text-red-600 transition-colors duration-300"
                    title="Delete offer"
                  >
                    <Trash2 size={20} />
                  </button>
                </div>
              )
            )}
          </div>
        )}

        <button
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center mt-6 shadow-md hover:shadow-lg transition-all duration-300 "
          type="button"
          onClick={() => dispatch(addExtraOffers({ classIdx }))}
        >
          <Plus size={20} className="mr-2" />
          Add Extra Offer
        </button>
      </Card>
    </div>
  );
};

export default FlightClassesSection;
