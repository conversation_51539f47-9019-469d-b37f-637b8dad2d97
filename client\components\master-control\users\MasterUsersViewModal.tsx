"use client";
import React, { useEffect, useState } from "react";
import {
  X,
  UserCircle,
  Building,
  ChevronDown,
  Edit2,
  AlertOctagon,
  Trash2,
  Eye,
  EyeOff,
  Ban,
  EyeIcon,
  ArrowUp,
  RotateCw,
} from "lucide-react";
import axios from "axios";
import { MasterUserResultType } from "@/utils/definitions/masterDefinitions";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import Link from "next/link";
import { CheckCircle } from "lucide-react";
import {
  fetchUserRequestForMaster,
  softDeleteUser,
  fetchUpdateUserProfileForMaster,
} from "@/lib/data/masterUsersData";
import { setLoading } from "@/redux/features/LoadingSlice";
import { AccountStatusEnum } from "@/utils/definitions/masterDefinitions";
import MasterUsersDeleteConfirmModal from "./MasterUsersDeleteConfirmModal";
import InputField from "@/components/account-hub/team-management/InputField";

export const ViewTeamMemberModal = ({
  isOpen,
  setIsOpen,
  user,
  onSuccess,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  user: MasterUserResultType | null;
  onSuccess: () => void;
}) => {
  // States
  const [isEditing, setIsEditing] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSuspendConfirm, setShowSuspendConfirm] = useState(false);
  const [loading, setLocalLoading] = useState(false);
  const [error, setError] = useState("");
  const [formErrors, setFormErrors] = useState<{
    [key: string]:
      | string
      | { city?: string; country?: string; street?: string };
  }>({});

  const dispatch = useAppDispatch();

  // Form data state
  const [formData, setFormData] = useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    username: user?.username || "",
    email: user?.email || "",
    phoneNumber: user?.phoneNumber || "",
    role: user?.role || "",
    agencyName: user?.role === "agency" ? user?.agencyName || "" : "",
    nationality: user?.nationality || "",
    dateOfBirth: user?.dateOfBirth || "",
    gender: user?.gender || "",
    logo: user?.logo || "",
    iataNo: user?.iataNo || "",
    commercialOperationNo: user?.commercialOperationNo || "",
    website: user?.website || "",
    address: {
      country: user?.address?.country || "",
      city: user?.address?.city || "",
      street: user?.address?.street || "",
    },
  });

  // Helper function to get original email from potentially modified email
  const getOriginalEmail = (email: string) => {
    return email.split("_")[0]; // Get the part before the timestamp
  };

  // Helper function to check if email has timestamp
  const hasTimestamp = (email: string) => {
    return email.includes("_");
  };

  // Update form data when user prop changes
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        username: user.username || "",
        email: user.email || "",
        phoneNumber: user.phoneNumber || "",
        role: user.role || "",
        agencyName: user.role === "agency" ? user.agencyName || "" : "",
        nationality: user.nationality || "",
        dateOfBirth: user.dateOfBirth || "",
        gender: user.gender || "",
        logo: user.logo || "",
        iataNo: user.iataNo || "",
        commercialOperationNo: user.commercialOperationNo || "",
        website: user.website || "",
        address: {
          ...user.address,
          country: user.address?.country || "",
          city: user.address?.city || "",
          street: user.address?.street || "",
        },
      });
    }
  }, [user]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    if (name.startsWith("address.")) {
      const addressField = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  const handleSaveClick = async () => {
    try {
      if (!user) return;

      // Validate required fields based on role
      if (formData.role === "agency") {
        if (!formData.agencyName) {
          dispatch(
            setMsg({ message: "Agency name is required", success: false })
          );
          return;
        }
      }

      dispatch(setLoading(true));

      // Remove timestamp from email if it exists
      const originalEmail = user.email.includes("_")
        ? user.email.split("_")[0]
        : user.email;

      // Create base update data with only the fields we want to update
      const baseUpdateData: any = {
        id: user.id,
        firstName: formData.firstName,
        lastName: formData.lastName,
        username: user.username,
        email: formData.email,
        hashedPassword: user.hashedPassword,
        phoneNumber: formData.phoneNumber,
        phoneNumberVerified: user.phoneNumberVerified,
        nationality: formData.nationality || "",
        dateOfBirth: formData.dateOfBirth || "",
        gender: formData.gender || "",
        role: formData.role,
        accountStatus: user.accountStatus,
        verified: user.verified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        address: {
          ...user.address,
          country: formData.address.country,
          city: formData.address.city,
          street: formData.address.street || "",
        },
      };

      // Add agency-specific fields only if role is agency
      const updateData =
        formData.role === "agency"
          ? {
              ...baseUpdateData,
              agencyName: formData.agencyName,
              logo: formData.logo || null,
              website: formData.website || null,
              iataNo: formData.iataNo || null,
              commercialOperationNo: formData.commercialOperationNo || null,
            }
          : {
              ...baseUpdateData,
              agencyName: null,
              logo: null,
              website: null,
              iataNo: null,
              commercialOperationNo: null,
            };

      const response = await fetchUpdateUserProfileForMaster(
        user.id,
        updateData as MasterUserResultType
      );

      if (response.success) {
        // Update the user object with the new data
        Object.assign(user, updateData);

        dispatch(
          setMsg({
            message:
              response.message || "User information updated successfully",
            success: true,
          })
        );
        onSuccess();
        setIsEditing(false);
        setIsOpen(false);
      } else {
        if (response.validationError) {
          const validationErrors = response.validationError;
          console.log("Validation errors:", validationErrors);
          const errorMessage = Object.values(validationErrors).join("\n");
          console.log("Error message:", errorMessage);
          dispatch(setMsg({ message: errorMessage, success: false }));
          setFormErrors(validationErrors);
          console.log("Form errors:", formErrors);
        } else {
          dispatch(
            setMsg({
              message: response.message || "Failed to update user information",
              success: false,
            })
          );
        }
      }
    } catch (error: any) {
      console.error("Error updating user:", error);
      if (error.response?.data?.validationError) {
        const validationErrors = error.response.data.validationError;
        const errorMessage = Object.values(validationErrors).join("\n");
        dispatch(setMsg({ message: errorMessage, success: false }));
      } else {
        dispatch(
          setMsg({
            message:
              error.response?.data?.message ||
              "Failed to update user information",
            success: false,
          })
        );
      }
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!user) return;

      dispatch(setLoading(true));
      const response = (await softDeleteUser(user.id)) as {
        success: boolean;
        message: string;
        results?: {
          accountStatus: string;
          email: string;
        };
      };

      if (!response.success) {
        dispatch(
          setMsg({
            message: response.message || "Failed to delete user",
            success: false,
          })
        );
        return;
      }

      // Update user data with the response from the API
      if (response.results) {
        Object.assign(user, {
          accountStatus: response.results.accountStatus as AccountStatusEnum,
          email: response.results.email,
        });
      }

      // Show success message and close all modals
      dispatch(
        setMsg({
          message: response.message || "User deleted successfully",
          success: true,
        })
      );
      onSuccess();
      setShowDeleteConfirm(false);
      setIsOpen(false); // Close the main modal as well
    } catch (error) {
      console.error("Error deleting user:", error);
      dispatch(
        setMsg({
          message: "An error occurred while deleting the user",
          success: false,
        })
      );
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleSuspendConfirm = async () => {
    try {
      if (!user) return;

      dispatch(setLoading(true));

      // If not soft deleted, toggle between accepted and suspended
      const response = await fetchUserRequestForMaster(user.id, {
        accountStatus:
          user.accountStatus === AccountStatusEnum.accepted
            ? AccountStatusEnum.suspended
            : AccountStatusEnum.accepted,
      });

      if (response.success) {
        Object.assign(user, response.results);
        dispatch(
          setMsg({
            message: response.message || "User status updated successfully",
            success: true,
          })
        );
        onSuccess();
        setShowSuspendConfirm(false);
        setIsOpen(false);
      } else {
        dispatch(
          setMsg({
            message: response.message || "Failed to update user status",
            success: false,
          })
        );
      }
    } catch (error) {
      console.error("Error updating user status:", error);
      dispatch(
        setMsg({
          message: "An error occurred while updating user status",
          success: false,
        })
      );
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleSuspendClick = () => {
    setShowSuspendConfirm(true);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    await handleSaveClick();
  };

  if (!isOpen || !user) return null;

  return (
    <>
      {/* Main Modal */}
      <div className="fixed inset-0 bg-gray-500/50 dark:bg-black/50 flex items-center justify-center p-4 overflow-y-auto custom-scrollbar">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg max-w-md w-full">
          <form onSubmit={handleSubmit} className="p-6">
            <section>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-800 dark:text-white capitalize">
                  {isEditing
                    ? `Edit ${user.role} User`
                    : `View ${user.role} User`}
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-lg w-8 h-8 flex items-center justify-center transition-colors duration-200"
                >
                  <X size={20} />
                </button>
              </div>
              {Object.keys(formErrors).length > 0 && (
                <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/50 rounded-lg">
                  <p className="text-red-600 dark:text-red-400 text-sm flex items-center">
                    {Object.values(formErrors).map((error, index) =>
                      typeof error === "string" ? (
                        <span key={index}>{error}</span>
                      ) : null
                    )}
                  </p>
                </div>
              )}
              {/* {successMessage && (
                <div className="mb-4 p-3 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-lg">
                  {successMessage}
                </div>
              )} */}
            </section>
            <div className="space-y-4">
              {/* User Information */}
              <section className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-6 space-y-2">
                <div className="flex items-center mb-2">
                  <UserCircle
                    size={20}
                    className="text-green-600 dark:text-green-500 mr-2"
                  />
                  <span className="text-gray-800 dark:text-white font-semibold">
                    {user.role === "agency"
                      ? "Agency Information"
                      : "Affiliate Information"}
                  </span>
                </div>
                {/* User Details */}
                <div className="grid grid-cols-2 gap-4">
                  {/* First Name */}
                  <InputField
                    type="text"
                    name="firstName"
                    label={
                      user.role === "agency"
                        ? "Agency Owner Name"
                        : "First Name"
                    }
                    placeholder="Enter first name"
                    value={formData.firstName || ""}
                    onChange={handleInputChange}
                    required={isEditing}
                    disabled={!isEditing}
                    formErrors={formErrors.firstName as string}
                  />
                  {/* Last Name */}
                  <InputField
                    type="text"
                    name="lastName"
                    label={
                      user.role === "agency"
                        ? "Agency Owner Surname"
                        : "Last Name"
                    }
                    placeholder="Enter last name"
                    value={formData.lastName || ""}
                    onChange={handleInputChange}
                    required={isEditing}
                    disabled={!isEditing}
                    formErrors={formErrors.lastName as string}
                  />
                </div>

                {/* Agency Name or Username */}
                <InputField
                  type="text"
                  name={user.role === "agency" ? "agencyName" : "username"}
                  label={user.role === "agency" ? "Agency Name" : "User Name"}
                  placeholder="Enter agency name"
                  value={
                    (user.role === "agency"
                      ? formData.agencyName
                      : formData.username) || ""
                  }
                  onChange={handleInputChange}
                  required={isEditing}
                  disabled={user.role === "agency" ? true : !isEditing}
                  formErrors={formErrors.agencyName as string}
                />

                {/* Agency ID or Country and Number of Active Agents or City */}
                <div className="grid grid-cols-1 gap-4">
                  {/* Agency ID or Country */}
                  <InputField
                    type="text"
                    name={user.role === "agency" ? "id" : "country"}
                    label={user.role === "agency" ? "Agency ID" : "Country"}
                    placeholder="Enter agency name"
                    value={
                      (user.role === "agency"
                        ? user.id
                        : formData.address.country) || ""
                    }
                    onChange={
                      user.role === "agency"
                        ? (e: React.ChangeEvent<HTMLInputElement>) => {}
                        : handleInputChange
                    }
                    required={user.role === "agency" ? false : isEditing}
                    disabled={user.role === "agency" ? true : !isEditing}
                    formErrors={
                      user.role === "agency"
                        ? (formErrors.id as string)
                        : (formErrors.country as string)
                    }
                  />
                  {/* Number of Active Agents or City */}
                  <InputField
                    type="text"
                    name={
                      user.role === "agency" ? "agents.length" : "address.city"
                    }
                    label={
                      user.role === "agency"
                        ? "Number of Active Agents"
                        : "City"
                    }
                    placeholder="Enter agency name"
                    value={
                      user.role === "agency"
                        ? user.agents.length
                        : formData.address.city
                    }
                    onChange={
                      user.role === "agency"
                        ? (e: React.ChangeEvent<HTMLInputElement>) => {}
                        : handleInputChange
                    }
                    required={user.role === "agency" ? false : isEditing}
                    disabled={user.role === "agency" ? true : !isEditing}
                    formErrors={
                      user.role === "agency"
                        ? (formErrors.agents as string)
                        : typeof formErrors.address === "object"
                        ? (formErrors.address?.city as string)
                        : undefined
                    }
                  />
                </div>
              </section>

              {/* Contact Info */}
              <section className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-6 space-y-2">
                <div className="flex items-center mb-2">
                  <Building
                    size={20}
                    className="text-blue-600 dark:text-blue-500 mr-2"
                  />
                  <span className="text-gray-800 dark:text-white font-semibold">
                    Contact Info
                  </span>
                </div>
                {/* Email */}
                <InputField
                  type={hasTimestamp(formData.email) ? "text" : "email"}
                  name="email"
                  label="Email Address"
                  placeholder="Enter email address"
                  value={formData.email}
                  onChange={handleInputChange}
                  required={isEditing}
                  disabled={!isEditing}
                  formErrors={formErrors.email as string}
                />

                {/* Phone Number */}
                <InputField
                  type="tel"
                  name="phoneNumber"
                  label="Phone Number"
                  placeholder="Enter phone number"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  required={isEditing}
                  disabled={!isEditing}
                  formErrors={formErrors.phoneNumber as string}
                />
              </section>
              {/* Action Buttons */}
              {user && !isEditing ? (
                <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
                  <div className="flex items-center mb-2">
                    <AlertOctagon size={20} className="text-yellow-500 mr-2" />
                    <span className="text-gray-900 dark:text-white font-semibold">
                      Actions
                    </span>
                  </div>
                  <div className="grid gap-4">
                    {/* Row 1: View and Edit (md:grid-cols-2) */}
                    <div
                      className={`grid gap-4 ${
                        user.accountStatus === AccountStatusEnum.pending
                          ? "md:grid-cols-1"
                          : "md:grid-cols-2"
                      }`}
                    >
                      <Link
                        href={`/master-control/users/${user.id}/account`}
                        className="flex items-center justify-center space-x-1 bg-gray-500 hover:bg-gray-600 text-white rounded-lg px-2 py-2 transition-colors duration-200"
                      >
                        <EyeIcon size={16} />
                        <span>View</span>
                      </Link>
                      {user.accountStatus !== AccountStatusEnum.pending && (
                        <button
                          onClick={handleEditClick}
                          className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg px-4 py-2 transition-colors duration-200"
                        >
                          <Edit2 size={16} />
                          <span>Edit</span>
                        </button>
                      )}
                    </div>

                    {/* Row 2: Restore User (md:grid-cols-1) */}
                    {user.accountStatus !== AccountStatusEnum.accepted &&
                      user.accountStatus !== AccountStatusEnum.pending && (
                        <div className="grid md:grid-cols-1">
                          <button
                            type="button"
                            onClick={handleSuspendClick}
                            className="flex flex-1 flex-grow items-center justify-center space-x-1 bg-green-500 hover:bg-green-600 text-white rounded-lg px-2 py-2 transition-colors duration-200"
                          >
                            <CheckCircle size={16} />
                            <span className="font-medium">
                              {user.accountStatus === AccountStatusEnum.disabled
                                ? "Restore User"
                                : "Enable Login"}
                            </span>
                          </button>
                        </div>
                      )}

                    {/* Row 3: Delete User (md:grid-cols-2) */}
                    <div className="grid md:grid-cols-2 gap-4">
                      {user.accountStatus === AccountStatusEnum.accepted && (
                        <>
                          <button
                            type="button"
                            onClick={handleSuspendClick}
                            className="flex flex-1 flex-grow items-center justify-center space-x-1 bg-orange-500 hover:bg-orange-600 text-white rounded-lg px-2 py-2 transition-colors duration-200"
                          >
                            <Ban size={16} />
                            <span>Disable Login</span>
                          </button>
                          <button
                            type="button"
                            onClick={handleDeleteClick}
                            className="flex items-center justify-center space-x-2 bg-red-500 hover:bg-red-600 text-white rounded-lg px-4 py-2 transition-colors duration-200"
                          >
                            <Trash2 size={16} />
                            <span>Delete</span>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}

              <div className="flex justify-end space-x-4 mt-6">
                {user && isEditing ? (
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={() => setIsOpen(false)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"
                  >
                    Close
                  </button>
                )}
                {(!user || isEditing) && (
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading
                      ? "Processing..."
                      : user
                      ? "Save Changes"
                      : "Add Team Member"}
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <MasterUsersDeleteConfirmModal
            showDeleteConfirmation={showDeleteConfirm}
            setShowDeleteConfirmation={setShowDeleteConfirm}
            handleDeleteConfirm={handleDeleteConfirm}
          />
        )}

        {/* Suspend Confirmation Modal */}
        {showSuspendConfirm && (
          <div className="fixed inset-0 bg-gray-500/50 dark:bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg max-w-sm w-full p-6 text-center">
              <div className="mb-2">
                <div
                  className={`w-12 h-12 rounded-full ${
                    user.accountStatus === AccountStatusEnum.accepted
                      ? "bg-orange-100 dark:bg-orange-500"
                      : "bg-green-100 dark:bg-green-500"
                  } mx-auto flex items-center justify-center`}
                >
                  {user.accountStatus === AccountStatusEnum.accepted ? (
                    <Ban
                      size={32}
                      className="text-orange-600 dark:text-white"
                    />
                  ) : (
                    <CheckCircle
                      size={32}
                      className="text-green-600 dark:text-white"
                    />
                  )}
                </div>
              </div>
              <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                {user.accountStatus === AccountStatusEnum.accepted
                  ? "Disable"
                  : "Enable"}{" "}
                Login
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6 text-base">
                Are you sure you want to{" "}
                {user.accountStatus === AccountStatusEnum.accepted
                  ? "disable"
                  : "enable"}{" "}
                this user?
                {user.accountStatus === AccountStatusEnum.accepted
                  ? " They will not be able to access their account."
                  : " This will restore their account access."}
              </p>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setShowSuspendConfirm(false)}
                  className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 focus:outline-none flex items-center justify-center space-x-2"
                >
                  <X size={16} />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={handleSuspendConfirm}
                  className={`px-6 py-2 ${
                    user.accountStatus === AccountStatusEnum.accepted
                      ? "bg-orange-500 hover:bg-orange-600"
                      : "bg-green-500 hover:bg-green-600"
                  } text-white rounded-lg focus:outline-none flex items-center justify-center space-x-2`}
                >
                  {user.accountStatus === AccountStatusEnum.accepted ? (
                    <Ban size={16} />
                  ) : (
                    <CheckCircle size={16} />
                  )}
                  <span>
                    {user.accountStatus === AccountStatusEnum.accepted
                      ? "Disable"
                      : "Enable"}
                  </span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ViewTeamMemberModal;
