import { configureStore, combineReducers } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // defaults to localStorage for web
import { FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';

// Reducers
import AuthSlice from "./features/AuthSlice";
import ActionMsgSlice from "./features/ActionMsgSlice";
import TicketFormSlice from "./features/TicketFormSlice";
import LoadingSlice from "./features/LoadingSlice";
import ticketSearchForm from "./features/TicketSearchSlice";
import selectedTicketSlice from "./features/SelectedTicketSlice";
import singleTicketSlice from "./features/SingleTicketSlice";
import ticketSlice from "./features/TicketSlice";
import bookingConfirmationReducer from "./features/BookingConfirmationSlice";
import fareResultReducer from "./features/FareResultSlice";

// Thunks
import { fetchTickets, clearTickets } from "./features/bookingThunks";

const persistConfig = {
  key: 'root',
  storage,
  // Optionally, whitelist or blacklist reducers
  // whitelist: ['bookingConfirmation'],
};

const rootReducer = combineReducers({
  auth: AuthSlice,
  actionMsg: ActionMsgSlice,
  ticketForm: TicketFormSlice,
  loading: LoadingSlice,
  ticketSearchForm: ticketSearchForm,
  selectedTicket: selectedTicketSlice,
  singleTicket: singleTicketSlice,
  fetchedTicket: ticketSlice,
  bookingConfirmation: bookingConfirmationReducer,
  fareResult: fareResultReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore redux-persist action types
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);

// Infer the type of makeStore
export type AppStore = typeof store;
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];
