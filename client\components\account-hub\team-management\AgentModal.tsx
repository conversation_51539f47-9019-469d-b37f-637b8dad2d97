import {
  X,
  UserCircle,
  Building,
  Edit2,
  Trash2,
  Ban,
  CheckCircle,
  AlertOctagon,
} from "lucide-react";
import {
  Agent,
  AgentRole,
  Department,
  PasswordStrength,
} from "@/utils/definitions/agentsDefinitions";
import InputField from "./InputField";
import PasswordField from "./PasswordField";
import SelectField from "./SelectField";
import ModalBlank from "@/components/common/modal-blank";
import ConfirmationModal from "./ConfirmationModal";
import { useState } from "react";
import RoleDropdown from "@/components/master-control/team-management/RoleDropdown";
import DepartmentDropdown from "@/components/master-control/team-management/DepartmentDropdown";

type ModalMode = "view" | "edit" | "add";

interface AgentModalProps {
  mode: ModalMode;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  setIsEditModalOpen: (open: boolean) => void;
  setIsViewModalOpen: (open: boolean) => void;
  formData: Agent;
  formErrors: Record<string, string>;
  passwordStrength: PasswordStrength | undefined;
  confirmPasswordStrength: PasswordStrength | undefined;
  setPasswordStrength: React.Dispatch<
    React.SetStateAction<PasswordStrength | undefined>
  >;
  setConfirmPasswordStrength: React.Dispatch<
    React.SetStateAction<PasswordStrength | undefined>
  >;
  showPassword: boolean;
  showConfirmPassword: boolean;
  isLoading: boolean;
  error?: string;
  successMessage?: string | null;
  onInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => void;
  onPasswordChange: (
    field: "password" | "confirmPassword",
    setStrength: React.Dispatch<
      React.SetStateAction<PasswordStrength | undefined>
    >
  ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
  onTogglePassword: (field: "password") => void;
  onToggleConfirmPassword: (field: "confirmPassword") => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void | Promise<void>;
  onAction?: (action: string, formData: any) => void;
  setOnAction?: (
    onAction: ((action: string, formData: any) => void) | undefined
  ) => void;
  selectedMember?: any;
  handleConfirmAction?: () => void;
}

const AgentModal = ({
  mode,
  isOpen,
  setIsOpen,
  setIsEditModalOpen,
  setIsViewModalOpen,
  formData,
  formErrors,
  passwordStrength,
  confirmPasswordStrength,
  setPasswordStrength,
  setConfirmPasswordStrength,
  showPassword,
  showConfirmPassword,
  isLoading,
  error,
  successMessage,
  onInputChange,
  onPasswordChange,
  onTogglePassword,
  onToggleConfirmPassword,
  onSubmit,
  onAction,
  setOnAction,
  selectedMember,
  handleConfirmAction,
}: AgentModalProps) => {
  const isEdit = mode === "edit";
  const isAdd = mode === "add";
  const isView = mode === "view";
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSuspendConfirm, setShowSuspendConfirm] = useState(false);

  const renderHeader = () => (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {isAdd && "Add New Employee"}
          {isEdit && "Edit Employee"}
          {isView && "View Employee"}
        </h2>
        <button
          type="button"
          onClick={() => setIsOpen(false)}
          className="bg-red-500 hover:bg-red-600 text-white rounded-lg w-8 h-8 flex items-center justify-center transition-colors duration-200"
        >
          <X size={20} />
        </button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/50 rounded-lg">
          <p className="text-red-600 dark:text-red-400 text-sm flex items-center">
            {error}
          </p>
        </div>
      )}
      {successMessage && (
        <div className="mb-4 p-3 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-lg">
          {successMessage}
        </div>
      )}
    </>
  );

  const renderAgentInfo = () => (
    <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
      <div className="flex items-center mb-2">
        <UserCircle size={20} className="text-green-500 mr-2" />
        <span className="text-gray-900 dark:text-white font-semibold">
          Employee Information
        </span>
      </div>
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <InputField
            type="text"
            name="firstName"
            label="First Name"
            placeholder="Enter first name"
            value={formData.firstName || ""}
            onChange={onInputChange}
            required={isAdd || isEdit}
            disabled={isView}
            formErrors={formErrors.firstName}
          />
          <InputField
            type="text"
            name="lastName"
            label="Last Name"
            placeholder="Enter last name"
            value={formData.lastName || ""}
            onChange={onInputChange}
            required={isAdd || isEdit}
            disabled={isView}
            formErrors={formErrors.lastName}
          />
        </div>
        <InputField
          type="email"
          name="email"
          label="Email"
          placeholder="Enter email address"
          value={formData.email || ""}
          onChange={onInputChange}
          required={isAdd || isEdit}
          disabled={isView}
          formErrors={formErrors.email}
        />
        {(isAdd || isEdit) && (
          <>
            <PasswordField
              name="password"
              label="Password"
              placeholder="Enter password"
              value={formData.password ?? ""}
              showPassword={showPassword}
              onChange={onPasswordChange("password", setPasswordStrength)}
              onToggleVisibility={() => onTogglePassword("password")}
              strength={passwordStrength}
              required={isAdd}
              formErrors={formErrors.password}
            />
            <PasswordField
              name="confirmPassword"
              label="Confirm Password"
              placeholder="Confirm password"
              value={formData.confirmPassword ?? ""}
              showPassword={showConfirmPassword}
              onChange={onPasswordChange(
                "confirmPassword",
                setConfirmPasswordStrength
              )}
              onToggleVisibility={() =>
                onToggleConfirmPassword("confirmPassword")
              }
              strength={confirmPasswordStrength}
              required={isAdd}
              formErrors={formErrors.confirmPassword}
            />
          </>
        )}
      </div>
    </div>
  );

  const renderPositionInfo = () => (
    <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
      <div className="flex items-center mb-2">
        <Building size={20} className="text-blue-500 mr-2" />
        <span className="text-gray-900 dark:text-white font-semibold">
          Position Information
        </span>
      </div>
      <div className="space-y-4">
        <RoleDropdown
          name="subRole"
          value={formData.subRole}
          onChange={onInputChange}
          disabled={isView}
          options={Object.values(AgentRole)}
          required
          formErrors={formErrors.subRole}
        />
        <DepartmentDropdown
          name="department"
          value={formData.department}
          onChange={onInputChange}
          disabled={isView}
          options={Object.values(Department)}
          required
          formErrors={formErrors.department}
        />
      </div>
    </div>
  );

  const renderActions = () => {
    if (isView && formData) {
      return (
        <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
          <div className="flex items-center mb-2">
            <AlertOctagon size={20} className="text-yellow-500 mr-2" />
            <span className="text-gray-900 dark:text-white font-semibold">
              Actions
            </span>
          </div>
          <div className="grid gap-4">
            <div className="grid md:grid-cols-2 gap-4">
              <button
                type="button"
                onClick={() => {
                  setIsOpen(false);
                  setIsEditModalOpen(true);
                  setIsViewModalOpen(false);
                  onAction?.("edit", formData);
                }}
                className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg px-4 py-2 transition-colors duration-200"
              >
                <Edit2 size={20} />
                <span>Edit</span>
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowDeleteConfirm(true);
                  if (onAction) {
                    onAction("delete", formData);
                  }
                }}
                className="flex items-center justify-center space-x-2 bg-red-500 hover:bg-red-600 text-white rounded-lg px-4 py-2 transition-colors duration-200"
              >
                <Trash2 size={16} />
                <span>Delete</span>
              </button>
            </div>
            <button
              type="button"
              onClick={() => {
                setShowSuspendConfirm(true);
                if (onAction) {
                  onAction("suspend", formData);
                }
              }}
              className={`flex items-center justify-center space-x-2 ${
                formData.status === "active"
                  ? "bg-orange-500 hover:bg-orange-600"
                  : "bg-green-500 hover:bg-green-600"
              } text-white rounded-lg px-4 py-2 transition-colors duration-200`}
            >
              {formData.status === "active" ? (
                <Ban size={16} />
              ) : (
                <CheckCircle size={16} />
              )}
              <span>
                {formData.status === "active"
                  ? "Disable Login"
                  : "Enable Login"}
              </span>
            </button>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderFooter = () => {
    if (isView) {
      return (
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      );
    }
    return (
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={() => {
            if (isEdit) {
              setIsEditModalOpen(false);
              setIsViewModalOpen(true);
            } else {
              setIsOpen(false);
            }
          }}
          className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50"
        >
          {isLoading ? "Processing..." : isAdd ? "Add Employee" : "Save Changes"}
        </button>
      </div>
    );
  };

  return (
    <ModalBlank isOpen={isOpen} setIsOpen={setIsOpen}>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[50] overflow-y-auto custom-scrollbar">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg max-w-md w-full">
          <form onSubmit={onSubmit} className="p-6">
            {renderHeader()}
            {renderAgentInfo()}
            {renderPositionInfo()}
            {renderActions()}
            {renderFooter()}
          </form>
        </div>
      </div>
      {/* // Confirmation Modals */}
      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        actionType="delete"
        onConfirm={() => {
          // First notify parent of the action to ensure confirmAction state is set
          if (onAction) {
            onAction("delete", formData);
          }

          // Then execute the confirmation action
          if (handleConfirmAction) {
            handleConfirmAction();
          }

          // Close modals
          setShowDeleteConfirm(false);
          setIsOpen(false);
        }}
        onCancel={() => setShowDeleteConfirm(false)}
        title="Delete Employee"
        description="Are you sure you want to delete this employee? This action cannot be undone."
      />
      {/* Suspend Confirmation Modal */}
      <ConfirmationModal
        isOpen={showSuspendConfirm}
        actionType="status-toggle"
        currentStatus={formData.status}
        onConfirm={() => {
          // First notify parent of the action to ensure confirmAction state is set
          if (onAction) {
            onAction("suspend", formData);
          }

          // Then execute the confirmation action
          if (handleConfirmAction) {
            handleConfirmAction();
          }

          // Close modals
          setShowSuspendConfirm(false);
          setIsOpen(false);
        }}
        onCancel={() => setShowSuspendConfirm(false)}
        title={`${
          formData.status === "active" ? "Disable" : "Enable"
        } Employee`}
        description={
          formData.status === "active"
            ? "Are you sure you want to disable this employee? They will not be able to access their account."
            : "Are you sure you want to enable this employee? This will restore their account access."
        }
      />
    </ModalBlank>
  );
};

export default AgentModal;
