import { setTickets } from "@/redux/features/TicketSlice";
import { RootState } from "@/redux/store";
import {
  FlightTicketRes,
  initialSearchState,
  SearchState,
} from "@/utils/definitions/blockSeatsDefinitions";
import { apiService } from "@/utils/functions/flightSearchTickets";
import { paramsToSearchState } from "@/utils/functions/flightSearchTickets";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import { useDispatch, useSelector } from "react-redux";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";

export const useSearchTickets = () => {
  // Selectors to get the necessary ticket data from the Redux store
  const {
    priceRange,
    departureTickets,
    returnTickets,
    filteredDepartureTickets,
    filteredReturnTickets,
  } = useSelector((state: RootState) => state.fetchedTicket);

  // Check if no tickets were found after filtering
  const noTicketsFound =
    filteredDepartureTickets.length === 0 && filteredReturnTickets.length === 0;

  const searchParams = useSearchParams(); // Hook to access the search parameters from the URL
  const dispatch = useDispatch();

  const prevSearchStateRef = useRef<SearchState>(initialSearchState); // Reference to hold the previous search state

  // ######### useState #########
  const [searchState, setSearchState] =
    useState<SearchState>(initialSearchState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const [selectedDeparture, setSelectedDeparture] = useState<string | null>(
    null
  );
  const [selectedReturn, setSelectedReturn] = useState<string | null>(null);

  // Track the type of the first selected ticket ("internal" or "third-party")
  const [selectedTicketType, setSelectedTicketType] = useState<
    "internal" | "third-party" | null
  >(null);
  // Feedback message for mismatched selection
  const [typeMismatchMessage, setTypeMismatchMessage] = useState<string | null>(
    null
  );

  // Helper to infer ticket type
  const getTicketType = (ticket: FlightTicketRes) => {
    if (!userId || !ticket?.owner) return "third-party";

    // A ticket is "internal" if:
    // 1. It's owned by the user directly (for individual users), OR
    // 2. It's owned by the user's agency (for agency users like agents/team members)
    const isInternal =
      ticket.ownerId === userId ||
      (ticket.owner?.agencyName &&
        "agencyName" in user &&
        user?.agencyName &&
        ticket.owner.agencyName.toLowerCase() ===
          user.agencyName.toLowerCase()) ||
      (userAgencyId && ticket.ownerId === userAgencyId) ||
      (userRoleType &&
        [
          "agency_admin",
          "agency_sales",
          "agency_accountant",
          "agency_operation",
          "agency_member",
          "agency_owner",
          "team_member",
        ].includes(userRoleType) &&
        (ticket.ownerId === userAgencyId ||
          (ticket.owner?.agencyName &&
            "agencyName" in user &&
            user?.agencyName &&
            ticket.owner.agencyName.toLowerCase() ===
              user.agencyName.toLowerCase())));

    return isInternal ? "internal" : "third-party";
  };

  // Get logged-in user info
  const user = useAppSelector(selectUser);
  const userId =
    typeof user === "object" && user && "id" in user ? user.id : undefined;
  const userAgencyId =
    typeof user === "object" && user && "agencyId" in user
      ? (user.agencyId as string)
      : undefined;
  const userRoleType =
    typeof user === "object" && user && "roleType" in user
      ? (user.roleType as string)
      : undefined;

  // Combine selected departure and return ticket IDs
  const combinedId = `${selectedDeparture}_${selectedReturn}`;

  // Handle the completion of the search and dispatch the results to Redux store
  const handleSearchComplete = useCallback(
    (tickets: {
      departureTicket: FlightTicketRes[];
      returnTicket: FlightTicketRes[];
    }) => {
      dispatch(
        setTickets({
          departureTickets: tickets.departureTicket,
          returnTickets: tickets.returnTicket,
          filteredDepartureTickets: tickets.departureTicket,
          filteredReturnTickets: tickets.returnTicket,
        })
      );
    },
    [dispatch]
  );

  // Fetch data based on the current search state
  const fetchData = useCallback(async (state: SearchState) => {
    if (!state.departure?.airportCode || !state.arrival?.airportCode) return;

    setLoading(true);
    setError(null);

    try {
      const results = await apiService.fetchRoundTripTickets(state);
      handleSearchComplete(results);
    } catch (error) {
      setError("An error occurred while fetching tickets");
    } finally {
      setLoading(false);
    }
  }, []);

  // Effect to monitor changes in the search parameters and trigger a new search if necessary
  // Debounced fetchData to avoid excessive API calls
  const debouncedFetchData = useDebouncedCallback(fetchData, 500);

  useEffect(() => {
    const newSearchState = paramsToSearchState(searchParams);

    if (
      JSON.stringify(newSearchState) !==
      JSON.stringify(prevSearchStateRef.current)
    ) {
      setSearchState(newSearchState);
      prevSearchStateRef.current = newSearchState;
      debouncedFetchData(newSearchState);
    }
  }, [searchParams, debouncedFetchData]);

  // Toggle the visibility of the sidebar
  const toggleSidebar = useCallback(
    () => setIsSidebarOpen((prev) => !prev),
    []
  );

  // Handle the selection of a ticket
  const handleSelectTicket = useCallback(
    (ticket: FlightTicketRes, isReturnFlight: boolean) => {
      const ticketType = getTicketType(ticket);
      console.log("Ticket type:", ticketType);

      const isDeselecting = isReturnFlight
        ? selectedReturn === ticket.id
        : selectedDeparture === ticket.id;

      // If the action is to deselect a ticket, handle it and exit.
      if (isDeselecting) {
        if (isReturnFlight) {
          setSelectedReturn(null);
          // If the departure ticket is also not selected, clear the ticket type
          if (!selectedDeparture) {
            setSelectedTicketType(null);
          }
        } else {
          setSelectedDeparture(null);
          // If the return ticket is also not selected, clear the ticket type
          if (!selectedReturn) {
            setSelectedTicketType(null);
          }
        }
        setTypeMismatchMessage(null); // Clear any existing mismatch message
        return;
      }

      // If we are here, it's a new selection action.
      // Check for type mismatch if a ticket type is already established.
      if (selectedTicketType && ticketType !== selectedTicketType) {
        setTypeMismatchMessage(
          `You cannot mix Internal and Third-Party tickets for a round trip. Please select a ${selectedTicketType.replace(
            "-",
            " "
          )} ticket.`
        );
        return;
      }

      // Perform the selection
      if (isReturnFlight) {
        setSelectedReturn(ticket.id);
      } else {
        setSelectedDeparture(ticket.id);
      }

      // If this is the first ticket being selected, establish the ticket type.
      if (!selectedTicketType) {
        setSelectedTicketType(ticketType);
      }

      setTypeMismatchMessage(null);
    },

    [selectedTicketType, selectedDeparture, selectedReturn, userId, user]
  );

  // Determine the state of a ticket (selected or not)
  const getTicketState = useCallback(
    (ticketId: string, isReturnFlight: boolean): "select" | "selected" =>
      (isReturnFlight ? selectedReturn : selectedDeparture) === ticketId
        ? "selected"
        : "select",
    [selectedDeparture, selectedReturn]
  );

  return {
    loading,
    setLoading,
    error,
    setError,
    fetchData,
    state: searchState,
    setState: setSearchState,
    isSidebarOpen,
    toggleSidebar,
    handleSelectTicket,
    getTicketState,
    combinedId,
    handleFindTicketsClick: () => debouncedFetchData(searchState),
    departureTickets,
    returnTickets,
    filteredDepartureTickets,
    filteredReturnTickets,
    selectedDeparture,
    selectedReturn,
    selectedTicketType,
    setSelectedTicketType,
    typeMismatchMessage,
    setTypeMismatchMessage,
    priceRange,
    noTicketsFound,
  };
};
