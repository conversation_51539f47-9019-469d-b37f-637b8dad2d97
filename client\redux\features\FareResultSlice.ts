import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface FareResultState {
  fareResult: any;
}

const initialState: FareResultState = {
  fareResult: null,
};

const fareResultSlice = createSlice({
  name: 'fareResult',
  initialState,
  reducers: {
    setFareResult(state, action: PayloadAction<any>) {
      state.fareResult = action.payload;
    },
    clearFareResult(state) {
      state.fareResult = null;
    },
  },
});

export const { setFareResult, clearFareResult } = fareResultSlice.actions;
export default fareResultSlice.reducer;
