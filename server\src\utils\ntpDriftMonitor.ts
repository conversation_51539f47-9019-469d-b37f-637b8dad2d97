/**
 * @module ntpDriftMonitor
 * Periodically checks system clock drift against an NTP server and logs/alerts if drift exceeds a threshold.
 * Uses UTC for all calculations.
 */
import logger from "./logger";
import ntpClient from "ntp-client";

const NTP_SERVER = process.env.NTP_SERVER || "pool.ntp.org";
const NTP_PORT = Number(process.env.NTP_PORT) || 123;
const DRIFT_THRESHOLD_MS = Number(process.env.NTP_DRIFT_THRESHOLD_MS) || 1000; // 1 second
const CHECK_INTERVAL_MS = Number(process.env.NTP_CHECK_INTERVAL_MS) || 10 * 60 * 1000; // 10 minutes

/**
 * Checks system time drift against the NTP server and logs/alerts if drift exceeds threshold.
 */
export function startNtpDriftMonitor() {
  setInterval(() => {
    ntpClient.getNetworkTime(NTP_SERVER, NTP_PORT, (err, date) => {
      const now = Date.now();
      if (err) {
        logger.error("NTP drift check failed", { error: err });
        return;
      }
      const ntpTime = date.getTime();
      const drift = Math.abs(now - ntpTime);
      logger.info("NTP drift check", {
        systemTimeUTC: new Date(now).toISOString(),
        ntpTimeUTC: new Date(ntpTime).toISOString(),
        driftMs: drift
      });
      if (drift > DRIFT_THRESHOLD_MS) {
        logger.log({
          level: 'crit',
          message: `CRITICAL: System clock drift exceeds threshold!`,
          driftMs: drift,
          systemTimeUTC: new Date(now).toISOString(),
          ntpTimeUTC: new Date(ntpTime).toISOString(),
        });
        // TODO: Notify admins (email, Slack, etc.)
        // Example: notifyAdmins(`System clock drift is ${drift}ms! Check NTP sync.`);
      }
    });
  }, CHECK_INTERVAL_MS);
}
