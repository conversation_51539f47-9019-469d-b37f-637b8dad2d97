"use client";
import { fetchLogin } from "@/lib/data/authData";
import { loginUser, selectIsLoggedIn } from "@/redux/features/AuthSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { AuthResTypes, LoginBody } from "@/utils/definitions/authDefinitions";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import AuthImage from "@/app/(auth)/auth-image";
import ProgressLoading from "../../utils/ProgressLoading";
import clerk from "@/public/images/logo/secured-by-clerk.svg";
import { ArrowRight, Mail, Shield } from "lucide-react";
import Image from "next/image";
import PasswordField from "@/components/common/PasswordField";
import { PasswordStrength } from "@/utils/definitions/agentsDefinitions";
import useDarkMode from "@/components/hooks/useDarkMode";
import { checkPasswordStrength } from "@/utils/passwordStrength";

const generateInputClassName = (hasError: boolean) => {
  const baseClasses =
    "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-1.5 md:py-2.5 w-full outline-none transition-all duration-300 border-0 focus:ring-2 focus:ring-red-500 focus:border-red-500 placeholder:text-xs md:placeholder:text-sm";
  const errorClasses = hasError ? "border-red-500" : "";
  return `${baseClasses} ${errorClasses}`;
};

const SigninFormInput = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  // default value
  const formData = {
    email: "",
    password: "",
  };

  // form input data
  const [form, setForm] = useState<LoginBody>({ email: "", password: "" });
  const [banner2WarningOpen, setBanner2WarningOpen] = useState<boolean>(true);
  const [loginInfo, setLoginInfo] = useState<AuthResTypes | null>(null);
  const [validationError, setValidationError] = useState<any>(null);
  const [authError, setAuthError] = useState<string | null>(null);
  const [loadingBtn, setLoadingBtn] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState(false);

  const darkMode = useDarkMode();
  const isDarkMode = typeof darkMode === "boolean" ? darkMode : false;

  const getInitialPasswordStrength = (): PasswordStrength => ({
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  });
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>(
    getInitialPasswordStrength()
  );
  const [debouncedPassword, setDebouncedPassword] = useState<string>("");

  // Debounce password strength check
  useEffect(() => {
    const handler = setTimeout(() => {
      setPasswordStrength(checkPasswordStrength(debouncedPassword, isDarkMode));
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [debouncedPassword, isDarkMode]);

  // Clear auth error when form changes
  useEffect(() => {
    if (authError) {
      setAuthError(null);
    }
  }, [form]);

  // handle when writing the input
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Update the corresponding field in formData based on the input's id
    setForm({
      ...form,
      [e.target.id]: e.target.value,
    });
  };

  // submit form
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoadingBtn(true);
    setValidationError(null);
    setAuthError(null);

    const password = form.password;
    // check password strength
    setPasswordStrength(() => checkPasswordStrength(password, isDarkMode));

    try {
      // fetch login
      const login = await fetchLogin(form);
      if (login) setLoginInfo(login);

      if (!login) {
        throw new Error("Login failed");
      }

      // if login save the user info in local storage and redirect to homepage
      if (login.success && login.results) {
        setForm(formData);
        dispatch(setMsg({ success: true, message: "login" }));
        // router.push("/authentication");
        dispatch(loginUser(login.results));
        return;
      }

      // handle authentication errors (suspended, inactive, token expired)
      const isAccessError =
        login.message?.toLowerCase().includes("access has been") ||
        login.message?.toLowerCase().includes("Too many login attempts");

      if (isAccessError) {
        setAuthError(login.message);
        return;
      }

      // set the validation errors
      if (login.success === false && login.validationErrors) {
        setValidationError(login.validationErrors);
        return;
      }

      // handle other errors
      if (login.success === false && login.message && !login.validationErrors) {
        dispatch(setMsg({ success: false, message: login.message }));
      }
    } catch (error) {
      console.error("Login error:", error);
      dispatch(
        setMsg({ success: false, message: "An unexpected error occurred" })
      );
    } finally {
      setLoadingBtn(false);
    }
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit}>
      {/* Email input */}
      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
        >
          Email Address
        </label>
        <div className="relative">
          <input
            id="email"
            type="email"
            className={`bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-1.5 md:py-2.5 w-full outline-none transition-all duration-300 border-0 focus:ring-2 focus:ring-red-500 focus:border-red-500 placeholder:text-xs md:placeholder:text-sm ${
              authError || validationError?.email ? "border-rose-500" : ""
            }`}
            placeholder="Enter your email"
            value={form.email}
            onChange={handleFormChange}
          />
          <Mail
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
            size={18}
          />
        </div>
        {/* Error Messages */}
        {authError && (
          <div className="text-sm mt-1 text-rose-500 flex items-start gap-1">
            <span>{authError}</span>
          </div>
        )}
        {!authError && validationError?.email && (
          <div className="text-sm mt-1 text-rose-500 flex items-start gap-1">
            {validationError.email}
          </div>
        )}
      </div>

      {/* Password input */}
      <PasswordField
        name="password"
        label="Password"
        placeholder="Enter your password"
        value={form.password}
        showPassword={showPassword}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          const newPassword = e.target.value;
          setForm({ ...form, password: newPassword });
          // Immediately update password strength
          setDebouncedPassword(newPassword);
        }}
        onToggleVisibility={() => setShowPassword(!showPassword)}
        formErrors={validationError?.password}
        labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
        inputClassName={generateInputClassName(validationError?.password)}
        strengthClassName="bg-gray-200 dark:bg-gray-700"
      />
      {/* Forget password */}
      <div className="flex items-center justify-between">
        <Link
          href="/reset-password"
          className="text-sm text-gray-600 hover:text-red-500 dark:text-gray-400 dark:hover:text-white transition-colors duration-300"
        >
          Forgot Password?
        </Link>
      </div>

      <button
        type="submit"
        className="bg-red-500 hover:bg-red-600 text-sm md:text-base text-white font-semibold py-1.5 md:py-2.5 px-6 rounded-lg transition-colors duration-300 w-full flex flex-wrap items-center justify-center"
      >
        {loadingBtn ? (
          <svg
            className="animate-spin w-4 h-4 fill-current shrink-0"
            viewBox="0 0 16 16"
          >
            <path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
          </svg>
        ) : (
          <>
            {" "}
            Sign In
            <ArrowRight className="ml-2" size={18} />
          </>
        )}
      </button>
    </form>
  );
};

export default function SignInForm() {
  const [loading, setLoading] = useState(true);
  const dispatch = useAppDispatch();
  const isLoggedIn = useAppSelector(selectIsLoggedIn);
  const router = useRouter();

  // ############# USEEFFECT ############
  // Redirect to homepage if already logged in
  useEffect(() => {
    if (isLoggedIn) {
      router.push("/blockseats");
    } else {
      setLoading(false); // Set loading to false once the check is done
    }
  }, [isLoggedIn, router]);

  // ############# RETURNS #################
  if (loading) {
    return <ProgressLoading />;
  }
  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 w-full max-w-6xl flex py-20">
        <div className="hidden md:block w-1/2 pr-8">
          <AuthImage />
        </div>
        <div className="w-full md:w-1/2 flex flex-col justify-center">
          <div className="mb-8">
            <div className="bg-red-500 text-white text-sm font-bold px-3 py-1 rounded inline-block mb-2">
              airvilla Charter
            </div>
            <h1 className="word-break  xs:text-4xl font-bold text-gray-800 dark:text-white mb-2">
              Welcome back
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              New here?{" "}
              <Link href="/signup" className="text-red-500 hover:underline">
                Create an account
              </Link>
            </p>
          </div>

          <SigninFormInput />

          <div className="mt-6 flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Shield size={16} className="mr-2" />
            {/* Your connection is secure */}
            <Image src={clerk} alt="logo" />
          </div>
        </div>
      </div>
    </div>
  );
}
