import axios from "axios";
import manifestUrl from "../endpoints/manifestEndpoint";

/**
 * Get all tickets with manifest data
 */
export interface ManifestFilter {
  status?: string;
  flightDate?: string;
  [key: string]: string | undefined;
}
export interface GetManifestTicketsResponse {
  success: boolean;
  results: {
    manifestTickets: ManifestTicket[];
    totalManifestTickets: number;
    nextCursor: string | null;
  };
}
interface ApiResponse {
  success: boolean;
  message?: string;
  [key: string]: any; // For any additional properties
}

export type ManifestStatus = "PENDING" | "SUBMITTED" | "NOT_SUBMITTED";

export interface ManifestTicket {
  id: string;
  refId: string;
  manifestId: string | null;
  flightDate: string;
  departure: {
    airportCode: string;
    city: string;
    country: string;
  };
  arrival: {
    airportCode: string;
    city: string;
    country: string;
  };
  departureTime: string;
  arrivalTime: string;
  flightDuration: string;
  flightStops: string;
  flightNumber: string;
  carrier: string;
  passengerCount: number;
  status: string;
  owner?: {
    id: string;
    agencyName: string;
    address?: string;
    email?: string;
    phoneNumber?: string;
    website?: string;
    commercialOperationNo?: string;
    iataNo?: string;
  };
  travelClass?: string;
  route?: string;
  manifestStatus: ManifestStatus;
  submittedAt?: string | null;
  Booking?: {
    id: string;
    status: string;
    tripType?: string; // Make this optional with ?
    travelers: Array<{
      id: string;
      bookingId: string;
      travelerId: string;
      infoStatus: string;
      traveler: {
        id: string;
        title: string;
        firstName: string;
        lastName: string;
        dateOfBirth: string;
        nationality: string;
        issuingCountry?: string;
        documentNumber?: string;
        documentExpiryDate?: string;
      };
    }>;
  }[];
}

export interface ManifestCounts {
  totalManifests: number;
}

export async function getManifestTickets(
  page: number = 1,
  cursor?: string | null,
  pageSize: number = cursor ? 10 : 20,
  filters: ManifestFilter = {},
  signal?: AbortSignal // Add signal parameter to allow aborting the request
): Promise<GetManifestTicketsResponse> {
  try {
    let url = manifestUrl.getManifestTickets(page, pageSize);
 if (cursor) {
      url += `&cursor=${cursor}`;
    }
    // // Build query string
    // const params = new URLSearchParams();
    // params.append("page", String(page));
    // params.append("pageSize", String(pageSize));
    // if (cursor) params.append("cursor", cursor);

    // Append filters (ignore "All" or "All Time" defaults)
    Object.entries(filters).forEach(([key, val]) => {
      if (val && val !== "All" && val !== "All Time") {
        url += `&${encodeURIComponent(key)}=${encodeURIComponent(
          val as string
        )}`;
      }
    });

    // // Build URL
    // const url = `${baseUrl}?${params.toString()}`;

    const response = await axios.get(url, {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true,
      signal,
    });

    if (response.data.success) {
      return response.data; // Return the data object directly
    } else {
      throw new Error(
        response.data.error || "Failed to fetch manifest tickets"
      );
    }
  } catch (error: any) {
    let message = "Failed to fetch manifest tickets";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

export async function sendManifestEmail(
  manifestId: string,
  pdfBase64: string,
  email: string
): Promise<ApiResponse> {
  try {
    const response = await axios.post(
      manifestUrl.sendManifestEmail(),
      {
        manifestId,
        pdfBase64,
        email,
      },
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );
    console.log(response.data);
    return { success: true, ...response.data };
  } catch (error: any) {
    let message = "Failed to send manifest email";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

export async function downloadManifestPdf(
  manifestId: string
): Promise<ApiResponse> {
  try {
    console.log({ manifestId });
    const response = await axios.get(manifestUrl.downloadPdf(manifestId), {
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
      responseType: "blob", // Important for handling binary data
    });

    // Create a download link and trigger it
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `manifest-${manifestId}.pdf`);
    document.body.appendChild(link);
    link.click();

    // Clean up
    link.remove();
    window.URL.revokeObjectURL(url);

    return { success: true };
  } catch (error: any) {
    let message = "Failed to download manifest pdf";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}
