import { User, Team<PERSON><PERSON>ber } from "@prisma/client";
// import { User, TeamMember } from "../../types/prismaEnums";
import { Response } from "express";
import checkUserAuth from "../authorization/checkUserAuth";
import { AuthRequest } from "../definitions";

// Function to check the status of a user or team member
const checkUserStatus = (account: User | TeamMember) => {
  // Check if the account object is null or undefined
  if (!account) {
    return { success: false, message: "Account not found1", status: 404 };
  }

  // Check if the account is verified
  // if (!account.verified) {
  if ("verified" in account && !account.verified) {
    return { success: false, message: "Account not verified", status: 401 };
  }

  // Check if the account status is accepted
  if (account.accountStatus !== "accepted") {
    return { success: false, message: "Account not approved", status: 403 };
  }

  // Type guard function to check if the account is a User
  const isUserAccount = (account: User | TeamMember) => {
    return "department" in account;
  };

  // Check if the account has appropriate role
  // For User model: must be master
  // For TeamMember model: must be master, moderator, or accountant

  if (isUserAccount(account)) {
    // It's a TeamMember
    if (
      (account as TeamMember).subRole !== "admin" &&
      (account as TeamMember).subRole !== "moderator" &&
      (account as TeamMember).subRole !== "accountant"
    ) {
      return {
        success: false,
        message:
          "Only admin, moderator, or accountant team members are allowed",
        status: 403,
      };
    }
  } else {
    if (account.role !== "master") {
      return {
        success: false,
        message: "Only master users are allowed",
        status: 403,
      };
    }
  }

  // If all checks pass, return success
  return { success: true };
};

// Function to handle master access based on user status
const getMasterAccess = async (req: AuthRequest, res: Response) => {
  try {
    // Check if the user is authenticated
    let account: User | TeamMember | null = null;
    if (req.accountType === "masterOwner") {
      account = (await checkUserAuth(req, res, "masterOwner")) as User;
    } else if (req.accountType === "masterUser") {
      account = (await checkUserAuth(req, res, "masterUser")) as TeamMember;
    } else {
      // res.status(403).json({
      //   success: false,
      //   message: "Invalid account type for master access",
      // });
      // return null;
      const error = new Error("Invalid account type for master access") as any;
      error.status = 403;
      throw error;
    }

    // Check the account's status
    let statusCheck;
    if (account) {
      statusCheck = checkUserStatus(account);
    }

    // If the status check fails, throw an error with status and message
    if (!statusCheck?.success) {
      const error = new Error(statusCheck?.message || "Access denied") as any;
      error.status = statusCheck?.status || 500;
      throw error;
    }

    // Return the account object on success
    return account;
  } catch (error) {
    console.error("Error checking master access:", error);
    throw error;
  }
};

export default getMasterAccess;
