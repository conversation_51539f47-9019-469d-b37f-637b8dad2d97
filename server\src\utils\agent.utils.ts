import { AuthRequest } from "../utils/definitions";

export async function getAgentAgencyIdFromRequest(
    req: AuthRequest,
    prisma: any
  ): Promise<string | null> {
    // Explicitly handle affiliate accounts
    if (req.accountType === "affiliate") {
      return "AFFILIATE";
    }
  
    const user = req.user as any;
    const agencyAgent = req.agencyAgent as any;
    const teamMember = req.teamMember as any;
    let agentAgencyId = req.agencyAgent?.agencyId || null;

    if (
      !agentAgencyId &&
      (req.accountType === "masterUser" ||
        req.accountType === "masterOwner" ||
        req.accountType === "agencyOwner" ||
        req.accountType === "agencyUser" ||
        req.accountType === "affiliate")
    ) {
      // First, try to get agency ID from teamMember if user is undefined
      if (!user && teamMember && teamMember.agencyName) {
        try {
          if (prisma.agency) {
            const agency = await prisma.agency.findFirst({
              where: { name: teamMember.agencyName },
            });
  
            if (agency) {
              agentAgencyId = agency.id;
  
              return agentAgencyId;
            }
          } else {
            const agency = await prisma.user.findFirst({
              where: { agencyName: teamMember.agencyName },
            });
  
            if (agency) {
              agentAgencyId = agency.id;
  
              return agentAgencyId;
            }
          }
        } catch (error) {
          console.error("Error fetching agency by name from teamMember:", error);
        }
      } else if (user && user.agencyName) {
        // If user is defined and has agencyName, proceed with original logic
        try {
          if (prisma.agency) {
            const agency = await prisma.agency.findFirst({
              where: { name: user.agencyName },
            });
  
            if (agency) {
              agentAgencyId = agency.id;
  
              return agentAgencyId;
            }
          } else {
            // Fallback to user table if agency model is not available
            const agency = await prisma.user.findFirst({
              where: { agencyName: user.agencyName },
            });
  
            if (agency) {
              agentAgencyId = agency.id;
  
              return agentAgencyId;
            }
          }
        } catch (error) {
          console.error("Error fetching agency by name:", error);
        }
      }
  
      // If no agency ID yet, try to get it through team membership
      if (!agentAgencyId && user && user.id) {
        try {
          const teamMembership = await prisma.teamMember.findFirst({
            where: { createdById: user.id },
            include: { team: true },
          });
  
          if (teamMembership?.agencyId) {
            agentAgencyId = teamMembership.agencyId;
            return agentAgencyId;
          }
        } catch (error) {
          console.error("Error fetching team membership:", error);
        }
      }
    }
  
    return agentAgencyId;
  }