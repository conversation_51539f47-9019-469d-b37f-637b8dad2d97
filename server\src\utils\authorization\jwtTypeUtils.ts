import { User, TeamMember, AgencyAgent } from "@prisma/client";

/**
 * Returns the jwtType string for a given user, team member, or agency agent record.
 * Only pass one non-null argument at a time.
 */
export function getJwtType({
  user,
  teamMember,
  agent,
}: {
  user?: Partial<User> | null | undefined;
  teamMember?: Partial<TeamMember> | null | undefined;
  agent?: Partial<AgencyAgent> | null | undefined;
}): string {
  let jwtType = "";
  if (user) {
    if (user.role === "master" && user.roleType === "master_owner") {
      jwtType = "masterOwner";
    } else if (user.role === "master" && user.roleType !== "master_owner") {
      jwtType = "masterUser";
    } else if (user.role === "agency" && user.roleType === "agency_owner") {
      jwtType = "agencyOwner";
    } else if (user.role === "agency" && user.roleType !== "agency_owner") {
      jwtType = "agencyUser";
    } else if (user.role === "affiliate") {
      jwtType = "affiliate";
    }
  } else if (teamMember) {
    jwtType = "teamMember";
  } else if (agent) {
    jwtType = "agencyUser";
  }
  // fallback for unexpected cases
  return jwtType;
}
