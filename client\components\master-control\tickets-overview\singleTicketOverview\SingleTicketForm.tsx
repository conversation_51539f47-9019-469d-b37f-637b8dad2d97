import {
  Card,
  Tooltip,
} from "@/components/flight-tickets/myTickets/addTicket/AddTicketComponents";
import { MasterTicketResultType } from "@/utils/definitions/masterDefinitions";
import {
  Building,
  Coins,
  FileText,
  Gift,
  NotebookPen,
  Plane,
  RockingChair,
  Ticket,
  Users,
  Edit,
  RefreshCw,
} from "lucide-react";
import React, { useState, useEffect } from "react";
import ProgressLoading from "@/components/utils/ProgressLoading";
import {
  fetchSingleTicketForMaster,
  rescheduleSingleMasterTicket,
  updateSingleMasterTicket,
  updateValidMasterTicket,
} from "@/lib/data/masterTicketsData";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { selectUser } from "@/redux/features/AuthSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { getFormatDate, getFormatTime } from "@/utils/functions/functions";
import { useRouter, useSearchParams } from "next/navigation";
import MyTicketHistoryLogs from "@/components/flight-tickets/myTickets/ticketId/MyTicketHistoryLogs";
import ReusableDropdown from "@/components/flight-tickets/myTickets/ticketId/ReusableDropdownUpdate";

import OriginalFlatpickr from "react-flatpickr";
import moment from "moment";
import SegmentFlightTime from "./SegmentFlightTime";

const statusOptions = [
  {
    id: 0,
    value: "available",
  },
  {
    id: 1,
    value: "unavailable",
  },
  {
    id: 2,
    value: "hold",
  },
];

const Flatpickr = OriginalFlatpickr as any;

export default function SingleTicketForm({ ticketId }: { ticketId: string }) {
  // Ticket State Variables
  const [singleTicket, setSingleTicket] = useState<MasterTicketResultType | {}>(
    {}
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Get ticket as FlightTicketRes type
  const ticket = singleTicket as MasterTicketResultType;

  // Comment for spacing

  // HOOKS
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const user = useAppSelector(selectUser);

  // Get editor name for comments
  const getEditorName = () => {
    return user &&
      "firstName" in user &&
      "lastName" in user &&
      user.firstName &&
      user.lastName
      ? `${user.firstName} ${user.lastName}`
      : "Master user";
  };

  const [editMode, setEditMode] = useState<boolean>(false);
  const [updateReqMode, setUpdateReqMode] = useState<boolean>(false);
  const [validationError, setValidationError] = useState<
    Record<string, string>
  >({});
  const [updatedTicket, setUpdatedTicket] =
    useState<MasterTicketResultType | null>(null);

  // Set edit mode based on URL query parameter
  useEffect(() => {
    const edit = searchParams.get("edit");
    if (edit === "true" && ticket && ticket.id) {
      // Ensure ticket is loaded
      if (!editMode) setEditMode(true); // Set editMode if not already set
      // Initialize updatedTicket if not already set or if it's for a different ticket
      if (
        !updatedTicket ||
        (updatedTicket && ticket && updatedTicket.refId !== ticket.refId)
      ) {
        setUpdatedTicket(JSON.parse(JSON.stringify(ticket))); // Deep clone
      }
    }
  }, [searchParams, ticket, editMode, updatedTicket]); // Added dependencies for robustness

  // Ensure updatedTicket is properly initialized when edit mode changes
  useEffect(() => {
    if (editMode || updateReqMode) {
      // Initialize if updatedTicket is not set or is for a different ticket, and base ticket is loaded
      if (
        ticket &&
        ticket.id &&
        (!updatedTicket ||
          (updatedTicket && ticket && updatedTicket.refId !== ticket.refId))
      ) {
        setUpdatedTicket(JSON.parse(JSON.stringify(ticket))); // Deep clone
      }
    } else {
      // If not in edit/update mode, updatedTicket should be cleared by cancel/submit actions.
      // No automatic clearing here to preserve state if user naviga
    }
  }, [editMode, updateReqMode, ticket]); // Removed updatedTicket from deps to prevent potential loops.

  useEffect(() => {
    // Function to fetch a single ticket for master
    const getTicket = async () => {
      // Set loading to true
      setIsLoading(true);

      // Fetch single ticket for master
      const singleTicket = await fetchSingleTicketForMaster(ticketId);

      // Check if the fetching was successful
      if (singleTicket.success) {
        setSingleTicket(singleTicket.results);
      }

      // Dispatch message based on the success of fetching
      dispatch(
        setMsg({
          success: singleTicket.success,
          message: singleTicket.message,
        })
      );

      // Set loading to false after fetching
      setIsLoading(false);
    };
    getTicket();
  }, [ticketId]);

  // handle back navigation
  useEffect(() => {
    const handleBackNavigation = (e: any) => {
      e.preventDefault();
      const ticketsPageUrl = "/master-control/tickets-overview";
      router.push(ticketsPageUrl + "#" + (ticket.ticketStatus || "available"));
    };

    window.addEventListener("popstate", handleBackNavigation);

    return () => {
      window.removeEventListener("popstate", handleBackNavigation);
    };
  }, [router, ticket.ticketStatus]);

  // Function to refresh ticket data after reschedule
  const refreshTicketData = async () => {
    setIsLoading(true);
    const singleTicket = await fetchSingleTicketForMaster(ticketId);
    if (singleTicket.success) {
      setSingleTicket(singleTicket.results);
      setUpdatedTicket(singleTicket.results);
    }
    dispatch(
      setMsg({
        success: singleTicket.success,
        message: singleTicket.message,
      })
    );
    setIsLoading(false);
  };

  // Function to calculate duration between two times
  const calculateDuration = (start: string, end: string) => {
    if (!start || !end) return "";
    const startTime = new Date(start).getTime();
    const endTime = new Date(end).getTime();
    if (isNaN(startTime) || isNaN(endTime)) return "";
    const diffMs = endTime - startTime;
    if (diffMs < 0) return "";
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${diffHours}h ${diffMinutes}m`;
  };

  // Calculate total duration for main card using first segment's departureTime and last segment's arrivalTime
  const getTotalDuration = () => {
    const segs =
      editMode || updateReqMode ? updatedTicket?.segments : ticket?.segments;
    if (segs && segs.length > 0) {
      const firstSegment = segs[0];
      const lastSegment = segs[segs.length - 1];
      return calculateDuration(
        firstSegment.departureTime,
        lastSegment.arrivalTime
      );
    }
    return "N/A";
  };

  // Show loading component if loading is true
  if (isLoading) {
    return <ProgressLoading />;
  }

  const ticketStatusOptions = [
    "available",
    "unavailable",
    "rejected",
    "blocked",
    "expired",
    "hold",
  ];
  // If ticket is not found, show 'No ticket found' message
  if (
    !ticketId ||
    !ticket?.id ||
    !ticketStatusOptions.includes(ticket.ticketStatus)
  ) {
    return (
      <div className="w-full h-[80vh] flex justify-center items-center">
        <h1 className="text-red-700 text-xl font-bold">Ticket not found</h1>
      </div>
    );
  }

  // get flight location value
  const getFlightLocation = (act: string) => {
    if (act === "departure" || act === "arrival") {
      const { airportCode, country, city } = ticket[act];
      return `${city}, ${country} - ${airportCode}`;
    } else {
      return "";
    }
  };

  // Sort the ticketHistoryLogs array by changedAt in descending order
  const sortedLogs = ticket.ticketHistoryLogs
    ? [...ticket.ticketHistoryLogs].sort(
        (a, b) =>
          new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()
      )
    : [];

  const getFlightLocationForSegment = (
    segment: {
      flightNumber: string;
      carrier: string;
      departure: {
        airportCode: string;
        country: string;
        city: string;
        airport: string;
      };
      arrival: {
        airportCode: string;
        country: string;
        city: string;
        airport: string;
      };
      departureTime: string;
      arrivalTime: string;
      duration: string;
    },

    act: "departure" | "arrival"
  ) => {
    if (segment[act]) {
      const { airportCode, country, city, airport } = segment[act];
      return `${city}, ${country} - ${airportCode} (${airport})`;
    }
    return "";
  };

  interface DropdownOption {
    id: number;
    value: string;
  }

  // Helper function to find initial selected ID
  const findInitialSelectedId = (
    options: DropdownOption[],
    currentValue: string | number | undefined
  ): number => {
    const foundOption = options.find(
      (option) => option.value === currentValue?.toString()
    );
    return foundOption ? foundOption.id : -1;
  };

  // Helper functions for ticket data

  // Helper to update main card times/duration from segments
  const updateMainCardTimesFromSegments = (ticketObj: any) => {
    if (!ticketObj.segments || ticketObj.segments.length === 0)
      return ticketObj;

    // Get the flight date (date part only)
    const flightDatePart = ticketObj.flightDate.split("T")[0];

    // Function to ensure time uses the correct date
    const ensureCorrectDate = (timeStr: string) => {
      if (!timeStr) return timeStr;
      const timePart = timeStr.split("T")[1];
      return timePart ? `${flightDatePart}T${timePart}` : timeStr;
    };

    // Find earliest departure and latest arrival
    const segments = ticketObj.segments;

    // Ensure all segment times use the correct date
    segments.forEach((seg: any) => {
      seg.departureTime = ensureCorrectDate(seg.departureTime);
      seg.arrivalTime = ensureCorrectDate(seg.arrivalTime);
    });

    // Assume segments are sorted, but just in case:
    const sortedByDep = [...segments].sort(
      (a, b) =>
        new Date(a.departureTime).getTime() -
        new Date(b.departureTime).getTime()
    );
    const sortedByArr = [...segments].sort(
      (a, b) =>
        new Date(a.arrivalTime).getTime() - new Date(b.arrivalTime).getTime()
    );

    const firstDep = sortedByDep[0].departureTime;
    const lastArr = sortedByArr[sortedByArr.length - 1].arrivalTime;

    // Set the main card times with the correct date
    ticketObj.departureTime = firstDep;
    ticketObj.arrivalTime = lastArr;
    ticketObj.duration = calculateDuration(firstDep, lastArr);

    return ticketObj;
  };

  // Form update handlers
  const handleCancelUpdateTicket = () => {
    setEditMode(false);
    setUpdateReqMode(false);
    setUpdatedTicket(null);
    setValidationError({});

    // Remove the edit=true parameter from the URL
    const currentPath = window.location.pathname;
    router.replace(currentPath, { scroll: false });
  };

  // Handle withdraw update request
  const handleWithdrawUpdateRequest = async (
    e: React.MouseEvent<HTMLButtonElement>
  ) => {
    e.preventDefault();
    try {
      // Get editor name
      const editorName = getEditorName();

      // Create a detailed comment with line breaks
      const detailedComment = [
        `Update request rejected by ${editorName}.`,
        `Ticket update withdrawn.`,
        `Reason: Manual rejection by administrator.`,
      ].join("\n");

      const result = await updateValidMasterTicket(ticketId, {
        updateRespond: "rejected",
        comment: detailedComment,
      });

      if (result.success) {
        dispatch(
          setMsg({
            message: "Update request rejected successfully",
            success: true,
          })
        );
        setUpdateReqMode(false);
        setUpdatedTicket(null);
        router.refresh();
      } else {
        dispatch(
          setMsg({
            message: result.message || "Failed to reject update request",
            success: false,
          })
        );
      }
    } catch (error: any) {
      dispatch(
        setMsg({
          message: error.message || "Failed to reject update request",
          success: false,
        })
      );
    }
  };
  const handleSubmitTicket = async (e: React.FormEvent | React.MouseEvent) => {
    e.preventDefault?.();
    // Remove the handleFormChange call as it expects a different event type
    if (!updatedTicket) return;
    // Validate form
    const errors = validateTicketForm(updatedTicket);
    if (Object.keys(errors).length > 0) {
      setValidationError(errors);
      return;
    }
    // Immediately reset edit modes to hide Update/Cancel buttons and show Edit Ticket button
    setEditMode(false);
    setUpdateReqMode(false);

    // Remove the edit=true parameter from the URL immediately
    const currentPath = window.location.pathname;
    router.replace(currentPath, { scroll: false });

    setIsLoading(true);
    try {
      // Make sure we have the latest updatedTicket with main card times updated from segments
      const updatedTicketWithTimes = updateMainCardTimesFromSegments({
        ...updatedTicket,
      });

      // Get the flight date (date part only)
      const flightDatePart = updatedTicketWithTimes.flightDate.split("T")[0];

      // Function to ensure time uses the correct date
      const ensureCorrectDate = (timeStr: string) => {
        const timePart = timeStr.split("T")[1];
        return `${flightDatePart}T${timePart}`;
      };

      // Get the updated values and ensure they use the correct date
      const segs = updatedTicketWithTimes.segments.map((seg: any) => ({
        ...seg,
        departureTime: ensureCorrectDate(seg.departureTime),
        arrivalTime: ensureCorrectDate(seg.arrivalTime),
      }));

      const departureTime = ensureCorrectDate(
        updatedTicketWithTimes.departureTime
      );
      const arrivalTime = ensureCorrectDate(updatedTicketWithTimes.arrivalTime);
      const duration = updatedTicketWithTimes.duration;

      // Create segment times array with the correct date in all time fields
      const segmentTimes =
        segs && segs.length > 0
          ? segs.map((segment: any) => ({
              segmentId: segment.id,
              departureTime: segment.departureTime, // Already has the correct date from above
              arrivalTime: segment.arrivalTime, // Already has the correct date from above
              duration: segment.duration,
            }))
          : undefined;

      console.log("Submit ticket request data:", {
        flightDate: updatedTicketWithTimes.flightDate,
        departureTime,
        arrivalTime,
        duration,
        segmentTimes,
      });

      // Get editor name
      const editorName = getEditorName();

      // Create a detailed comment about what was changed with before and after values
      const changeLines = [`Ticket updated by ${editorName}.`];

      // Track if any changes were made
      let hasChanges = false;

      // Format flight date changes
      if (updatedTicketWithTimes.flightDate !== ticket.flightDate) {
        const oldDate = getFormatDate(ticket.flightDate);
        const newDate = getFormatDate(updatedTicketWithTimes.flightDate);
        changeLines.push(`Flight date changed: ${oldDate} -> ${newDate}`);
        hasChanges = true;
      }

      // Format departure time changes
      if (departureTime !== ticket.departureTime) {
        const oldTime = getFormatTime(ticket.departureTime);
        const newTime = getFormatTime(departureTime);
        changeLines.push(`Departure time changed: ${oldTime} -> ${newTime}`);
        hasChanges = true;
      }

      // Format arrival time changes
      if (arrivalTime !== ticket.arrivalTime) {
        const oldTime = getFormatTime(ticket.arrivalTime);
        const newTime = getFormatTime(arrivalTime);
        changeLines.push(`Arrival time changed: ${oldTime} -> ${newTime}`);
        hasChanges = true;
      }

      // Format segment time changes if any
      if (segmentTimes && segmentTimes.length > 0) {
        segmentTimes.forEach(
          (
            segTime: {
              segmentId: string;
              departureTime: string;
              arrivalTime: string;
              duration: string;
            },
            index: number
          ) => {
            const originalSeg = ticket.segments.find(
              (s) => s.id === segTime.segmentId
            );
            if (originalSeg) {
              if (segTime.departureTime !== originalSeg.departureTime) {
                const oldTime = getFormatTime(originalSeg.departureTime);
                const newTime = getFormatTime(segTime.departureTime);
                changeLines.push(
                  `Segment ${
                    index + 1
                  } departure time changed: ${oldTime} -> ${newTime}`
                );
                hasChanges = true;
              }
              if (segTime.arrivalTime !== originalSeg.arrivalTime) {
                const oldTime = getFormatTime(originalSeg.arrivalTime);
                const newTime = getFormatTime(segTime.arrivalTime);
                changeLines.push(
                  `Segment ${
                    index + 1
                  } arrival time changed: ${oldTime} -> ${newTime}`
                );
                hasChanges = true;
              }
            }
          }
        );
      }

      // If no changes were detected, show a message and don't proceed with the update
      if (!hasChanges) {
        dispatch(
          setMsg({
            success: false,
            message: "No changes detected. Update canceled.",
          })
        );
        setIsLoading(false);
        return;
      }

      // Create a detailed comment with line breaks
      const detailedComment = changeLines.join("\n");

      // Use rescheduleSingleMasterTicket to update full ticket data including times
      const response = await rescheduleSingleMasterTicket(
        updatedTicketWithTimes.refId,
        {
          flightDate: updatedTicketWithTimes.flightDate,
          departureTime,
          arrivalTime,
          duration,
          comment: detailedComment,
          segmentTimes,
        }
      );
      if (response.success && response.results) {
        setSingleTicket(response.results);
        setUpdatedTicket(null);
        setEditMode(false);
        setUpdateReqMode(false); // Reset updateReqMode as well
        dispatch(
          setMsg({
            success: true,
            message: "Ticket updated successfully",
          })
        );
      } else {
        // If update fails, try refreshing ticket data to ensure UI is up-to-date
        await refreshTicketData();
        dispatch(
          setMsg({
            success: false,
            message: response.message || "Failed to update ticket",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        setMsg({
          success: false,
          message:
            error.message || "An error occurred while updating the ticket",
        })
      );
    } finally {
      setIsLoading(false);
      // Always reset edit modes when the update is complete, regardless of success or failure
      setEditMode(false);
      setUpdateReqMode(false);
    }
  };

  // Validate ticket form data
  const validateTicketForm = (
    ticketData: MasterTicketResultType
  ): Record<string, string> => {
    const errors: Record<string, string> = {};
    if (!ticketData.flightDate) errors.flightDate = "Flight date is required";
    if (!ticketData.departureTime)
      errors.departureTime = "Departure time is required";
    if (!ticketData.arrivalTime)
      errors.arrivalTime = "Arrival time is required";
    if (!ticketData.departure.airportCode)
      errors["departure.airportCode"] = "Departure airport code is required";
    if (!ticketData.arrival.airportCode)
      errors["arrival.airportCode"] = "Arrival airport code is required";
    return errors;
  };

  // Component render

  return (
    <div className="mb-4 sm:mb-0 w-full">
      <div className="sm:flex sm:justify-between sm:items-center mb-5">
        {/* Left: Title */}
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl md:text-3xl text-slate-800 dark:text-slate-100 font-bold">
            {`${editMode ? "Update " : ""}Ticket`}{" "}
            <span className="text-slate-500 text-xl">#{ticketId}</span>
          </h1>
        </div>

        {/* Right: Actions */}
        <div className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
          {/* EDIT BTN */}
          {!editMode && ticket.ticketStatus === "rejected" && (
            <button
              onClick={() => {
                setEditMode(true);
                if (ticket && ticket.id) {
                  setUpdatedTicket(JSON.parse(JSON.stringify(ticket))); // Deep clone
                }
              }}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
            >
              <Edit size={20} className="mr-2" />
              <span className="ml-2">Edit</span>
            </button>
          )}
          {/* UPDATE REQUEST BTN - Only visible when not in edit mode */}
          {!editMode &&
            !updateReqMode &&
            ["available", "unavailable"].includes(ticket.ticketStatus) &&
            ticket.updated === false && (
              <button
                onClick={() => {
                  setUpdateReqMode(true);
                  setUpdatedTicket({ ...ticket }); // Initialize with current ticket data
                }}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
              >
                <RefreshCw size={20} className="mr-2" />
                <span className="ml-2">Edit Ticket</span>
              </button>
            )}
        </div>
      </div>

      <form>
        <div className="space-y-4">
          {/* Agency */}
          <Card icon={<Building color="#EE4544" />} title="Agency">
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
              {/* Agency Name */}
              <InputField
                id="agencyName"
                label="Agency Name"
                type="text"
                placeholder="Enter agency name"
                tooltip="This field displays the name of the agency associated with the ticket."
                value={ticket?.owner?.agencyName ?? ""}
                disabled={true}
              />
              {/* User Name */}
              <InputField
                id="userFullName"
                label="Agent"
                type="text"
                placeholder="Agent name"
                tooltip="This field displays the name of the agent associated with the ticket."
                value={`${
                  ticket?.agencyAgent?.firstName ?? ticket?.owner?.firstName
                } ${
                  ticket?.agencyAgent?.lastName ?? ticket?.owner?.lastName
                }  `}
                disabled={true}
              />
            </div>
          </Card>

          {/* Main */}
          <Card icon={<Ticket color="#EE4544" />} title="Main">
            {/* ROW 1 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
              {/* Ticket Status */}
              <InputField
                id="ticketStatus"
                label="Ticket Status"
                type="text"
                placeholder="Ticket status"
                tooltip="This field displays the current status of the ticket."
                value={ticket?.ticketStatus ?? ""}
                disabled={true}
                input={
                  ticket.ticketStatus !== "expired" ? (
                    <ReusableDropdown
                      options={statusOptions}
                      value={ticket.ticketStatus}
                      initialSelectedId={findInitialSelectedId(
                        statusOptions,
                        ticket.ticketStatus as string
                      )}
                      placeholder="Select flight class"
                      refId={ticket.refId}
                      isTicketStatus={true}
                      isMasterAdmin={true}
                    />
                  ) : (
                    ""
                  )
                }
              />
              {/* Seats */}
              <InputField
                id="seats"
                label="Seats"
                type="number"
                placeholder="Number of seats"
                tooltip="This field displays the number of seats available for the ticket."
                value={ticket?.remainingSeats ?? ""}
                disabled={true}
              />
            </div>
            {/* ROW 2 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
              {/* Departure Date */}
              <InputField
                id="departure"
                label="Departure"
                type="text"
                placeholder="Departure"
                tooltip="This field displays the date when the ticket will depart."
                value={ticket.segments && getFlightLocation("departure")}
                disabled={true}
              />
              {/* Departure Time */}
              <InputField
                id="arrival"
                label="arrival"
                type="text"
                placeholder="Arrival"
                tooltip="This field displays the date when the ticket will arrival."
                value={ticket.segments && getFlightLocation("arrival")}
                disabled={true}
              />
            </div>

            {/* ROW 3 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
              {/* departureTime */}
              <InputField
                id="departureTime"
                label="Departure Time"
                type="text"
                placeholder="departure time"
                tooltip="This field displays the time when the ticket will departure."
                value={getFormatTime(
                  (editMode || updateReqMode
                    ? updatedTicket?.departureTime
                    : ticket.departureTime) ?? ""
                )}
                disabled={true}
              />
              {/* arrivalTime */}
              <InputField
                id="arrivalTime"
                label="Arrival Time"
                type="text"
                placeholder="arrival time"
                tooltip="This field displays the time when the ticket will arrival."
                value={getFormatTime(
                  (editMode || updateReqMode
                    ? updatedTicket?.arrivalTime
                    : ticket.arrivalTime) ?? ""
                )}
                disabled={true}
              />
            </div>
            {/* ROW 4 */}
            <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
              {/* flightDate */}
              <InputField
                id="flightDate"
                label="Flight Date"
                tooltip="This field displays the date when the Flight will departure."
                input={
                  editMode || updateReqMode ? (
                    <Flatpickr
                      value={
                        updatedTicket?.flightDate
                          ? moment(updatedTicket.flightDate).toDate()
                          : undefined
                      }
                      options={{ dateFormat: "M j, Y" }}
                      onChange={([date]: [Date]) =>
                        setUpdatedTicket((prev) =>
                          prev
                            ? {
                                ...prev,
                                flightDate: moment(date).format(
                                  "YYYY-MM-DDTHH:mm:ss.SSS"
                                ),
                              }
                            : prev
                        )
                      }
                      className="w-full outline-none border-0 text-gray-600 dark:text-gray-200 bg-gray-50 dark:bg-gray-600 rounded-lg py-2 px-3 shadow focus:ring-2 focus:ring-red-500 focus:outline-none transition-all duration-300"
                    />
                  ) : undefined
                }
                value={
                  !(editMode || updateReqMode)
                    ? getFormatDate(ticket.flightDate) ?? ""
                    : undefined
                }
                disabled={!(editMode || updateReqMode)}
              />
              {/* duration */}
              <InputField
                id="duration"
                label="duration"
                type="text"
                placeholder="duration"
                tooltip="This field displays the duration when the flight will take."
                value={
                  getTotalDuration() ||
                  calculateDuration(
                    ticket.segments[0].departureTime,
                    ticket?.segments[ticket?.segments.length - 1].arrivalTime
                  )
                }
                disabled={true}
              />
            </div>
            {/* ROW 5 */}
            {ticket?.description && (
              <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                {/* Description */}
                <InputField
                  id="description"
                  label="description"
                  type="textarea"
                  rows={4}
                  placeholder="Flight Ticket Description..."
                  tooltip="This field displays the description of the Flight."
                  value={ticket?.description ?? ""}
                  disabled={true}
                />
              </div>
            )}
          </Card>

          {/* Segments */}
          <div style={{ margin: "1.5rem 0" }}>
            <Card icon={<Plane color="#EE4544" />} title="Segments">
              {ticket?.segments?.map((segment, segIdx) => (
                <div key={segIdx} className="space-y-4 mt-4">
                  {/* Segment Title */}
                  <div className="text-gray-800 dark:text-gray-100 font-semibold">
                    {`Segment ${segIdx !== 0 ? segIdx + 1 : ""}`}
                  </div>

                  {/* ROW 1 */}
                  <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                    {/* Flight Number */}
                    <InputField
                      id={`flightNumber-${segIdx}`}
                      label={`Flight NO ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      type="text"
                      tooltip="This field displays the flight number."
                      value={segment.flightNumber ?? ""}
                      disabled={true}
                    />

                    {/* Carrier */}
                    <InputField
                      id={`carrier-${segIdx}`}
                      label={`Carrier ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      type="text"
                      tooltip="This field displays the carrier name."
                      value={segment.carrier ?? ""}
                      disabled={true}
                    />
                  </div>

                  {/* ROW 2 */}
                  <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                    {/* Departure Location */}
                    <InputField
                      id={`departure-${segIdx}`}
                      label={`Departure ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      type="text"
                      tooltip="This field displays the departure location."
                      value={getFlightLocationForSegment(segment, "departure")}
                      disabled={true}
                    />

                    {/* Arrival Location */}
                    <InputField
                      id={`arrival-${segIdx}`}
                      label={`Arrival ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      type="text"
                      tooltip="This field displays the arrival location."
                      value={getFlightLocationForSegment(segment, "arrival")}
                      disabled={true}
                    />
                  </div>

                  {/* ROW 3 */}
                  <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                    {/* Departure Time */}
                    <InputField
                      id={`departureTime-${segIdx}`}
                      tooltip="This field displays the departure time."
                      label={`Departure Time ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      input={
                        editMode || updateReqMode ? (
                          <SegmentFlightTime
                            act="departureTime"
                            segmentIndex={segIdx}
                            ticket={updatedTicket ? updatedTicket : ticket}
                            setUpdatedTicket={setUpdatedTicket}
                          />
                        ) : undefined
                      }
                      value={
                        !(editMode || updateReqMode)
                          ? getFormatTime(segment.departureTime) ?? ""
                          : undefined
                      }
                      disabled={!(editMode || updateReqMode)}
                    />

                    {/* Arrival Time */}
                    <InputField
                      id={`arrivalTime-${segIdx}`}
                      tooltip="This field displays the arrival time."
                      label={`Arrival Time ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      input={
                        editMode || updateReqMode ? (
                          <SegmentFlightTime
                            act="arrivalTime"
                            segmentIndex={segIdx}
                            ticket={updatedTicket ? updatedTicket : ticket}
                            setUpdatedTicket={setUpdatedTicket}
                          />
                        ) : undefined
                      }
                      value={
                        !(editMode || updateReqMode)
                          ? getFormatTime(segment.arrivalTime) ?? ""
                          : undefined
                      }
                      disabled={!(editMode || updateReqMode)}
                    />

                    {/* Duration */}
                    <InputField
                      id={`duration-${segIdx}`}
                      label={`Duration ${segIdx !== 0 ? segIdx + 1 : ""}`}
                      type="text"
                      tooltip="This field displays the flight duration."
                      value={
                        (editMode || updateReqMode) && updatedTicket?.segments?.[segIdx]
                          ? calculateDuration(
                              updatedTicket.segments[segIdx].departureTime,
                              updatedTicket.segments[segIdx].arrivalTime
                            ) || updatedTicket.segments[segIdx].duration
                          : ticket?.segments?.[segIdx] // Check if original segment exists
                          ? calculateDuration(
                              ticket.segments[segIdx].departureTime,
                              ticket.segments[segIdx].arrivalTime
                            ) || ticket.segments[segIdx].duration
                          : "" // Fallback if segment
                      }
                      disabled={true}
                    />
                  </div>
                  {/* Conditionally render <hr> if it's not the last segment */}
                  {segIdx < ticket.segments.length - 1 && (
                    <hr className="border-t border-gray-300 dark:border-gray-500 my-1"></hr>
                  )}
                </div>
              ))}
            </Card>
          </div>

          {/* FLIGHT CLASSES  */}
          {ticket?.flightClasses?.map((flightClass, classIdx) => (
            <div key={classIdx}>
              <Card icon={<Users color="#EE4544" />} title="Flight Class">
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
                  {/* FLIGHT CLASSES */}
                  <InputField
                    id="flightClass"
                    label="Flight Class"
                    type="text"
                    tooltip="This field displays the Flight Class of the Flight."
                    value={flightClass?.type ?? ""}
                    disabled={true}
                  />
                </div>
              </Card>

              {/* Baggage */}
              <Card icon={<Users color="#EE4544" />} title="Baggage">
                {/* ROW 1 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4">
                  {/* carryOnAllowed */}
                  <InputField
                    id="carryOnAllowed"
                    label="Carry On Allowed"
                    type="text"
                    tooltip="This field displays the maximum number of carry-on bags allowed."
                    value={flightClass?.carryOnAllowed ?? ""}
                    disabled={true}
                  />

                  {/* carryOnWeight */}
                  <InputField
                    id="carryOnWeight"
                    label="Carry On Weight"
                    type="text"
                    tooltip="This field displays the Maximum weight allowed for each carry-on bag."
                    value={flightClass?.carryOnWeight ?? ""}
                    disabled={true}
                  />
                </div>

                {/* ROW 2 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* checkedAllowed */}
                  <InputField
                    id="checkedAllowed"
                    label="Checked Bags Allowed"
                    type="text"
                    tooltip="This field displays the maximum number of checked bags allowed."
                    value={flightClass?.checkedAllowed ?? ""}
                    disabled={true}
                  />

                  {/* carryOnWeight */}
                  <InputField
                    id="checkedWeight"
                    label="Checked Bags Weight"
                    type="text"
                    tooltip="This field displays the Maximum weight allowed for each Checked bag."
                    value={flightClass?.checkedWeight ?? ""}
                    disabled={true}
                  />
                </div>

                {/* ROW 3 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* checkedFee */}
                  <InputField
                    id="checkedFee"
                    label="Checked Bag Fee"
                    type="text"
                    tooltip="This field displays the fee for each checked bag."
                    value={flightClass?.checkedFee ?? ""}
                    disabled={true}
                  />

                  {/* additionalFee */}
                  <InputField
                    id="additionalFee"
                    label="Additional Bag Fee"
                    type="text"
                    tooltip="This field displays the fee for any additional bags beyond the allowed limit."
                    value={flightClass?.additionalFee ?? ""}
                    disabled={true}
                  />
                </div>
              </Card>

              {/* Price */}
              <Card icon={<Coins color="#EE4544" />} title="Price">
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* adult */}
                  <InputField
                    id="adult"
                    label="Adult (JOD)"
                    type="text"
                    tooltip="This field displays the price for an adult ticket."
                    value={flightClass?.price.adult ?? ""}
                    disabled={true}
                  />
                  {/* child */}
                  <InputField
                    id="child"
                    label="Child (JOD)"
                    type="text"
                    tooltip="This field displays the price for a child ticket."
                    value={flightClass?.price.child ?? ""}
                    disabled={true}
                  />
                </div>
                {/* ROW 2 */}
                <div className="md:flex space-y-4 md:space-y-0 md:space-x-4 mt-4">
                  {/* infant */}
                  <InputField
                    id="infant"
                    label="Infant (JOD)"
                    type="text"
                    tooltip="This field displays the price for an infant ticket."
                    value={flightClass?.price.infant ?? ""}
                    disabled={true}
                  />
                  {/* tax */}
                  <InputField
                    id="tax"
                    label="Tax % (JOD)"
                    type="text"
                    tooltip="This field displays the tax amount for the ticket in percentage."
                    value={flightClass?.price.tax ?? ""}
                    disabled={true}
                  />
                </div>
              </Card>

              {/* extraOffers */}
              {flightClass.extraOffers.length > 0 && (
                <Card icon={<Gift color="#EE4544" />} title="Extra Offers">
                  <div key={classIdx}>
                    {flightClass.extraOffers.map((offers, offerIdx) => (
                      <div
                        key={offerIdx}
                        className="md:flex space-y-4 md:space-y-0 md:space-x-4"
                      >
                        {/* Offer Name */}
                        <InputField
                          id={`name-${classIdx}-${offerIdx}`}
                          label={`Name ${offerIdx !== 0 ? offerIdx + 1 : ""}`}
                          type="text"
                          tooltip="This field displays the name of the extra offer."
                          value={offers.name}
                          disabled={true}
                        />

                        {/* Offer Availability */}
                        <InputField
                          id={`available-${classIdx}-${offerIdx}`}
                          label={`Available ${
                            offerIdx !== 0 ? offerIdx + 1 : ""
                          }`}
                          type="text"
                          tooltip="This field displays the availability of the extra offer."
                          value={offers.available ?? ""}
                          disabled={true}
                        />
                      </div>
                    ))}
                  </div>
                </Card>
              )}
            </div>
          ))}
          {/* Reason */}
          {(sortedLogs[0].changeType === "rejected" ||
            sortedLogs[0].changeType === "blocked") && (
            <Card icon={<NotebookPen color="#EE4544" />} title="Reason">
              {/* Description */}
              <InputField
                id="reason"
                label="Comment"
                type="textarea"
                rows={4}
                max={500}
                placeholder="Please provide a clear and detailed reason for rejecting or blocking this ticket request.



































                        The ticket issuer will be able to view this reason."
                tooltip="This field displays the reason of  rejection the ticket."
                value={JSON.parse(sortedLogs[0].changeDetails).comment ?? ""}
                // onChange={(e) => setRejectReason(e.target.value)}
                disabled={true}
              />
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                {JSON.parse(sortedLogs[0].changeDetails).comment.length}
                /500 characters
              </div>
            </Card>
          )}

          {/* Buttons */}
          {(editMode || updateReqMode) && (
            <div className="text-right space-x-0 md:space-x-5 border-t-2 py-5 border-slate-500 dark:border-slate-400 flex md:flex-row flex-col-reverse justify-end">
              {/* Update and Cancel buttons - Visible when in edit mode */}
              {["cancel", "update"].map((action, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                    action === "update"
                      ? handleSubmitTicket(e)
                      : handleCancelUpdateTicket()
                  }
                  className={`btn text-white capitalize px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-base font-semibold ${
                    action === "update"
                      ? "bg-red-500 hover:bg-red-600"
                      : "bg-blue-500 hover:bg-blue-600 mt-5 md:mt-0"
                  }`}
                >
                  {action.charAt(0).toUpperCase() + action.slice(1)}
                </button>
              ))}
            </div>
          )}

          {/* withdraw update request button  */}
          {ticket.updated && (
            <div className="text-right space-x-5 border-t-2 py-5 border-slate-500 dark:border-slate-400">
              <button
                type="button"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                  handleWithdrawUpdateRequest(e)
                }
                className="btn text-white capitalize px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-base font-semibold bg-red-500 hover:bg-red-600"
              >
                Withdraw Update Request
              </button>
            </div>
          )}

          {/* Ticket Logs */}
          {/* Content container */}
          <Card icon={<FileText color="#EE4544" />} title="Ticket Logs">
            {/* Logs section */}
            <div className="bg-gray-50 dark:bg-gray-600 rounded-lg p-6 mb-6 shadow-md">
              {/* Section header with icon and tooltip */}
              <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-white flex items-center">
                <FileText className="mr-2 text-red-500" />
                Recent Activity
                <Tooltip
                  text="This section displays the most recent ticket-related
                            actions, sorted by date with the latest updates shown
                            first."
                />
              </h2>
              {/* Scrollable log entries container */}
              <div className="max-h-96 overflow-y-auto custom-scrollbar-logs">
                {/* Map through log entries and render each one */}
                {sortedLogs &&
                  sortedLogs.length > 0 &&
                  sortedLogs.map((ticketLogs, idx) => (
                    <MyTicketHistoryLogs
                      key={idx}
                      ticketLogs={ticketLogs}
                      index={sortedLogs.length - idx}
                    />
                  ))}
              </div>
            </div>
          </Card>
        </div>
      </form>
    </div>
  );
}

export const InputField = ({
  id,
  label,
  placeholder,
  icon,
  tooltip,
  value,
  onChange,
  type,
  min,
  max,
  validationError,
  input,
  rows,
  disabled,
  required = true,
}: {
  id?: string;
  label: string;
  placeholder?: string;
  icon?: any;
  tooltip?: string;
  value?: string | number | undefined;
  onChange?: (
    e:
      | React.ChangeEvent<HTMLTextAreaElement>
      | React.ChangeEvent<HTMLInputElement>
  ) => void;
  type?: string;
  min?: number;
  max?: number;
  validationError?: any;
  input?: any;
  rows?: number;
  disabled?: boolean;
  required?: boolean;
}) => (
  <div className="flex-1 min-h-[5.6rem]">
    <label
      className="text-sm text-gray-800 dark:text-white font-medium mb-2 flex items-center capitalize space-x-2"
      htmlFor={id}
    >
      <p>{label}</p>
      {required && <span className="text-red-500">*</span>}
      {tooltip && <Tooltip text={tooltip} />}
    </label>
    {input ? (
      input
    ) : type === "textarea" ? (
      <textarea
        id={id}
        style={{ resize: "none" }}
        className="w-full border-0 text-opacity-70 dark:text-gray-200 disabled:text-gray-600 dark:disabled:text-gray-400 bg-white dark:bg-gray-600 disabled:bg-gray-400/50 disabled:dark:bg-gray-800/50 rounded-lg py-2 px-3 shadow focus:ring-2 focus:ring-red-500 disabled:cursor-not-allowed  custom-scrollbar"
        rows={rows}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
      />
    ) : (
      <div className="relative">
        <input
          placeholder={placeholder}
          className="w-full border-0 text-opacity-70 dark:text-gray-200 disabled:text-gray-600 dark:disabled:text-gray-400 dark:placeholder:text-gray-200 placeholder:text-opacity-70 bg-white dark:bg-gray-600 rounded-lg py-2 px-3 shadow dark:shadow-inner focus:ring-2 focus:ring-red-500 focus:outline-none transition-all duration-300 no-arrows disabled:cursor-not-allowed disabled:bg-gray-400/50 disabled:dark:bg-gray-800/50 capitalize"
          id={id}
          value={value ?? ""}
          min={min}
          max={max}
          type={type}
          onChange={onChange}
          inputMode={type === "number" ? "numeric" : "text"}
          pattern={type === "number" ? "[0-9]*" : undefined}
          onWheel={(e) => (e.target as HTMLInputElement).blur()}
          disabled={disabled}
        />
        {icon && (
          <span className="absolute right-3 top-2.5 text-gray-400">{icon}</span>
        )}
      </div>
    )}
    <div className="text-sm mt-1 text-red-500">{validationError}</div>
  </div>
);
