export type BookingSource = "INTERNAL" | "THIRD_PARTY";
export type BookingHoldType = "QUICK_HOLD" | "SUBMIT_BOOKING";

export type Booking = {
  id: string;
  requestId: string;
  ticketId: string;
  returnTicketId?: string;
  status: string;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
  travelers?: TravelerDto[];
  passengerCounts?: {
    adults: number;
    children: number;
    infants: number;
    travelClass: string;
  };
  // Add more fields as needed
};

export type TravelerDto = {
  id?: string;
  title: string;
  firstName: string;
  lastName: string;
  nationality: string;
  dateOfBirth: string;
  gender: string;
  documentType: string;
  documentNumber: string;
  issuingCountry: string;
  expirationDate: string;
  contactEmail: string;
  contactPhone: string;
  travelerId?: string; // Optional, for existing travelers
};

export type CreateInternalBookingDto = {
  ticketId: string;
  returnTicketId?: string; // Added for round-trip bookings
  userId: string;
  agencyAgentId: string;
  teamMemberId?: string;
  type: BookingHoldType;
  source: BookingSource;
  tripType: 'ONE_WAY' | 'ROUND_TRIP';
  travelers: TravelerDto[];
  seats: Array<{ seatNumber?: string; flightClass: string; totalPrice: number }>;
  referenceNumber: string;
};

// --- Third-party booking response type ---
export interface ThirdPartyBookingResponse {
  message?: string;
  booking: Booking;
  returnBooking: Booking | null;
  eTicket: any;   // Optionally replace 'any' with a specific eTicket type
  receipt: any;   // Optionally replace 'any' with a specific receipt type
}