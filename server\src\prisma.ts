/**
 * Prisma Client Singleton and Database Connection Module
 * ----------------------------------------------------
 * This module provides a robust, production-grade, and DRY setup for Prisma Client.
 * - Uses environment variables for configuration (connection string, pool size, etc.)
 * - Supports test and production environments
 * - Ensures connection pool is managed efficiently
 * - <PERSON><PERSON> process shutdown and disconnects Prisma cleanly
 * - Ready for atomicity/locking and clock drift monitoring in distributed systems
 * - Structured logging for all critical events
 *
 * @module prisma
 */

import { PrismaClient } from "@prisma/client";
import { withAccelerate } from "@prisma/extension-accelerate";
import URL from "./utils/DatabaseUrl";
import logger from "./utils/logger";

// --- Constants & Environment Config ---
const { NODE_ENV, DATABASE_URL, TEST_DATABASE_URL } = process.env;
const IS_TEST = NODE_ENV === "test";
const CONNECTION_STRING = IS_TEST ? TEST_DATABASE_URL : URL;

if (!CONNECTION_STRING) {
  throw new Error("DATABASE_URL/TEST_DATABASE_URL is not set!");
}

// Pool config can be extended via env vars for scalability
// const POOL_CONFIG = {
//   connectionString: CONNECTION_STRING,
// max: parseInt(process.env.DB_POOL_MAX || "10", 10),
// min: parseInt(process.env.DB_POOL_MIN || "2", 10),
// idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || "30000", 10),
// };

// --- Postgres Pool & Prisma Adapter ---
// const pool = new Pool(POOL_CONFIG);
// const adapter = new PrismaPg(pool); // Enable if using Prisma's adapter-pg

/**
 * Singleton Prisma Client instance.
 * Logs only warnings and errors for production robustness.
 * Uses environment-based connection string and transaction options.
 */
const prisma = new PrismaClient({
  // adapter, // Uncomment if using PrismaPg adapter
  log: [
    { emit: "event", level: "query" },
    { emit: "event", level: "error" },
    { emit: "event", level: "warn" },
  ],
  datasources: {
    db: { url: CONNECTION_STRING },
  },
  transactionOptions: {
    maxWait: 5000, // ms to wait for transaction lock
    timeout: 10000, // ms transaction timeout
  },
  errorFormat: NODE_ENV === 'dev' ? 'pretty' : 'minimal',
});

// Threshold (ms) above which queries are considered "slow"
const SLOW_QUERY_THRESHOLD_MS = 1000;

// Log slow queries
prisma.$on("query", (e: any) => {
  if (e.duration > SLOW_QUERY_THRESHOLD_MS) {
    // Log queries taking more than 1 second
    logger.warn({
      message: "Slow query detected",
      query: e.query,
      duration: e.duration,
      timestamp: e.timestamp,
    });
  }
});

prisma.$on("error", (e: any) => {
  logger.error({
    message: "Database error occurred",
    error: e.message,
    timestamp: new Date().toISOString(),
  });
});

// Accelerate extension for all queries
const prismaWithAccelerate = prisma.$extends(withAccelerate());

// Connection management
let isConnected = false;

async function connect() {
  try {
    await prisma.$connect();
    isConnected = true;
    logger.info("Database connected successfully");
  } catch (error) {
    logger.error("Database connection failed:", error);
    process.exit(1);
  }
}

export { prisma, prismaWithAccelerate, connect, isConnected };
export default prisma;

// --- Process Shutdown & Connection Cleanup ---
/**
 * Ensures Prisma disconnects cleanly on process exit (SIGINT/SIGTERM)
 * and logs the shutdown event. This prevents connection leaks in production.
 */
// --- Graceful Shutdown & Connection Cleanup ---
// Ensures Prisma disconnects cleanly on process exit (SIGINT/SIGTERM/SIGQUIT)
// and logs the shutdown event. Prevents connection leaks in production.
const shutdownSignals = ["SIGINT", "SIGTERM", "SIGQUIT"];
shutdownSignals.forEach((signal) => {
  process.on(signal, async () => {
    try {
      logger.info(`[PRISMA] Received ${signal}, disconnecting Prisma...`, {
        utc: new Date().toISOString(),
      });
      await prisma.$disconnect();
      logger.info("[PRISMA] Prisma disconnected cleanly.", {
        utc: new Date().toISOString(),
      });
    } catch (err) {
      logger.error("[PRISMA] Error during disconnect:", err);
    } finally {
      process.exit(0);
    }
  });
});