import React from "react";
import { FlightTicketRes } from "@/utils/definitions/blockSeatsDefinitions";

export default function InfoCard({ ticket }: { ticket: FlightTicketRes }) {
  return (
    <div className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 md:p-8 text-base text-gray-800 dark:text-white ">
      {/* Header */}
      <div className="w-full mb-4">
        <h2 className="text-xl md:text-2xl font-bold leading-8 tracking-tight">
          Ticket Description
        </h2>
      </div>
      <h3 className="mb-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 p-4 rounded-lg">
        → {ticket.description}{" "}
      </h3>
    </div>
  );
}
