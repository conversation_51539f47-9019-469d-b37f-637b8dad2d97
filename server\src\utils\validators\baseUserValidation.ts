import Joi from "joi";

const NAME_PATTERN = /\p{L}+(?:[''-]\p{L}+)*(?:\s\p{L}+(?:[''-]\p{L}+)*)*\s*/u;
// Check for common patterns
export const commonPatterns = [
  "password",
  "123456",
  "qwerty",
  "admin",
  "letmein",
  "welcome",
];

export const baseUserValidation = {
  firstName: Joi.string()
    .min(3)
    .max(30)
    .regex(NAME_PATTERN)
    // .trim()
    .required()
    .messages({
      "string.pattern.base":
        "First name can only contain letters, apostrophes, hyphens, and spaces",
      "string.empty": "First name is required",
      "string.min": "First name must be at least 3 characters long",
      "string.max": "First name must be at most 30 characters long",
    }),

  lastName: Joi.string()
    .min(3)
    .max(30)
    .regex(NAME_PATTERN)
    // .trim()
    .required()
    .messages({
      "string.pattern.base":
        "Last name can only contain letters, apostrophes, hyphens, and spaces",
      "string.empty": "Last name is required",
      "string.min": "Last name must be at least 3 characters long",
      "string.max": "Last name must be at most 30 characters long",
    }),

  // email: Joi.string().email().required(),

  // password: Joi.string()
  //   .min(8)
  //   .max(128)
  //   .disallow(...commonPatterns)
  //   // .pattern(
  //   //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+|~=`{}\[\]:";'<>?,./])[A-Za-z\d!@#$%^&*()_+|~=`{}\[\]:";'<>?,./]{8,128}$/
  //   // )
  //   .messages({
  //     "string.pattern.base":
  //       "Password must contain at least one uppercase, lowercase, number, and special character",
  //   }),
};
