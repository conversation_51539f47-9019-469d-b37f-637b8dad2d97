// hooks/useSunStatus.ts
"use client";

import axios from "axios";
import { useEffect, useState } from "react";

export function useSunStatus(lat: number, lng: number, dateTimeISO: string) {
  const [isDayTime, setIsDayTime] = useState<boolean | null>(null);

  useEffect(() => {
    const fetchSunTimes = async () => {
      try {
        const nowUTC = new Date(dateTimeISO);
        const dateStr = nowUTC.toISOString().split("T")[0]; // YYYY-MM-DD

        const res = await axios.get(
          `https://api.sunrise-sunset.org/json?lat=${lat}&lng=${lng}&date=${dateStr}&formatted=0`
        );
        const data = res.data;

        const sunriseUTC = new Date(data.results.sunrise);
        const sunsetUTC = new Date(data.results.sunset);

        const isDay =
          nowUTC.getTime() >= sunriseUTC.getTime() &&
          nowUTC.getTime() < sunsetUTC.getTime();

        setIsDayTime(isDay);
      } catch (error) {
        console.error("Failed to fetch sun data:", error);
        setIsDayTime(true); // Fallback to day icon
      }
    };

    fetchSunTimes();
  }, [lat, lng, dateTimeISO]);

  return isDayTime;
}
