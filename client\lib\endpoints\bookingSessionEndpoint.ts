// Server base URL
import { API_VERSION } from '../constants/apiVersion';
const SERVER_URL = process.env.SERVER_URL;

// Base URL for booking endpoints
const BASE_URL = `${SERVER_URL}${API_VERSION}/booking-session`;

/**
 * Booking session endpoint URLs
 *
 * This object contains all the endpoint URLs related to booking sessions.
 * Each endpoint is documented with a clear description of its purpose.
 */
const bookingSessionUrl = {
  // 1. Start booking session
  startBookingSession: `${BASE_URL}/start`,

  // 2. Validate booking session
  validateBookingSession: `${BASE_URL}/validate`,

  // 3. End booking session
  endBookingSession: `${BASE_URL}/end`,

  // 4. Get booking session by token
  getBookingSessionByToken: (token: string) => `${BASE_URL}/${token}`,

  // 5. Get all booking sessions
  getAllBookingSessions: `${BASE_URL}/all`,

  // 6. Delete booking session by token
  deleteBookingSessionByToken: (token: string) => `${BASE_URL}/${token}`,

  // 7. Update booking session by token
  updateBookingSessionByToken: (token: string) => `${BASE_URL}/${token}`,

  // 8. Get booking session by user ID
  getBookingSessionByUserId: (userId: string) => `${BASE_URL}/user/${userId}`,

  // 9. Get booking session by flight ID
  getBookingSessionByFlightId: (flightId: string) => `${BASE_URL}/flight/${flightId}`,

  // 10. Get booking session by user ID and flight ID
  getBookingSessionByUserIdAndFlightId: (userId: string, flightId: string) => `${BASE_URL}/user/${userId}/flight/${flightId}`,

};
export default bookingSessionUrl;
