import { Ban } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { setLoading } from "@/redux/features/LoadingSlice";
import { softDeleteUser } from "@/lib/data/masterUsersData";
import { fetchDeleteUserProfile } from "@/lib/data/userProfileData";
import { logoutUser } from "@/redux/features/AuthSlice";

/**
 * Custom AlertDialog Component System with Dark Mode Support
 * A set of composable components for creating consistent alert dialogs
 */

// Main dialog container with overlay background
const AlertDialog = ({
  open,
  onOpenChange,
  children,
}: {
  open: boolean;
  onOpenChange: React.Dispatch<React.SetStateAction<boolean>>;
  children: JSX.Element | JSX.Element[];
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black/30 dark:bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full">
        {children}
      </div>
    </div>
  );
};

// Content container with padding
const AlertDialogContent = ({ children }: { children: React.ReactNode }) => (
  <div className="p-6">{children}</div>
);

// Header section for the dialog
const AlertDialogHeader = ({ children }: { children: React.ReactNode }) => (
  <div className="mb-4">{children}</div>
);

// Footer section with action buttons
const AlertDialogFooter = ({ children }: { children: React.ReactNode }) => (
  <div className="mt-6 flex justify-end space-x-2">{children}</div>
);

// Title component for dialog
const AlertDialogTitle = ({ children }: { children: React.ReactNode }) => (
  <h3 className="text-2xl font-bold text-red-600 dark:text-red-500 mb-2">
    {children}
  </h3>
);

// Description container for dialog body content
const AlertDialogDescription = ({
  children,
}: {
  children: React.ReactNode;
}) => <div className="text-gray-700 dark:text-gray-300">{children}</div>;

// Primary action button (destructive but less severe)
const AlertDialogAction = ({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}) => (
  <button
    type="button"
    onClick={onClick}
    className="bg-red-600 dark:bg-red-500 text-white hover:bg-red-700 dark:hover:bg-red-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300 text-sm leading-[22px]"
  >
    {children}
  </button>
);

// Secondary action button (cancel)
const AlertDialogCancel = ({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}) => (
  <button
    type="button"
    onClick={onClick}
    className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300 text-sm leading-[22px]"
  >
    {children}
  </button>
);

/**
 * DeactivateAccountButton - Component for user self-initiated account deactivation
 * Displays a deactivation button and confirmation dialog with detailed information
 * Supports both light and dark modes
 */

interface DeactivateAccountButtonProps {
  userId?: string;
}

const DeactivateAccountButton = ({ userId }: DeactivateAccountButtonProps) => {
  // State management
  const [showDeactivateConfirmation, setShowDeactivateConfirmation] =
    useState(false);
  const [deactivateConfirmText, setDeactivateConfirmText] = useState("");
  const [deactivationReason, setDeactivationReason] = useState("");
  const [errors, setErrors] = useState("");
  const dispatch = useAppDispatch();
  const router = useRouter();
  // Event Handlers
  const handleAccountDeactivation = () => {
    setShowDeactivateConfirmation(true);
  };

  const handleConfirmDeactivation = async () => {
    if (deactivateConfirmText.toUpperCase() === "DEACTIVATE") {
      dispatch(setLoading(true));
      const result = await fetchDeleteUserProfile();

      console.log({ result });

      if (result.success) {
        setErrors(result.message);
        dispatch(
          setMsg({
            success: true,
            message: result.message,
          })
        );
        setShowDeactivateConfirmation(false);
        dispatch(logoutUser());
        router.push("/signin");
      } else {
        setErrors(result.message || "Deactivation failed");
        dispatch(
          setMsg({
            success: false,
            message: result.message || "Deactivation failed",
          })
        );
      }
      dispatch(setLoading(false));
    } else {
      setErrors("Please type 'DEACTIVATE' to confirm account deactivation.");
      dispatch(
        setMsg({
          success: false,
          message: "Please type 'DEACTIVATE' to confirm account deactivation.",
        })
      );
    }
  };

  const handleCancelDeactivation = () => {
    setShowDeactivateConfirmation(false);
    setDeactivateConfirmText("");
    setDeactivationReason("");
  };

  return (
    <div className="text-gray-800 dark:text-white">
      <section>
        <button
          type="button"
          onClick={handleAccountDeactivation}
          className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-300 flex items-center"
          aria-label="Deactivate account"
        >
          <Ban size={18} className="mr-2" />
          Deactivate My Account
        </button>
      </section>

      {/* Deactivation Confirmation Dialog */}
      <AlertDialog
        open={showDeactivateConfirmation}
        onOpenChange={setShowDeactivateConfirmation}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deactivate Your Account</AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-4 text-sm leading-[22px] font-medium text-gray-800 dark:text-gray-200">
                Are you sure you want to deactivate your account?
              </p>

              <div className="bg-gray-200 dark:bg-gray-700 p-4 rounded-lg mb-4">
                <h4 className="text-base font-medium mb-3 text-gray-800 dark:text-gray-100">
                  What happens when you deactivate your account:
                </h4>
                <ul className="list-disc text-sm leading-[22px] text-gray-700 dark:text-gray-300 space-y-1 ml-6">
                  {[
                    "Your profile will become hidden to other users",
                    "All agent accounts will also be deactivated",
                    "Your wallet balance is preserved for 180 days, then forfeited",
                    "All account data is retained for 180 days, then permanently deleted",
                    "You can instantly reactivate within 30 days by simply logging in",
                  ].map((item, index) => (
                    <li key={index} className="pl-2">
                      <span className="block ml-1">{item}</span>
                    </li>
                  ))}
                </ul>
                <p className="mt-3 text-sm leading-[22px] text-gray-700 dark:text-gray-300">
                  <strong>
                    After 30 days, please contact Airvilla Support for
                    assistance in restoring your access
                  </strong>
                </p>

                <div className="border-t border-gray-300 dark:border-gray-600 mt-4 pt-3">
                  <h4 className="text-base font-medium mb-3 text-gray-800 dark:text-gray-100">
                    Account Recovery:
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300 text-sm leading-[22px]">
                    During the first 30 days, you can log in anytime to
                    reactivate automatically. From days 31-180, you'll need to
                    contact support to restore your account. After 180 days, all
                    data will be permanently deleted, including your wallet
                    balance.
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  We're sorry to see you go. Would you mind sharing why?
                </p>
                <select
                  className="w-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 border-0"
                  value={deactivationReason}
                  onChange={(e) => setDeactivationReason(e.target.value)}
                  aria-label="Reason for deactivation"
                >
                  <option value="">Select a reason (optional)</option>
                  {[
                    "Taking a break",
                    "Privacy concerns",
                    "Not finding it useful",
                    "Too busy",
                    "Found an alternative service",
                    "Other reason",
                  ].map((option, index) => (
                    <option
                      key={index}
                      value={option.toLowerCase().replace(/ /g, "_")}
                    >
                      {option}
                    </option>
                  ))}
                </select>
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                Please type "DEACTIVATE" in the box below to confirm:
              </p>
              <input
                type="text"
                className="w-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md px-3 py-2 mt-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500  border-0"
                placeholder="Type DEACTIVATE here"
                value={deactivateConfirmText}
                onChange={(e) => setDeactivateConfirmText(e.target.value)}
                aria-label="Confirmation text"
              />
              {errors && <p className="text-red-500 text-sm mt-1">{errors}</p>}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDeactivation}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                handleConfirmDeactivation();
                console.log("Confirm");
              }}
            >
              Deactivate Account
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
export default DeactivateAccountButton;
