import moment from "moment";
import {
  FlightClassesRes,
  FlightTicketRes,
} from "../definitions/blockSeatsDefinitions";

// ticket format date
export const getFormatDate = (date: string) => {
  return moment(date).format("MMMM D, YYYY");
};

export const getFormatDateTable = (date: string) => {
  return moment(date).format("DD/MM/YYYY");
};
// ticket format date
export const getFormatTime = (date: string) => {
  return moment(date).format("HH:mm");
};

export const convertTimeToISOString = (time: any) => {
  const [hours, minutes] = time.split(":");
  const date = moment().set({
    hour: hours,
    minute: minutes,
    second: 0,
    millisecond: 0,
  });

  // Format to ISO string without timezone conversion
  return date.format("YYYY-MM-DDTHH:mm:ss.SSS");
};

// Helper function to format itinerary
export const formatItinerary = (itinerary: string): string => {
  switch (itinerary) {
    case "one_way":
      return "One Way";
    case "multi_city":
      return "Multi City";
    case "round_trip":
      return "Round Trip";
    default:
      return itinerary;
  }
};

// Helper function to format price
export const formatPrice = (amount: number, currency: string): string => {
  if (amount === undefined || currency === undefined) {
    return "N/A";
  }
  return `${amount.toFixed(2)} ${currency} `;
};

// Helper function to format Date
export const formatCustomDate = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    // hour12: true,
  };

  const formattedDate = new Intl.DateTimeFormat("en-US", options).format(date);

  // Replace slashes with hyphens for the date part
  const [datePart, timePart] = formattedDate.split(", ");
  const dateWithHyphens = datePart.replace(/\//g, "-");

  return `${dateWithHyphens}, ${timePart}`;
};

// Helper function to format price
export function formattedPrice(price: number, currency: string): string {
  const formatted = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);
  // Remove the currency symbol and append "JD" manually
  const formattedWithoutCurrency = formatted.replace(/[^\d.,]/g, "");
  return `${formattedWithoutCurrency} ${currency}`;
}

// Helper function to calculate time difference
export function calculateTimeDifference(
  startTime: string,
  endTime: string
): string {
  const diffInMs = new Date(endTime).getTime() - new Date(startTime).getTime();
  const hours = Math.floor(diffInMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffInMs % (1000 * 60 * 60)) / (1000 * 60));
  return `${hours}h ${minutes}m`;
}

// Helper function to format ticket date in ####### single ticket page of block seates ################
export const formatTicketDate = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = {
    weekday: "short",
    day: "numeric",
    month: "short",
    year: "numeric",
  };

  try {
    return date.toLocaleDateString("en-GB", options);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Unknown Date";
  }
};

// Helper function to format ticket time in ####### single ticket page of block seats ################
export const formatTicketTime = (date: string): string => {
  const newDate = new Date(date);
  const options: Intl.DateTimeFormatOptions = {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  };

  try {
    return newDate.toLocaleTimeString("en-US", options);
  } catch (error) {
    console.error("Error formatting time:", error);
    return "Unknown Time";
  }
};

// helper function to show fee in ####### search ticket modal page of block seats ################
export const getCheckedFeeText = (
  checkedFee: number,
  additionalFee: number
) => {
  if (checkedFee === 0 && !additionalFee) {
    return "Free";
  } else if (checkedFee !== 0 && !additionalFee) {
    return `${checkedFee} JOD`;
  } else if (checkedFee === 0 && additionalFee) {
    return `Free / ${additionalFee} JOD`;
  } else if (checkedFee !== 0 && additionalFee) {
    return `${checkedFee} JOD / ${additionalFee} JOD`;
  }
};

// helper function to format date by adding 1 day in params for search ticket list page ################# search ticket list page of block seats ################
export const formatDate = (date: string) => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0"); // Months are zero-based
  const day = String(d.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// Helper function to Handle availability icon for search ticket list page ################# search ticket modal page of block seats ################
export const getAvailabilityIcon = (availability: string) => {
  if (availability === "yes") {
    // return <CheckIcon className="h-6 w-6 text-green-500 inline" />;
    return "✔️";
  } else if (availability === "no") {
    // return <XIcon className="h-6 w-6 text-red-500 inline" />;
    return "❌";
  } else {
    return "At charge";
  }
};

export const calculateMinMaxPrices = (
  departureTickets: FlightTicketRes[],
  returnTickets: FlightTicketRes[]
) => {
  const allTickets = [...departureTickets, ...returnTickets];
  const prices = allTickets.flatMap((ticket) =>
    ticket.flightClasses.map((fc: FlightClassesRes) => Number(fc.price.adult))
  );

  return {
    minPrice: Math.min(...prices),
    maxPrice: Math.max(...prices),
  };
};

/**
 * Retrieves the min and max prices from local storage.
 *
 * @return {Object} An object containing the minimum and maximum prices.
 * @property {number} minPrice - The minimum price retrieved from local storage.
 * @property {number} maxPrice - The maximum price retrieved from local storage.
 */
export const getMinMaxPricesFromLocalStorage = (
  departureTickets: FlightTicketRes[],
  returnTickets: FlightTicketRes[]
) => {
  // const minPrice = localStorage.getItem("initialMinPrice");
  // const maxPrice = localStorage.getItem("initialMaxPrice");

  const { minPrice: calculatedMinPrice, maxPrice: calculatedMaxPrice } =
    calculateMinMaxPrices(departureTickets, returnTickets);

  return {
    minPrice: calculatedMinPrice,
    maxPrice: calculatedMaxPrice,
    // minPrice: calculatedMinPrice ?? parseFloat(minPrice!),
    // maxPrice: calculatedMaxPrice ?? parseFloat(maxPrice!),
  };
};

//  Helper function to format department name
export const formatDepartmentName = (department: string): string => {
  // Special case for IT
  if (department.toLowerCase() === "it") {
    return "IT";
  }

  // Replace underscores with spaces and capitalize each word
  return department
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

// Add this helper function to decode HTML entities in team management page
export const decodeHtml = (html: string) => {
  const txt = document.createElement("textarea");
  txt.innerHTML = html;
  return txt.value;
};

/**
 * Centralized department name formatting
 */
export const formatDepartmentNameTeamMember = (
  department: string | undefined
) => {
  if (!department) return "N/A";

  const departmentMapping: Record<string, string> = {
    customer_support: "Customer Support",
    management: "Management",
    finance: "Finance",
    marketing: "Marketing",
    sales: "Sales",
    it: "IT",
    operations: "Operations",
  };

  return departmentMapping[department.toLowerCase()] || department;
};

// First, define the color mappings for both background and text
type StatusColor =
  | "yellow"
  | "red"
  | "green"
  | "blue"
  | "purple"
  | "gray"
  | "orange"
  | "indigo"
  | "pink"
  | "teal"
  | "cyan"
  | "lime"
  | "amber"
  | "fuchsia"
  | "violet"
  | "emerald"
  | "zinc";

export const getColorClasses = (color: StatusColor): string => {
  const colorMap: Record<StatusColor, string> = {
    yellow: "bg-yellow-100 text-yellow-800",
    red: "bg-red-100 text-red-800",
    green: "bg-green-100 text-green-800",
    blue: "bg-blue-100 text-blue-800",
    purple: "bg-purple-100 text-purple-800",
    gray: "bg-gray-100 text-gray-800",
    orange: "bg-orange-100 text-orange-800",
    indigo: "bg-indigo-100 text-indigo-800",
    pink: "bg-pink-100 text-pink-800",
    teal: "bg-teal-100 text-teal-800",
    cyan: "bg-cyan-100 text-cyan-800",
    lime: "bg-lime-100 text-lime-800",
    amber: "bg-amber-100 text-amber-800",
    fuchsia: "bg-fuchsia-100 text-fuchsia-800",
    violet: "bg-violet-100 text-violet-800",
    emerald: "bg-emerald-100 text-emerald-800",
    zinc: "bg-zinc-100 text-zinc-800",
  };
  return colorMap[color];
};

export const getDotColorClass = (color: StatusColor): string => {
  const dotColorMap: Record<StatusColor, string> = {
    yellow: "bg-yellow-400",
    red: "bg-red-400",
    green: "bg-green-400",
    blue: "bg-blue-400",
    purple: "bg-purple-400",
    gray: "bg-gray-400",
    orange: "bg-orange-400",
    indigo: "bg-indigo-400",
    pink: "bg-pink-400",
    teal: "bg-teal-400",
    cyan: "bg-cyan-400",
    lime: "bg-lime-400",
    amber: "bg-amber-400",
    fuchsia: "bg-fuchsia-400",
    violet: "bg-violet-400",
    emerald: "bg-emerald-400",
    zinc: "bg-zinc-400",
  };
  return dotColorMap[color];
};

// Get name validation error
export const getNameValidationError = (
  validationError: any,
  fieldName: "firstName" | "lastName"
): string | undefined => {
  // Check direct field error
  if (validationError[fieldName]) {
    return validationError[fieldName];
  }

  // Check array-style validation errors
  if (Array.isArray(validationError)) {
    const nameError = validationError.find(
      (error) => error?.field === fieldName
    );
    if (nameError?.message) {
      return nameError.message;
    }
  }

  // Check object-style validation errors
  if (typeof validationError === "object" && validationError["0"]) {
    const nameError = validationError["0"];
    if (nameError?.field === fieldName && nameError?.message) {
      return nameError.message;
    }
  }

  return undefined;
};

// Utility to unflatten a flat object with dot/bracket notation keys
export function unflatten(obj: Record<string, any>) {
  const result: any = {};
  for (const flatKey in obj) {
    const keys = flatKey
      .split(".")
      .map((k) => (k.match(/^\d+$/) ? Number(k) : k));
    keys.reduce((acc, key, idx) => {
      if (idx === keys.length - 1) {
        acc[key] = obj[flatKey];
        return;
      }
      if (!(key in acc)) {
        acc[key] = typeof keys[idx + 1] === "number" ? [] : {};
      }
      return acc[key];
    }, result);
  }
  return result;
}

export function capitalizeFirst(str?: string) {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export const normalizePaymentMethod = (method: string) => {
  switch (method) {
    case "cashDeposit":
      return "Cash Deposit";
    case "cliq":
      return "Cliq";
    case "cheque":
      return "Cheque";
    case "bankTransfer":
      return "Bank Transfer";
    case "creditCard":
      return "Credit Card";
    default:
      return method;
  }
};


// Calculate ticket price based on traveler type and price object
export const calculateTravelerPrice = (traveler: any, price: any) => {
  if (!traveler || !price) return 0;

  const travelerType = getTravelerType(traveler.dateOfBirth);

  switch (travelerType) {
    case "ADULT":
      return price.adult || 0;
    case "CHILD":
      return price.child || 0;
    case "INFANT":
      return price.infant || 0;
    default:
      return price.adult || 0;
  }
};

// Helper function to determine traveler type based on date of birth
export const getTravelerType = (dateOfBirth: string) => {
  const birthDate = new Date(dateOfBirth);
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();

  if (age < 2) return "INFANT";
  if (age < 12) return "CHILD";
  return "ADULT";
};

export const calculatePrice = (b: any) => {
  // Calculate price based on travelers and flight class prices
  if (b.travelers?.length > 0 && b.ticket?.flightClasses?.[0]?.price) {
    const total = b.travelers.reduce((sum: number, t: any) => {
      const traveler = t.traveler || t;

      return (
        sum + calculateTravelerPrice(traveler, b.ticket.flightClasses[0].price)
      );
    }, 0);

    // Add tax if available
    const tax = b.ticket.flightClasses[0]?.price?.tax || 0;
    const totalWithTax = total + total * (tax / 100);

    return `${totalWithTax.toFixed(2)}`;
  }

  // Fallback to ticket price if available
  return b.ticket?.price ? `${parseFloat(b.ticket.price).toFixed(2)}` : "-";
};