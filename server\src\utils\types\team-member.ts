import { Department, Role, RoleType, TeamMemberRole } from "@prisma/client";

export interface TeamMemberValidationError {
  field: string;
  message: string;
}

export interface SanitizedTeamMember {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: string;
  subRole?: string;
  department: string;
  teamId: string;
  roleType?: string;
  errors: TeamMemberValidationError[];
}

export interface TeamMemberInput {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: Role;
  department: Department;
  teamId: string;
  roleType?: RoleType | Role;
  subRole?: TeamMemberRole;
  // accountStatus?: TeamMemberStatus;
}
