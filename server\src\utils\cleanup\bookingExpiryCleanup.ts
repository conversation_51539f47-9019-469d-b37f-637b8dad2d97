import { BookingType } from "@prisma/client";
// import { BookingType } from "../../types/prismaEnums";
import { BookingService } from "../../services/booking.service";
import { acquireLock, releaseLock } from "../locks/jobLock";
import logger from "../logger";
import { prisma } from "../../prisma";
const bookingService = new BookingService();

const JOB_NAME = "bookingExpiryCleanup";

/**
 * Checks for expired bookings and marks them as timed out.
 * Should be scheduled to run every minute.
 */
export async function processExpiredBookings() {
  if (!(await acquireLock(JOB_NAME))) {
    logger.info(`[${JOB_NAME}] Lock not acquired, skipping this run.`);
    return;
  }
  const BATCH_SIZE = 100;
  try {
    const now = new Date();
    let lastId: string | undefined = undefined;
    let hasMore = true;
    let totalProcessed = 0;

    while (hasMore) {
      const expiredBookings: { id: string }[] = await prisma.booking.findMany({
        where: {
          type: { in: [BookingType.QUICK_HOLD, BookingType.SUBMIT_BOOKING] },
          expiresAt: { lte: now },
          ...(lastId ? { id: { gt: lastId } } : {}),
        },
        orderBy: { id: "asc" },
        select: { id: true },
        take: BATCH_SIZE,
      });
      if (expiredBookings.length === 0) break;
      for (const booking of expiredBookings) {
        try {
          await bookingService.timeoutBooking(booking.id);
          totalProcessed++;
        } catch (err) {
          logger.error(`Failed to expire booking ${booking.id}:`, err);
        }
      }
      lastId = expiredBookings[expiredBookings.length - 1].id;
      hasMore = expiredBookings.length === BATCH_SIZE;
    }
    logger.info(`[${JOB_NAME}] Processed ${totalProcessed} expired bookings.`);
  } catch (error) {
    logger.error("Error checking/updating expired booking holds:", error);
  } finally {
    await releaseLock(JOB_NAME);
  }
}
