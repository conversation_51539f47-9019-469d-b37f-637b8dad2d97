import { Response, NextFunction } from "express";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
// import { FlightTicket, TicketAccess } from "@prisma/client";

// Extend AuthRequest to include ticket
interface TicketAuthRequest extends AuthRequest {
  ticket?: any & {
    ticketAccess?: any[];
  };
}

export const checkTicketAccess = async (
  req: TicketAuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const ticketId = req.params.ticketId || req.params.refId;
    if (!ticketId) {
      return res.status(400).json({
        success: false,
        message: "Ticket ID not provided",
      });
    }

    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    // Find the ticket
    const ticket = await prisma.flightTicket.findFirst({
      where: {
        OR: [{ id: ticketId }, { refId: ticketId }],
      },
      include: {
        ticketAccess: true,
        owner: true,
        agencyAgent: {
          select: {
            id: true,
            agencyId: true,
          },
        },
      },
    });

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: "Ticket not found",
      });
    }

    // Get the current user's agency ID
    const userAgencyId = req.agencyAgent?.agencyId;

    // Normalize account type for case-insensitive comparison
    const accountType = req.accountType;

    // Check access based on account type
    if (accountType === "masterOwner" || accountType === "masterUser") {
      // For master account users, check if they have:
      // 1. Explicit access
      // 2. Created the ticket
      // 3. Are in the same team/agency as the ticket's creator
      const hasAccess =
        ticket.ticketAccess?.some(
          (access: any) => access.teamMemberId === userId
        ) ||
        ticket.createdBy === userId ||
        (ticket.agencyAgent?.agencyId &&
          req.agencyAgent?.agencyId === ticket.agencyAgent.agencyId);

      if (hasAccess) {
        req.ticket = ticket;
        return next();
      }
    } else if (accountType === "agencyOwner") {
      // Agency owners can see all tickets from their agency
      // 1. Tickets they own
      // 2. Tickets created by their agents
      // 3. Tickets where the owner is their agency
      if (
        ticket.ownerId === userId || // They own the ticket
        ticket.agencyAgent?.agencyId === userId || // Their agent created it
        ticket.ownerId === userAgencyId
      ) {
        // The ticket is owned by their agency
        req.ticket = ticket;
        return next();
      }
    } else if (accountType === "agencyUser") {
      // Agency users can see:
      // 1. Tickets they created
      // 2. Tickets created by other agents in the same agency
      // 3. Tickets owned by their agency
      const userIsCreator = ticket.agencyAgent?.id === req.agencyAgent?.id;
      const sameAgency =
        ticket.agencyAgent?.agencyId === userAgencyId ||
        ticket.ownerId === userAgencyId ||
        (ticket.agencyAgent && ticket.agencyAgent.agencyId === userAgencyId);

      if (userIsCreator || sameAgency) {
        req.ticket = ticket;
        return next();
      }
    }

    // If no access is found
    return res.status(403).json({
      success: false,
      message: "You do not have permission to access this ticket",
    });
  } catch (error) {
    console.error("Ticket access check error:", error);
    return res.status(500).json({
      success: false,
      message: "Error checking ticket access",
    });
  }
};
