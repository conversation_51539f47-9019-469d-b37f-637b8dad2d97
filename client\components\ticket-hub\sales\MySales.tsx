"use client";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { Search, Edit, Download } from "lucide-react";
import {
  getSalesAgentNames,
  getAllSales,
  getBookingById,
} from "@/lib/data/bookingData";
import { useInView } from "react-intersection-observer";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import {
  clearBookingConfirmationData,
  selectDepartureTicket,
  selectReturnTicket,
  selectTicketsLoading,
  setBookingConfirmationData,
  setTravelerData,
} from "@/redux/features/BookingConfirmationSlice";
import { AppDispatch, RootState } from "@/redux/store";
import FilterDropdown from "@/components/common/FilterDropdown";
import ProgressLoading from "@/components/utils/ProgressLoading";
import {
  calculatePrice,
  capitalizeFirst,
  getFormatDate,
  getFormatDateTable,
  getFormatTime,
  normalizePaymentMethod,
} from "@/utils/functions/functions";
import {
  exportToCSV,
  formatSalesForExport,
} from "@/utils/functions/exportUtils";
import useAgencyUserAuth from "@/components/hooks/useAgencyUserAuth";
import { PassengerDisplay } from "../bookings/MyBookings";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { clearTickets, fetchTickets } from "@/redux/features/bookingThunks";
import { Booking, TravelerDto } from "@/utils/types/booking.types";

// Utility to normalize booking status/action strings for display
function normalizeBookingStatus(status: string): string {
  if (!status) return "-";
  if (status === "BOOKING_CONFIRMED") return "Booking Confirmed";
  return status
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export const denormalizePaymentMethod = (displayMethod: string): string => {
  if (!displayMethod) return displayMethod;

  // Handle special cases first
  if (displayMethod.toLowerCase() === "airvilla wallet") {
    return "airvillaWallet";
  }

  // Convert display format to camelCase
  return displayMethod
    .toLowerCase()
    .split(" ")
    .map((word, index) =>
      index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
    )
    .join("");
};

// Constants for application status types
const TRIP_TYPES = {
  ROUND_TRIP: "Round Trip",
  ONE_WAY: "One Way",
};

const SALE_STATUS = {
  COMPLETED: "Completed",
  REFUNDED: "Refunded",
  // PARTIALLY_REFUNDED: "Partially Refunded",
  // PENDING: "Pending",
  // NOT_VALID: "Not Valid",
};

// Booking status constants
const BOOKING_STATUS = {
  TIMED_OUT: "TIMED_OUT",
  REJECTED: "REJECTED",
  QUICK_HOLD: "QUICK_HOLD",
  PENDING_APPROVAL: "PENDING_APPROVAL",
  BOOKING_CONFIRMED: "BOOKING_CONFIRMED",
  CANCELLED: "CANCELLED",
  CANCELLED_BY_SYSTEM: "CANCELLED_BY_SYSTEM",
  CANCELLED_BY_USER: "CANCELLED_BY_USER",
};

interface Sale {
  reference: string;
  saleDate: string;
  flightDate: string;
  passenger: string;
  route: string;
  tripType: string;
  carrier: string;
  price: string | number;
  status: string;
  source: string;
  agent: string;
  paymentMethod: string;
}

/**
 * Determines the appropriate sale status based on booking status
 * @param {string} bookingStatus - The status of the booking
 * @returns {string} - The appropriate sale status
 */
function determineSaleStatus(bookingStatus: string): string {
  if (!bookingStatus) return SALE_STATUS.COMPLETED;

  // Convert to uppercase for consistent comparison
  const status = bookingStatus.toUpperCase();

  // // If Booking Status is Timed Out or Rejected, Sale Status must be Not Valid
  // if (
  //   status === BOOKING_STATUS.TIMED_OUT ||
  //   status === BOOKING_STATUS.REJECTED
  // ) {
  //   return SALE_STATUS.NOT_VALID;
  // }

  // // If Booking Status is Quick Hold or Pending Approval, Sale Status is typically Pending
  // if (
  //   status === BOOKING_STATUS.QUICK_HOLD ||
  //   status === BOOKING_STATUS.PENDING_APPROVAL
  // ) {
  //   return SALE_STATUS.PENDING;
  // }

  // If Booking Status is Booking Confirmed, Sale Status is typically Completed
  if (status === BOOKING_STATUS.BOOKING_CONFIRMED) {
    return SALE_STATUS.COMPLETED;
  }

  // If Booking Status is Cancelled, Sale Status may be Refunded
  if (
    status === BOOKING_STATUS.CANCELLED ||
    status === BOOKING_STATUS.CANCELLED_BY_USER
  ) {
    return SALE_STATUS.REFUNDED;
  }

  // Default to Completed for any other status
  return SALE_STATUS.COMPLETED;
}

/**
 * Normalizes the source value to ensure consistent usage throughout the application
 * @param {string} source - The original source value
 * @returns {string} - The normalized source value
 */
function normalizeSourceValue(source: string): string {
  if (!source) return SALE_SOURCE.INTERNAL;

  // Convert to uppercase for consistent comparison
  if (source === "INTERNAL") {
    return SALE_SOURCE.INTERNAL;
  } else if (source === "THIRD_PARTY") {
    return SALE_SOURCE.THIRD_PARTY;
  }

  return source;
}

const SALE_SOURCE = {
  INTERNAL: "Internal",
  THIRD_PARTY: "Third-Party",
};

// Define payment methods by source
const PAYMENT_METHODS = {
  [SALE_SOURCE.INTERNAL]: [
    "Cash Deposit",
    "Cliq",
    "Cheque",
    "Bank Transfer",
    "Credit Card",
  ],
  [SALE_SOURCE.THIRD_PARTY]: ["Airvilla Wallet"],
};

// Label mapping for sale status and source
const SALE_STATUS_LABELS: Record<string, string> = {
  COMPLETED: "Completed",
  REFUNDED: "Refunded",
  PARTIALLY_REFUNDED: "Partially Refunded",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
  QUICK_HOLD: "Quick Hold",
  PENDING_APPROVAL: "Pending Approval",
  BOOKING_CONFIRMED: "Sale Confirmed",
  REJECTED: "Sale Rejected",
  TIMED_OUT: "Timed Out",
  CANCELLED: "Cancelled",
  CANCELLED_BY_SYSTEM: "Cancelled",
  CANCELLED_BY_USER: "Cancelled",
};

const SALE_SOURCE_LABELS: Record<string, string> = {
  THIRD_PARTY: "Third-Party",
  "Third-Party": "Third-Party",
  INTERNAL: "Internal",
  Internal: "Internal",
};

// Status styles for visual indication
const STATUS_STYLES = {
  [SALE_STATUS.COMPLETED]: "bg-green-200 text-green-800",
  [SALE_STATUS.REFUNDED]: "bg-red-200 text-red-800",
  // [SALE_STATUS.PARTIALLY_REFUNDED]: "bg-yellow-200 text-yellow-800",
  // [SALE_STATUS.PENDING]: "bg-blue-200 text-blue-800",
  // [SALE_STATUS.NOT_VALID]: "bg-red-200 text-red-800",
};

const SOURCE_STYLES = {
  [SALE_SOURCE.INTERNAL]: "bg-blue-200 text-blue-800",
  [SALE_SOURCE.THIRD_PARTY]: "bg-purple-200 text-purple-800",
};

/**
 * Filter options for dropdown selects
 * @const {Object} FILTER_OPTIONS - Contains options for filter dropdowns
 */
const FILTER_OPTIONS = {
  SaleStatus: ["All", ...Object.values(SALE_STATUS)],
  SaleSource: ["All", ...Object.values(SALE_SOURCE)],
  Agent: [
    "All",
    "Sarah Johnson",
    "Michael Brown",
    "Lisa Chen",
    "Robert Davis",
    "Jennifer Lee",
    "Thomas Wilson",
    "Emily Taylor",
    "Daniel Smith",
    "External Agent",
  ],
  SaleDate: [
    "All Time",
    "Last 12 Months",
    "Last 6 Months",
    "Last 3 Months",
    "Last Month",
    "Last 7 Days",
    "Today",
  ],
  travelDateRange: [
    "All Time",
    "Next 90 Days",
    "Next 30 Days",
    "Next 7 Days",
    "Today",
  ],
  PaymentMethod: [
    "All",
    ...PAYMENT_METHODS[SALE_SOURCE.INTERNAL],
    ...PAYMENT_METHODS[SALE_SOURCE.THIRD_PARTY],
  ],
};

// Table column headers
const TABLE_HEADERS = [
  "ID",
  "Flight Date",
  "Passenger",
  "Route",
  "Trip Type",
  "Carrier",
  "Sale Price",
  "Sale Status",
  "Sale Source",
  "Employee",
  "Payment Method",
  "Sale Date",
  "Actions",
];

// Map display status to enum value for API
const mapStatusToEnum = (displayStatus: string): string | undefined => {
  if (displayStatus === "All") return undefined;

  // Map frontend status values to backend BookingStatus enum values
  const statusMap: Record<string, string> = {
    completed: "BOOKING_CONFIRMED",
    refunded: "CANCELLED_BY_USER",
    cancelled: "CANCELLED_BY_USER",
    pending: "PENDING_APPROVAL",
    "quick hold": "QUICK_HOLD",
    "timed out": "TIMED_OUT",
    rejected: "BOOKING_REJECTED",
    "cancelled by system": "CANCELLED_BY_SYSTEM",
  };

  // Convert to lowercase for case-insensitive matching
  const lowerStatus = displayStatus.toLowerCase();
  return statusMap[lowerStatus] || displayStatus; // Return the original if no mapping found
};
const mapSourceToApiValue = (displayValue: string): string | undefined => {
  if (displayValue === "All") return undefined;
  const entry = Object.entries(SALE_SOURCE).find(
    ([_, value]) => value === displayValue
  );
  return entry ? entry[0] : undefined;
};

/**
 * Simple filter display component - purely for UI display purposes
 */
const FilterCard = ({
  filters,
  setFilters,
  onFilterChange,
  searchQuery,
  onSearchChange,
}: {
  filters: any;
  setFilters: (filters: any) => void;
  onFilterChange: (filterName: string, value: string) => void;
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => {
  const [selectedStatus, setSelectedStatus] = useState("All");
  const [selectedAgent, setSelectedAgent] = useState("All");
  const [selectedSource, setSelectedSource] = useState("All");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("All");
  const [selectedSaleDate, setSelectedSaleDate] = useState("All Time");
  const [selectedFlightDateRange, setSelectedFlightDateRange] =
    useState("All Time");
  const [paymentMethods, setPaymentMethods] = useState([
    "All",
    ...PAYMENT_METHODS[SALE_SOURCE.INTERNAL],
    ...PAYMENT_METHODS[SALE_SOURCE.THIRD_PARTY],
  ]);
  const [loading, setLoading] = useState(true);
  const [sales, setSales] = useState<Sale[]>([]);
  const [salesCounts, setSalesCounts] = useState({
    totalSales: 0,
  });
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState(null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [agentNames, setAgentNames] = useState<string[]>(["All"]);

  // Helper to derive agent names from bookings list
  const buildAgentNameOptions = (bookings: any[]): string[] => {
    const names = bookings.map((b: any) =>
      b.source === SALE_SOURCE.THIRD_PARTY
        ? "External Agent"
        : b.buyerAgentName || b.meta?.buyerAgentName || b.agent || "-"
    );

    return [
      "All",
      ...Array.from(new Set(names))
        .filter((n) => n && n !== "-")
        .sort(),
    ];
  };

  // Get the current user from Redux store at the component level
  const currentUser = useAppSelector(selectUser);

  // Fetch agent names from API
  useEffect(() => {
    const fetchAgentNames = async () => {
      try {
        const agentNames = await getSalesAgentNames();
        setAgentNames([
          "All",
          ...agentNames.filter((name): name is string => name !== "All"),
        ]);
      } catch (error) {
        console.error("Error fetching agent names:", error);
        setAgentNames(["All"]);
      }
    };
    fetchAgentNames();
  }, []);

  /**
   * Fetches initial sales data from API
   */
  // useEffect(() => {
  const fetchSales = useCallback(async () => {
    try {
      setLoading(true);
      setSales([]);
      setNextCursor(null);
      setHasMore(true);

      // Get user ID and account type to pass to the server
      let userId;
      let accountType;
      let agencyId;

      if (currentUser.isLogin) {
        const user = currentUser as StoredUser;
        userId = user.id;
        agencyId = user.agencyId;

        // Determine account type based on user role and role type
        if (user.role === "agency") {
          if (user.roleType === "agency_owner") {
            accountType = "agencyOwner";
          } else {
            accountType = "agencyUser";
          }
        } else if (user.role === "master") {
          if (user.roleType === "master_owner") {
            accountType = "masterOwner";
          } else {
            accountType = "masterUser";
          }
        } else {
          accountType = "affiliate";
        }
      }

      const statusFilter =
        filters.bookingStatus !== "All"
          ? mapStatusToEnum(filters.bookingStatus)
          : undefined;
      const sourceFilter =
        filters.bookingSource !== "All"
          ? mapSourceToApiValue(filters.bookingSource)
          : undefined;
      const searchTerm = searchQuery.trim() || undefined;
      const agentNameFilter =
        filters.bookingAgent !== "All" ? filters.bookingAgent : undefined;
      const travelDateRange =
        filters.travelDateRange !== "All Time"
          ? filters.travelDateRange
          : undefined;
      const paymentMethodFilter =
        filters.paymentMethod !== "All"
          ? denormalizePaymentMethod(filters.paymentMethod)
          : undefined;
      const saleDateFilter =
        filters.saleDate !== "All Time" ? filters.saleDate : undefined;

      // Fetch more sales using the cursor with a limit of 10
      const data = await getAllSales(
        null,
        20,
        statusFilter,
        sourceFilter,
        agentNameFilter,
        travelDateRange,
        paymentMethodFilter,
        saleDateFilter,
        searchTerm
      );

      if (data && data.success && data.results) {
        // Immediately update the total count
        if (data.results.bookingsTotal) {
          const totalCount = data.results.bookingsTotal;
          setSalesCounts({ totalSales: totalCount });
        }

        const salesData = data.results.bookings || [];

        // Update Employee Name filter options
        setAgentNames(buildAgentNameOptions(salesData));

        // Map backend booking data to UI sales structure
        const mappedSales = salesData.map((b: any) => {
          const isDepartureRoundTrip =
            b.ticket.departureTime === b.meta?.departure?.departureTime;
          const isArrivalRoundTrip =
            b.ticket.departureTime === b.meta?.return?.departureTime;
          const price = calculatePrice(b);
          return {
            reference: b.requestId || b.id || "",
            saleDate: b.createdAt
              ? new Date(b.createdAt).toLocaleDateString()
              : b.updatedAt
              ? new Date(b.updatedAt).toLocaleDateString()
              : "-",
            flightDate: b.ticket?.flightDate
              ? new Date(b.ticket.flightDate).toLocaleDateString()
              : b.flightDate
              ? new Date(b.flightDate).toLocaleDateString()
              : "-",
            // Passenger: first and last name of the first traveler
            passenger:
              b.travelers &&
              Array.isArray(b.travelers) &&
              b.travelers.length > 0
                ? (() => {
                    const tr = b.travelers[0].traveler || b.travelers[0];
                    return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                  })()
                : "-",
            travelers: b.travelers || [],
            // Route: use meta data or ticket description
            route: (() => {
              // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights
              if (
                isReturnFlight &&
                b.meta?.return?.departureAirport &&
                b.meta?.return?.arrivalAirport
              ) {
                return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
              }

              // For departure flights (default)
              if (
                b.meta?.departure?.departureAirport &&
                b.meta?.departure?.arrivalAirport
              ) {
                return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
              }

              // Fallback
              if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
              }

              return "-";
            })(),
            carrier: (() => {
              // Use the same logic as route to determine if this is a return flight
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights, use return flight's carrier
              if (isReturnFlight && b.meta?.return?.carrier) {
                return b.meta.return.carrier;
              }

              // For departure flights or fallback
              return b.meta?.departure?.carrier || b.meta?.carrier || "-";
            })(),
            // Sale Price: use totalAmount, fallback to bookedSeats[0].totalPrice
            price:
              price ||
              (b.totalAmount && b.totalAmount !== "0"
                ? b.totalAmount
                : b.bookedSeats && b.bookedSeats.length > 0
                ? b.bookedSeats[0].totalPrice
                : "-"),
            // Agent: use meta agentName or agencyAgentId
            agent: b.meta?.buyerAgentName || b.agencyAgentId || "-",
            // Payment Method: use payment.paymentMethod if available
            paymentMethod:
              normalizePaymentMethod(b.payment?.paymentMethod) || "-",
            // Required Sale fields for type safety
            tripType: b.tripType || "-",
            // Apply sale status validation based on booking status
            status: (() => {
              const originalStatus = b.status;
              const determinedStatus = determineSaleStatus(originalStatus);

              return determinedStatus;
            })(),
            source: (() => {
              const originalSource = b.source;
              const normalizedSource = normalizeSourceValue(originalSource);

              return normalizedSource;
            })(),
          };
        });

        setSales(mappedSales);

        // Total count is already set above

        // Update cursor for next page
        setNextCursor(data.results.nextCursor || null);

        // Check if there are more sales to load
        setHasMore(!!data.results.nextCursor);

        // Update counts if available
        if (data.results.bookingsTotal !== undefined) {
          setSalesCounts((prev) => ({
            ...prev,
            totalSales: data.results.bookingsTotal,
          }));
        }
      } else {
        setError("Failed to fetch sales data. Please try again later." as any);
      }

      setLoading(false);
    } catch (err) {
      console.error("Error fetching sales:", err);
      setError("Failed to fetch sales data. Please try again later." as any);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
    // }, [currentUser]);
  }, [filters, searchQuery, loading, currentUser]);

  // Update available payment methods when source changes
  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const source = e.target.value;
    setSelectedSource(source);

    if (source === "All") {
      setPaymentMethods([
        "All",
        ...PAYMENT_METHODS[SALE_SOURCE.INTERNAL],
        ...PAYMENT_METHODS[SALE_SOURCE.THIRD_PARTY],
      ]);
    } else {
      setPaymentMethods(["All", ...PAYMENT_METHODS[source]]);
    }
  };

  // Reset all filters to default values
  const handleResetFilters = () => {
    setSelectedStatus("All");
    setSelectedAgent("All");
    setSelectedSource("All");
    setSelectedPaymentMethod("All");
    setSelectedSaleDate("All Time");
    setSelectedFlightDateRange("All Time");
    // Reset the filters state
    setFilters({
      bookingStatus: "All",
      bookingAgent: "All",
      bookingSource: "All",
      paymentMethod: "All",
      saleDate: "All Time",
      travelDateRange: "All Time",
    });
    setPaymentMethods([
      "All",
      ...PAYMENT_METHODS[SALE_SOURCE.INTERNAL],
      ...PAYMENT_METHODS[SALE_SOURCE.THIRD_PARTY],
    ]);
  };

  // Handle filter changes
  const handleFilterChange = <K extends keyof typeof filters>(
    key: K,
    value: (typeof filters)[K]
  ) => {
    // Update the filters state
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // If this is the bookingSource change, handle payment methods
    if (key === "bookingSource") {
      if (value === "All") {
        setPaymentMethods([
          "All",
          ...PAYMENT_METHODS[SALE_SOURCE.INTERNAL],
          ...PAYMENT_METHODS[SALE_SOURCE.THIRD_PARTY],
        ]);
      } else {
        setPaymentMethods(["All", ...PAYMENT_METHODS[value as string]]);
      }
    }
  };

  // Effect to fetch data when filters or search query changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        await fetchSales();
      } catch (error) {
        console.error("Error fetching sales:", error);
        setError("Failed to fetch sales data. Please try again later." as any);
      } finally {
        setLoading(false);
      }
    };

    // Add a small debounce to avoid too many API calls
    const timer = setTimeout(() => {
      fetchData();
    }, 300);

    return () => clearTimeout(timer);
  }, [filters, searchQuery]);
  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg pt-5 pb-7 mb-5 sm:px-3
    flex flex-col lg:flex-row items-stretch lg:items-end space-y-4 lg:space-y-0 lg:space-x-4
    "
    >
      <div className="flex-1">
        <FilterDropdown
          label="Sale Status"
          options={FILTER_OPTIONS.SaleStatus}
          value={filters.bookingStatus}
          onChange={(val) => handleFilterChange("bookingStatus", val)}
        />
      </div>
      <div className="flex-1">
        <FilterDropdown
          label="Sale Source"
          options={FILTER_OPTIONS.SaleSource}
          value={filters.bookingSource}
          onChange={(val) => {
            handleFilterChange("bookingSource", val);
            if (val === "All") {
              setPaymentMethods([
                "All",
                ...PAYMENT_METHODS[SALE_SOURCE.INTERNAL],
                ...PAYMENT_METHODS[SALE_SOURCE.THIRD_PARTY],
              ]);
            } else {
              setPaymentMethods(["All", ...PAYMENT_METHODS[val]]);
            }
          }}
        />
      </div>
      <div className="flex-1">
        <FilterDropdown
          label="Employee Name"
          options={agentNames}
          value={filters.bookingAgent}
          onChange={(val) => handleFilterChange("bookingAgent", val)}
        />
      </div>
      <div className="flex-1">
        <FilterDropdown
          label="Payment Method"
          options={paymentMethods}
          value={filters.paymentMethod}
          onChange={(val) => handleFilterChange("paymentMethod", val)}
        />
      </div>
      <div className="flex-1">
        <FilterDropdown
          label="Sale Date"
          options={FILTER_OPTIONS.SaleDate}
          value={filters.saleDate}
          onChange={(val) => handleFilterChange("saleDate", val)}
        />
      </div>
      <div className="flex-1">
        <FilterDropdown
          label="Flight Date"
          options={FILTER_OPTIONS.travelDateRange}
          value={filters.travelDateRange}
          onChange={(val) => handleFilterChange("travelDateRange", val)}
        />
      </div>
      <div className="flex-1">
        <button
          className="h-[45px] bg-blue-500 text-white text-sm hover:bg-blue-600 transition duration-300 py-2 px-3 rounded-lg"
          onClick={handleResetFilters}
        >
          Reset Filters
        </button>
      </div>
    </div>
  );
};

/**
 * Component for individual sale row
 * @param {Object} props - Component properties
 * @param {Object} props.sale - Sale data
 */
const SaleTableRow = ({
  sale,
  onEdit,
}: {
  sale: any;
  onEdit: (bookingId: string) => void;
}) => {
  const {
    reference,
    saleDate,
    flightDate,
    passenger, // Use the mapped passenger field
    travelers,
    route,
    tripType,
    carrier,
    price,
    status,
    source,
    agent,
    paymentMethod,
  } = sale;

  // Normalize source for consistent comparison
  const normalizedSource = normalizeSourceValue(source);

  // Override payment method for third-party source
  const displayPaymentMethod =
    normalizedSource === SALE_SOURCE.THIRD_PARTY
      ? "Airvilla Wallet"
      : normalizePaymentMethod(paymentMethod);

  const isRoundTrip =
    tripType?.toString().toUpperCase().replace(/\s+/g, "_") === "ROUND_TRIP";
  // Format route display based on trip type
  const formattedRoute = isRoundTrip
    ? route.replace("→", "↔") // Replace one-way arrow with bidirectional arrow for round trips
    : route.replace("↔", "→"); // Replace bidirectional arrow with one-way arrow for one-way trips

  // Format price display based on status
  const formattedPrice =
    status === SALE_STATUS.REFUNDED
      ? `-${price}` // Show negative value for refunded sales
      : `${price}`;

  // Validate that payment method is allowed for the source
  const sourceKey = normalizedSource;

  const isValidPaymentMethod =
    PAYMENT_METHODS[sourceKey] &&
    PAYMENT_METHODS[sourceKey].includes(paymentMethod);
  const paymentMethodClasses = isValidPaymentMethod
    ? "p-4 whitespace-nowrap"
    : "p-4 whitespace-nowrap";

  // Prepare passengers data for display
  const getPassengersData = () => {
    // If we have travelers array, use it
    if (travelers && Array.isArray(travelers) && travelers.length > 0) {
      return travelers
        .map((t) => {
          if (!t?.traveler) return null;
          const traveler = t.traveler;
          return {
            ...traveler,
            // Fallback to name from traveler object if available
            name:
              traveler.firstName && traveler.lastName
                ? `${traveler.firstName} ${traveler.lastName}`
                : traveler.firstName || traveler.lastName || "Passenger",
          };
        })
        .filter(Boolean); // Remove any null entries
    }

    // Fallback to the passenger string if available
    if (passenger) {
      return [
        {
          name: passenger,
          firstName: passenger.split(" ")[0],
          lastName: passenger.split(" ").slice(1).join(" "),
        },
      ];
    }

    // No passenger data available
    return [];
  };

  const passengersData = getPassengersData();

  return (
    <tr className="border-b border-gray-300 dark:border-gray-700">
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-semibold text-blue-400">
        {reference}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {getFormatDateTable(flightDate)}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {travelers && travelers?.length > 1 ? (
          <PassengerDisplay passengers={passengersData} />
        ) : (
          <div className="group">
            {/* Truncated Name */}
            <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
              {passenger}
            </div>
            <div className="relative">
              {/* Tooltip with Full Name */}
              <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
                {passenger}
              </div>
            </div>
          </div>
        )}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {formattedRoute}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {tripType &&
        typeof tripType === "string" &&
        TRIP_TYPES[tripType as keyof typeof TRIP_TYPES]
          ? TRIP_TYPES[tripType as keyof typeof TRIP_TYPES]
          : tripType}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {carrier}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {formattedPrice}
      </td>
      <td>
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            STATUS_STYLES[status] || "bg-gray-300 text-gray-800"
          }`}
        >
          {SALE_STATUS_LABELS[status] || normalizeBookingStatus(status)}
        </span>
      </td>
      <td>
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            SOURCE_STYLES[normalizedSource] || "bg-gray-300 text-gray-800"
          }`}
        >
          {normalizedSource === SALE_SOURCE.INTERNAL
            ? "Internal"
            : "Third-Party"}
        </span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <div className="group">
          {/* Truncated Name */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {normalizedSource === SALE_SOURCE.INTERNAL
              ? agent
              : "External Agent"}
          </div>
          <div className="relative">
            {/* Tooltip with Full Name */}
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {normalizedSource === SALE_SOURCE.INTERNAL
                ? agent
                : "External Agent"}
            </div>
          </div>
        </div>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {displayPaymentMethod}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {getFormatDateTable(saleDate)}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100 flex space-x-2">
        <button
          className="text-blue-400 hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300"
          aria-label={`View details for sale ${reference}`}
          onClick={() => onEdit(reference)}
        >
          <Edit size={18} />
        </button>
      </td>
    </tr>
  );
};

/**
 * Sales table component
 * @param {Object} props - Component properties
 * @param {Array} props.sales - Array of sale objects
 * @param {boolean} props.hasMore - Whether there are more sales to load
 * @param {boolean} props.loadingMore - Whether more sales are currently being loaded
 * @param {any} props.observerRef - Ref for the intersection observer
 */
const SalesTable = ({
  sales,
  onEditBooking,
  hasMore,
  loadingMore,
  observerRef,
}: {
  sales: any[];
  onEditBooking: (bookingId: string) => void;
  hasMore: boolean;
  loadingMore: boolean;
  observerRef: any; // Using any to accommodate the ref function from useInView
}) => (
  <div className="overflow-x-auto custom-scrollbar max-h-[calc(100vh-400px)]">
    <table className="table-auto w-full">
      {/* Table header */}
      <thead className="text-xs font-semibold capitalize text-gray-800 dark:text-gray-50 bg-gray-50 dark:bg-gray-900/20 border-t border-b border-gray-200 dark:border-gray-700 text-left sticky -top-0.5 z-[5]">
        <tr>
          {TABLE_HEADERS.map((header) => (
            <th
              key={header}
              className="pl-2 p-4 whitespace-nowrap font-semibold text-sm bg-gray-300 dark:bg-gray-700"
            >
              {header}
            </th>
          ))}
        </tr>
      </thead>
      {/* Table body */}
      <tbody className="text-sm divide-y divide-gray-200 dark:divide-gray-700">
        {sales.map((sale, index) => (
          <SaleTableRow
            key={`${sale.reference}-${index}`}
            sale={sale}
            onEdit={() => onEditBooking(sale.reference)}
          />
        ))}
      </tbody>
    </table>

    {sales.length === 0 && (
      <div className="text-center py-8">
        <p className="text-lg dark:text-gray-400">No Sales Found</p>
      </div>
    )}

    <div className="w-full">
      {loadingMore && (
        <div className="py-4">
          <div className="flex justify-center">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse delay-75"></div>
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse delay-150"></div>
            </div>
          </div>
        </div>
      )}
      {!loadingMore && hasMore && (
        <div ref={observerRef} className="w-full h-10" />
      )}
    </div>
  </div>
);

/**
 * Navigation tabs component
 * @param {Object} props - Component properties
 * @param {string} props.activeTab - Currently active tab
 * @param {Function} props.onTabChange - Handler for tab change
 */
const TabNavigation = ({
  activeTab,
  onTabChange,
  salesCounts,
  filteredSales,
}: {
  activeTab: string;
  onTabChange: (tab: string) => void;
  salesCounts: { totalSales: number };
  filteredSales: number;
}) => {
  // Tab options
  const tabs = [{ name: "All" }, { name: "Completed" }, { name: "Refunded" }];

  return (
    <div className="flex items-center space-x-6">
      <div className="flex items-center">
        <span className="text-2xl font-bold text-red-500 mr-2">
          {activeTab !== "All" ? filteredSales : salesCounts.totalSales}
        </span>
        <span className="text-lg font-semibold">Total Sales</span>
      </div>
      <div className="h-8 w-px bg-gray-300 dark:bg-gray-700"></div>
      {/* Tab Navigation */}
      <div className="flex items-center space-x-2 text-sm">
        {tabs.map((tab) => (
          <span
            key={tab.name}
            className={`cursor-pointer py-1 px-2 rounded ${
              activeTab === tab.name
                ? "bg-red-500 text-white"
                : "text-gray-700 hover:bg-gray-300 dark:text-gray-300 dark:hover:bg-gray-700"
            }`}
            onClick={() => onTabChange(tab.name)}
          >
            {tab.name}
          </span>
        ))}
      </div>
    </div>
  );
};

const MySalesList = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [sales, setSales] = useState<Sale[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<Sale | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [salesCounts, setSalesCounts] = useState({
    totalSales: 0,
  });
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isDocumentProcessing, setIsDocumentProcessing] = useState<{
    type: "e-ticket" | "receipt" | null;
    action: "download" | "print" | null;
  }>({ type: null, action: null });

  const [filters, setFilters] = useState({
    bookingStatus: "All",
    bookingAgent: "All",
    bookingSource: "All",
    paymentMethod: "All",
    saleDate: "All Time",
    travelDateRange: "All Time",
  });
  const [searchInput, setSearchInput] = useState(searchQuery);
  const filtersRef = useRef(filters);
  const searchQueryRef = useRef(searchQuery);

  // const bookingResult = useSelector(
  //   (state: RootState) => state.bookingConfirmation.bookingResult
  // );
  // const travelerData = useSelector(
  //   (state: RootState) => state.bookingConfirmation.travelerData
  // );
  const fullTicket = useSelector(
    (state: RootState) => state.bookingConfirmation.fullTicket
  );
  const passengerCounts = useSelector(
    (state: RootState) => state.bookingConfirmation.passengerCounts
  );
  const bookingType = useSelector(
    (state: RootState) => state.ticketSearchForm.value.bookingType
  );
  const itinerary = useSelector(
    (state: RootState) => state.ticketSearchForm.value.itinerary
  );

  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );
  const travelerData2 = useSelector(
    (state: RootState) => state.bookingConfirmation.travelerData
  );

  // Now you can access the data
  const { bookingResult } = bookingConfirmation;

  // Get the booking data from Redux
  const travelers = bookingResult?.travelers || [];
  const travelerData = travelers.some((t: any) => t.traveler)
    ? travelers.map((t: any) => t.traveler) // Use nested traveler data if available
    : travelerData2 || [];

  const departureTicket = useSelector(selectDepartureTicket);
  const returnTicket = useSelector(selectReturnTicket);
  const ticketsLoading = useSelector(selectTicketsLoading);

  // Fetch tickets when component mounts or bookingResult changes
  useEffect(() => {
    if (bookingResult) {
      // Type assertion to handle the thunk action
      (dispatch as AppDispatch)(
        fetchTickets({
          departureTicketId: bookingResult.departureTicketId,
          returnTicketId: bookingResult.returnTicketId,
        })
      );
    }

    // Cleanup function to clear tickets when component unmounts
    return () => {
      (dispatch as AppDispatch)(clearTickets());
    };
  }, [bookingResult, dispatch]);

  // Create a combined booking object for components that expect it
  const booking = {
    bookingResult,
    travelerData,
    fullTicket,
    passengerCounts,
    bookingType,
    itinerary,
    departureTicket,
    returnTicket,
    ticketsLoading,
  };

  // Use IntersectionObserver via useInView hook
  const { ref, inView } = useInView({
    threshold: 0.5, // Trigger when 50% of the element is visible
    triggerOnce: false,
    rootMargin: "0px 0px 0px 0px", // No extra margin
    initialInView: false,
  });

  // Ref to track if we're currently loading more sales
  const isLoadingMoreRef = useRef(false);

  // Function to load more sales when scrolling
  const loadMoreSales = async () => {
    // Don't load more if already loading or no more data to load
    if (isLoadingMoreRef.current || loadingMore || !nextCursor || !hasMore) {
      return;
    }

    // Set loading flag in ref to prevent multiple simultaneous calls
    isLoadingMoreRef.current = true;
    setLoadingMore(true);

    // Add a small delay to ensure the loading indicator is visible
    await new Promise((resolve) => setTimeout(resolve, 500));

    try {
      // Get user ID and account type to pass to the server
      let userId;
      let accountType;
      let agencyId;

      if (currentUser.isLogin) {
        const user = currentUser as StoredUser;
        userId = user.id;
        agencyId = user.agencyId;

        // Determine account type based on user role and role type
        if (user.role === "agency") {
          if (user.roleType === "agency_owner") {
            accountType = "agencyOwner";
          } else {
            accountType = "agencyUser";
          }
        } else if (user.role === "master") {
          if (user.roleType === "master_owner") {
            accountType = "masterOwner";
          } else {
            accountType = "masterUser";
          }
        } else {
          accountType = "affiliate";
        }
      }

      const statusFilter =
        filters.bookingStatus !== "All"
          ? mapStatusToEnum(filters.bookingStatus)
          : undefined;
      const sourceFilter =
        filters.bookingSource !== "All"
          ? mapSourceToApiValue(filters.bookingSource)
          : undefined;
      const searchTerm = searchQuery.trim() || undefined;
      const agentNameFilter =
        filters.bookingAgent !== "All" ? filters.bookingAgent : undefined;
      const travelDateRange =
        filters.travelDateRange !== "All Time"
          ? filters.travelDateRange
          : undefined;
      const paymentMethodFilter =
        filters.paymentMethod !== "All" ? filters.paymentMethod : undefined;
      const saleDateFilter =
        filters.saleDate !== "All Time" ? filters.saleDate : undefined;

      // Fetch more sales using the cursor with a limit of 10
      const data = await getAllSales(
        nextCursor,
        10,
        statusFilter,
        sourceFilter,
        agentNameFilter,
        travelDateRange,
        paymentMethodFilter,
        saleDateFilter,
        searchTerm
      );

      if (data && data.success && data.results) {
        // Update total count if available
        if (data.results.bookingsTotal) {
          const totalCount = data.results.bookingsTotal;
          setSalesCounts({ totalSales: totalCount });
        }

        // Map the new sales to UI format
        const newSales = data.results.bookings || [];

        if (newSales.length === 0) {
          setHasMore(false);
          return;
        }

        // Process each booking to add as a sale
        const newMappedSales = newSales.map((b: any) => {
          const isDepartureRoundTrip =
            b.ticket.departureTime === b.meta?.departure?.departureTime;
          const isArrivalRoundTrip =
            b.ticket.departureTime === b.meta?.return?.departureTime;
          const price = calculatePrice(b);
          return {
            reference: b.requestId || b.id || "",
            saleDate: b.createdAt
              ? new Date(b.createdAt).toLocaleDateString()
              : b.updatedAt
              ? new Date(b.updatedAt).toLocaleDateString()
              : "-",
            flightDate: b.ticket?.flightDate
              ? new Date(b.ticket.flightDate).toLocaleDateString()
              : b.flightDate
              ? new Date(b.flightDate).toLocaleDateString()
              : "-",
            // Passenger: first and last name of the first traveler
            passenger:
              b.travelers &&
              Array.isArray(b.travelers) &&
              b.travelers.length > 0
                ? (() => {
                    const tr = b.travelers[0].traveler || b.travelers[0];
                    return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                  })()
                : "-",
            travelers: b.travelers || [],
            // Route: use meta data or ticket description
            route: (() => {
              // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights
              if (
                isReturnFlight &&
                b.meta?.return?.departureAirport &&
                b.meta?.return?.arrivalAirport
              ) {
                return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
              }

              // For departure flights (default)
              if (
                b.meta?.departure?.departureAirport &&
                b.meta?.departure?.arrivalAirport
              ) {
                return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
              }

              // Fallback
              if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
              }

              return "-";
            })(),
            carrier: (() => {
              // Use the same logic as route to determine if this is a return flight
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights, use return flight's carrier
              if (isReturnFlight && b.meta?.return?.carrier) {
                return b.meta.return.carrier;
              }

              // For departure flights or fallback
              return b.meta?.departure?.carrier || b.meta?.carrier || "-";
            })(),
            // Sale Price: use totalAmount, fallback to bookedSeats[0].totalPrice
            price:
              price ||
              (b.totalAmount && b.totalAmount !== "0"
                ? b.totalAmount
                : b.bookedSeats && b.bookedSeats.length > 0
                ? b.bookedSeats[0].totalPrice
                : "-"),
            // Agent: use meta agentName or agencyAgentId
            agent: b.meta?.agentName || b.agencyAgentId || "-",
            // Payment Method: use payment.paymentMethod if available
            paymentMethod: b.payment?.paymentMethod || "-",
            // Required Sale fields for type safety
            tripType: b.tripType || "-",
            // Apply sale status validation based on booking status
            status: (() => {
              const originalStatus = b.status;
              const determinedStatus = determineSaleStatus(originalStatus);
              return determinedStatus;
            })(),
            source: (() => {
              const originalSource = b.source;
              const normalizedSource = normalizeSourceValue(originalSource);
              return normalizedSource;
            })(),
          };
        });

        // Update state with new sales
        setSales((prevSales) => [...prevSales, ...newMappedSales]);

        // Update cursor for next page
        const newCursor = data.results.nextCursor || null;
        setNextCursor(newCursor);

        // Check if there are more sales to load
        setHasMore(!!newCursor);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error loading more sales:", error);
    } finally {
      // Reset loading states
      setLoadingMore(false);
      isLoadingMoreRef.current = false;
    }
  };

  // Track the last time we loaded more sales
  const lastLoadTimeRef = useRef<number>(0);

  // Effect to load more sales when user scrolls to the bottom
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (inView && !loading && !loadingMore && hasMore && nextCursor) {
      // Check if enough time has passed since the last load (at least 1 second)
      const now = Date.now();
      const timeSinceLastLoad = now - lastLoadTimeRef.current;

      if (timeSinceLastLoad < 1000) {
        return; // Don't load if less than 1 second has passed
      }

      // Add a small delay to prevent rapid loading
      timeoutId = setTimeout(() => {
        // Check again before loading to prevent race conditions
        if (!loadingMore && hasMore && nextCursor) {
          lastLoadTimeRef.current = Date.now(); // Update last load time
          loadMoreSales();
        }
      }, 300);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [inView, loading, loadingMore, hasMore, nextCursor]);

  // Get the current user from Redux store at the component level
  const currentUser = useAppSelector(selectUser);

  useEffect(() => {
    filtersRef.current = filters;
    searchQueryRef.current = searchQuery;
  }, [filters, searchQuery]);
  /**
   * Fetches initial sales data from API
   */
  // useEffect(() => {
  const fetchSales = useCallback(async () => {
    try {
      setLoading(true);
      setSales([]);
      setNextCursor(null);
      setHasMore(true);

      // Get user ID and account type to pass to the server
      let userId;
      let accountType;
      let agencyId;

      if (currentUser.isLogin) {
        const user = currentUser as StoredUser;
        userId = user.id;
        agencyId = user.agencyId;

        // Determine account type based on user role and role type
        if (user.role === "agency") {
          if (user.roleType === "agency_owner") {
            accountType = "agencyOwner";
          } else {
            accountType = "agencyUser";
          }
        } else if (user.role === "master") {
          if (user.roleType === "master_owner") {
            accountType = "masterOwner";
          } else {
            accountType = "masterUser";
          }
        } else {
          accountType = "affiliate";
        }
      }

      const statusFilter =
        filters.bookingStatus !== "All"
          ? mapStatusToEnum(filters.bookingStatus)
          : undefined;
      const sourceFilter =
        filters.bookingSource !== "All"
          ? mapSourceToApiValue(filters.bookingSource)
          : undefined;
      const searchTerm = searchQuery.trim() || undefined;
      const agentNameFilter =
        filters.bookingAgent !== "All" ? filters.bookingAgent : undefined;
      const travelDateRange =
        filters.travelDateRange !== "All Time"
          ? filters.travelDateRange
          : undefined;
      const paymentMethodFilter =
        filters.paymentMethod !== "All" ? filters.paymentMethod : undefined;
      const saleDateFilter =
        filters.saleDate !== "All Time" ? filters.saleDate : undefined;
      console.log({
        statusFilter,
        sourceFilter,
        searchTerm,
        agentNameFilter,
        travelDateRange,
        paymentMethodFilter,
        saleDateFilter,
      });

      // Fetch more sales using the cursor with a limit of 10
      const data = await getAllSales(
        null,
        20,
        statusFilter,
        sourceFilter,
        agentNameFilter,
        travelDateRange,
        paymentMethodFilter,
        saleDateFilter,
        searchTerm
      );

      if (data && data.success && data.results) {
        // Immediately update the total count
        if (data.results.bookingsTotal) {
          const totalCount = data.results.bookingsTotal;
          setSalesCounts({ totalSales: totalCount });
        }

        const salesData = data.results.bookings || [];

        // Map backend booking data to UI sales structure
        const mappedSales = salesData.map((b: any) => {
          const isDepartureRoundTrip =
            b.ticket.departureTime === b.meta?.departure?.departureTime;
          const isArrivalRoundTrip =
            b.ticket.departureTime === b.meta?.return?.departureTime;
          const price = calculatePrice(b);
          return {
            reference: b.requestId || b.id || "",
            saleDate: b.createdAt
              ? new Date(b.createdAt).toLocaleDateString()
              : b.updatedAt
              ? new Date(b.updatedAt).toLocaleDateString()
              : "-",
            flightDate: b.ticket?.flightDate
              ? new Date(b.ticket.flightDate).toLocaleDateString()
              : b.flightDate
              ? new Date(b.flightDate).toLocaleDateString()
              : "-",
            // Passenger: first and last name of the first traveler
            passenger:
              b.travelers &&
              Array.isArray(b.travelers) &&
              b.travelers.length > 0
                ? (() => {
                    const tr = b.travelers[0].traveler || b.travelers[0];
                    return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                  })()
                : "-",
            travelers: b.travelers || [],
            // Route: use meta data or ticket description
            route: (() => {
              // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights
              if (
                isReturnFlight &&
                b.meta?.return?.departureAirport &&
                b.meta?.return?.arrivalAirport
              ) {
                return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
              }

              // For departure flights (default)
              if (
                b.meta?.departure?.departureAirport &&
                b.meta?.departure?.arrivalAirport
              ) {
                return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
              }

              // Fallback
              if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
              }

              return "-";
            })(),
            carrier: (() => {
              // Use the same logic as route to determine if this is a return flight
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights, use return flight's carrier
              if (isReturnFlight && b.meta?.return?.carrier) {
                return b.meta.return.carrier;
              }

              // For departure flights or fallback
              return b.meta?.departure?.carrier || b.meta?.carrier || "-";
            })(),
            // Sale Price: use totalAmount, fallback to bookedSeats[0].totalPrice
            price:
              price ||
              (b.totalAmount && b.totalAmount !== "0"
                ? b.totalAmount
                : b.bookedSeats && b.bookedSeats.length > 0
                ? b.bookedSeats[0].totalPrice
                : "-"),
            // Agent: use meta agentName or agencyAgentId
            agent: b.meta?.buyerAgentName || b.agencyAgentId || "-",
            // Payment Method: use payment.paymentMethod if available
            paymentMethod:
              normalizePaymentMethod(b.payment?.paymentMethod) || "-",
            // Required Sale fields for type safety
            tripType: b.tripType || "-",
            // Apply sale status validation based on booking status
            status: (() => {
              const originalStatus = b.status;
              const determinedStatus = determineSaleStatus(originalStatus);

              return determinedStatus;
            })(),
            source: (() => {
              const originalSource = b.source;
              const normalizedSource = normalizeSourceValue(originalSource);

              return normalizedSource;
            })(),
          };
        });

        setSales(mappedSales);

        // Total count is already set above

        // Update cursor for next page
        setNextCursor(data.results.nextCursor || null);

        // Check if there are more sales to load
        setHasMore(!!data.results.nextCursor);
        // Update counts if available
        if (data.results.bookingsTotal !== undefined) {
          setSalesCounts((prev) => ({
            ...prev,
            totalSales: data.results.bookingsTotal,
          }));
        }
      } else {
        setError("Failed to fetch sales data. Please try again later." as any);
      }

      setLoading(false);
    } catch (err) {
      console.error("Error fetching sales:", err);
      setError("Failed to fetch sales data. Please try again later." as any);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [currentUser, filters, searchQuery]);

  //   fetchSales();
  // }, [currentUser]);

  // When filters change
  useEffect(() => {
    fetchSales();
  }, [filters, searchQuery, fetchSales]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchInput !== searchQuery) {
        setSearchQuery(searchInput);
      }
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchInput, searchQuery]);
  // Handler for tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  /**
   * Handler for exporting sales data to CSV
   */
  const handleExportSales = () => {
    try {
      // Format the sales data for export
      const formattedData = formatSalesForExport(filteredSales);

      // Generate a filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split("T")[0]; // YYYY-MM-DD format
      const filename = `airvilla-sales-${dateStr}`;

      // Export the data to CSV
      exportToCSV(formattedData, filename);
    } catch (error) {
      console.error("Error exporting sales:", error);
    }
  };

  /**
   * Filters sales based on search query and active tab
   *
   * @returns {Array} Filtered sales based on current filters
   */
  const filteredSales = sales.filter((sale) => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const agentDisplay =
        (sale as any).source === SALE_SOURCE.THIRD_PARTY
          ? "External Agent".toLowerCase()
          : (sale as any).agent?.toLowerCase();

      const salePaymentMethod =
        (sale as any).paymentMethod === SALE_SOURCE.THIRD_PARTY
          ? "Airvilla Wallet".toLowerCase()
          : (sale as any).paymentMethod?.toLowerCase();

      return (
        (sale as any).reference?.toLowerCase().includes(query) ||
        (sale as any).passenger?.toLowerCase().includes(query) ||
        (sale as any).carrier?.toLowerCase().includes(query) ||
        (sale as any).route?.toLowerCase().includes(query) ||
        agentDisplay?.includes(query) ||
        salePaymentMethod?.includes(query) ||
        (sale as any).flightDate?.toLowerCase().includes(query)
      );
    }

    // Tab filter
    if (activeTab !== "All") {
      return (sale as any).status === activeTab;
    }

    return true;
  });

  // Handler for editing booking
  const handleEditBooking = async (bookingId: string) => {
    try {
      // Show loading state
      setLoading(true);

      if (!bookingId) {
        console.error("No bookingId provided to getBookingById");
        setLoading(false);
        return;
      }
      // Fetch the full booking data from API
      const bookingData = await getBookingById(bookingId);

      if (bookingData) {
        // Clear any existing booking data
        dispatch(clearBookingConfirmationData());

        // Transform and set the booking data in Redux
        const transformedBooking = {
          ...bookingData,
          bookingResult: bookingData,
          travelerData: bookingData.travelers,
          travelers:
            bookingData.travelers?.map((t: any) => t.traveler || t) || [],
          ticket: bookingData,
          fullTicket: bookingData,
          passengerCounts: {
            adults: bookingData.passengerCounts?.adults || 0,
            children: bookingData.passengerCounts?.children || 0,
            infants: bookingData.passengerCounts?.infants || 0,
            travelClass: bookingData.passengerCounts?.travelClass || "",
          },
        };

        // Update Redux state with the new booking data
        dispatch(setBookingConfirmationData(transformedBooking));

        // Set traveler data in Redux if available
        if (transformedBooking.travelers?.length > 0) {
          dispatch(setTravelerData(transformedBooking.travelers));
        }

        // Wait for Redux state to update
        await new Promise((resolve) => setTimeout(resolve, 100));

        handleDocumentAction(transformedBooking, "receipt", "print");

        // Navigate to the manage booking page
        // router.push(
        //   `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingId}`
        // );
        // router.push(
        //   `/blockseats/list/${bookingId}/checkout/booking-confirmation?bookingId=${bookingId}`
        // );
      } else {
        console.error("Failed to fetch booking data");
      }
    } catch (error) {
      console.error("Error fetching booking data:", error);
    } finally {
      // Hide loading state
      setLoading(false);
    }
  };

  const handleDocumentAction = async (
    booking: any,
    type: "e-ticket" | "receipt",
    action: "download" | "print"
  ): Promise<void> => {
    try {
      // Set document processing state
      setIsDocumentProcessing({ type, action });

      // Get the container element that wraps the booking confirmation content
      // Try multiple selectors to find the booking content
      const containerSelectors = [
        ".print-container",
        ".receipt-container",
        ".booking-container",
        ".booking-content",
        "body > div", // Fallback to first div in body
        "body", // Last resort use body
      ];

      let container: HTMLElement | null = null;
      for (const selector of containerSelectors) {
        container = document.querySelector(selector) as HTMLElement;
        if (container) {
          break;
        }
      }

      if (!container) {
        console.error(
          "Container element not found. Tried selectors:",
          containerSelectors
        );
        dispatch(
          setMsg({
            message:
              "Failed to generate document: Could not find booking content",
            success: false,
          })
        );
        setIsDocumentProcessing({ type: null, action: null });
        return;
      }

      // Get flight data from the correct structure
      const ticket = booking?.fullTicket || {};
      const bookingResult = booking?.bookingResult || {};

      // Handle API response structure when coming from tables
      const apiTicket = bookingResult?.ticket || {};

      // Determine if we're using data from Redux or from API response
      const effectiveTicket =
        Object.keys(ticket).length > 0 ? ticket : apiTicket;

      const segments = effectiveTicket?.segments || [];
      // Get the first segment for flight details
      const firstSegment = segments[0] || {};

      // Get baggage allowance from flight class
      const carryOnAllowed = firstSegment?.flightClass?.carryOnAllowed || 0;
      const carryOnWeight = firstSegment?.flightClass?.carryOnWeight || 0;
      const checkedAllowed = firstSegment?.flightClass?.checkedAllowed || 0;
      const checkedWeight = firstSegment?.flightClass?.checkedWeight || 0;

      // Format baggage display
      const baggageDisplay = [];
      if (checkedAllowed > 0) {
        baggageDisplay.push(`${checkedAllowed} x ${checkedWeight} kg Checked`);
      }
      if (carryOnAllowed > 0) {
        baggageDisplay.push(`${carryOnAllowed} x ${carryOnWeight} kg Carry-on`);
      }

      if (action === "download") {
        const html2pdf = (await import("html2pdf.js")).default;

        // Clone the container to avoid modifying the original
        const element = container.cloneNode(true) as HTMLElement;
        // element.style.padding = "20px"; // Add some padding for better print layout

        const travelerDataArr = Array.isArray(booking.travelers)
          ? booking.travelers.map((item: any) => item.traveler || {})
          : [];

        let travelerCards = "";

        travelerDataArr.forEach((traveler: TravelerDto, index: number) => {
          const t = traveler || {};
          travelerCards += `<div class="card">
        <div class="card-header">
          <h2 class="card-title">Traveler Details</h2>
        </div>
        <div class="card-content">
          <div class="passenger-section">
            <div class="section-header">
              <span style="font-size: 18px; font-weight: 600;">Traveler ${
                index + 1
              }</span>
            </div>
            <div class="divider"></div>
  
            <div style="margin-bottom: 24px;">
              <h3 class="section-title">Personal Information</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Title</label>
                  <div class="field-value">${capitalizeFirst(t.title)}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Gender</label>
                  <div class="field-value">${capitalizeFirst(t.gender)}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">First Name</label>
                  <div class="field-value">${t.firstName}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Last Name</label>
                  <div class="field-value">${t.lastName}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Date of Birth</label>
                  <div class="field-value">${getFormatDateTable(
                    t.dateOfBirth
                  )}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Nationality</label>
                  <div class="field-value">${t.nationality}</div>
                </div>
              </div>
            </div>
  
            <div class="divider"></div>
  
            <div style="margin-bottom: 24px;">
              <h3 class="section-title">Identification Documents</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Passport Number</label>
                  <div class="field-value">${t.documentNumber}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Passport Issuing Country</label>
                  <div class="field-value">${t.issuingCountry}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Passport Expiry</label>
                <div class="field-value">${getFormatDateTable(
                  t.expirationDate
                )}</div>
              </div>
            </div>
  
            <div class="divider"></div>
  
            <div>
              <h3 class="section-title">Contact Information</h3>
              <div style="margin-bottom: 16px;">
                <div class="form-field">
                  <label class="field-label">Email</label>
                  <div class="field-value">${t.contactEmail}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Phone Number</label>
                <div class="field-value">${t.contactPhone}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
        });

        // Set the HTML content
        element.innerHTML = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flight Booking Receipt - Airvilla Charters</title>
  <style>
    /* Print-optimized styles with light colors to preserve ink */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: Arial, sans-serif;
    }
    
    body {
      background-color: white;
      color: #333;
      line-height: 1.5;
    }
    
    .container {
      max-width: 1024px;
      margin: 0 auto;
      padding: 20px;
    }
    
    @media print {
      .container {
        padding: 0;
      }
    }
    
    /* Header styles */
    .receipt-header {
      border: 1px solid #ddd;
      border-radius: 12px;
      background-color: #ffffff;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .header-row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    .company-info {
      display: flex;
      align-items: center;
    }
    
    .company-details {
      margin-left: 12px;
    }
    
    .company-name {
      font-weight: bold;
      font-size: 24px;
    }
    
    .company-address {
      color: #555;
      font-size: 14px;
    }
    
    .receipt-label {
      text-align: right;
      padding: 8px 16px;
    }
    
    .receipt-label h2 {
      font-weight: bold;
      font-size: 20px;
    }
    
    .receipt-label p {
      color: #555;
      font-size: 14px;
    }
    
    .metadata-row {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
    }
    
    .metadata-box {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .metadata-label {
      color: #555;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .metadata-value {
      font-weight: 500;
      font-size: 14px;
    }
    
    /* Grid layout */
    .content-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 32px;
    }
    
    .left-column, .right-column {
      background-color: #f8f8f8;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 24px;
      background-color: #fff;
    }
    
    .card-header {
      border-bottom: 1px solid #eee;
      padding: 16px;
    }
    
    .card-title {
      font-weight: bold;
      font-size: 20px;
    }
    
    .card-content {
      padding: 16px;
    }
    
    /* Booking information */
    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }
    
    .info-box {
      padding: 16px;
      border-radius: 8px;
      background-color: #f0f0f0;
    }
    
    .info-label {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: #555;
    }
    
    .info-label svg {
      margin-right: 8px;
    }
    
    .info-value {
      font-weight: 600;
      font-size: 18px;
    }
    
    /* Flight card */
    .flight-card {
      padding: 16px;
      border-radius: 8px;
      background-color: #f0f0f0;
      margin-bottom: 24px;
    }
    
    .flight-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .flight-type {
      padding: 4px 12px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
    }
    
    .outbound {
      background-color: #ffe6e6;
      color: #c73232;
    }
    
    .return {
      background-color: #e6f0ff;
      color: #2952a3;
    }
    
    .flight-date {
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    
    .flight-date svg {
      margin-right: 8px;
    }
    
    .flight-route {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
    }
    
    .flight-codes {
      display: flex;
      align-items: center;
    }
    
    .flight-code {
      font-size: 20px;
      font-weight: bold;
    }
    
    .flight-arrow {
      margin: 0 8px;
      color: #777;
    }
    
    .flight-airline {
      text-align: right;
      font-weight: 500;
    }
    
    .flight-meta {
      color: #777;
      font-size: 14px;
      margin-top: 4px;
    }
    
    .flight-times {
      display: flex;
      font-size: 14px;
    }
    
    .time-item {
      display: flex;
      align-items: center;
      margin-right: 16px;
    }
    
    .time-item svg {
      margin-right: 4px;
    }
    
    .time-separator {
      margin: 0 16px;
    }
    
    /* Flight separator */
    .flight-separator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 0;
      margin-bottom: 12px;
    }
    
    .separator-line {
      height: 1px;
      background-color: #ddd;
      flex-grow: 1;
    }
    
    .separator-circle {
      margin: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #eee;
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
    
    /* Passenger section */
    .passenger-section {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 1px;
      background-color: #ddd;
      width: 100%;
      margin: 12px 0;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .form-field {
      margin-bottom: 8px;
    }
    
    .field-label {
      display: block;
      font-size: 14px;
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    .field-value {
      width: 100%;
      background-color: #fff;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .section-title {
      font-weight: bold;
      margin-bottom: 12px;
      font-size: 18px;
    }
    
    /* Payment details */
    .payment-status {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .payment-status svg {
      margin-right: 8px;
      color: #22c55e;
    }
    
    .price-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .price-label {
      color: #555;
    }
    
    .price-value {
      font-weight: 500;
    }
    
    .price-total {
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      padding-top: 4px;
    }
    
    .document-section {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .document-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    
    .document-title {
      display: flex;
      align-items: center;
    }
    
    .document-title svg {
      margin-right: 8px;
      color: #c73232;
    }
    
    .document-format {
      font-size: 14px;
      color: #777;
    }
    
    .document-description {
      color: #555;
      font-size: 14px;
      margin-bottom: 12px;
    }
    
    .button-row {
      display: flex;
      gap: 12px;
    }
    
    .button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
    
    .button:hover {
      background-color: #eee;
    }
    
    .button svg {
      margin-right: 6px;
    }
    
    .support-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .support-item svg {
      margin-right: 12px;
      color: #c73232;
    }
    
    .support-link {
      color: #333;
      text-decoration: none;
    }
    
    .support-link:hover {
      color: #c73232;
    }

    /* Icon classes */
    .icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }

    .icon-sm {
      width: 14px;
      height: 14px;
    }

    .icon-lg {
      width: 20px;
      height: 20px;
    }
    
    /* Receipt Footer */
    .receipt-footer {
      margin-top: 40px;
      border-top: 1px solid #ddd;
      padding-top: 24px;
      font-size: 14px;
    }
    
    .footer-content {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .footer-logo {
      flex: 1;
    }
    
    .footer-info {
      flex: 3;
      padding-left: 20px;
    }
    
    .footer-legal {
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    
    .footer-text {
      color: #555;
      margin-bottom: 4px;
    }
    
    .footer-validation {
      flex: 2;
      text-align: right;
    }
    
    .verification-seal {
      display: inline-flex;
      align-items: center;
      background-color: #f8f8f8;
      padding: 8px 14px;
      border-radius: 4px;
      border: 1px dashed #c73232;
      margin-bottom: 12px;
    }
    
    .verification-seal span {
      font-weight: 600;
      margin-left: 8px;
      color: #c73232;
    }
    
    .footer-date {
      color: #555;
      font-size: 13px;
    }
    
    .footer-disclaimer {
      border-top: 1px solid #eee;
      padding-top: 16px;
      padding-bottom: 6px;
      font-size: 12px;
      color: #777;
      text-align: center;
    }
    
    .footer-disclaimer p {
      margin-bottom: 6px;
    }
    
    .footer-contact-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-top: 16px;
      margin-bottom: 16px;
    }
    
    .footer-contact-item {
      display: flex;
      align-items: center;
    }
    
    .footer-contact-label {
      margin-left: 8px;
      font-weight: 500;
    }
    
    .footer-badges {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .footer-badge {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      background-color: #f8f8f8;
      border-radius: 4px;
      font-size: 12px;
      color: #555;
      font-weight: 500;
    }
    
    .footer-badge svg {
      margin-right: 6px;
    }

    /* For printing */
    @media print {
      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      
      .card, .receipt-header, .left-column, .right-column {
        break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Receipt Header -->
    <div class="receipt-header">
      <div class="header-row">
        <div class="company-info">
          <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="48">
          <div class="company-details">
            <h1 class="company-name">Airvilla Charters</h1>
            <p class="company-address">Wasfi At-Tall St. 101, Amman, Jordan</p>
          </div>
        </div>
        <div class="receipt-label">
          <h2>RECEIPT</h2>
          <p>#
          ${bookingResult?.receipt?.receiptNumber}
          </p>
        </div>
      </div>
      
      <div class="metadata-row">
        <div class="metadata-box">
          <p class="metadata-label">Receipt Date</p>
          <p class="metadata-value info-value">${getFormatDateTable(
            bookingResult?.receipt?.createdAt
          )}</p>
        </div>
        <div class="metadata-box">
          <p class="metadata-label">Transaction Time</p>
          <p class="metadata-value info-value">${getFormatTime(
            bookingResult?.receipt?.createdAt
          )}</p>
        </div>
        <div class="metadata-box">
          <p class="metadata-label">Transaction ID</p>
          <p class="metadata-value info-value">${
            bookingResult?.booking?.transactionId ?? "N/A"
          }</p>
        </div>
      </div>
    </div>
    
    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Booking Information -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Booking Information</h2>
          </div>
          <div class="card-content">
            <div class="info-grid">
              <div class="info-box">
                <div class="info-label">
                  Booking ID
                </div>
                <div class="info-value">${
                  bookingResult?.eTicket?.bookingId
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Booking Reference Number
                </div>
                <div class="info-value">${
                  bookingResult?.eTicket?.eTicketNumber
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Payment Method
                </div>
                <div class="info-value">${
                  bookingResult?.booking?.source === "THIRD_PARTY"
                    ? "Airvilla Wallet"
                    : normalizePaymentMethod(
                        bookingResult?.paymentMethod ||
                          bookingResult?.payment?.paymentMethod ||
                          bookingResult?.data?.fullTicket?.payment
                            ?.paymentMethod ||
                          "N/A"
                      )
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Booking Date
                </div>
                <div class="info-value">${getFormatDateTable(
                  bookingResult?.createdAt || "N/A"
                )}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Purchased Itinerary -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Purchased Itinerary</h2>
          </div>
          <div class="card-content">
            <!-- Outbound Flight -->
            <div class="flight-card">
              <div class="flight-header">
                <div class="flight-type outbound">OUTBOUND</div>
                <div class="flight-date">
                  ${getFormatDateTable(
                    bookingResult?.booking?.ticket?.flightDate ||
                      booking?.fullTicket?.ticket?.flightDate ||
                      "N/A"
                  )}
                </div>
              </div>
              
              <div class="flight-route">
                <div>
                  <div class="flight-codes">
                    <span class="flight-code">
                      ${bookingResult?.booking?.departure?.airportCode || "N/A"}
                    </span>
                    <span class="flight-arrow">→</span>
                    <span class="flight-code">${
                      bookingResult?.booking?.arrival?.airportCode || "N/A"
                    }</span>
                  </div>
                  <div class="flight-meta">
                    ${
                      bookingResult?.booking?.ticket?.stops === "0"
                        ? "Direct Flight"
                        : `${bookingResult?.booking?.ticket?.stops} Stops`
                    } • ${bookingResult?.booking?.ticket?.duration || "N/A"}  
                  </div>
                </div>
                <div class="flight-airline">
                  ${bookingResult?.booking?.airline?.name || "N/A"} (${
          bookingResult?.booking?.flightNumber || "N/A"
        })
                </div>
              </div>
              
              <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                <div class="time-item" style="margin-right: 0;">
                  Departure: ${getFormatTime(
                    bookingResult?.booking?.departureTime ||
                      booking?.fullTicket?.ticket?.departureTime ||
                      "N/A"
                  )}
                </div>
                <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                <div class="time-item" style="margin-left: 0;">
                  Arrival: ${getFormatTime(
                    bookingResult?.booking?.arrivalTime ||
                      booking?.fullTicket?.ticket?.arrivalTime ||
                      "N/A"
                  )}
                </div>
              </div>
            </div>
            
            <!-- Flight Separator -->
            <div class="flight-separator">
              <div class="separator-line"></div>
              <div class="separator-circle">↕</div>
              <div class="separator-line"></div>
            </div>
            
            <!-- Return Flight -->
            <div class="flight-card">
              <div class="flight-header">
                <div class="flight-type return">RETURN</div>
                <div class="flight-date">
                  ${getFormatDateTable(
                    bookingResult?.booking?.ticket?.flightDate
                  )}
                </div>
              </div>
              
              <div class="flight-route">
                <div>
                  <div class="flight-codes">
                    <span class="flight-code">${
                      bookingResult?.booking?.arrivalAirport?.iataCode || "N/A"
                    }</span>
                    <span class="flight-arrow">→</span>
                    <span class="flight-code">${
                      bookingResult?.booking?.departureAirport?.iataCode ||
                      "N/A"
                    }</span>
                  </div>
                  <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            } 
                  </div>
                </div>
                <div class="flight-airline">
                  ${bookingResult?.booking?.airline?.name || "N/A"} (${
          bookingResult?.booking?.flightNumber || "N/A"
        })
                </div>
              </div>
              
              <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                <div class="time-item" style="margin-right: 0;">
                  Departure: ${getFormatTime(
                    bookingResult?.booking?.arrivalTime ||
                      booking?.fullTicket?.ticket?.arrivalTime ||
                      "N/A"
                  )}
                </div>
                <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                <div class="time-item" style="margin-left: 0;">
                  Arrival: ${getFormatTime(
                    bookingResult?.booking?.departureTime ||
                      booking?.fullTicket?.ticket?.departureTime ||
                      "N/A"
                  )}
                </div>
              </div>
            </div>
            
            <!-- Passenger & Baggage -->
            <div class="flight-card">
              <h3 class="section-title" style="margin-bottom: 8px;">Passenger & Baggage</h3>
              <div class="divider"></div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; padding-bottom: 4px;">
                <div style="font-weight: 500;">${
                  bookingResult?.passengerCounts?.adults
                } Adult</div>
                <div style="font-size: 14px; color: #555;">${capitalizeFirst(
                  bookingResult?.passengerCounts?.[0]?.travelClass || "Economy"
                )}
                <span>${baggageDisplay.length > 0 ? " • " : ""}${
          baggageDisplay.length > 0
            ? baggageDisplay.map((item) => item).join(" • ")
            : "No baggage included"
        }</span></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Traveler Details -->
        ${travelerCards}
      </div>
      
      <!-- Right Column -->
      <div class="right-column">
        <!-- Payment Details -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Payment Details</h2>
          </div>
          <div class="card-content">
            <!-- Payment confirmation card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <div class="payment-status">
                <h3 style="font-weight: 600; font-size: 16px;">Payment Successful</h3>
              </div>
              <div class="divider"></div>
              <div style="font-size: 14px;">
                <div class="price-item">
                  <span class="price-label">Transaction ID:</span>
                  <div class="info-value" style="font-size: 12px;">${
                    booking?.bookingResult?.booking?.transactionId || "N/A"
                  }</div>
                </div>
                <div>
                  <span class="price-label">Transaction Date:</span>
                  <div class="info-value" style="font-size: 12px;">${getFormatDateTable(
                    booking?.bookingResult?.booking?.transactionDate || "N/A"
                  )}</div>
                </div>
              </div>
            </div>
            
            <!-- Price breakdown card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Price Details</h3>
              <div class="divider"></div>
              
              <!-- Price details will be populated dynamically -->
              <div class="price-item">
                <span class="price-label" style="font-weight: 600; font-size: 16px;">Total</span>
                <span>${
                  booking?.bookingResult?.totalAmount ||
                  bookingResult?.bookedSeats?.[0]?.totalPrice ||
                  "0.00"
                } ${
          booking?.bookingResult?.currency ||
          bookingResult?.meta?.pricing?.departure?.currency ||
          "JOD"
        }
                </span>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Receipt Information card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Receipt Information</h3>
              <div class="divider"></div>
              <div style="margin-top: 16px;">
                <div class="price-item">
                  <span class="price-label" style="font-size: 12px;">Receipt Number:</span>
                  <div class="info-value" style="font-size: 12px;">${
                    bookingResult?.receipt?.receiptNumber
                  }</div>
                </div>
                <div>
                  <span class="price-label" style="font-size: 12px;">Booking ID:</span>
                  <div class="info-value" style="font-size: 12px;">${
                    bookingResult?.receipt?.bookingId
                  }</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Footer Section - Placed properly after content-grid but before closing container div -->
    <div class="receipt-footer">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="72">
        </div>
        <div class="footer-info">
          <p class="footer-legal">OFFICIAL RECEIPT DOCUMENT</p>
          <p class="footer-text">Thank you for choosing Airvilla Charters. This document serves as your official receipt and booking confirmation.</p>
          <p class="footer-text">Please retain this document for your records and present it when required.</p>
        </div>
        <div class="footer-validation">
          <div class="verification-seal">
            <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="#c73232" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span>Verified & Approved</span>
          </div>
          <div class="footer-date">Generated on: ${getFormatDate(
            new Date().toString()
          )} • ${getFormatTime(new Date().toString())}</div>
        </div>
      </div>
      
      <div class="footer-contact-grid">
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
          <span class="footer-contact-label"><EMAIL></span>
        </div>
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="9" y1="21" x2="9" y2="9"></line>
          </svg>
          <span class="footer-contact-label">www.airvilla-charters.travel</span>
        </div>
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
          <span class="footer-contact-label">Secure Booking Platform</span>
        </div>
      </div>
      
      <div class="footer-badges">
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          Secure Transaction
        </div>
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 6v6l4 2"></path>
          </svg>
          24/7 Support
        </div>
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
          </svg>
          IATA Certified
        </div>
      </div>
      
      <div class="footer-disclaimer">
        <p>This document contains confidential information and is intended for the named recipient only and Authorized Agents. Unauthorized reproduction is strictly prohibited.</p>
        <p> 2025 Airvilla LLC. All rights reserved.</p>
      </div>
    </div>
  </div>`;
        // Add print-specific styles
        const style = document.createElement("style");
        style.textContent = `
          @page {
            size: A4;
          }
          @media print {
            body { 
              margin: 0; 
              padding: 0; 
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print { 
              display: none !important; 
            }
            .container { 
              width: 100% !important; 
              max-width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              box-shadow: none !important;
            }
            .card {
              page-break-inside: avoid;
              break-inside: avoid;
            }
          }
          body { 
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        `;
        element.prepend(style);

        // Set options for PDF generation
        const opt = {
          margin: [10, 0, 10, 0], // top, right, bottom, left
          filename: `booking-confirmation-${
            booking.bookingResult?.id || "ticket"
          }.pdf`,
          image: {
            type: "jpeg",
            quality: 0.98,
          },
          html2canvas: {
            scale: 2,
            useCORS: true,
            logging: true,
            letterRendering: true,
            scrollY: 0,
            scrollX: 0,
            windowWidth: document.documentElement.offsetWidth,
            allowTaint: true,
          },
          jsPDF: {
            unit: "mm",
            format: "a4",
            orientation: "portrait",
          },
          pagebreak: {
            mode: ["avoid-all", "css", "legacy"],
          },
        };

        // Create a temporary container for PDF generation
        const tempContainer = document.createElement("div");
        // tempContainer.style.position = "absolute";
        // tempContainer.style.left = "-9999px";
        tempContainer.appendChild(element);
        document.body.appendChild(tempContainer);

        try {
          // Generate and download PDF
          await html2pdf()
            .set(opt)
            .from(element)
            .save()
            .then(() => {
              dispatch(
                setMsg({
                  message: "Document downloaded successfully",
                  success: true,
                })
              );
            });
        } catch (error: any) {
          console.error("Error generating PDF:", error);
          dispatch(
            setMsg({
              message: `Failed to generate PDF: ${error.message}`,
              success: false,
            })
          );
        } finally {
          // Clean up the temporary container and reset processing state
          if (document.body.contains(tempContainer)) {
            document.body.removeChild(tempContainer);
          }
          setIsDocumentProcessing({ type: null, action: null });
        }
      } else if (action === "print") {
        // Declare printWindow at the function scope level
        let printWindow: Window | null = null;

        // Get flight data from the correct structure
        const ticket = booking?.fullTicket || {};
        const bookingResult = booking?.bookingResult || {};

        // Handle API response structure when coming from tables
        const apiTicket = bookingResult?.ticket || {};

        // Determine if we're using data from Redux or from API response
        const effectiveTicket =
          Object.keys(ticket).length > 0 ? ticket : apiTicket;

        const segments = effectiveTicket?.segments || [];
        // Get the first segment for flight details
        const firstSegment = segments[0] || {};

        const baggages = bookingResult?.ticket?.flightClasses?.[0];

        // Get baggage allowance from flight class
        const carryOnAllowed =
          firstSegment?.flightClass?.carryOnAllowed ??
          baggages?.carryOnAllowed ??
          0;
        const carryOnWeight =
          firstSegment?.flightClass?.carryOnWeight ??
          baggages?.carryOnWeight ??
          0;
        const checkedAllowed =
          firstSegment?.flightClass?.checkedAllowed ??
          baggages?.checkedAllowed ??
          0;
        const checkedWeight =
          firstSegment?.flightClass?.checkedWeight ??
          baggages?.checkedWeight ??
          0;

        // Format baggage display
        const baggageDisplay = [];
        if (checkedAllowed > 0) {
          baggageDisplay.push(
            `${checkedAllowed} x ${checkedWeight} kg Checked`
          );
        }
        if (carryOnAllowed > 0) {
          baggageDisplay.push(
            `${carryOnAllowed} x ${carryOnWeight} kg Carry-on`
          );
        }

        const travelerDataArr = Array.isArray(booking.travelerData)
          ? booking.travelerData.map((item: any) => item.traveler || {})
          : Array.isArray(booking.travelers)
          ? booking.travelers.map((item: any) => item.traveler || {})
          : [];

        const isRoundTrip =
          (booking?.tripType === "ROUND_TRIP" ||
            bookingResult?.tripType === "ROUND_TRIP" ||
            booking?.fullTicket?.tripType === "ROUND_TRIP" ||
            bookingResult?.meta?.tripType === "ROUND_TRIP") &&
          Array.isArray(booking?.ticket);

        const flightClasses =
          bookingConfirmation?.ticket?.ticket?.flightClasses ||
          bookingConfirmation?.fullTicket?.ticket?.flightClasses ||
          effectiveTicket?.flightClasses ||
          [];
        const returnFlightClasses =
          bookingConfirmation?.ticket?.ticket?.returnFlightClasses ||
          bookingConfirmation?.fullTicket?.ticket?.returnFlightClasses ||
          effectiveTicket?.returnFlightClasses ||
          [];
        const flightClass = flightClasses[0] || {};
        const returnFlightClass = returnFlightClasses[0] || {};
        const price = flightClass?.price || {};
        const returnPrice = returnFlightClass?.price || {};

        // Get passenger counts from booking data or default to 1 adult
        const passengerCounts = booking?.travelerData || {};

        const calculatedPassengerCounts = (() => {
          // Calculate from booking travelers if available
          const travelers = booking?.bookingResult?.travelers || [];

          if (travelers.length > 0) {
            const now = new Date();

            const getAge = (birthDate: string) => {
              const birth = new Date(birthDate);
              let age = now.getFullYear() - birth.getFullYear();
              const monthDiff = now.getMonth() - birth.getMonth();
              if (
                monthDiff < 0 ||
                (monthDiff === 0 && now.getDate() < birth.getDate())
              ) {
                age--;
              }
              return age;
            };

            let adults = 0;
            let children = 0;
            let infants = 0;

            travelers.forEach((traveler: any) => {
              const dob = traveler?.traveler?.dateOfBirth;
              if (!dob) {
                // If no DOB, assume adult
                adults++;
                return;
              }

              const age = getAge(dob);
              if (age < 2) {
                infants++;
              } else if (age < 12) {
                children++;
              } else {
                adults++;
              }
            });

            return {
              adults,
              children,
              infants,
              travelClass:
                booking?.bookingResult?.travelClass ||
                booking?.fullTicket?.ticket?.flightClasses[0]?.type ||
                "Economy",
            };
          }

          // Fallback to 1 adult by default
          return {
            adults: 1,
            children: 0,
            infants: 0,
            travelClass: "Economy",
          };
          // }, [passengerCounts, booking]);
        })();

        // Check if return ticket exists
        const hasReturnTicket = returnFlightClasses.length > 0;

        // Get currency and prices
        const currency = price?.currency || "JOD";
        const adultPrice = price?.adult || 0;
        const childPrice = price?.child || 0;
        const infantPrice = price?.infant || 0;
        const returnAdultPrice = returnPrice?.adult || 0;
        const returnChildPrice = returnPrice?.child || 0;
        const returnInfantPrice = returnPrice?.infant || 0;
        const taxRate = price?.tax / 100 || 0;

        // Calculate fares based on passenger counts
        const adultFare = adultPrice * (calculatedPassengerCounts?.adults || 0);
        const childFare =
          childPrice * (calculatedPassengerCounts?.children || 0);
        const infantFare =
          infantPrice * (calculatedPassengerCounts?.infants || 0);
        const returnAdultFare =
          returnAdultPrice * (calculatedPassengerCounts?.adults || 0);
        const returnChildFare =
          returnChildPrice * (calculatedPassengerCounts?.children || 0);
        const returnInfantFare =
          returnInfantPrice * (calculatedPassengerCounts?.infants || 0);

        // Calculate base fares
        const outboundBaseFare = adultFare + childFare + infantFare;
        const returnBaseFare =
          returnAdultFare + returnChildFare + returnInfantFare;

        // Calculate taxes (for both outbound and return if applicable)
        const totalBaseFare =
          outboundBaseFare + (hasReturnTicket ? returnBaseFare : 0);
        const taxes = totalBaseFare * taxRate;

        // Fixed transaction fee
        const transactionFee = 0;
        // Calculate total with fallback to bookedSeats totalPrice if available
        const total =
          totalBaseFare + taxes + transactionFee ||
          (bookingResult?.bookedSeats && bookingResult.bookedSeats.length > 0
            ? parseFloat(bookingResult.bookedSeats[0].totalPrice)
            : 0);

        const outboundFareBreakdown = [
          {
            label: "Adult",
            count: calculatedPassengerCounts.adults,
            perPersonValue: adultPrice,
            value: adultFare,
          },
          {
            label: "Child",
            count: calculatedPassengerCounts.children,
            perPersonValue: childPrice,
            value: childFare,
          },
          {
            label: "Infant",
            count: calculatedPassengerCounts.infants,
            perPersonValue: infantPrice,
            value: infantFare,
          },
        ].filter((i) => i.count > 0);

        const returnFareBreakdown = hasReturnTicket
          ? [
              {
                label: "Adult",
                count: calculatedPassengerCounts.adults,
                perPersonValue: returnAdultPrice,
                value: returnAdultFare,
              },
              {
                label: "Child",
                count: calculatedPassengerCounts.children,
                perPersonValue: returnChildPrice,
                value: returnChildFare,
              },
              {
                label: "Infant",
                count: calculatedPassengerCounts.infants,
                perPersonValue: returnInfantPrice,
                value: returnInfantFare,
              },
            ].filter((i) => i.count > 0)
          : [];

        let travelerCards = "";

        try {
          // Open the print window
          printWindow = window.open("", "_blank");

          if (!printWindow) {
            throw new Error(
              "Could not open print window. Please allow popups for this site."
            );
          }

          // Get the container element that wraps the booking confirmation content
          // const container = document.querySelector(".container");
          for (const selector of containerSelectors) {
            container = document.querySelector(selector);
            if (container) break;
          }

          if (!container) {
            throw new Error("Booking details container not found");
          }

          // Clone the container and its styles
          const element = container.cloneNode(true) as HTMLElement;
          element.style.padding = "20px"; // Add some padding for better print layout

          // Add print-specific styles
          const style = document.createElement("style");
          style.textContent = `
            @page {
              size: A4;
              margin: 10mm;
            }
            body { 
              margin: 0; 
              padding: 20px; 
              font-family: Arial, sans-serif;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print { 
              display: none !important; 
            }
            .container { 
              width: 100% !important; 
              max-width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              box-shadow: none !important;
            }
            .card {
              page-break-inside: avoid;
              break-inside: avoid;
              margin-bottom: 20px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              overflow: hidden;
            }
            .card-header {
              border-bottom: 1px solid #eee;
              padding: 16px;
            }
            .card-title {
              margin: 0;
              font-size: 20px;
              font-weight: 600;
            }
            .card-content {
              padding: 16px;
            }
            @media print {
              body { 
                margin: 0; 
                padding: 0; 
              }
              .no-print { 
                display: none !important; 
              }
              .container { 
                width: 100% !important; 
                margin: 0 !important;
                padding: 0 !important;
              }
              .card {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 10mm;
              }
              .card:last-child {
                margin-bottom: 0;
              }
            }
          `;

          // Create a new document for printing
          const printDocument = printWindow.document;
          printDocument.open();
          printDocument.write(`<!DOCTYPE html>
            <html>
              <head>
                <title>Booking Confirmation - ${
                  booking.bookingResult?.id || ""
                }</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>${style.textContent}</style>
              </head>
              <body>
                ${element.outerHTML}
                <script>
                  // Close the print window after printing
                  window.onload = function() {
                    setTimeout(function() {
                      window.print();
                      setTimeout(function() {
                        window.close();
                      }, 100);
                    }, 200);
                  };
                </script>
              </body>
            </html>
          `);

          printDocument.close();
          printWindow.focus();
        } catch (error: any) {
          console.error("Error preparing print:", error);
          dispatch(
            setMsg({
              message: `Failed to prepare print: ${error.message}`,
              success: false,
            })
          );
          setIsDocumentProcessing({ type: null, action: null });
        }

        travelerDataArr?.forEach((traveler: any, index: number) => {
          const t =
            traveler !== undefined ? traveler : traveler?.traveler || {};

          travelerCards += `
        <div class="card-content">
          <div class="passenger-section">
            <div class="section-header">
              <span style="font-size: 18px; font-weight: 600;">Traveler ${
                index + 1
              }: ${t?.firstName || "N/A"} ${t?.lastName || "N/A"}
              </span>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Personal Information</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Title</label>
                  <div class="field-value">${capitalizeFirst(t.title)}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Gender</label>
                  <div class="field-value">${capitalizeFirst(t.gender)}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">First Name</label>
                  <div class="field-value">${t.firstName}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Last Name</label>
                  <div class="field-value">${t.lastName}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Date of Birth</label>
                  <div class="field-value">${getFormatDateTable(
                    t.dateOfBirth
                  )}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Nationality</label>
                  <div class="field-value">${t.nationality}</div>
                </div>
              </div>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Identification Documents</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Passport Number</label>
                  <div class="field-value">${t.documentNumber}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Passport Issuing Country</label>
                  <div class="field-value">${t.issuingCountry}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Passport Expiry</label>
                <div class="field-value">${getFormatDateTable(
                  t.expirationDate
                )}</div>
              </div>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Contact Information</h3>
              <div style="margin-bottom: 16px;">
                <div class="form-field">
                  <label class="field-label">Email</label>
                  <div class="field-value">${t.contactEmail}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Phone Number</label>
                <div class="field-value">${t.contactPhone}</div>
              </div>
            </div>
          </div>
        </div>
    `;
        });

        if (!printWindow) {
          throw new Error("Print window is not available");
        }

        // Write the closing HTML and close the document
        printWindow.document.write(`<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Booking Receipt - Airvilla Charters</title>
    <style>
      /* Print-optimized styles with light colors to preserve ink */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: Arial, sans-serif;
      }
      
      body {
        background-color: white;
        color: #333;
        line-height: 1.5;
      }
      
      .container {
        max-width: 1024px;
        margin: 0 auto;
        padding: 20px;
      }
      
      @media print {
        .container {
          padding: 0;
        }
      }
      
      /* Header styles */
      .receipt-header {
        border: 1px solid #ddd;
        border-radius: 12px;
        background-color: #ffffff;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      
      .header-row {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
      }
      
      .company-info {
        display: flex;
        align-items: center;
      }
      
      .company-details {
        margin-left: 12px;
      }
      
      .company-name {
        font-weight: bold;
        font-size: 24px;
      }
      
      .company-address {
        color: #555;
        font-size: 14px;
      }
      
      .receipt-label {
        text-align: right;
        padding: 8px 16px;
      }
      
      .receipt-label h2 {
        font-weight: bold;
        font-size: 20px;
      }
      
      .receipt-label p {
        color: #555;
        font-size: 14px;
      }
      
      .metadata-row {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
      }
      
      .metadata-box {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .metadata-label {
        color: #555;
        font-size: 14px;
        margin-bottom: 8px;
      }
      
      .metadata-value {
        font-weight: 500;
        font-size: 14px;
      }
      
      /* Grid layout */
      .content-grid {
        display: flex;
        flex-direction: column-reverse;
        gap: 24px;
        width: 100%;
        max-width: 100%;
      }
      
      .left-column, .right-column {
        background-color: #f8f8f8;
        border-radius: 12px;
        padding: 48px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      
      .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 8px;
        background-color: #fff;
      }
      
      .card-header {
        border-bottom: 1px solid #eee;
        padding: 16px;
      }
      
      .card-title {
        font-weight: bold;
        font-size: 20px;
      }
      
      .card-content {
        padding: 16px;
      }
      
      /* Booking information */
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
      }
      
      .info-box {
        padding: 16px;
        border-radius: 8px;
        background-color: #f0f0f0;
      }
      
      .info-label {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: #555;
      }
      
      .info-label svg {
        margin-right: 8px;
      }
      
      .info-value {
        font-weight: 600;
        font-size: 18px;
      }
      
      /* Flight card */
      .flight-card {
        padding: 16px;
        border-radius: 8px;
        background-color: #f0f0f0;
        margin-bottom: 8px;
      }
      
      .flight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .flight-type {
        padding: 4px 12px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
      }
      
      .outbound {
        background-color: #ffe6e6;
        color: #c73232;
      }
      
      .return {
        background-color: #e6f0ff;
        color: #2952a3;
      }
      
      .flight-date {
        font-weight: 500;
        display: flex;
        align-items: center;
      }
      
      .flight-date svg {
        margin-right: 8px;
      }
      
      .flight-route {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }
      
      .flight-codes {
        display: flex;
        align-items: center;
      }
      
      .flight-code {
        font-size: 20px;
        font-weight: bold;
      }
      
      .flight-arrow {
        margin: 0 8px;
        color: #777;
      }
      
      .flight-airline {
        text-align: right;
        font-weight: 500;
      }
      
      .flight-meta {
        color: #777;
        font-size: 14px;
        margin-top: 4px;
      }
      
      .flight-times {
        display: flex;
        font-size: 14px;
      }
      
      .time-item {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      
      .time-item svg {
        margin-right: 4px;
      }
      
      .time-separator {
        margin: 0 16px;
      }
      
      /* Flight separator */
      .flight-separator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 0;
        margin-bottom: 12px;
      }
      
      .separator-line {
        height: 1px;
        background-color: #ddd;
        flex-grow: 1;
      }
      
      .separator-circle {
        margin: 0 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #eee;
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }
      
      /* Passenger section */
      .passenger-section {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .divider {
        height: 1px;
        background-color: #ddd;
        width: 100%;
        margin: 12px 0;
      }
      
      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .form-field {
        margin-bottom: 8px;
      }
      
      .field-label {
        display: block;
        font-size: 14px;
        margin-bottom: 4px;
        font-weight: 500;
      }
      
      .field-value {
        width: 100%;
        background-color: #eee;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
      }
      
      .section-title {
        font-weight: bold;
        margin-bottom: 12px;
        font-size: 18px;
      }
      
      /* Payment details */
      .payment-status {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .payment-status svg {
        margin-right: 8px;
        color: #22c55e;
      }
      
      .price-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      
      .price-label {
        color: #555;
      }
      
      .price-value {
        font-weight: 500;
      }
      
      .price-total {
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        padding-top: 4px;
      }
      
      .document-section {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .document-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
      }
      
      .document-title {
        display: flex;
        align-items: center;
      }
      
      .document-title svg {
        margin-right: 8px;
        color: #c73232;
      }
      
      .document-format {
        font-size: 14px;
        color: #777;
      }
      
      .document-description {
        color: #555;
        font-size: 14px;
        margin-bottom: 12px;
      }
      
      .button-row {
        display: flex;
        gap: 12px;
      }
      
      .button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        background-color: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
      }
      
      .button:hover {
        background-color: #eee;
      }
      
      .button svg {
        margin-right: 6px;
      }
      
      .support-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
      }
      
      .support-item svg {
        margin-right: 12px;
        color: #c73232;
      }
      
      .support-link {
        color: #333;
        text-decoration: none;
      }
      
      .support-link:hover {
        color: #c73232;
      }
  
      /* Icon classes */
      .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin-right: 4px;
      }
  
      .icon-sm {
        width: 14px;
        height: 14px;
      }
  
      .icon-lg {
        width: 20px;
        height: 20px;
      }
      
      /* Receipt Footer */
      .receipt-footer {
        margin-top: 40px;
        border-top: 1px solid #ddd;
        padding-top: 24px;
        font-size: 14px;
      }
      
      .footer-content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      
      .footer-logo {
        flex: 1;
      }
      
      .footer-info {
        flex: 3;
        padding-left: 20px;
      }
      
      .footer-legal {
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }
      
      .footer-text {
        color: #555;
        margin-bottom: 4px;
      }
      
      .footer-validation {
        flex: 2;
        text-align: right;
      }
      
      .verification-seal {
        display: inline-flex;
        align-items: center;
        background-color: #f8f8f8;
        padding: 8px 14px;
        border-radius: 4px;
        border: 1px dashed #c73232;
        margin-bottom: 12px;
      }
      
      .verification-seal span {
        font-weight: 600;
        margin-left: 8px;
        color: #c73232;
      }
      
      .footer-date {
        color: #555;
        font-size: 13px;
      }
      
      .footer-disclaimer {
        border-top: 1px solid #eee;
        padding-top: 16px;
        padding-bottom: 6px;
        font-size: 12px;
        color: #777;
        text-align: center;
      }
      
      .footer-disclaimer p {
        margin-bottom: 6px;
      }
      
      .footer-contact-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin-top: 16px;
        margin-bottom: 16px;
      }
      
      .footer-contact-item {
        display: flex;
        align-items: center;
      }
      
      .footer-contact-label {
        margin-left: 8px;
        font-weight: 500;
      }
      
      .footer-badges {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .footer-badge {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        background-color: #f8f8f8;
        border-radius: 4px;
        font-size: 12px;
        color: #555;
        font-weight: 500;
      }
      
      .footer-badge svg {
        margin-right: 6px;
      }
  
      /* For printing */
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        
        .card, .receipt-header, .left-column, .right-column {
          break-inside: avoid;
        }
      }
    </style>
  </head>
  <body>
    <div class="container print-container">
      <!-- Receipt Header -->
      <div class="receipt-header">
        <div class="header-row">
          <div class="company-info">
            <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="48">
            <div class="company-details">
              <h1 class="company-name">Airvilla Charters</h1>
              <p class="company-address">Wasfi At-Tall St. 101, Amman, Jordan</p>
            </div>
          </div>
          <div class="receipt-label">
            <h2>ELECTRONIC TICKET RECEIPT</h2>
            <p>#
            ${
              bookingResult?.Receipt?.[0]?.receiptNumber ||
              bookingResult?.receipt?.receiptNumber
            }
            </p>
          </div>
        </div>
        
        <div class="metadata-row">
          <div class="metadata-box">
            <p class="metadata-label">Receipt Date</p>
            <p class="metadata-value info-value">${getFormatDateTable(
              bookingResult?.Receipt?.[0]?.createdAt ||
                bookingResult?.receipt?.createdAt ||
                booking?.fullTicket?.createdAt
            )}</p>
          </div>
          <div class="metadata-box">
            <p class="metadata-label">Transaction Time</p>
            <p class="metadata-value info-value">${getFormatTime(
              booking?.fullTicket?.transactionDate ||
                bookingResult?.Receipt?.[0]?.createdAt ||
                bookingResult?.receipt?.createdAt
            )}</p>
          </div>
          <div class="metadata-box">
            <p class="metadata-label">Transaction ID</p>
            <p class="metadata-value info-value">${
              bookingResult?.booking?.transactionId ||
              booking?.fullTicket?.transactionId ||
              "N/A"
            }</p>
          </div>
        </div>
      </div>
      
      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Left Column -->
        <div class="left-column">
          <!-- Booking Information -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Booking Information</h2>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-box">
                  <div class="info-label">
                    Booking ID
                  </div>
                  <div class="info-value">${
                    bookingResult?.eTickets?.[0]?.bookingId
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Booking Reference Number
                  </div>
                  <div class="info-value">${
                    bookingResult?.eTickets?.[0]?.eTicketNumber
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Payment Method
                  </div>
                  <div class="info-value">${
                    bookingResult?.booking?.source === "THIRD_PARTY" ||
                    booking?.bookingType === "THIRD_PARTY" ||
                    bookingResult?.source === "THIRD_PARTY"
                      ? "Airvilla Wallet"
                      : normalizePaymentMethod(
                          bookingResult?.paymentMethod ||
                            bookingResult?.payment?.paymentMethod ||
                            bookingResult?.data?.fullTicket?.payment
                              ?.paymentMethod ||
                            "N/A"
                        )
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Booking Date
                  </div>
                  <div class="info-value">${getFormatDateTable(
                    bookingResult?.createdAt || "N/A"
                  )}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Purchased Itinerary -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Purchased Itinerary</h2>
            </div>
            <div class="card-content">
              <!-- Outbound Flight -->
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-type outbound">OUTBOUND</div>
                  <div class="flight-date">
                    ${getFormatDateTable(
                      booking?.fullTicket?.ticket?.flightDate ||
                        bookingResult?.booking?.ticket?.flightDate
                    )}
                  </div>
                </div>
                
                <div class="flight-route">
                  <div>
                    <div class="flight-codes">
                      <span class="flight-code">
                        ${
                          bookingResult?.booking?.departure?.airportCode ||
                          bookingResult?.meta?.departure?.arrivalAirport ||
                          booking?.fullTicket?.meta?.departure
                            ?.departureAirport ||
                          "N/A"
                        }
                      </span>
                      <span class="flight-arrow">→</span>
                      <span class="flight-code">${
                        bookingResult?.booking?.arrival?.airportCode ||
                        bookingResult?.meta?.return?.arrivalAirport ||
                        booking?.fullTicket?.meta?.departure?.arrivalAirport ||
                        "N/A"
                      }</span>
                    </div>
                    <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            } 
                    </div>
                  </div>
                  <div class="flight-airline">
                    ${
                      booking?.ticket?.ticket?.segments?.[0]?.carrier ||
                      booking?.fullTicket?.ticket?.segments?.[0]?.carrier ||
                      bookingResult?.booking?.airline?.name ||
                      bookingResult?.booking?.airline?.name ||
                      bookingResult?.meta?.departure?.carrier ||
                      "N/A"
                    } (${
          booking?.ticket?.ticket?.segments?.[0]?.flightNumber ||
          booking?.fullTicket?.ticket?.segments?.[0]?.flightNumber ||
          bookingResult?.booking?.flightNumber ||
          bookingResult?.meta?.departure?.flightNumber ||
          "N/A"
        })
                  </div>
                </div>
                
                <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                  <div class="time-item" style="margin-right: 0;">
                    Departure: ${getFormatTime(
                      bookingResult?.booking?.departureTime ||
                        booking?.fullTicket?.ticket?.departureTime ||
                        "N/A"
                    )}
                  </div>
                  <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                  <div class="time-item" style="margin-left: 0;">
                    Arrival: ${getFormatTime(
                      bookingResult?.booking?.arrivalTime ||
                        booking?.fullTicket?.ticket?.arrivalTime ||
                        "N/A"
                    )}
                  </div>
                </div>
              </div>
              
              ${
                isRoundTrip
                  ? `
              <!-- Flight Separator -->
              <div class="flight-separator">
                <div class="separator-line"></div>
                <div class="separator-circle">↕</div>
                <div class="separator-line"></div>
              </div>
              
              <!-- Return Flight -->
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-type return">RETURN</div>
                  <div class="flight-date">
                    ${getFormatDateTable(
                      bookingResult?.booking?.ticket?.flightDate
                    )}
                  </div>
                </div>
                
                <div class="flight-route">
                  <div>
                    <div class="flight-codes">
                      <span class="flight-code">${
                        bookingResult?.booking?.arrivalAirport?.iataCode ||
                        "N/A"
                      }</span>
                      <span class="flight-arrow">→</span>
                      <span class="flight-code">${
                        bookingResult?.booking?.departureAirport?.iataCode ||
                        "N/A"
                      }</span>
                    </div>
                    <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            }
                    </div>
                  </div>
                  <div class="flight-airline">
                    ${bookingResult?.booking?.airline?.name || "N/A"} (${
                      bookingResult?.booking?.flightNumber || "N/A"
                    })
                  </div>
                </div>
                
                <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                  <div class="time-item" style="margin-right: 0;">
                    Departure: ${getFormatTime(
                      bookingResult?.booking?.arrivalTime ||
                        booking?.fullTicket?.ticket?.arrivalTime ||
                        "N/A"
                    )}
                  </div>
                  <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                  <div class="time-item" style="margin-left: 0;">
                    Arrival: ${getFormatTime(
                      bookingResult?.booking?.departureTime ||
                        booking?.fullTicket?.ticket?.departureTime ||
                        "N/A"
                    )}
                  </div>
                </div>
              </div>`
                  : ""
              }
              
              <!-- Passenger & Baggage -->
              <div class="flight-card">
                <h3 class="section-title" style="margin-bottom: 8px;">Passenger & Baggage</h3>
                <div class="divider"></div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; padding-bottom: 4px;">
                  <div style="font-weight: 500;">${
                    calculatedPassengerCounts.adults > 0
                      ? `${calculatedPassengerCounts.adults} ${
                          calculatedPassengerCounts.adults === 1
                            ? "Adult"
                            : "Adults"
                        }`
                      : ""
                  }
                ${
                  calculatedPassengerCounts.children > 0
                    ? ` • ${calculatedPassengerCounts.children} ${
                        calculatedPassengerCounts.children === 1
                          ? "Child"
                          : "Children"
                      }`
                    : ""
                }
                ${
                  calculatedPassengerCounts.infants > 0
                    ? ` • ${calculatedPassengerCounts.infants} ${
                        calculatedPassengerCounts.infants === 1
                          ? "Infant"
                          : "Infants"
                      }`
                    : ""
                }</div>
                  <div style="font-size: 14px; color: #555;">
                   <p style="capitalize">
                  ${
                    capitalizeFirst(
                      bookingResult?.passengerCounts?.[0]?.travelClass
                    ) ||
                    capitalizeFirst(flightClass?.type) ||
                    (bookingResult?.bookedSeats &&
                    bookingResult.bookedSeats.length > 0
                      ? capitalizeFirst(
                          bookingResult.bookedSeats[0].flightClass
                        )
                      : "Economy")
                  }
                  ${
                    baggageDisplay.length > 0
                      ? ` • ${baggageDisplay.join(" • ")}`
                      : " • No baggage included"
                  }
                </p>
                </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Traveler Details -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Traveler Details</h2>
            <div class="dark:text-white text-gray-700 text-sm">
              ${travelers.length}
              ${travelers.length === 1 ? "Traveler" : "Travelers"}
            </div>
          </div>
          ${travelerCards}
          </div>
        </div>
        
        <!-- Right Column -->
        <div class="right-column">
          <!-- Payment Details -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Payment Details</h2>
            </div>
            <div class="card-content">
              <!-- Payment confirmation card -->
              <div class="info-box" style="margin-bottom: 16px;">
                <div class="payment-status">
                  <h3 style="font-weight: 600; font-size: 16px;">Payment Successful</h3>
                </div>
                <div class="divider"></div>
                <div style="font-size: 18px; display: grid; grid-template-columns: 3fr 1fr;">
                <section>  
                <div>
                    <span class="price-label" style="font-size: 14px;">Transaction ID:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      booking?.transactionId ||
                      bookingResult?.transactionId ||
                      booking?.bookingResult?.booking?.transactionId ||
                      bookingResult?.booking?.transactionId ||
                      booking?.fullTicket?.transactionId ||
                      "N/A"
                    }</div>
                  </div>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Transaction Date:</span>
                    <div class="price-value" style="font-size: 16px;">${getFormatDateTable(
                      booking?.timerStartedAt ||
                        booking?.bookingResult?.booking?.transactionDate ||
                        bookingResult?.booking?.transactionDate ||
                        booking?.fullTicket?.transactionDate ||
                        bookingResult?.transactionDate ||
                        bookingResult?.payment?.createdAt ||
                        "N/A"
                    )}</div>
                  </div>
                  </section>
                  <section>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Receipt Number:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      booking?.Receipt?.[0]?.receiptNumber ||
                      bookingResult?.receipt?.receiptNumber ||
                      bookingResult?.Receipt?.[0]?.receiptNumber
                    }</div>
                  </div>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Booking ID:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      booking?.Receipt?.[0]?.bookingId ||
                      bookingResult?.receipt?.bookingId ||
                      bookingResult?.Receipt?.[0]?.bookingId
                    }</div>
                  </div>
                  </section>
                </div>
              </div>
              
                <!-- Price breakdown card -->
                <div class="info-box" style="margin-bottom: 16px;">
                  <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Price Details</h3>
                  <div class="divider"></div>
                  
                  <!-- Price details will be populated dynamically -->
                  <span class="flight-type outbound" style="display: inline-block; margin-bottom: 8px;">OUTBOUND</span>
                  <div className="block mb-3">
                  ${outboundFareBreakdown
                    .map(
                      (item) => `
                    <div class="price-item">
                      <span class="price-label">
                        ${item.count} ${item.label}${
                        item.count > 1 ? "s" : ""
                      } ×
                        ${item.perPersonValue.toFixed(2)} ${currency}
                      </span>
                      <span>${item.value.toFixed(2)} ${currency}</span>
                    </div>`
                    )
                    .join("")}
                  </div>
                  ${
                    isRoundTrip
                      ? `<span class="flight-type return" style="display: inline-block; margin-bottom: 8px;">RETURN ddd</span>
                  <div class="price-item">
                  <span class="price-label">Base Fare</span>
                  <span>${
                    returnBaseFare >= 0
                      ? `${returnBaseFare.toFixed(2)} ${currency}`
                      : "N/A"
                  }</span>
                  </div>`
                      : ""
                  }
                  <div class="divider"></div>
                  <div class="price-item">
                  <span class="price-label">Taxes</span>
                  <span>${
                    taxes >= 0 ? `${taxes.toFixed(2)} ${currency}` : "N/A"
                  }</span>
                  </div>
                  <div class="price-item">
                  <span class="price-label">Transaction Fee</span>
                  <span>${
                    transactionFee >= 0
                      ? `${transactionFee.toFixed(2)} ${currency}`
                      : "N/A"
                  }</span>
                  </div>
                  <div class="divider"></div>
                  <div class="price-item"  style="font-weight: 600; font-size: 16px;">
                    <span class="price-label">Total</span>
                    <span>${
                      total >= 0
                        ? `${total.toFixed(2)} ${currency}`
                        : `
                      Number(
                        bookingResult?.bookedSeats?.[0]?.totalPrice ||
                          booking?.bookingResult?.totalAmount
                      ).toFixed(2) || "0.00" 
                    
                      ${
                        booking?.bookingResult?.currency ||
                        bookingResult?.meta?.pricing?.departure?.currency ||
                        "JOD"
                      }`
                    }
                    </span>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer Section - Placed properly after content-grid but before closing container div -->
      <div class="receipt-footer">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="72">
          </div>
          <div class="footer-info">
            <p class="footer-legal">ELECTRONIC TICKET RECEIPT</p>
            <p class="footer-text">Thank you for choosing Airvilla Charters. This document serves as your official receipt and booking confirmation.</p>
            <p class="footer-text">Please retain this document for your records and present it when required.</p>
          </div>
          <div class="footer-validation">
            <div class="verification-seal">
              <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="#c73232" stroke-width="2">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              <span>Verified & Approved</span>
            </div>
            <div class="footer-date">Generated on: ${getFormatDate(
              new Date().toString()
            )} • ${getFormatTime(new Date().toString())}</div>
          </div>
        </div>
        
        <div class="footer-contact-grid">
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <span class="footer-contact-label"><EMAIL></span>
          </div>
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
            <span class="footer-contact-label">www.airvilla-charters.travel</span>
          </div>
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            <span class="footer-contact-label">Secure Booking Platform</span>
          </div>
        </div>
        
        <div class="footer-badges">
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            Secure Transaction
          </div>
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
            24/7 Support
          </div>
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            IATA Certified
          </div>
        </div>
        
        <div class="footer-disclaimer">
          <p>This document contains confidential information and is intended for the named recipient only and Authorized Agents. Unauthorized reproduction is strictly prohibited.</p>
          <p> 2025 Airvilla LLC. All rights reserved.</p>
        </div>
      </div>
    </div>`);
      }
    } catch (error) {
      console.error("Error generating receipt:", error);
      dispatch(
        setMsg({
          message: `Error generating document: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
          success: false,
        })
      );
    } finally {
      setIsDocumentProcessing({ type: null, action: null });
    }
    return; // Explicitly return void
  };

  return (
    <div className="dark:text-white max-w-7xl mx-auto">
      <div className="w-full">
        <div className="mb-5">
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            My Sales
          </h1>
        </div>

        <FilterCard
          filters={filters}
          setFilters={setFilters}
          onFilterChange={handleTabChange}
          searchQuery={searchQuery}
          onSearchChange={(e) => setSearchInput(e.target.value)}
        />

        {loading ? (
          <div className="dark:bg-gray-800 rounded-lg p-8 text-center">
            <p className="text-lg">Loading sales data...</p>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg">
            <div className="bg-white dark:bg-gray-800 py-4 px-6 flex items-center justify-between border-b border-gray-300 dark:border-gray-700">
              <TabNavigation
                activeTab={activeTab}
                onTabChange={handleTabChange}
                salesCounts={salesCounts}
                filteredSales={filteredSales.length}
              />
              <div className="flex items-center space-x-4">
                <button
                  className="flex items-center space-x-2 bg-red-500 text-white text-sm hover:bg-red-600 transition duration-300 px-3 py-2 rounded-lg"
                  onClick={handleExportSales}
                >
                  <Download size={16} />
                  <span>Export</span>
                </button>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search sales..."
                    className="bg-gray-200 dark:bg-gray-700 dark:text-white rounded-lg py-2 pl-10 pr-4 w-64 mx-auto md:w-64 border-none focus:outline-none focus:ring-2 focus:ring-red-500 hover:ring-2 hover:ring-red-500 hover:border-red-500"
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                  />
                  <Search
                    className="absolute left-3 top-2.5 text-gray-400"
                    size={20}
                  />
                </div>
              </div>
            </div>
            <SalesTable
              sales={filteredSales}
              // onEditBooking={() => handleDocumentAction("receipt", "print")}
              onEditBooking={(bookingId) => handleEditBooking(bookingId)}
              // onEditBooking={(bookingId) => handleDocumentAction(bookingId, "receipt", "print")}
              hasMore={hasMore}
              loadingMore={loadingMore}
              observerRef={ref}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default function MySales() {
  // check user's access
  const loading = useAgencyUserAuth();

  if (loading) {
    return <ProgressLoading />;
  }
  return (
    <div className="pt-8 w-full max-w-[96rem] mx-auto">
      <MySalesList />
    </div>
  );
}
