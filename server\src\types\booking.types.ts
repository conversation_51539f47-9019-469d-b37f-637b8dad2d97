import {
  BookingType,
  BookingSource,
  CustomerDocumentType,
  CustomerInfoTitle,
} from "@prisma/client";

// Data for creating a single traveler associated with a booking
export interface BookingTravelerInput {
  // Link to existing traveler or create new
  travelerId?: string; // Use if traveler already exists in the system

  // Or provide details to create a new traveler
  title: CustomerInfoTitle;
  firstName: string;
  lastName: string;
  nationality: string;
  dateOfBirth: string; // Assuming ISO 8601 string format e.g., 'YYYY-MM-DD'
  gender: string; // Consider defining a Gender enum if needed
  documentType: CustomerDocumentType;
  documentNumber: string;
  issuingCountry: string;
  expirationDate?: string; // Assuming ISO 8601 string format e.g., 'YYYY-MM-DD'
  contactEmail?: string;
  contactPhone?: string;
  isPrimary: boolean;
  specialNeeds?: string;
}

// Request body for creating a new internal booking
export interface CreateInternalBookingDto {
  ticketId: string;
  returnTicketId?: string;
  userId: string;
  agencyAgentId: string;
  teamMemberId: string;
  travelers?: BookingTravelerInput[];
  seats: SeatInput[];
  returnSeats?: SeatInput[]; // Added for round-trip bookings
  type: BookingType;
  source: BookingSource;
  tripType: "ONE_WAY" | "ROUND_TRIP";
}

export interface SeatInput {
  seatNumber: string;
  flightClass: string;
  totalPrice: number;
  travelerId?: string | null;
}

// Indicates if booking actions like payment completion or seat release are allowed
export interface BookingStatusDto {
  status: string;
  isPaid: boolean;
  actionsAllowed: boolean;
  expiresAt?: Date;
}

export interface BookingMeta {
  agentName?: string;
  buyerAgentName?: string;
  [key: string]: any; // Allow other properties
}