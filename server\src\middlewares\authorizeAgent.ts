import { Response, NextFunction } from "express";
import { AuthRequest } from "../utils/definitions";

/**
 * @deprecated Use requireAgencyAdmin middleware instead for better role-based access control.
 * This middleware is kept for backward compatibility but should be phased out.
 * 
 * Middleware to ensure the authenticated user is an AgencyAgent.
 * Should be used AFTER userAuth middleware.
 */
export const authorizeAgent = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // userAuth middleware should have populated req.accountType and potentially req.agencyAgent
    if (
      (req.accountType === "masterUser" && req.teamMember) ||
      (req.accountType === "masterOwner" && req.user) ||
      (req.accountType === "agencyOwner" && req.user) ||
      (req.accountType === "agencyUser" && req.agencyAgent)
    ) {
      // User is an agency agent, proceed
      return next();
    }
    
    // User is not an authorized agent type
    return res.status(403).json({ 
      success: false,
      message: "Forbidden: Access restricted to authorized users." 
    });
  } catch (error) {
    console.error("Authorization error:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred during authorization"
    });
  }
};
