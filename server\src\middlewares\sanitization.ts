import { Request, Response, NextFunction } from 'express';
import xss from 'xss';

export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj !== 'object') return obj;
    
    return Object.keys(obj).reduce((acc: any, key) => {
      if (typeof obj[key] === 'string') {
        acc[key] = xss(obj[key]);
      } else if (typeof obj[key] === 'object') {
        acc[key] = sanitizeObject(obj[key]);
      } else {
        acc[key] = obj[key];
      }
      return acc;
    }, {});
  };

  req.body = sanitizeObject(req.body);
  req.query = sanitizeObject(req.query);
  req.params = sanitizeObject(req.params);
  
  next();
};