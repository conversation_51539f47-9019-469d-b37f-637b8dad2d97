{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "proxy": "http://localhost:3000", "engines": {"node": ">=18"}, "prisma": {"schema": "./prisma/schema/schema.prisma", "generator": {"provider": "@prisma/client", "output": "./prisma/client"}}, "scripts": {"dev": "nodemon --watch src --exec ts-node src/app.ts", "build": "tsc", "install-and-build": "yarn && yarn build", "build:digitalocean": "yarn install --production=false && yarn build", "start": "node dist/app.js", "test": "cross-env NODE_ENV=dev jest", "docker:reset": "docker-compose down --rmi all -v --remove-orphans", "docker:start": "docker-compose up", "docker:stop": "docker-compose down"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@prisma/adapter-pg": "6.6.0", "@prisma/client": "6.6.0", "@prisma/extension-accelerate": "^1.3.0", "@sentry/node": "^9.14.0", "@types/bcryptjs": "^2.4.6", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.5", "@types/ioredis": "^5.0.0", "@types/nodemailer": "^6.4.15", "@types/swagger-jsdoc": "^6.0.4", "@types/validator": "^13.12.2", "@types/web-push": "^3.6.4", "@vonage/server-sdk": "^3.14.2", "axios": "^1.7.3", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "compression": "^1.8.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "express": "^5.1.0", "express-prometheus-middleware": "^1.2.0", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.1", "heapdump": "^0.3.15", "helmet": "^8.1.0", "http-proxy-middleware": "^3.0.0", "ioredis": "^5.6.0", "joi": "^17.13.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libphonenumber-js": "^1.11.11", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.7", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "nodemon": "^3.1.3", "ntp-client": "^0.5.3", "pg": "^8.13.1", "prom-client": "^13.2.0", "rate-limit-redis": "^4.2.0", "redis": "^4.7.0", "shrink-ray-current": "^4.1.3", "socket.io": "^4.7.5", "sqlstring": "^2.3.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "toobusy-js": "^0.5.1", "twilio": "^5.3.3", "typescript": "^5.4.5", "validator": "^13.12.0", "web-push": "^3.6.7", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0", "winston-loggly-bulk": "^3.3.2", "xss": "^1.0.15", "yamljs": "^0.3.0", "yarn": "1.22.22", "yup": "^1.4.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/bcrypt": "^5.0.2", "@types/cookie": "^0.6.0", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-prometheus-middleware": "^1.2.3", "@types/express-rate-limit": "^6.0.0", "@types/heapdump": "^0.3.4", "@types/jest": "^29.5.14", "@types/joi-phone-number": "^5.0.8", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.12", "@types/node": "^20.14.2", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.6", "@types/socket.io": "^3.0.2", "@types/sqlstring": "^2.3.2", "@types/supertest": "^6.0.2", "@types/toobusy-js": "^0.5.4", "@types/yamljs": "^0.2.34", "cross-env": "^7.0.3", "jest": "^29.7.0", "prettier": "^3.4.2", "prettier-plugin-prisma": "^5.0.0", "prisma": "6.6.0", "supertest": "^7.0.0", "ts-jest": "^29.1.4", "ts-node": "^10.9.2"}, "packageManager": "yarn@1.22.22"}