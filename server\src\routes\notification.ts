import express from "express";
import notificationController from "../controllers/NotificationController";
import userAuth from "../middlewares/userAuth";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

const router = express.Router();
const authMiddleware = [userAuth];
// router.use(enterpriseApiLimiter);

/**
 * @openapi
 * /notification/notifications:
 *   post:
 *     tags:
 *       - Notification
 *     summary: Create notification
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Notification created
 *
 * /notification/notifications/user/{userId}:
 *   get:
 *     tags:
 *       - Notification
 *     summary: Get user notifications
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of notifications
 *
 * /notification/notifications/{id}:
 *   get:
 *     tags:
 *       - Notification
 *     summary: Get notification by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification details
 *   delete:
 *     tags:
 *       - Notification
 *     summary: Delete notification
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification deleted
 *   patch:
 *     tags:
 *       - Notification
 *     summary: Mark notification as read
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification marked as read
 *
 * /notification/notifications/unread-count:
 *   get:
 *     tags:
 *       - Notification
 *     summary: Get unread notification count
 *     responses:
 *       200:
 *         description: Unread notification count
 *
 * /notification/notifications/subscribe:
 *   post:
 *     tags:
 *       - Notification
 *     summary: Subscribe to notifications
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Subscribed to notifications
 *
 * /notification/notifications/user/{userId}/read:
 *   patch:
 *     tags:
 *       - Notification
 *     summary: Mark all user notifications as read
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: All notifications marked as read
 */
router.post("/", notificationController.createNotification);
router.get(
  "/user/:userId",
  authMiddleware,
  notificationController.getUserNotifications
);
router.get("/:id", notificationController.getNotification);
router.get(
  "/unread-count",
  //   authMiddleware,
  notificationController.getUnreadCount
);

router.post(
  "/subscribe",
  authMiddleware,
  notificationController.storePushSubscription
);
router.patch("/:id/read", notificationController.markAsRead);
router.patch(
  "/user/:userId/read",
  notificationController.markAllAsRead
);
router.delete("/:id", notificationController.deleteNotification);

export default router;
