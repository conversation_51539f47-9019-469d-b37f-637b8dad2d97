// middlewares/rateLimiter.ts
/**
 * Prisma-based distributed rate limiting middleware
 * Uses PostgreSQL for persistent rate limit tracking
 * Implements tier-based rate limiting with Prisma store
 */

import rateLimit from "express-rate-limit";
import { AuthRequest } from "../utils/definitions";
import {
  getClientIdentifier,
  getRateLimitConfig,
} from "../utils/rate-limit/rateLimitUtils";
import { RateLimitTier } from "../utils/types/rate-limit";
import logger from "../utils/logger";
import { Response } from "express";
import { rateLimitHits, rateLimitBlocks, rateLimitErrors } from "../utils/rate-limit/rateLimitMetrics";

// Unified handler function with tier context
const createRateLimitHandler = (tier: RateLimitTier) => {
  return (req: AuthRequest, res: Response) => {
    const config = getRateLimitConfig(tier);
    // Prometheus: increment block count
    rateLimitBlocks.inc({
      tier,
      endpoint: req.path,
      ip: req.ip,
      userId: req.user?.id || 'anonymous',
    });
    // Enhanced logging with tier metadata
    logger.warn({
      message: "Rate limit exceeded",
      tier,
      clientId: req.user?.id,
      path: req.path,
      ip: req.ip,
      method: req.method,
      userAgent: req.headers["user-agent"],
      limitDetails: {
        window: config.windowMs,
        maxAttempts: config.max,
        burstLimit: config.burstMax,
      },
    });

    // Calculate reset time from actual windowMs
    const retryAfter = Math.ceil(config.windowMs / 1000);
    const rateLimitReset = new Date(Date.now() + config.windowMs);

    // Standardized error response
    res.status(429).json({
      success: false,
      error: {
        code: "RATE_LIMIT_EXCEEDED",
        message: `Rate limit exceeded for ${tier} tier`,
        details: {
          retryAfter,
          rateLimitReset: rateLimitReset.toISOString(),
          currentLimit: config.max,
          remaining: 0,
          tier,
          burstLimit: config.burstMax,
        },
      },
      forceLogout: tier === "free",
      message: "Too many requests, please try again later",
    });
  };
};

export const createTieredLimiter = (tier: RateLimitTier) => {
  const config = getRateLimitConfig(tier);

  if (!config) {
    throw new Error(`Invalid rate limit tier: ${tier}`);
  }

  return rateLimit({
    ...config,
    store: config.store, // Use Prisma store from config
    keyGenerator: getClientIdentifier,
    handler: createRateLimitHandler(tier),
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => req.path.startsWith("/health"),
    validate: {
      trustProxy: true, // Proper proxy handling
    },
  });
};

// Tier-specific limiters
export const loginRateLimit = createTieredLimiter("free");
export const apiLimiter = createTieredLimiter("premium");
export const enterpriseApiLimiter = createTieredLimiter("enterprise");
