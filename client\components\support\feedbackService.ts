import { api } from "@/utils/functions/apiClient";

interface FeedbackResponse {
  success: boolean;
  message: string;
}

export interface FeedbackFormData {
  subject: string;
  description: string;
  category: string;
  customFields: Record<string, any>;
  files?: File[];
}

export const feedbackService = {
  /**
   * Submit feedback with optional file attachments
   */
  async submitFeedback(data: FeedbackFormData): Promise<FeedbackResponse> {
    const formData = new FormData();

    // Append text fields
    formData.append("subject", data.subject);
    formData.append("description", data.description);
    formData.append("category", data.category);

    // Append custom fields as JSON string
    if (data.customFields) {
      formData.append("customFields", JSON.stringify(data.customFields));
    }

    // Append files if any
    if (data.files && data.files.length > 0) {
      data.files.forEach((file) => {
        formData.append("files", file);
      });
    }

    // Make the API call
    try {
      const response: FeedbackResponse = await api.post(
        "/api/v1/submit-feedback",
        formData,
        {
          withCredentials: true,
        }
      );

      if (!response.success) {
        throw new Error(response.message);
      }

      return {
        success: response.success,
        message: response.message || "Feedback submitted successfully!",
      };
    } catch (error: any) {
      console.error("Error submitting feedback:", error);
      return {
        success: false,
        message:
          error.message || "Failed to submit feedback. Please try again.",
      };
    }
  },
};
