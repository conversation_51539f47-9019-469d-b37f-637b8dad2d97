"use client";
import { selectUser, logoutUser } from "@/redux/features/AuthSlice";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import React, { createContext, useContext, useEffect, useState } from "react";
import io, { Socket } from "socket.io-client";
import Cookies from "js-cookie";

interface SocketContextProps {
  socket: Socket | null;
}

// Export a custom hook useSocket that allows components to access the context value:
const SocketContext = createContext<SocketContextProps>({
  socket: null,
});

export const useSocket = () => useContext(SocketContext);

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const user = useAppSelector(selectUser);
  const userInfo = user as StoredUser;
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Don't initialize socket if user is not logged in or has restricted status
    if (
      !userInfo?.id ||
      userInfo.accountStatus === "suspended" ||
      userInfo.accountStatus === "rejected"
    ) {
      if (socket) {
        console.log("Disconnecting socket - user logged out or restricted");
        socket.disconnect();
        setSocket(null);
      }
      return;
    }

    // Initialize the WebSocket connection
    const socketIo = io(process.env.SERVER_URL || "http://localhost:3000", {
      withCredentials: true,
    });

    // Join the user-specific room after the connection is established
    socketIo.on("connect", () => {
      if (userInfo && userInfo.id) {
        socketIo.emit("joinRoom", userInfo.id); // Emit an event to join the user's room
        console.log(`Joined room with ID: ${userInfo.id}`);
      }
    });

    // Handle account status changes
    socketIo.on(
      "accountStatusChanged",
      (data: { accountStatus: string; forceLogout: boolean }) => {
        if (data.forceLogout) {
          // Clear cookies and local storage
          Cookies.remove("token");

          // Dispatch logout action
          dispatch(logoutUser());

          // Disconnect socket before redirect
          socketIo.disconnect();
          setSocket(null);
        }
      }
    );

    // Handle disconnection
    socketIo.on("disconnect", () => {
      console.log("Disconnected from server");
    });

    setSocket(socketIo);

    // Cleanup on unmount
    return () => {
      socketIo.disconnect();
    };
  }, [userInfo?.id, userInfo?.accountStatus]); // Also depend on account status

  return (
    <SocketContext.Provider value={{ socket }}>
      {children}
    </SocketContext.Provider>
  );
};
