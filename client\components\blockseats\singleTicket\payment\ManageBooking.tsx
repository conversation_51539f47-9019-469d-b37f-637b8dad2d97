"use client";
import React, {
  useEffect,
  useState,
  useMemo,
  useRef,
  useCallback,
} from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";
import {
  selectFullTicket,
  selectTravelerData,
  selectPassengerCounts,
  selectBookingType,
  selectItinerary,
  selectDepartureTicket,
  selectReturnTicket,
  selectTicketsLoading,
  selectBookingResult,
  setBookingConfirmationData,
} from "@/redux/features/BookingConfirmationSlice";
import { fetchTickets, clearTickets } from "@/redux/features/bookingThunks";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { AppDispatch, RootState } from "@/redux/store";
import {
  Calendar,
  AlertCircle,
  Mail,
  Headphones,
  FileText,
  CreditCard,
  User,
  DollarSign,
  Hash,
  MessageSquare,
  CircleCheckBig,
  CircleAlert,
  Printer,
  Download,
  Ticket,
  Tag,
  Globe,
  Building,
  Loader,
} from "lucide-react";
import PaymentDialog from "./PopupPaymentForm";
import BookingActionConfirmation from "./BookingActionConfirmation";
import { updateTravelerInfo } from "@/lib/data/bookingData";

import {
  capitalizeFirst,
  getFormatDate,
  getFormatDateTable,
  getFormatTime,
  normalizePaymentMethod,
} from "@/utils/functions/functions";
import {
  releaseSeat,
  approveBooking,
  rejectBooking,
  completePayment,
  getBookingsByStatus,
  getBookingById,
  cancelBooking,
  rescheduleBooking,
  getTicketForReschedule,
  getBookingStatus,
  refundBooking,
} from "@/lib/data/bookingData";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { TravelerDto } from "@/utils/types/booking.types";
import html2pdf from "html2pdf.js";
import { TravelerForm } from "../TravelerForm";
import {
  Traveler,
  TravelerType,
} from "@/utils/definitions/blockSeatsDefinitions";
import FareBreakdownRow from "@/components/common/FareBreakdownRow";

// Helper function to normalize traveler data
const normalizeTravelerData = (traveler: any): Traveler => {
  if (!traveler) return traveler;

  // Create a copy of the traveler object
  const normalized = { ...traveler };

  // If passportIssuingCountry is not set but issuingCountry is, use that
  if (!normalized.passportIssuingCountry && normalized.issuingCountry) {
    normalized.passportIssuingCountry = normalized.issuingCountry;
  }

  // If passportExpiry is not set but expirationDate is, use that
  if (!normalized.passportExpiry && normalized.expirationDate) {
    normalized.passportExpiry = normalized.expirationDate;
  }

  // If passportNumber is not set but documentNumber is, use that
  if (!normalized.passportNumber && normalized.documentNumber) {
    normalized.passportNumber = normalized.documentNumber;
  }

  return normalized;
};

// ===============================
// Theme and Typography Constants
// ===============================
export const COLORS = {
  bgPrimary: "#1f2937",
  bgSecondary: "#374151",
  bgCard: "#4b5563",
  //   bgPrimary: "bg-white dark:bg-gray-800",
  //   bgSecondary: "bg-gray-100 dark:bg-gray-700",
  //   bgCard: "bg-gray-200 dark:bg-gray-600",
};

export const TYPOGRAPHY = {
  heading: { fontSize: "24px", lineHeight: "32px", fontWeight: "700" },
  label: { fontSize: "14px", lineHeight: "22px", fontWeight: "500" },
};

// ===============================
// Enums and Interfaces
// ===============================
export enum Gender {
  Male = "Male",
  Female = "Female",
  Other = "Other",
}

export interface TravelerInfo {
  title: string;
  firstName: string;
  lastName: string;
  gender: Gender;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  passportCountry: string;
  passportExpiry: string;
  email: string;
  phone: string;
}

export interface FlightData {
  origin: string;
  destination: string;
  airline: string;
  duration: string;
  departure: string;
  arrival: string;
  requestNumber: string;
  requestDate: string;
  agentCode: string;
  travelerInfo: TravelerInfo;
}

// ===============================
// Placeholder Data Fetching Functions
// ===============================
/**
 * Fetch flight booking data from backend API.
 * Replace with actual API call implementation.
 */
export const fetchFlightData = async (): Promise<FlightData> => {
  try {
    // TODO: Replace with real API call
    // Example: const response = await fetch('/api/flight-data');
    // return await response.json();
    throw new Error("Not implemented");
  } catch (error) {
    // Handle error as appropriate for your app
    throw error;
  }
};

/**
 * Update traveler information in backend API.
 * Replace with actual API call implementation.
 */
export const updateTraveler = async (
  bookingId: string,
  travelerInfo: { travelers: Array<{ traveler: any }> }
): Promise<any> => {
  try {
    const response = await updateTravelerInfo(bookingId, travelerInfo);
    return response;
  } catch (error: any) {
    console.error("Error in updateTraveler:", error);
    throw error;
  }
};

// Utility to normalize booking status/action strings for display
function normalizeBookingStatus(status: string): string {
  if (!status) return "N/A";

  // Handle specific status values
  if (status === "BOOKING_CONFIRMED") return "Booking Confirmed";
  if (status === "BOOKING_REJECTED") return "Booking Rejected";
  if (status === "CANCELLED_BY_USER") return "Cancelled By User";
  if (status === "CANCELLED_BY_SYSTEM") return "Cancelled By System";
  if (status === "PENDING_APPROVAL") return "Pending Approval";
  if (status === "QUICK_HOLD") return "Quick Hold";
  if (status === "TIMED_OUT") return "Timed Out";

  // Fallback for any other status
  return status
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

// ===============================
// Main Booking Page Props
// ===============================
export interface BookingRequestPageProps {
  flightData: FlightData;
  onUpdateTravelerInfo?: (travelerInfo: TravelerInfo) => Promise<void>;
}

// ======== CUSTOM CARD COMPONENTS ========
// ===============================
// Card Layout Components
// ===============================
interface CardProps {
  className?: string;
  style?: React.CSSProperties;
  children: React.ReactNode;
}

export const Card: React.FC<CardProps> = ({
  className = "",
  style = {},
  children,
}) => (
  <div className={`rounded-lg shadow ${className}`} style={style}>
    {children}
  </div>
);

export const CardHeader: React.FC<CardProps> = ({
  className = "",
  children,
}) => <div className={`p-6 pb-3 ${className}`}>{children}</div>;

export const CardTitle: React.FC<CardProps> = ({
  className = "",
  style = {},
  children,
}) => (
  <h3 className={`text-xl font-semibold ${className}`} style={style}>
    {children}
  </h3>
);

export const CardContent: React.FC<CardProps> = ({
  className = "",
  children,
}) => <div className={`p-6 pt-3 ${className}`}>{children}</div>;

// ======== REUSABLE COMPONENTS ========
// ===============================
// Reusable UI and Form Field Components
// ===============================
interface SectionHeadingProps {
  title: string;
}
export const SectionHeading: React.FC<SectionHeadingProps> = ({ title }) => (
  <CardTitle
    className="font-semibold dark:text-white text-gray-700"
    style={TYPOGRAPHY.heading}
  >
    {title}
  </CardTitle>
);

interface DisplayFieldProps {
  label: string;
  value: string | number;
  isRequired?: boolean;
}
export const DisplayField: React.FC<DisplayFieldProps> = ({
  label,
  value,
  isRequired = true,
}) => (
  <div>
    <label
      className="block dark:text-white text-gray-700 text-sm mb-1"
      style={TYPOGRAPHY.label}
    >
      {label} {isRequired && <span className="text-red-500">*</span>}
    </label>
    <div className="w-full bg-gray-100 dark:bg-gray-700 dark:text-white text-gray-700 p-2 rounded-md border dark:border-gray-600 border-gray-50">
      {value || "N/A"}
    </div>
  </div>
);

interface DateDisplayFieldProps {
  label: string;
  value: string;
  isRequired?: boolean;
}
export const DateDisplayField: React.FC<DateDisplayFieldProps> = ({
  label,
  value,
  isRequired = true,
}) => (
  <div>
    <label
      className="block dark:text-white text-gray-800 text-sm mb-1"
      style={TYPOGRAPHY.label}
    >
      {label} {isRequired && <span className="text-red-500">*</span>}
    </label>
    <div className="relative">
      <div className="w-full bg-gray-100 dark:bg-gray-700 dark:text-white text-gray-700 p-2 rounded-md border dark:border-gray-600 border-gray-50">
        {value || "N/A"}
      </div>
      <Calendar className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
    </div>
  </div>
);

interface EditableFieldProps {
  label: string;
  value: string;
  onChange: (val: string) => void;
  isRequired?: boolean;
  type?: string;
}
export const EditableField: React.FC<EditableFieldProps> = ({
  label,
  value,
  onChange,
  isRequired = true,
  type = "text",
}) => (
  <div>
    <label
      className="block dark:text-white text-gray-700 text-sm mb-1"
      style={TYPOGRAPHY.label}
    >
      {label} {isRequired && <span className="text-red-500">*</span>}
    </label>
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full bg-gray-200 dark:bg-gray-700 dark:text-white text-gray-700 p-2 rounded-md border dark:border-gray-600 border-gray-50 hover:border-red-500 hover:ring-1 hover:ring-red-500 focus:border-red-500 focus:ring-1 focus:ring-red-500 focus:outline-none"
    />
  </div>
);

interface EditableDateFieldProps {
  label: string;
  value: string;
  onChange: (val: string) => void;
  isRequired?: boolean;
}
export const EditableDateField: React.FC<EditableDateFieldProps> = ({
  label,
  value,
  onChange,
  isRequired = true,
}) => (
  <div>
    <label
      className="block dark:text-white text-gray-700 text-sm mb-1"
      style={TYPOGRAPHY.label}
    >
      {label} {isRequired && <span className="text-red-500">*</span>}
    </label>
    <div className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full bg-gray-200 dark:bg-gray-700 dark:text-white text-gray-700 p-2 rounded-md border dark:border-gray-600 border-gray-50 hover:border-red-500 hover:ring-1 hover:ring-red-500 focus:border-red-500 focus:ring-1 focus:ring-red-500 focus:outline-none"
      />
      <Calendar className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
    </div>
  </div>
);

interface EditableSelectProps<T extends string | number = string> {
  label: string;
  value: T;
  onChange: (val: T) => void;
  options: T[];
  isRequired?: boolean;
}
export function EditableSelect<T extends string | number = string>({
  label,
  value,
  onChange,
  options = [],
  isRequired = true,
}: EditableSelectProps<T>): JSX.Element {
  return (
    <div>
      <label
        className="block dark:text-white text-gray-700 text-sm mb-1"
        style={TYPOGRAPHY.label}
      >
        {label} {isRequired && <span className="text-red-500">*</span>}
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value as T)}
        className="w-full bg-gray-200 dark:bg-gray-700 dark:text-white text-gray-700 p-2 rounded-md border dark:border-gray-600 border-gray-50 hover:border-red-500 hover:ring-1 hover:ring-red-500 focus:border-red-500 focus:ring-1 focus:ring-red-500 focus:outline-none"
      >
        {options.map((option, index) => (
          <option key={index} value={option}>
            {option}
          </option>
        ))}
      </select>
    </div>
  );
}

interface TravelerSectionProps {
  title: string;
  children: React.ReactNode;
}
export const TravelerSection: React.FC<TravelerSectionProps> = ({
  title,
  children,
}) => (
  <div className="dark:bg-gray-600 bg-white p-4 rounded-lg mb-4">
    <h3
      className="dark:text-white text-gray-700 mb-3"
      style={TYPOGRAPHY.heading}
    >
      {title}
    </h3>
    {children}
  </div>
);

interface InfoAlertProps {
  icon?: React.ReactElement;
  title: string;
  text: string;
  iconColor: string;
}
export const InfoAlert: React.FC<InfoAlertProps> = ({
  icon,
  title,
  text,
  iconColor,
}) => (
  <div className="dark:bg-gray-600 bg-white p-4 rounded-lg flex items-start gap-3">
    {icon &&
      React.cloneElement(icon, {
        className: `h-5 w-5 text-${iconColor} flex-shrink-0 mt-0.5`,
      })}
    <div>
      <p className="dark:text-white text-gray-700 font-semibold mb-1">
        {title}
      </p>
      <p className="dark:text-gray-300 text-gray-700 text-sm">{text}</p>
    </div>
  </div>
);

// ======== MAIN BOOKING MANAGEMENT COMPONENTS ========

// Agent Payment Details
const AgentPaymentDetails = (data: any) => {
  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading title="Submitted Payment Details" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-6 mb-4">
          {[
            {
              label: "Payment Method",
              value:
                data?.bookingType === "THIRD_PARTY" ||
                data?.booking?.source === "THIRD_PARTY" ||
                data?.source === "THIRD_PARTY"
                  ? "Airvilla Wallet"
                  : data?.bookingResult?.payment?.paymentMethod ||
                    data?.data?.fullTicket?.payment?.paymentMethod ||
                    data?.bookingResult?.paymentMethod ||
                    data?.bookingResult?.payment?.paymentMethod ||
                    data?.data?.fullTicket?.payment?.paymentMethod
                  ? normalizePaymentMethod(
                      data?.bookingResult?.payment?.paymentMethod ||
                        data?.data?.fullTicket?.payment?.paymentMethod ||
                        data?.bookingResult?.paymentMethod ||
                        data?.bookingResult?.payment?.paymentMethod ||
                        data?.data?.fullTicket?.payment?.paymentMethod
                    )
                  : "N/A",
              icon: <CreditCard className="h-4 w-4 text-red-500" />,
            },
            {
              label: "Reference Number",
              value: data?.data?.fullTicket?.payment?.paymentReference || "N/A",
              icon: <Hash className="h-4 w-4 text-red-500" />,
            },
            {
              label: "Transaction Date",
              value:
                data?.data?.fullTicket?.payment?.createdAt ||
                data?.data?.fullTicket?.createdAt
                  ? getFormatDateTable(
                      data?.data?.fullTicket?.payment?.createdAt ||
                        data?.data?.fullTicket?.createdAt
                    )
                  : "N/A",
              icon: <Calendar className="h-4 w-4 text-red-500" />,
            },
            {
              label: "Payment Amount",
              value:
                `${Number(
                  data?.data?.fullTicket?.payment?.amount ??
                    data?.data?.fullTicket?.bookedSeats?.[0]?.totalPrice
                )?.toFixed(2)} ${
                  data?.data?.fullTicket?.payment?.currency || "JOD"
                }` || "N/A",
              icon: <DollarSign className="h-4 w-4 text-red-500" />,
            },
          ].map((item, index) => (
            <div
              key={index}
              className="p-4 rounded-lg dark:bg-gray-600 bg-white"
            >
              <div className="flex items-center mb-2">
                {item.icon}
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  {item.label}
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {item.value}
              </p>
            </div>
          ))}
        </div>
        {data?.data?.fullTicket?.payment?.paymentMeta?.remarks && (
          <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
            <div className="flex items-center mb-2">
              <MessageSquare className="h-4 w-4 dark:text-red-500 text-gray-700" />
              <p className="dark:text-gray-300 text-gray-700 ml-2">Remarks</p>
            </div>
            <p className="dark:text-white text-gray-700 font-semibold text-lg min-h-16 py-2 break-words">
              {data?.data?.fullTicket?.payment?.paymentMeta?.remarks}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Ticket Source card
const TicketSource = ({ data }: { data: any }) => {
  return (
    <>
      <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
        <CardHeader>
          <SectionHeading title="Ticket Source" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
              <div className="flex items-center mb-2">
                <Globe className="h-4 w-4 text-red-500" />
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  {" "}
                  Booking Source
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {data?.fullTicket?.source === "INTERNAL"
                  ? "Internal"
                  : "Third-Party"}
              </p>
            </div>

            <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
              <div className="flex items-center mb-2">
                <Tag className="h-4 w-4 text-red-500" />
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  Ticket ID
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {data?.fullTicket?.ticket.refId}
              </p>
            </div>

            <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
              <div className="flex items-center mb-2">
                <Building className="h-4 w-4 text-red-500" />
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  Seller Agency
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {data?.fullTicket?.meta?.sellerAgencyName ||
                  data?.bookingResult?.meta?.sellerAgencyName ||
                  data?.bookingResult?.meta?.departureSeller?.agencyName ||
                  "N/A"}
              </p>
            </div>
            <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
              <div className="flex items-center mb-2">
                <Building className="h-4 w-4 text-red-500" />
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  Booking Agency
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {data?.fullTicket?.source === "INTERNAL"
                  ? data?.fullTicket?.meta?.sellerAgencyName ||
                    data?.bookingResult?.meta?.sellerAgencyName ||
                    data?.bookingResult?.meta?.departureSeller?.agencyName ||
                    "N/A"
                  : data?.fullTicket?.meta?.buyerAgencyName ||
                    data?.bookingResult?.meta?.buyerAgencyName ||
                    "N/A"}
              </p>
            </div>

            <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
              <div className="flex items-center mb-2">
                <User className="h-4 w-4 text-red-500" />
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  Booking Agent Name
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl break-words">
                {data?.fullTicket?.meta?.buyerAgentName}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

// Request Information card
const RequestInformation = ({
  data,
  viewType,
}: {
  data: any;
  viewType: any;
}) => {
  // Get booking data from the correct structure
  const bookingResult = data?.bookingResult || {};

  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading
          title={
            viewType === "master" ||
            (viewType === "internal" &&
              bookingResult?.status === "BOOKING_CONFIRMED")
              ? "Booking Information"
              : "Request Information"
          }
        />
      </CardHeader>
      <CardContent>
        {viewType === "master" ? (
          <div className="grid grid-cols-2 gap-6">
            {[
              {
                label: "Booking ID",
                value:
                  bookingResult?.requestId ??
                  bookingResult[0]?.requestId ??
                  bookingResult[1]?.requestId ??
                  "N/A",
                icon: <FileText className="h-4 w-4 text-red-500" />,
              },
              {
                label: "Booking Reference Number",
                value:
                  bookingResult?.referenceNumber ||
                  bookingResult?.eTicket?.eTicketNumber ||
                  bookingResult?.Tickets?.[0]?.eTicketNumber ||
                  bookingResult?.eTickets?.[0]?.eTicketNumber ||
                  "N/A",
                icon: <Ticket className="h-4 w-4 text-red-500" />,
              },
              {
                label: "Payment Method",
                value:
                  data?.bookingType === "THIRD_PARTY" ||
                  data?.booking?.source === "THIRD_PARTY" ||
                  data?.source === "THIRD_PARTY" ||
                  bookingResult?.source === "THIRD_PARTY"
                    ? "Airvilla Wallet"
                    : data?.bookingResult?.payment?.paymentMethod ||
                      data?.data?.fullTicket?.payment?.paymentMethod ||
                      data?.bookingResult?.paymentMethod ||
                      data?.bookingResult?.payment?.paymentMethod ||
                      data?.data?.fullTicket?.payment?.paymentMethod
                    ? normalizePaymentMethod(
                        data?.bookingResult?.payment?.paymentMethod ||
                          data?.data?.fullTicket?.payment?.paymentMethod ||
                          data?.bookingResult?.paymentMethod ||
                          data?.bookingResult?.payment?.paymentMethod ||
                          data?.data?.fullTicket?.payment?.paymentMethod
                      )
                    : "N/A",
                icon: <CreditCard className="h-4 w-4 text-red-500" />,
              },
              {
                label: "Booking Date",
                value: getFormatDateTable(bookingResult?.createdAt) || "N/A",
                icon: <Calendar className="h-4 w-4 text-red-500" />,
              },
            ].map((item, index) => (
              <div
                key={index}
                className="p-4 rounded-lg dark:bg-gray-600 bg-white"
              >
                <div className="flex items-center mb-2">
                  {item.icon}
                  <p className="dark:text-gray-300 text-gray-700 ml-2">
                    {item.label}
                  </p>
                </div>
                <p className="dark:text-white text-gray-700 font-semibold text-xl">
                  {item.value}
                </p>
              </div>
            ))}
            <div className="col-span-2 p-4 rounded-lg dark:bg-gray-600 bg-white">
              <div className="flex items-center mb-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  Booking Status
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {normalizeBookingStatus(
                  bookingResult?.status ||
                    // departureBooking?.status ||
                    // returnBooking?.status ||
                    bookingResult[0]?.status ||
                    bookingResult[1]?.status
                ) || "N/A"}
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6">
            {[
              {
                label: "Request ID",
                value:
                  bookingResult?.requestId ??
                  bookingResult[0]?.requestId ??
                  bookingResult[1]?.requestId ??
                  "N/A",
                icon: <FileText className="h-4 w-4 text-red-500" />,
              },
              {
                label: "Employee Name",
                value:
                  bookingResult?.meta?.agentName ??
                  bookingResult?.meta?.buyerAgentName ??
                  bookingResult[0]?.meta?.buyerAgentName ??
                  bookingResult[1]?.meta?.buyerAgentName ??
                  "N/A",
                icon: <User className="h-4 w-4 text-red-500" />,
              },
              {
                label: "Booking Status",
                value:
                  normalizeBookingStatus(
                    bookingResult?.status ??
                      bookingResult[0]?.status ??
                      bookingResult[1]?.status
                  ) ?? "N/A",
                icon: <AlertCircle className="h-4 w-4 text-red-500" />,
              },
              {
                label: "Request Date",
                value: getFormatDateTable(bookingResult?.createdAt) || "N/A",
                icon: <Calendar className="h-4 w-4 text-red-500" />,
              },
            ].map((item, index) => (
              <div
                key={index}
                className="p-4 rounded-lg dark:bg-gray-600 bg-white"
              >
                <div className="flex items-center mb-2">
                  {item.icon}
                  <p className="dark:text-gray-300 text-gray-700 ml-2">
                    {item.label}
                  </p>
                </div>
                <p className="dark:text-white text-gray-700 font-semibold text-xl">
                  {item.value}
                </p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Traveler Details with edit functionality
// Traveler Details with edit state notification
const TravelerDetails = ({
  data,
  onEditStateChange,
  bookingStatus,
  loadingStatus,
  isAgencyOwner,
  editable,
  nonEditableStatuses = [],
  onUpdateTravelerInfo,
}: {
  data: any;
  onEditStateChange?: (state: { editing: boolean; saving: boolean }) => void;
  bookingStatus: {
    isPaid?: boolean;
    actionsAllowed?: boolean;
    status?: string;
  } | null;
  loadingStatus: boolean;
  isAgencyOwner: boolean;
  editable?: boolean;
  nonEditableStatuses?: string[];
  onUpdateTravelerInfo?: (travelerData: TravelerDto) => Promise<void>;
}) => {
  const dispatch = useAppDispatch();
  const booking = useAppSelector(selectBookingResult);
  const params = useSearchParams();
  // State to track which traveler form is currently expanded
  const [expandedTraveler, setExpandedTraveler] = useState<number>(0);
  const [editingTraveler, setEditingTraveler] = useState<Set<number>>(
    new Set()
  );
  const [isSaving, setIsSaving] = useState(false);
  // const [isEditing, setIsEditing] = useState<number | null>(null);
  const isEditing = editingTraveler.has(expandedTraveler);
  const currentUser = useSelector((state: RootState) => state.auth.value);

  const [currentTravelerFormData, setCurrentTravelerFormData] =
    useState<Traveler | null>(null);
  const [openTravelerIndex, setOpenTravelerIndex] = useState<number | null>(0);
  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );
  const bookingResult = useSelector(
    (state: RootState) => state.bookingConfirmation.bookingResult
  );
  const travelerData = useSelector(
    (state: RootState) => state.bookingConfirmation.travelerData
  );
  const fullTicket = useSelector(
    (state: RootState) => state.bookingConfirmation.fullTicket
  );
  const passengerCounts = useSelector(
    (state: RootState) => state.bookingConfirmation.passengerCounts
  );
  // Notify parent of edit/saving state
  useEffect(() => {
    if (onEditStateChange) {
      onEditStateChange({
        editing: editingTraveler !== null,
        saving: isSaving,
      });
    }
  }, [editingTraveler, isSaving, onEditStateChange]);

  // Initialize travelers array from data
  const [travelersInfo, setTravelersInfo] = useState<any[]>(() => {
    // Handle case where traveler data is nested in traveler.traveler
    const extractTravelerData = (traveler: any) => {
      const travelerData = traveler.traveler || traveler;
      return {
        ...travelerData,
        // Map API fields to form fields
        passportNumber: travelerData.documentNumber,
        issuingCountry: travelerData.issuingCountry,
        passportExpiry: travelerData.expirationDate,
        contactEmail: travelerData.contactEmail,
        contactPhone: travelerData.contactPhone,
        // Include the original traveler object for reference
        _original: traveler,
      };
    };
    // If data has travelerData array, use it
    if (data?.travelerData && Array.isArray(data.travelerData)) {
      return data.travelerData.map(extractTravelerData);
    }
    // If data has travelers array with nested traveler object, use it
    else if (data?.travelers && Array.isArray(data.travelers)) {
      return data.travelers.map(extractTravelerData);
    }
    // If data has travelers array with nested traveler object, use it
    else if (data?.travelers && Array.isArray(data.travelers)) {
      return data.travelers.map(extractTravelerData);
    }
    // If data has bookingResult.travelers array, use it
    else if (
      data?.bookingResult?.travelers &&
      Array.isArray(data.bookingResult.travelers)
    ) {
      return data.bookingResult.travelers.map(extractTravelerData);
    }
    // Fallback to empty array
    return [];
  });

  // Update a specific field for a specific traveler
  const updateField = (
    travelerIndex: number,
    fieldName: string,
    value: string
  ) => {
    setTravelersInfo((prev) => {
      const updated = [...prev];
      updated[travelerIndex] = {
        ...updated[travelerIndex],
        [fieldName]: value,
      };
      return updated;
    });
  };

  // Save changes for all travelers being edited
  const handleSaveAll = async () => {
    try {
      console.log(
        "handleSaveAll called with editingTraveler:",
        Array.from(editingTraveler)
      );
      console.log("Current travelersInfo:", travelersInfo);

      const savePromises = Array.from(editingTraveler).map((index) => {
        console.log(`Saving traveler ${index}:`, travelersInfo[index]);
        return handleSave(travelersInfo[index], index); // Pass the index to handleSave
      });
      await Promise.all(savePromises);
      setEditingTraveler(new Set());
    } catch (error) {
      console.error("Error saving travelers:", error);
    }
  };

  // Handle save changes for a specific traveler
  const handleSave = async (
    travelerData: TravelerDto,
    travelerIndex?: number
  ): Promise<void> => {
    const currentIndex =
      typeof travelerIndex === "number"
        ? travelerIndex
        : Array.from(editingTraveler)[0];
    if (currentIndex === undefined) {
      dispatch(
        setMsg({ success: false, message: "No traveler selected for editing" })
      );
      return;
    }

    // Use the traveler data from travelersInfo array instead of currentTravelerFormData
    const currentTravelerData = travelersInfo[currentIndex];
    if (!currentTravelerData) {
      dispatch(setMsg({ success: false, message: "No traveler data found" }));
      return;
    }
    // Check for one-way booking
    const isOneWay = booking && !Array.isArray(booking);
    // Check for round-trip booking
    const isRoundTrip = Array.isArray(booking) && booking.length === 2;

    if (isOneWay) {
      // Handle one-way booking
      if (!booking?.id) {
        dispatch(setMsg({ success: false, message: "No booking ID" }));
        return;
      }
    } else if (isRoundTrip) {
      // Handle round-trip booking
      if (!booking?.[0]?.id || !booking?.[1]?.id) {
        dispatch(
          setMsg({
            success: false,
            message: "Missing booking information for round trip",
          })
        );
        return;
      }
    } else {
      // Handle invalid booking data
      dispatch(
        setMsg({
          success: false,
          message: "Invalid booking information",
        })
      );
      return;
    }
    try {
      setIsSaving(true);

      const travelerToSave: TravelerDto = {
        ...currentTravelerData,
        // Map frontend fields to backend fields
        documentType: (
          currentTravelerData.documentType || "passport"
        ).toLowerCase(),
        // Map passportNumber to documentNumber for backend
        documentNumber:
          currentTravelerData.passportNumber ||
          currentTravelerData.documentNumber ||
          "",
        // Map passportIssuingCountry to issuingCountry for backend
        issuingCountry: currentTravelerData.issuingCountry || "",
        // Map passportExpiry to expirationDate for backend
        expirationDate:
          currentTravelerData.passportExpiry ||
          currentTravelerData.expirationDate ||
          "",
        // Ensure contact info is included
        contactEmail: currentTravelerData.contactEmail || "",
        contactPhone: currentTravelerData.contactPhone || "",
        // Include any other required fields
        title: currentTravelerData.title || "",
        firstName: currentTravelerData.firstName || "",
        lastName: currentTravelerData.lastName || "",
        nationality: currentTravelerData.nationality || "",
        dateOfBirth: currentTravelerData.dateOfBirth || "",
        gender: currentTravelerData.gender || "",
      };

      // Prepare the payload in the expected format
      // const payload = {
      //   travelers: [
      //     {
      //       traveler: {
      //         ...travelerData,
      //         // Ensure we have the traveler ID
      //         id: travelerId,
      //         title: currentTravelerFormData.title
      //           ? currentTravelerFormData.title.toUpperCase()
      //           : "",
      //         issuingCountry:
      //           travelerData.passportIssuingCountry ||
      //           travelerData.issuingCountry,
      //       },
      //     },
      //   ],
      // };

      // Get the traveler ID from the original data
      const originalTraveler =
        currentTravelerData._original || currentTravelerData;
      const travelerId = originalTraveler.traveler?.id || originalTraveler.id;

      if (!travelerId) {
        throw new Error("Cannot update traveler: Missing traveler ID");
      }

      // Update the travelersInfo state with the new data
      setTravelersInfo((prev) => {
        const updated = [...prev];
        updated[currentIndex] = {
          ...updated[currentIndex],
          ...travelerToSave,
          // Map back to the API format
          documentNumber: travelerToSave.documentNumber,
          issuingCountry: travelerToSave.issuingCountry,
          expirationDate: travelerToSave.expirationDate,
        };
        return updated;
      });

      // Prepare the payload for the API
      const payload = {
        travelers: [
          {
            traveler: {
              ...travelerToSave,
              id: travelerId,
              title: travelerToSave.title.toUpperCase(),
            },
          },
        ],
      };

      // Call the update function
      let updatedBooking;
      const bookingArray = Array.isArray(booking) ? booking : [booking];
      if (isRoundTrip) {
        // For round trips, update both outbound and return bookings
        const [outboundBooking, returnBooking] = bookingArray;

        // For round trips, we need to update both outbound and return bookings
        const outboundBookingId = outboundBooking?.id;
        const returnBookingId = returnBooking?.id;

        // Update outbound booking
        updatedBooking = await updateTraveler(outboundBookingId, payload);

        // If return booking exists, update it as well
        if (returnBookingId) {
          await updateTraveler(returnBookingId, payload);
        }
      } else {
        // For one-way trips, just update the single booking
        const bookingId = Array.isArray(booking) ? booking[0].id : booking.id;
        updatedBooking = await updateTraveler(bookingId, payload);
      }

      console.log("API update response:", updatedBooking);

      // Wait a moment for the database to be updated, then fetch fresh data
      await new Promise((resolve) => setTimeout(resolve, 500));

      const response = await getBookingById(updatedBooking.id);
      console.log("Server response after save:", response);
      console.log("Server travelers data:", response.travelers);

      // Update Redux state with fresh data from server
      dispatch(
        setBookingConfirmationData({
          bookingResult: response,
          travelerData: response.travelers,
          ticket: response,
          fullTicket: fullTicket,
          passengerCounts: passengerCounts,
        })
      );

      // Don't refresh local data after save to preserve order and avoid data corruption
      // The data has been successfully saved to the server, so we keep our local changes
      console.log(
        "Save successful - keeping local traveler data to preserve order and changes"
      );

      // Show success message
      dispatch(
        setMsg({
          success: true,
          message: "Traveler information updated successfully",
        })
      );

      // Exit edit mode
      setEditingTraveler(new Set());
      setCurrentTravelerFormData(null);
      setExpandedTraveler(expandedTraveler);
      setOpenTravelerIndex(expandedTraveler);
    } catch (error: any) {
      console.error("Failed to update traveler:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        response: error.response?.data,
      });
      dispatch(
        setMsg({
          success: false,
          message: error.message || "Failed to update traveler information",
        })
      );
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    if (isSaving) {
      // Update the local state immediately when saving starts
      setTravelersInfo((prevTravelers) => {
        if (!currentTravelerFormData) return prevTravelers;

        return prevTravelers.map((traveler, index) =>
          editingTraveler.has(index)
            ? {
                ...traveler,
                ...currentTravelerFormData,
                // Ensure title is in uppercase
                title: currentTravelerFormData.title.toUpperCase(),
              }
            : traveler
        );
      });
    }
  }, [isSaving, currentTravelerFormData, editingTraveler]);

  // Toggle which traveler form is expanded
  const toggleTravelerExpand = (index: number) => {
    // If clicking the currently expanded traveler and it's the only one, do nothing
    if (expandedTraveler === index && travelersInfo.length === 1) {
      return;
    }
    // If we're in edit mode, don't allow collapsing
    // if (editingTraveler !== null) {
    //   return;
    // }
    // Otherwise, toggle the clicked traveler
    const newExpandedIndex = expandedTraveler === index ? -1 : index;
    setExpandedTraveler(newExpandedIndex);
    setOpenTravelerIndex(newExpandedIndex);
  };

  // // Start editing a specific traveler
  // const startEditing = (index: number) => {
  //   setEditingTraveler(index);
  // };

  // // Cancel editing and reset to original data
  // const cancelEditing = (index: number) => {
  //   setEditingTraveler(null);
  //   // Reset the traveler data to original
  //   if (data?.booking?.travelers && Array.isArray(data.travelers)) {
  //     setTravelersInfo((prev) => {
  //       const updated = [...prev];
  //       updated[index] = {
  //         ...data.travelers[index].traveler,
  //         passportNumber: data.travelers[index].traveler.documentNumber,
  //         passportCountry: data.travelers[index].traveler.issuingCountry,
  //         passportExpiry: data.travelers[index].traveler.expirationDate,
  //         email: data.travelers[index].traveler.contactEmail,
  //         phone: data.travelers[index].traveler.contactPhone,
  //       };
  //       return updated;
  //     });
  //   }
  // };

  const startEditingAll = () => {
    const allTravelerIndices = new Set(travelersInfo.map((_, index) => index));
    setEditingTraveler(allTravelerIndices);
  };

  const startEditing = useCallback(
    (travelerIndex: number) => {
      const travelerData = travelersInfo[travelerIndex];
      console.log({ travelerIndex });
      console.log({ travelerData });
      if (!travelerData) return;

      // Make sure we have the latest data
      const currentData =
        Array.isArray(travelersInfo) && travelersInfo[travelerIndex];
      console.log({ currentData });
      if (!currentData) return;

      // Transform the traveler data to match the Traveler type
      const traveler: Traveler = {
        title: currentData.title?.toUpperCase() || "",
        gender: currentData.gender || "",
        firstName: currentData.firstName || "",
        lastName: currentData.lastName || "",
        contactEmail: currentData.email || currentData.contactEmail || "",
        contactPhone: currentData.phone || currentData.contactPhone || "",
        dateOfBirth: currentData.dateOfBirth || "",
        nationality: currentData.nationality || "",
        passportNumber:
          currentData.passportNumber || currentData.documentNumber || "",
        issuingCountry: currentData.issuingCountry || "",
        passportExpiry:
          currentData.passportExpiry || currentData.expirationDate || "",
        type: TravelerType.ADULT,
        errors: {},
      };

      setEditingTraveler(new Set([travelerIndex]));
      setCurrentTravelerFormData(traveler);
      setOpenTravelerIndex(travelerIndex);
    },
    [travelersInfo]
  );

  const handleTravelerUpdate = useCallback(
    (updatedTraveler: Traveler, travelerIndex?: number) => {
      console.log("handleTravelerUpdate called:", {
        updatedTraveler,
        travelerIndex,
        firstName: updatedTraveler.firstName,
        dateOfBirth: updatedTraveler.dateOfBirth,
      });

      if (travelerIndex !== undefined) {
        // Update specific traveler in the array
        setTravelersInfo((prev) => {
          const newTravelersInfo = prev.map((traveler, index) =>
            index === travelerIndex
              ? {
                  ...traveler,
                  ...updatedTraveler,
                  // Ensure critical fields are properly updated
                  firstName: updatedTraveler.firstName,
                  lastName: updatedTraveler.lastName,
                  dateOfBirth: updatedTraveler.dateOfBirth,
                  title: updatedTraveler.title,
                  gender: updatedTraveler.gender,
                  nationality: updatedTraveler.nationality,
                  passportNumber:
                    updatedTraveler.passportNumber ||
                    updatedTraveler.documentNumber,
                  documentNumber:
                    updatedTraveler.documentNumber ||
                    updatedTraveler.passportNumber,
                  issuingCountry: updatedTraveler.issuingCountry,
                  passportExpiry:
                    updatedTraveler.passportExpiry ||
                    updatedTraveler.expirationDate,
                  expirationDate:
                    updatedTraveler.expirationDate ||
                    updatedTraveler.passportExpiry,
                  contactEmail: updatedTraveler.contactEmail,
                  contactPhone: updatedTraveler.contactPhone,
                  errors: {
                    ...(traveler.errors || {}),
                    ...(updatedTraveler.errors || {}),
                  },
                }
              : traveler
          );
          console.log(
            `Updated traveler ${travelerIndex}:`,
            newTravelersInfo[travelerIndex]
          );
          console.log("Full updated travelersInfo:", newTravelersInfo);
          return newTravelersInfo;
        });
      } else {
        // Fallback to old behavior for single traveler
        setCurrentTravelerFormData((prev) => {
          if (!prev) return updatedTraveler;
          // Merge updated fields and errors
          const newErrors = {
            ...(prev.errors || {}),
            ...(updatedTraveler.errors || {}),
          };
          return { ...prev, ...updatedTraveler, errors: newErrors };
        });
      }
    },
    []
  );

  const saveChanges = useCallback(
    async (travelerIndex: number) => {
      if (!currentTravelerFormData) return;

      // Check for validation errors
      const hasErrors = Object.values(
        currentTravelerFormData.errors || {}
      ).some((error) => error !== undefined && error !== "");

      if (hasErrors) {
        // Show error message or prevent saving
        console.error("Please fix validation errors before saving");
        return;
      }

      setIsSaving(true);

      try {
        // Transform back to API format
        const travelerData = {
          ...travelersInfo[travelerIndex],
          title: currentTravelerFormData.title.toUpperCase(),
          gender: currentTravelerFormData.gender,
          firstName: currentTravelerFormData.firstName,
          lastName: currentTravelerFormData.lastName,
          email: currentTravelerFormData.contactEmail,
          phone: currentTravelerFormData.contactPhone,
          dateOfBirth: currentTravelerFormData.dateOfBirth,
          nationality: currentTravelerFormData.nationality,
          documentNumber: currentTravelerFormData.passportNumber,
          issuingCountry: currentTravelerFormData.issuingCountry,
          expirationDate: currentTravelerFormData.passportExpiry,
        };

        // Update the travelersInfo state
        const updatedTravelers = [...travelersInfo];
        updatedTravelers[travelerIndex] = travelerData;
        setTravelersInfo(updatedTravelers);

        // Call the API or parent handler if provided
        if (onUpdateTravelerInfo) {
          await onUpdateTravelerInfo(travelerData);
        }

        // Exit edit mode
        setEditingTraveler(new Set());
        setCurrentTravelerFormData(null);
        setOpenTravelerIndex(0);
      } catch (error) {
        console.error("Failed to save traveler data:", error);
      } finally {
        setIsSaving(false);
      }
    },
    [currentTravelerFormData, onUpdateTravelerInfo, travelersInfo]
  );

  const cancelEditing = useCallback((travelerIndex: number) => {
    setEditingTraveler(new Set());
    setCurrentTravelerFormData(null);
    // setOpenTravelerIndex(0);
    setExpandedTraveler(travelerIndex);
    // setOpenTravelerIndex(travelerIndex);
  }, []);

  // // Render fields based on whether we're editing or not
  // const renderField = (
  //   travelerIndex: number,
  //   type: string,
  //   fieldName: string,
  //   label: string
  // ) => {
  //   console.log("renderField", {
  //     travelerIndex,
  //     editingSet: Array.from(editingTraveler),
  //   });
  //   const isEditing = editingTraveler.has(travelerIndex);
  //   const travelerInfo = travelersInfo[travelerIndex];
  //   console.log("travelerInfo", travelerInfo);

  //   // Fallback logic: check both top-level and nested traveler object
  //   const value =
  //     travelerInfo?.[fieldName] ?? travelerInfo?.traveler?.[fieldName] ?? "N/A";

  //   if (!isEditing) {
  //     return type === "date" ? (
  //       <DateDisplayField label={label} value={getFormatDate(value)} />
  //     ) : (
  //       <DisplayField label={label} value={value} />
  //     );
  //   } else {
  //     // Render editable fields
  //     switch (type) {
  //       case "date":
  //         return (
  //           <EditableDateField
  //             label={label}
  //             value={travelerInfo?.[fieldName]}
  //             onChange={(value) => updateField(travelerIndex, fieldName, value)}
  //           />
  //         );
  //       case "select":
  //         const options =
  //           fieldName === "title" ? ["Mr", "Mrs", "Ms"] : ["male", "female"];
  //         return (
  //           <EditableSelect
  //             label={label}
  //             value={value}
  //             options={options}
  //             onChange={(val) => updateField(travelerIndex, fieldName, val)}
  //           />
  //         );
  //       default:
  //         return (
  //           <EditableField
  //             label={label}
  //             type={type}
  //             value={value}
  //             onChange={(val) => updateField(travelerIndex, fieldName, val)}
  //           />
  //         );
  //     }
  //   }
  // };

  // // Render a single traveler form
  // const renderTravelerForm = (travelerIndex: number) => {
  //   const isExpanded = expandedTraveler === travelerIndex;
  //   const isEditing = editingTraveler.has(travelerIndex);
  //   const travelerInfo = travelersInfo[travelerIndex];

  //   return (
  //     <div key={travelerIndex} className="mb-6">
  //       {/* Traveler header - clickable to expand/collapse */}
  //       <div
  //         className="p-4 rounded-lg mb-4 cursor-pointer flex justify-between items-center dark:bg-gray-600 bg-white"
  //         onClick={() => toggleTravelerExpand(travelerIndex)}
  //       >
  //         <span className="dark:text-white text-gray-700 font-semibold text-lg">
  //           Traveler {travelerIndex + 1}:{" "}
  //           {travelerInfo?.firstName ||
  //             travelerInfo?.traveler?.firstName ||
  //             "N/A"}{" "}
  //           {travelerInfo?.lastName ||
  //             travelerInfo?.traveler?.lastName ||
  //             "N/A"}
  //         </span>
  //         {/* <span className="dark:text-white text-gray-700">
  //           {isExpanded ? "▲" : "▼"}
  //         </span> */}
  //         <div className="flex items-end justify-center">
  //           <div className="relative w-3 h-3 mx-4">
  //             <span className="absolute top-1/2 left-0 w-full h-0.5 bg-gray-500 dark:bg-gray-300 transition-transform duration-500 transform -translate-y-1/2"></span>
  //             <span
  //               className={`absolute top-0 left-1/2 w-0.5 h-full bg-gray-500 dark:bg-gray-300 transition-transform duration-500 transform origin-center -translate-x-1/2 ${
  //                 isExpanded ? "rotate-90" : "rotate-0"
  //               }`}
  //             ></span>
  //           </div>
  //         </div>
  //       </div>

  //       {/* Traveler details - only shown when expanded */}
  //       {isExpanded && (
  //         <div
  //           className={`overflow-hidden transition-max-height duration-1000 ease-in-out ${
  //             isExpanded ? "max-h-[5000px]" : "max-h-0"
  //           }`}
  //         >
  //           {/* Personal Information */}
  //           <TravelerSection title="Personal Information ee">
  //             <div className="grid grid-cols-2 gap-4 mb-4">
  //               {renderField(travelerIndex, "select", "title", "Title")}
  //               {renderField(travelerIndex, "select", "gender", "Gender")}
  //             </div>
  //             <div className="grid grid-cols-2 gap-4 mb-4">
  //               {renderField(travelerIndex, "text", "firstName", "First Name")}
  //               {renderField(travelerIndex, "text", "lastName", "Last Name")}
  //             </div>
  //             <div className="grid grid-cols-2 gap-4">
  //               {renderField(
  //                 travelerIndex,
  //                 "date",
  //                 "dateOfBirth",
  //                 "Date of Birth"
  //               )}
  //               {renderField(
  //                 travelerIndex,
  //                 "text",
  //                 "nationality",
  //                 "Nationality"
  //               )}
  //               {renderField(
  //                 travelerIndex,
  //                 "text",
  //                 "travelerType",
  //                 "Traveler Type"
  //               )}
  //             </div>
  //           </TravelerSection>

  //           {/* Document Information */}
  //           <TravelerSection title="Identification Documents">
  //             <div className="grid grid-cols-2 gap-4 mb-4">
  //               {renderField(
  //                 travelerIndex,
  //                 "text",
  //                 "documentNumber",
  //                 "Passport Number"
  //               )}
  //               {renderField(
  //                 travelerIndex,
  //                 "text",
  //                 "issuingCountry",
  //                 "Passport Issuing Country"
  //               )}
  //             </div>
  //             {renderField(
  //               travelerIndex,
  //               "date",
  //               "expirationDate",
  //               "Passport Expiry"
  //             )}
  //           </TravelerSection>

  //           {/* Contact Information */}
  //           <TravelerSection title="Contact Information">
  //             <div className="mb-4">
  //               {renderField(travelerIndex, "email", "contactEmail", "Email")}
  //             </div>
  //             {renderField(
  //               travelerIndex,
  //               "tel",
  //               "contactPhone",
  //               "Phone Number"
  //             )}
  //           </TravelerSection>
  //         </div>
  //       )}
  //     </div>
  //   );
  // };

  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <SectionHeading title="Traveler Details" />
            <div className="dark:text-white text-gray-700 text-sm">
              {travelersInfo.length}{" "}
              {travelersInfo.length === 1 ? "Traveler" : "Travelers"}
            </div>
          </div>

          {/* Edit/Save buttons */}
          <div className="mt-4 flex justify-end">
            {!isEditing ? (
              !loadingStatus &&
              !editable &&
              (isAgencyOwner ||
                ("role" in currentUser &&
                  (currentUser as any).role === "master") ||
                bookingStatus?.actionsAllowed) &&
              !nonEditableStatuses.includes(bookingStatus?.status || "") &&
              // Additional conditions for specific contexts
              // For Global Bookings (Master Booking Control): Allow editing for PENDING and BOOKING_CONFIRMED
              ((params.get("entryPoint") === "global-bookings" &&
                (bookingStatus?.status === "PENDING" ||
                  bookingStatus?.status === "BOOKING_CONFIRMED")) ||
                // For Manage Internal Booking: Allow editing for QUICK_HOLD and PENDING_APPROVAL
                (params.get("entryPoint") === "my-bookings" &&
                  (bookingStatus?.status === "QUICK_HOLD" ||
                    bookingStatus?.status === "PENDING_APPROVAL")) ||
                // For other contexts: Use existing logic (not in nonEditableStatuses)
                (params.get("entryPoint") !== "global-bookings" &&
                  params.get("entryPoint") !== "my-bookings")) ? (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // startEditing(expandedTraveler);
                    startEditingAll();
                    // setEditingTraveler(new Set([expandedTraveler]));
                    setExpandedTraveler(expandedTraveler);
                    // setOpenTravelerIndex(expandedTraveler);
                    setCurrentTravelerFormData({
                      ...travelersInfo[expandedTraveler],
                      errors: {}, // Clear any previous errors
                    });
                  }}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
                >
                  Edit Details
                </button>
              ) : null
            ) : (
              <div className="flex gap-3">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    cancelEditing(expandedTraveler);
                  }}
                  className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Save all travelers that are currently being edited
                    handleSaveAll();
                  }}
                  disabled={isSaving || !currentTravelerFormData}
                  className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 transition-colors flex items-center justify-center"
                >
                  {isSaving ? (
                    <Loader className="animate-spin mr-2 h-4 w-4" />
                  ) : (
                    "Save Changes"
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* {travelersInfo.map((_, index) => renderTravelerForm(index))} */}
        {travelersInfo.map((traveler, index) => {
          const normalizedTraveler = normalizeTravelerData(
            travelersInfo[index]
          );
          console.log({ traveler });
          console.log({ index });
          console.log({ normalizedTraveler });
          return (
            <div key={index}>
              {editingTraveler.has(index) ? (
                <TravelerForm
                  key={`edit-${index}`}
                  travelerNumber={index + 1}
                  traveler={normalizedTraveler}
                  isOpen={openTravelerIndex === index}
                  onToggle={() => {
                    // If clicking the currently expanded traveler and it's the only one, do nothing
                    if (
                      openTravelerIndex === index &&
                      travelersInfo.length === 1
                    ) {
                      return;
                    }
                    // Otherwise, toggle the clicked traveler
                    setOpenTravelerIndex(
                      openTravelerIndex === index ? -1 : index
                    );
                  }}
                  readOnly={false}
                  onUpdate={(updatedTraveler: Traveler) =>
                    handleTravelerUpdate(updatedTraveler, index)
                  }
                />
              ) : (
                // Keep the existing read-only view here
                <TravelerForm
                  key={`view-${index}`}
                  travelerNumber={index + 1}
                  traveler={{
                    ...normalizeTravelerData(traveler),
                    issuingCountry:
                      normalizeTravelerData(traveler)?.issuingCountry ||
                      traveler._original?.issuingCountry,
                    passportExpiry:
                      normalizeTravelerData(traveler)?.passportExpiry,
                    passportNumber:
                      normalizeTravelerData(traveler)?.passportNumber,
                  }}
                  isOpen={openTravelerIndex === index}
                  onToggle={() => {
                    // If clicking the currently expanded traveler and it's the only one, do nothing
                    if (
                      openTravelerIndex === index &&
                      travelersInfo.length === 1
                    ) {
                      return;
                    }
                    // Otherwise, toggle the clicked traveler
                    setOpenTravelerIndex(
                      openTravelerIndex === index ? -1 : index
                    );
                  }}
                  readOnly={true}
                  onUpdate={() => {}}
                />
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

// Additional Information alerts
const AdditionalInformation = () => (
  <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
    <CardHeader>
      <SectionHeading title="Approval Guidelines" />
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        <InfoAlert
          icon={<AlertCircle />}
          iconColor="yellow-500"
          title="Verification Required"
          text="Please verify all traveler details match the provided documentation before approving this request."
        />
        <InfoAlert
          icon={<AlertCircle />}
          iconColor="blue-500"
          title="Payment Verification"
          text="Confirm that the agent's payment details and transaction reference are valid before proceeding."
        />
        <InfoAlert
          icon={<AlertCircle />}
          iconColor="red-500"
          title="Rejection Policy"
          text="If rejecting this request, you must provide a detailed reason that will be sent to the requesting agent."
        />
      </div>
    </CardContent>
  </Card>
);

// Flight Card component
interface FlightCardProps {
  type: string;
  date: string;
  origin: string;
  destination: string;
  airline: string;
  duration: string;
  stops: string;
  departure: string;
  arrival: string;
  bgColor?: string;
}
const FlightCard = ({
  type,
  date,
  origin,
  destination,
  airline,
  duration,
  stops,
  departure,
  arrival,
  bgColor,
}: FlightCardProps) => (
  <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
    <div className="flex justify-between items-center mb-4">
      <div
        className={`${bgColor} dark:text-white text-gray-700 px-3 py-1 rounded-md font-medium text-sm inline-block`}
      >
        {type}
      </div>
      <div className="dark:text-white text-gray-700">{date}</div>
    </div>
    <div className="flex justify-between items-start mb-3">
      <div>
        <div className="flex items-center">
          <span className="text-xl font-bold dark:text-white text-gray-700 mr-2">
            {origin}
          </span>
          <span className="dark:text-white text-gray-700 mx-2">→</span>
          <span className="text-xl font-bold dark:text-white text-gray-700">
            {destination}
          </span>
        </div>
        <div className="dark:text-gray-300 text-gray-700 text-sm mt-1">
          {stops === "0" ? "Direct Flight" : `${stops} Stops`} • {duration}
        </div>
      </div>
      <div className="text-right dark:text-white text-gray-700 font-medium">
        {airline}
      </div>
    </div>
    <div className="flex dark:text-white text-gray-700 text-sm">
      <div className="mr-4">Departure: {departure}</div>
      <div>•</div>
      <div className="ml-4">Arrival: {arrival}</div>
    </div>
  </div>
);

// Itinerary Card
const ItineraryCard = ({
  data,
  departureFlight,
  returnFlight,
  isRoundTrip,
}: {
  data: any;
  departureFlight?: any;
  returnFlight?: any;
  isRoundTrip?: boolean;
}) => {
  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );

  // Get flight data from the correct structure
  const ticket =
    data?.fullTicket || data?.fullTicket?.ticket || data?.ticket || {};
  const hasReturnTicket = !!isRoundTrip;

  // Now you can access the data
  const { bookingResult } = bookingConfirmation;

  // Handle API response structure when coming from tables
  const apiTicket = bookingResult?.ticket || {};

  // Determine if we're using data from Redux or from API response
  const effectiveTicket = Object.keys(ticket).length > 0 ? ticket : apiTicket;

  const segments =
    effectiveTicket?.segments || effectiveTicket?.ticket?.segments || [];
  const flightClasses =
    bookingConfirmation?.ticket?.ticket?.flightClasses ||
    bookingConfirmation?.fullTicket?.ticket?.flightClasses ||
    effectiveTicket?.flightClasses ||
    departureFlight?.flightClasses ||
    [];
  const returnFlightClasses =
    bookingConfirmation?.ticket?.ticket?.returnFlightClasses ||
    bookingConfirmation?.fullTicket?.ticket?.returnFlightClasses ||
    effectiveTicket?.returnFlightClasses ||
    returnFlight?.flightClasses ||
    [];
  const flightClass = flightClasses[0] || {};
  const returnFlightClass = returnFlightClasses[0] || {};
  const price = flightClass?.price || {};
  const returnPrice = returnFlightClass?.price || {};

  // Get passenger counts from booking data or default to 1 adult
  const passengerCounts = data?.travelerData || {};

  const calculatedPassengerCounts = useMemo(() => {
    // Calculate from booking travelers if available
    const travelers =
      passengerCounts ||
      data?.bookingResult?.travelers ||
      data?.travelerData ||
      [];

    if (travelers.length > 0) {
      const now = new Date();

      const getAge = (birthDate: string) => {
        const birth = new Date(birthDate);
        let age = now.getFullYear() - birth.getFullYear();
        const monthDiff = now.getMonth() - birth.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && now.getDate() < birth.getDate())
        ) {
          age--;
        }
        return age;
      };

      let adults = 0;
      let children = 0;
      let infants = 0;

      travelers.forEach((traveler: any) => {
        const dob = traveler?.traveler?.dateOfBirth || traveler?.dateOfBirth;

        if (!dob) {
          // If no DOB, assume adult
          adults++;
          return;
        }

        const age = getAge(dob);
        if (age < 2) {
          infants++;
        } else if (age < 12) {
          children++;
        } else {
          adults++;
        }
      });

      return {
        adults,
        children,
        infants,
        travelClass:
          data?.bookingResult?.travelClass ||
          data?.fullTicket?.ticket?.flightClasses[0]?.type ||
          "Economy",
      };
    }

    // Fallback to 1 adult by default
    return {
      adults: 1,
      children: 0,
      infants: 0,
      travelClass: "Economy",
    };
  }, [passengerCounts, data]);

  // Get currency and prices
  const currency = price?.currency || "JOD";
  const adultPrice = price?.adult || 0;
  const childPrice = price?.child || 0;
  const infantPrice = price?.infant || 0;
  const returnAdultPrice = returnPrice?.adult || 0;
  const returnChildPrice = returnPrice?.child || 0;
  const returnInfantPrice = returnPrice?.infant || 0;
  const departureTaxRate = price?.tax / 100 || 0;
  const returnTaxRate = returnPrice?.tax / 100 || 0;

  // Calculate fares based on passenger counts
  const departureAdultFare =
    adultPrice * (calculatedPassengerCounts?.adults || 0);
  const departureChildFare =
    childPrice * (calculatedPassengerCounts?.children || 0);
  const departureInfantFare =
    infantPrice * (calculatedPassengerCounts?.infants || 0);
  const returnAdultsFare =
    returnAdultPrice * (calculatedPassengerCounts?.adults || 0);
  const returnChildrenFare =
    returnChildPrice * (calculatedPassengerCounts?.children || 0);
  const returnInfantsFare =
    returnInfantPrice * (calculatedPassengerCounts?.infants || 0);

  // Calculate base fares
  const outboundBaseFare =
    departureAdultFare + departureChildFare + departureInfantFare;
  const returnBaseFare =
    returnAdultsFare + returnChildrenFare + returnInfantsFare;

  // Calculate taxes separately for departure and return
  const departureTax = outboundBaseFare * departureTaxRate;
  const returnTax = hasReturnTicket ? returnBaseFare * returnTaxRate : 0;

  // Calculate taxes (for both outbound and return if applicable)
  const totalBaseFare = outboundBaseFare + returnBaseFare;
  const taxes = departureTax + returnTax;

  // Fixed transaction fee
  const transactionFee = 0;

  // Calculate total with fallback to bookedSeats totalPrice if available
  const total =
    totalBaseFare + taxes + transactionFee ||
    (bookingResult?.bookedSeats && bookingResult.bookedSeats.length > 0
      ? parseFloat(bookingResult.bookedSeats[0].totalPrice)
      : 0);

  // Set pendingBookingData when total changes (must be after total is defined)
  const [pendingBookingData, setPendingBookingData] = useState<any>(null);

  useEffect(() => {
    setPendingBookingData({
      ticket: effectiveTicket,
      flightClasses,
      returnFlightClasses,
      flightClass,
      returnFlightClass,
      price,
      returnPrice,
      passengerCounts,
      bookingResult,
      totalBaseFare,
      outboundBaseFare,
      returnBaseFare,
      taxes,
      transactionFee,
      total,
      adultPrice,
      childPrice,
      infantPrice,
      returnAdultPrice,
      returnChildPrice,
      returnInfantPrice,
      departureTaxRate,
      returnTaxRate,
    });
  }, [total]);

  const [bookingStatus, setBookingStatus] = useState<{
    isPaid: boolean;
    actionsAllowed: boolean;
  } | null>(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  // Fetch booking status
  useEffect(() => {
    const fetchBookingStatus = async (bookingId: string) => {
      try {
        setLoadingStatus(true);
        // Try to use getBookingStatus first, fall back to getBookingById if it fails
        try {
          if (!bookingId) {
            console.error("No bookingId provided to getBookingById");
            setLoadingStatus(false);
            return;
          }
          const response = await getBookingStatus(bookingId);
          if (response) {
            // Use the status information from the response
            const isPaid = response.isPaid || response.status === "PAID";
            const actionsAllowed =
              response.actionsAllowed ||
              (response.status !== "COMPLETED" &&
                response.status !== "CANCELLED");
            setBookingStatus({ isPaid, actionsAllowed });

            return; // Exit early if successful
          }
        } catch (statusError) {
          console.error(
            "Error using getBookingStatus, falling back to getBookingById:",
            statusError
          );
        }

        // Fall back to getBookingById if getBookingStatus fails
        const response = await getBookingById(bookingId);
        if (response) {
          // Adjust logic to handle missing actionsAllowed property
          // Assuming actions are allowed if status is not 'COMPLETED' or similar
          const isPaid = response.status === "PAID";
          const actionsAllowed =
            response.status !== "COMPLETED" && response.status !== "CANCELLED";
          setBookingStatus({ isPaid, actionsAllowed });
        } else {
          console.error("Failed to fetch booking status", response);
          setBookingStatus({ isPaid: false, actionsAllowed: true });
        }
      } catch (error) {
        console.error("Error fetching booking status:", error);
        setBookingStatus({ isPaid: false, actionsAllowed: true });
      } finally {
        setLoadingStatus(false);
      }
    };

    if (bookingResult?.id) {
      fetchBookingStatus(bookingResult.id);
    } else {
      setLoadingStatus(false);
    }
  }, [bookingResult]);

  // Get the first segment for flight details
  const firstSegment = segments[0] || {};

  // Get baggage allowance from flight class
  const carryOnAllowed = flightClass?.carryOnAllowed || 0;
  const carryOnWeight = flightClass?.carryOnWeight || 0;
  const checkedAllowed = flightClass?.checkedAllowed || 0;
  const checkedWeight = flightClass?.checkedWeight || 0;

  // Format baggage display
  const baggageDisplay = [];
  if (checkedAllowed > 0) {
    baggageDisplay.push(`${checkedAllowed} x ${checkedWeight} kg Checked`);
  }
  if (carryOnAllowed > 0) {
    baggageDisplay.push(`${carryOnAllowed} x ${carryOnWeight} kg Carry-on`);
  }

  // Get flight details with fallbacks from API response
  const flightDate = effectiveTicket?.ticket?.flightDate
    ? getFormatDateTable(effectiveTicket?.ticket?.flightDate)
    : effectiveTicket?.flightDate
    ? getFormatDateTable(effectiveTicket?.flightDate)
    : ticket[0]?.flightDate
    ? getFormatDateTable(ticket[0]?.flightDate)
    : "N/A";

  // Try to get origin/destination from different possible sources
  const origin =
    effectiveTicket?.departure?.airportCode ||
    effectiveTicket?.ticket?.departure?.airportCode ||
    firstSegment?.departure?.airportCode ||
    bookingResult?.meta?.departureAirport ||
    bookingResult?.meta?.departure?.departureAirport ||
    "N/A";

  const destination =
    effectiveTicket?.arrival?.airportCode ||
    effectiveTicket?.ticket?.arrival?.airportCode ||
    firstSegment?.arrival?.airportCode ||
    bookingResult?.meta?.arrivalAirport ||
    bookingResult?.meta?.departure?.arrivalAirport ||
    "N/A";

  const getAirlineInfo = (obj: any): string | undefined => {
    if (!obj) return undefined;
    const carrier = obj.carrier || obj[0]?.carrier;
    const flightNumber = obj.flightNumber || obj[0]?.flightNumber;
    return carrier && carrier !== "undefined" && carrier !== "null"
      ? carrier
      : undefined;
  };

  const airline = (() => {
    const sources = [
      bookingConfirmation?.fullTicket?.segments?.[0],
      bookingConfirmation?.ticket?.segments?.[0],
      bookingResult?.fullTicket?.segments?.[0],
      bookingResult?.ticket?.segments?.[0],
      bookingResult?.meta?.departure,
      bookingResult?.meta,
      ticket?.segments?.[0],
    ].filter(Boolean); // Remove any undefined/null sources

    for (const source of sources) {
      const carrier = getAirlineInfo(source);
      if (carrier) {
        const flightNumber = source.flightNumber || source[0]?.flightNumber;
        return flightNumber ? `${carrier} (${flightNumber})` : carrier;
      }
    }

    return "N/A";
  })();

  const duration =
    effectiveTicket?.ticket?.duration ||
    effectiveTicket?.duration ||
    ticket[0]?.duration ||
    "N/A";
  const stops =
    effectiveTicket?.ticket?.stops?.toString() ||
    effectiveTicket?.stops?.toString() ||
    ticket[0]?.stops?.toString() ||
    "N/A";

  const departureTime = effectiveTicket?.ticket?.departureTime
    ? getFormatTime(effectiveTicket?.ticket?.departureTime)
    : effectiveTicket?.departureTime
    ? getFormatTime(effectiveTicket?.departureTime)
    : ticket[0]?.departureTime
    ? getFormatTime(ticket[0]?.departureTime)
    : "N/A";

  const arrivalTime = effectiveTicket?.ticket?.arrivalTime
    ? getFormatTime(effectiveTicket?.ticket?.arrivalTime)
    : effectiveTicket?.arrivalTime
    ? getFormatTime(effectiveTicket?.arrivalTime)
    : ticket[0]?.arrivalTime
    ? getFormatTime(ticket[0]?.arrivalTime)
    : "N/A";

  // Get return flight details if it exists
  const returnFlightDate = hasReturnTicket
    ? getFormatDateTable(
        effectiveTicket?.returnFlightDate ||
          effectiveTicket?.ticket?.returnFlightDate
      )
    : null;
  const returnDuration = hasReturnTicket
    ? effectiveTicket?.returnDuration || effectiveTicket?.ticket?.returnDuration
    : null;
  const returnStops = hasReturnTicket
    ? effectiveTicket?.returnStops?.toString() ||
      effectiveTicket?.ticket?.returnStops?.toString() ||
      "0"
    : "0";
  const returnDepartureTime = hasReturnTicket
    ? getFormatTime(
        effectiveTicket?.returnDepartureTime ||
          effectiveTicket?.ticket?.returnDepartureTime
      )
    : null;
  const returnArrivalTime = hasReturnTicket
    ? getFormatTime(
        effectiveTicket?.returnArrivalTime ||
          effectiveTicket?.ticket?.returnArrivalTime
      )
    : null;
  const params = useSearchParams();
  const renderTitle = () => {
    if (params.get("entryPoint") === "global-bookings") {
      return "Traveler Itinerary";
    }
    if (
      params.get("entryPoint") === "booking-requests" ||
      data?.bookingResult?.status === "PENDING_APPROVAL" ||
      data?.bookingResult?.status === "QUICK_HOLD"
    ) {
      return "Requested Itinerary";
    }
    if (params.get("entryPoint") === "my-bookings") {
      return "Requested Itinerary";
    } else {
      return "Requested Itinerary";
    }
  };

  // Format traveler count with singular/plural form
  const formatTravelerCount = (count: number, type: string): string => {
    if (!count) return "";
    try {
      const singular = type.endsWith("s") ? type.slice(0, -1) : type;
      return `${count} ${count === 1 ? singular : type}`;
    } catch (error) {
      console.error("Error formatting traveler count:", error);
      return `${count} ${type}`;
    }
  };
  // Determine if we should show the first FlightCard
  const showFirstFlightCard = !isRoundTrip;
  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading title={renderTitle()} />
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          {showFirstFlightCard && (
            <FlightCard
              type="OUTBOUND"
              date={flightDate}
              origin={origin}
              destination={destination}
              airline={airline}
              duration={duration}
              departure={departureTime}
              stops={stops}
              arrival={arrivalTime}
              bgColor="bg-red-500/20"
            />
          )}
          {!returnFlight &&
            hasReturnTicket &&
            returnFlightDate &&
            returnDepartureTime &&
            returnArrivalTime && (
              <div className="mt-6">
                <FlightCard
                  type="RETURN"
                  date={returnFlightDate}
                  origin={destination}
                  destination={origin}
                  airline={airline}
                  duration={returnDuration || duration}
                  departure={returnDepartureTime}
                  stops={returnStops}
                  arrival={returnArrivalTime}
                  bgColor="bg-blue-500/20"
                />
              </div>
            )}
        </div>

        {/* Departure Flight */}
        {bookingConfirmation.itinerary === "round trip" && departureFlight && (
          <FlightCard
            type="OUTBOUND"
            date={getFormatDateTable(departureFlight.flightDate)}
            origin={departureFlight.departure.airportCode}
            destination={departureFlight.arrival.airportCode}
            airline={`${departureFlight.airline} ${
              departureFlight.flightNumber
                ? `(${departureFlight.flightNumber})`
                : ""
            }`}
            duration={departureFlight.duration || "N/A"}
            stops={departureFlight.stops.toString()}
            departure={
              departureFlight.departure.time
                ? getFormatTime(departureFlight.departure.time)
                : "N/A"
            }
            arrival={
              departureFlight.arrival.time
                ? getFormatTime(departureFlight.arrival.time)
                : "N/A"
            }
            bgColor="bg-red-500/20"
          />
        )}

        {/* Return Flight */}
        {bookingConfirmation.itinerary === "round trip" &&
          isRoundTrip &&
          returnFlight && (
            <>
              <div className="flex items-center gap-2 mt-6">
                <div className="h-px flex-grow bg-gray-400/50 dark:bg-gray-500"></div>
                <div className="text-gray-500 dark:text-white bg-gray-300 dark:bg-gray-600 rounded-full p-2 h-8 w-8 flex items-center justify-center">
                  <span>↕</span>
                </div>
                <div className="h-px flex-grow bg-gray-400/50 dark:bg-gray-500"></div>
              </div>
              <div className="my-6">
                <FlightCard
                  type="RETURN"
                  date={getFormatDateTable(returnFlight.flightDate)}
                  origin={returnFlight.departure.airportCode}
                  destination={returnFlight.arrival.airportCode}
                  airline={`${returnFlight.airline} ${
                    returnFlight.flightNumber
                      ? `(${returnFlight.flightNumber})`
                      : ""
                  }`}
                  duration={returnFlight.duration || "N/A"}
                  stops={returnFlight.stops.toString()}
                  departure={
                    returnFlight.departure.time
                      ? getFormatTime(returnFlight.departure.time)
                      : "N/A"
                  }
                  arrival={
                    returnFlight.arrival.time
                      ? getFormatTime(returnFlight.arrival.time)
                      : "N/A"
                  }
                  bgColor="bg-blue-500/20"
                />
              </div>
            </>
          )}

        <div>
          <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
            <div className="flex justify-between items-center mb-3">
              <div className="dark:text-white text-gray-700 font-medium">
                Passenger & Baggage
              </div>
            </div>
            <div className="h-px dark:bg-gray-600 bg-gray-200 w-full mb-3"></div>
            <div className="flex justify-between items-center">
              <div className="dark:text-white text-gray-700 flex items-center">
                {calculatedPassengerCounts.adults +
                  calculatedPassengerCounts.children +
                  calculatedPassengerCounts.infants}{" "}
                {calculatedPassengerCounts.adults +
                  calculatedPassengerCounts.children +
                  calculatedPassengerCounts.infants >
                1
                  ? "Travelers"
                  : "Traveler"}{" "}
                • {calculatedPassengerCounts.adults}{" "}
                {calculatedPassengerCounts.adults === 1 ? "Adult" : "Adults"}
                {calculatedPassengerCounts.children > 0 &&
                  ` • ${calculatedPassengerCounts.children} ${
                    calculatedPassengerCounts.children === 1
                      ? "Child"
                      : "Children"
                  }`}
                {calculatedPassengerCounts.infants > 0 &&
                  ` • ${calculatedPassengerCounts.infants} ${
                    calculatedPassengerCounts.infants === 1
                      ? "Infant"
                      : "Infants"
                  }`}
              </div>
              <div className="dark:text-gray-300 text-gray-700 text-sm flex items-center gap-2">
                <p className="capitalize">
                  {passengerCounts?.travelClass ||
                    flightClass?.type ||
                    (bookingResult?.bookedSeats &&
                    bookingResult.bookedSeats.length > 0
                      ? bookingResult.bookedSeats[0].flightClass
                      : "Economy")}
                </p>
                {baggageDisplay.length > 0 ? (
                  baggageDisplay.map((item, index) => (
                    <span key={index}>
                      {" • "}
                      {item}
                    </span>
                  ))
                ) : (
                  <span>• No baggage included</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Booking Actions
const BookingActions = ({
  bookingStatus,
  handleOpenPayment,
  onReleaseSeat,
  onCompletePayment,
  isAgencyOwner,
  disabled,
}: {
  bookingStatus: { isPaid: boolean; actionsAllowed: boolean } | null;
  handleOpenPayment: () => void;
  onReleaseSeat: () => void;
  onCompletePayment?: () => void;
  isAgencyOwner?: boolean;
  disabled?: boolean;
}) => {
  const [showReleaseConfirm, setShowReleaseConfirm] = useState(false);
  const [isReleasing, setIsReleasing] = useState(false);
  const [showReleaseInProgress, setShowReleaseInProgress] = useState(false);

  const handleReleaseClick = () => {
    setShowReleaseConfirm(true);
  };

  const confirmRelease = async () => {
    setShowReleaseConfirm(false);
    setShowReleaseInProgress(true);
    setIsReleasing(true);

    try {
      // Show loading for 15 seconds before actually releasing
      await new Promise((resolve) => setTimeout(resolve, 15000));
      await onReleaseSeat();

      // Redirect to My Bookings after successful release
      window.location.href = "/ticket-hub/myBookings";
    } catch (error) {
      console.error("Error releasing seat:", error);
      // Handle error (you might want to show an error message)
    } finally {
      setIsReleasing(false);
      setShowReleaseInProgress(false);
    }
  };

  const cancelRelease = () => {
    setShowReleaseConfirm(false);
  };

  return (
    <div className="space-y-3">
      <button
        onClick={disabled ? undefined : handleOpenPayment}
        disabled={disabled}
        className={`w-full py-3 px-6 font-semibold rounded-lg transition-colors duration-300 ${
          disabled
            ? "bg-gray-400 cursor-not-allowed text-white"
            : "bg-blue-500 hover:bg-blue-600 text-white"
        }`}
      >
        Complete Payment
      </button>
      <button
        onClick={handleReleaseClick}
        disabled={isReleasing || disabled}
        className={`w-full py-3 px-6 text-white font-semibold rounded-lg transition-colors duration-300 ${
          isReleasing || disabled
            ? "bg-gray-400 cursor-not-allowed"
            : "bg-red-500 hover:bg-red-600"
        }`}
      >
        {isReleasing ? "Releasing Seat..." : "Release Seat"}
      </button>

      {/* Release Confirmation Popup */}
      {showReleaseConfirm && !disabled && (
        <BookingActionConfirmation
          onConfirm={confirmRelease}
          onCancel={cancelRelease}
          actionType="reject"
          title="Confirm Release Seat"
          message="Are you sure you want to release this seat? This action cannot be undone."
          isLoading={isReleasing}
        />
      )}

      {/* Release In Progress Popup */}
      {showReleaseInProgress && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 text-center py-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 max-w-md w-full">
            <div className="text-center flex flex-col justify-center items-center">
              <div className="mb-4">
                <Loader className="h-12 w-12 text-red-500 animate-spin" />
              </div>
              <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                Releasing Seat
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Please wait while we process your request...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Request Actions
const RequestActions = ({
  bookingStatus,
  handleOpenPayment,
  onReleaseSeat,
  onCompletePayment,
}: {
  bookingStatus: { isPaid: boolean; actionsAllowed: boolean } | null;
  handleOpenPayment: () => void;
  onReleaseSeat: () => void;
  onCompletePayment?: () => void;
}) => {
  return (
    <div className="space-y-3">
      <button
        onClick={onCompletePayment}
        className="w-full py-3 px-6 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 transition-colors duration-300"
      >
        Approve Request
      </button>
      <button
        onClick={onReleaseSeat}
        className="w-full py-3 px-6 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors duration-300"
      >
        Reject Request
      </button>
    </div>
  );
};

// Master Actions
const MasterActions = ({
  bookingStatus,
  bookingSource,
  handleOpenPayment,
  onReleaseSeat,
  onCompletePayment,
  isAgencyOwner,
  handleCancelBooking,
  handleRefundBooking,
  handleRescheduleBooking,
}: {
  bookingStatus: string | null;
  bookingSource?: "INTERNAL" | "THIRD_PARTY" | null;
  handleOpenPayment: () => void;
  onReleaseSeat: () => void;
  onCompletePayment?: () => void;
  isAgencyOwner?: boolean;
  handleCancelBooking?: () => void;
  handleRefundBooking?: () => void;
  handleRescheduleBooking?: () => void;
}) => {
  const isDisabled = bookingStatus !== "BOOKING_CONFIRMED";
  // Determine booking source type
  const isInternalBooking = bookingSource === "INTERNAL";
  const isThirdPartyBooking = bookingSource === "THIRD_PARTY";
  const isDisabledCancel = isDisabled || isThirdPartyBooking;
  const isDisabledRefund = isDisabled || isInternalBooking;
  const disabledTooltip =
    "This action is only available for confirmed bookings";
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showRefundConfirm, setShowRefundConfirm] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingAction, setProcessingAction] = useState<
    "cancel" | "refund" | null
  >(null);
  const router = useRouter();

  const handleCancelClick = () => {
    if (isDisabled) return;
    setShowCancelConfirm(true);
  };

  const handleRefundClick = () => {
    if (isDisabled) return;
    setShowRefundConfirm(true);
  };

  const handleConfirmCancel = async () => {
    if (!handleCancelBooking) return;

    setProcessingAction("cancel");
    setIsProcessing(true);
    setShowCancelConfirm(false);

    try {
      // Wait for 5 seconds before processing the cancellation
      await new Promise((resolve) => setTimeout(resolve, 5000));
      await handleCancelBooking();
      // Wait for 15 seconds before redirecting
      setTimeout(() => {
        router.push("/master-control/global-bookings");
      }, 15000);
    } catch (error) {
      console.error("Error cancelling booking:", error);
      setIsProcessing(false);
      setProcessingAction(null);
    }
  };

  const handleConfirmRefund = async () => {
    if (!handleRefundBooking) return; // We'll use cancelBooking as the refund action

    setProcessingAction("refund");
    setIsProcessing(true);
    setShowRefundConfirm(false);

    try {
      // Wait for 5 seconds before processing the refund
      await new Promise((resolve) => setTimeout(resolve, 5000));
      // Process the refund directly
      await handleRefundBooking(); // This will trigger the refund process

      // Wait for 15 seconds before redirecting
      setTimeout(() => {
        router.push("/master-control/global-bookings");
      }, 15000);
    } catch (error: any) {
      console.error("Error processing refund:", error);
      setIsProcessing(false);
      setProcessingAction(null);
    }
  };

  const handleCloseCancel = () => {
    setShowCancelConfirm(false);
  };

  const handleCloseRefund = () => {
    setShowRefundConfirm(false);
  };

  return (
    <div className="space-y-3">
      {/* Cancel Booking Button – INTERNAL bookings only */}
      <button
        onClick={handleCancelClick}
        title={isDisabledCancel ? disabledTooltip : "Cancel this booking"}
        className={`w-full py-3 px-6 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors duration-300 ${
          isDisabledCancel ? "cursor-not-allowed opacity-50" : ""
        }`}
        disabled={isDisabledCancel || isProcessing}
      >
        Cancel Booking
      </button>

      {/* Refund Booking Button – THIRD-PARTY bookings only */}
      <button
        onClick={handleRefundClick}
        title={isDisabledRefund ? disabledTooltip : "Refund this booking"}
        className={`w-full py-3 px-6 ${
          isDisabledRefund ? "cursor-not-allowed opacity-50" : ""
        } bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 transition-colors duration-300`}
        disabled={isDisabledRefund || isProcessing}
      >
        Refund Booking
      </button>

      {/* Reschedule Booking Button */}
      <button
        onClick={() => (isDisabled ? null : handleRescheduleBooking?.())}
        title={isDisabled ? disabledTooltip : "Reschedule this booking"}
        className={`w-full py-3 px-6 ${
          isDisabled ? "cursor-not-allowed opacity-50" : ""
        } bg-orange-500 text-white font-semibold rounded-lg hover:bg-orange-600 transition-colors duration-300`}
        disabled={isDisabled || isProcessing}
      >
        Reschedule Booking
      </button>

      {/* Cancel Confirmation Dialog */}
      {showCancelConfirm && (
        <BookingActionConfirmation
          onConfirm={handleConfirmCancel}
          onCancel={handleCloseCancel}
          message="Are you sure you want to cancel this booking?"
          title="Confirm Action"
          actionType="reject"
          isLoading={isProcessing}
        />
      )}

      {/* Refund Confirmation Dialog */}
      {showRefundConfirm && (
        <BookingActionConfirmation
          onConfirm={handleConfirmRefund}
          onCancel={handleCloseRefund}
          message="Are you sure you want to refund this booking?"
          title="Confirm Action"
          actionType="reject"
          isLoading={isProcessing}
        />
      )}

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <Loader className="h-12 w-12 text-red-500 animate-spin" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {processingAction === "cancel"
                  ? "Canceling booking, please wait..."
                  : "Refunding booking, please wait..."}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This may take a few moments. You will be redirected to the
                Global Bookings page when complete.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Payment Summary sidebar
const PaymentSummary = ({
  data,
  onReleaseSeat,
  onCompletePayment,
  isAgencyOwner = false,
  handleCancelBooking,
  handleRefundBooking,
  handleRescheduleBooking,
  viewType,
  nonActionableStatuses,
  nonEditableStatuses,
  timeLeft,
  formatTimer,
  handleDocumentAction,
  isAgent,
  departureFlight,
  returnFlight,
  isRoundTrip,
}: {
  data: any;
  onReleaseSeat: () => void;
  onCompletePayment?: () => void;
  isAgencyOwner?: boolean;
  handleCancelBooking?: () => void;
  handleRefundBooking?: () => void;
  handleRescheduleBooking?: () => void;
  viewType: string;
  nonActionableStatuses: string[];
  nonEditableStatuses: string[];
  timeLeft: number;
  formatTimer: (seconds: number) => string;
  handleDocumentAction: (
    type: "e-ticket" | "receipt",
    action: "download" | "print"
  ) => Promise<void>;
  isAgent?: boolean;
  departureFlight?: any;
  returnFlight?: any;
  isRoundTrip?: boolean;
}) => {
  const router = useRouter();
  const params = useSearchParams();

  // State to control payment modal visibility
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  // Optionally, store booking data if passed from confirmation
  const [pendingBookingData, setPendingBookingData] = useState<any>(null);

  useEffect(() => {
    // Check if redirected from confirmation and booking data is in query or storage
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      if (params.get("fromConfirmation") === "true") {
        // Optionally, retrieve booking data from storage or context if needed
        // setPendingBookingData();
      }
    }
  }, []);

  // Handler to open payment modal
  const handleOpenPayment = () => {
    setShowPaymentModal(true);
  };
  // Handler to close payment modal
  const handleClosePayment = () => {
    setShowPaymentModal(false);
  };

  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );

  // Now you can access the data
  const { bookingResult } = bookingConfirmation;

  // Get data from the booking
  const ticket = data?.fullTicket || {};
  // const bookingResult = data?.bookingResult || {};

  // Handle API response structure when coming from tables
  const apiTicket = bookingResult?.ticket || {};

  // Determine if we're using data from Redux or from API response
  const effectiveTicket = Object.keys(ticket).length > 0 ? ticket : apiTicket;

  const flightClasses = !isRoundTrip
    ? bookingConfirmation?.ticket?.ticket?.flightClasses ||
      bookingConfirmation?.fullTicket?.ticket?.flightClasses ||
      effectiveTicket?.flightClasses
    : departureFlight?.flightClasses ||
      ticket[0]?.flightClasses ||
      bookingConfirmation?.fullTicket?.[0]?.ticket?.flightClasses ||
      [];
  const returnFlightClasses = !isRoundTrip
    ? bookingConfirmation?.ticket?.ticket?.returnFlightClasses ||
      bookingConfirmation?.fullTicket?.ticket?.returnFlightClasses ||
      effectiveTicket?.returnFlightClasses
    : returnFlight?.flightClasses ||
      returnFlight?.returnFlightClasses ||
      ticket[1]?.returnFlight?.returnFlightClasses ||
      ticket[1]?.returnFlightClasses ||
      bookingConfirmation?.fullTicket?.[1]?.ticket?.returnFlightClasses ||
      [];

  // Make sure flightClasses is always an array too
  const safeFlightClasses = Array.isArray(flightClasses) ? flightClasses : [];
  const safeReturnFlightClasses = Array.isArray(returnFlightClasses)
    ? returnFlightClasses
    : [];

  const flightClass = safeFlightClasses[0] || {};
  const returnFlightClass = safeReturnFlightClasses[0] || {};
  const price = flightClass?.price || {};
  const returnPrice = returnFlightClass?.price || {};

  // Get passenger counts from booking data or default to 1 adult
  const passengerCounts = data?.travelerData || {};

  const calculatedPassengerCounts = useMemo(() => {
    // Calculate from booking travelers if available
    const travelers =
      passengerCounts ||
      data?.bookingResult?.travelers ||
      data?.travelerData ||
      [];

    if (travelers.length > 0) {
      const now = new Date();

      const getAge = (birthDate: string) => {
        const birth = new Date(birthDate);
        let age = now.getFullYear() - birth.getFullYear();
        const monthDiff = now.getMonth() - birth.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && now.getDate() < birth.getDate())
        ) {
          age--;
        }
        return age;
      };

      let adults = 0;
      let children = 0;
      let infants = 0;

      travelers.forEach((traveler: any) => {
        const dob = traveler?.traveler?.dateOfBirth || traveler?.dateOfBirth;

        if (!dob) {
          // If no DOB, assume adult
          adults++;
          return;
        }

        const age = getAge(dob);
        if (age < 2) {
          infants++;
        } else if (age < 12) {
          children++;
        } else {
          adults++;
        }
      });

      return {
        adults,
        children,
        infants,
        travelClass:
          data?.bookingResult?.travelClass ||
          data?.fullTicket?.ticket?.flightClasses[0]?.type ||
          "Economy",
      };
    }

    // Fallback to 1 adult by default
    return {
      adults: 1,
      children: 0,
      infants: 0,
      travelClass: "Economy",
    };
  }, [passengerCounts, data]);

  // Check if return ticket exists
  const hasReturnTicket =
    (Array.isArray(returnFlightClasses) && returnFlightClasses.length > 0) ||
    (Array.isArray(bookingResult?.returnFlightClasses) &&
      bookingResult.returnFlightClasses.length > 0) ||
    (Array.isArray(bookingResult) && bookingResult.length > 1) ||
    Boolean(isRoundTrip);

  // Get currency and prices
  const currency = price?.currency || "JOD";
  const adultPrice = price?.adult || 0;
  const childPrice = price?.child || 0;
  const infantPrice = price?.infant || 0;
  const returnAdultPrice = returnPrice?.adult || 0;
  const returnChildPrice = returnPrice?.child || 0;
  const returnInfantPrice = returnPrice?.infant || 0;
  const departureTaxRate = price?.tax / 100 || 0;
  const returnTaxRate = returnPrice?.tax / 100 || 0;

  // Calculate fares based on passenger counts
  const departureAdultsFare =
    adultPrice * (calculatedPassengerCounts?.adults || 0);
  const departureChildrenFare =
    childPrice * (calculatedPassengerCounts?.children || 0);
  const departureInfantsFare =
    infantPrice * (calculatedPassengerCounts?.infants || 0);
  const returnAdultsFare =
    returnAdultPrice * (calculatedPassengerCounts?.adults || 0);
  const returnChildrenFare =
    returnChildPrice * (calculatedPassengerCounts?.children || 0);
  const returnInfantsFare =
    returnInfantPrice * (calculatedPassengerCounts?.infants || 0);

  // Calculate base fares
  const outboundBaseFare =
    departureAdultsFare + departureChildrenFare + departureInfantsFare;
  const returnBaseFare =
    returnAdultsFare + returnChildrenFare + returnInfantsFare;

  // Calculate taxes separately for departure and return
  const departureTax = outboundBaseFare * departureTaxRate;
  const returnTax = hasReturnTicket ? returnBaseFare * returnTaxRate : 0;

  // Calculate taxes (for both outbound and return if applicable)
  const totalBaseFare = outboundBaseFare + returnBaseFare;
  const taxes = departureTax + returnTax;

  // Fixed transaction fee
  const transactionFee = 0;
  // Calculate total with fallback to bookedSeats totalPrice if available
  const total =
    totalBaseFare + taxes + transactionFee ||
    (bookingResult?.bookedSeats && bookingResult.bookedSeats.length > 0
      ? parseFloat(bookingResult.bookedSeats[0].totalPrice)
      : 0);

  const outboundFareBreakdown = [
    {
      label: "Adult",
      count: calculatedPassengerCounts.adults,
      perPersonValue: adultPrice,
      value: departureAdultsFare,
    },
    {
      label: "Child",
      count: calculatedPassengerCounts.children,
      perPersonValue: childPrice,
      value: departureChildrenFare,
    },
    {
      label: "Infant",
      count: calculatedPassengerCounts.infants,
      perPersonValue: infantPrice,
      value: departureInfantsFare,
    },
  ].filter((i) => i.count > 0);

  const returnFareBreakdown = hasReturnTicket
    ? [
        {
          label: "Adult",
          count: calculatedPassengerCounts.adults,
          perPersonValue: returnAdultPrice,
          value: returnAdultsFare,
        },
        {
          label: "Child",
          count: calculatedPassengerCounts.children,
          perPersonValue: returnChildPrice,
          value: returnChildrenFare,
        },
        {
          label: "Infant",
          count: calculatedPassengerCounts.infants,
          perPersonValue: returnInfantPrice,
          value: returnInfantsFare,
        },
      ].filter((i) => i.count > 0)
    : [];

  // Set pendingBookingData when total changes (must be after total is defined)
  useEffect(() => {
    setPendingBookingData({
      ticket,
      flightClasses,
      returnFlightClasses,
      flightClass,
      returnFlightClass,
      price,
      returnPrice,
      passengerCounts,
      bookingResult,
      departureTaxRate,
      returnTaxRate,
      totalBaseFare,
      outboundBaseFare,
      returnBaseFare,
      taxes,
      transactionFee,
      total,
      departureAdultsFare,
      departureChildrenFare,
      departureInfantsFare,
      returnAdultsFare,
      returnChildrenFare,
      returnInfantsFare,
      outboundFareBreakdown,
      returnFareBreakdown,
    });
  }, [total]);

  const [bookingStatus, setBookingStatus] = useState<{
    isPaid: boolean;
    actionsAllowed: boolean;
  } | null>(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  // Fetch booking status
  useEffect(() => {
    const fetchBookingStatus = async (bookingId: string) => {
      try {
        setLoadingStatus(true);

        if (!bookingId) {
          console.error("No bookingId provided to getBookingById");
          setLoadingStatus(false);
          return;
        }
        const response = await getBookingById(bookingId);
        if (response) {
          setBookingStatus({
            isPaid: response.status === "PAID",
            actionsAllowed: response.status !== "COMPLETED",
          });
        } else {
          console.error("Failed to fetch booking status");
        }
      } catch (error) {
        console.error("Error fetching booking status:", error);
      } finally {
        setLoadingStatus(false);
      }
    };

    if (data?.id) {
      fetchBookingStatus(data.id);
    } else if (data?.booking?.id) {
      fetchBookingStatus(data.booking.id);
    } else {
      setLoadingStatus(false);
    }
  }, [data]);

  const renderTitle = () => {
    const entryPoint = params.get("entryPoint");
    if (entryPoint === "global-bookings") return "Master Booking Control";
    if (entryPoint === "booking-requests") return "Manage Booking Request";
    if (entryPoint === "my-bookings") return "Manage Internal Booking";
    return "Manage Internal Booking";
  };

  const renderActionTitle = () => {
    if (renderTitle() === "Manage Booking Request") return "Request Actions";
    if (renderTitle() === "Manage Internal Booking") return "Booking Actions";
    if (renderTitle() === "Master Booking Control") return "Master Actions";
    return "Booking Actions"; // Default fallback
  };

  // Only show buttons for allowed booking statuses
  const notAllowedSubRoles = [
    "BOOKING_CONFIRMED",
    "BOOKING_REJECTED",
    "CANCELLED_BY_USER",
    "CANCELLED_BY_SYSTEM",
    "TIMED_OUT",
    "PENDING_APPROVAL",
  ];
  const currentStatus =
    data?.bookingResult?.status ||
    data?.status ||
    departureFlight?.status ||
    returnFlight?.status;
  const title = renderTitle();

  const isQuickHold = useMemo(() => {
    if (isRoundTrip) {
      // For round trips, check both flights
      return (
        (departureFlight?.status === "QUICK_HOLD" ||
          returnFlight?.status === "QUICK_HOLD") &&
        timeLeft > 0
      );
    }
    // For one-way trips, check the main status
    return currentStatus === "QUICK_HOLD" && timeLeft > 0;
  }, [
    isRoundTrip,
    departureFlight?.status,
    returnFlight?.status,
    currentStatus,
    timeLeft,
  ]);

  const renderActionButtons = () => {
    // Always show Master Actions in Master Booking Control view, regardless of booking status
    if (title === "Master Booking Control") {
      return (
        <MasterActions
          bookingStatus={data?.bookingStatus}
          bookingSource={
            data?.fullTicket?.source ?? data?.bookingResult?.source ?? null
          }
          handleOpenPayment={handleOpenPayment}
          onReleaseSeat={onReleaseSeat}
          onCompletePayment={onCompletePayment}
          isAgencyOwner={isAgencyOwner}
          handleCancelBooking={handleCancelBooking}
          handleRefundBooking={handleRefundBooking}
          handleRescheduleBooking={handleRescheduleBooking}
        />
      );
    }

    // Handle other views when bookingStatus is null
    if (!bookingStatus) {
      if (title === "Manage Booking Request") {
        return (
          <RequestActions
            bookingStatus={{ isPaid: false, actionsAllowed: false }}
            handleOpenPayment={handleOpenPayment}
            onReleaseSeat={onReleaseSeat}
            onCompletePayment={onCompletePayment}
          />
        );
      } else if (
        title === "Manage Internal Booking" &&
        (currentStatus === "QUICK_HOLD" || currentStatus === "PENDING_APPROVAL")
      ) {
        return (
          <BookingActions
            bookingStatus={{ isPaid: false, actionsAllowed: false }}
            handleOpenPayment={handleOpenPayment}
            onReleaseSeat={onReleaseSeat}
            onCompletePayment={onCompletePayment}
            isAgencyOwner={isAgencyOwner}
            disabled={isQuickHold && !isAgent}
          />
        );
        // } else if (title === "Master Booking Control") {
        //   return (
        //     <MasterActions
        //       bookingStatus={{ isPaid: false, actionsAllowed: false }}
        //       handleOpenPayment={handleOpenPayment}
        //       onReleaseSeat={onReleaseSeat}
        //       onCompletePayment={onCompletePayment}
        //       isAgencyOwner={isAgencyOwner}
        //       handleCancelBooking={handleCancelBooking}
        //       handleRescheduleBooking={handleRescheduleBooking}
        //     />
        //   );
      }
      return null;
    }

    // For non-null bookingStatus
    const normalizedStatus = data?.bookingStatus
      ? normalizeBookingStatus(data.bookingStatus)
      : "Unknown";
    const isActionable = !nonActionableStatuses.includes(normalizedStatus);
    const allowedRoleTypes = [
      "master_owner",
      "master_admin",
      "master_accountant",
      "master_moderator",
      "agency_owner",
      "agency_admin",
      "agency_accountant",
      "agency_operation",
      "agency_sales",
    ];
    const currentUser = data?.currentUser || {};
    const isAllowedRole =
      "roleType" in currentUser &&
      allowedRoleTypes.includes(currentUser.roleType);
    const showRequestActions =
      normalizedStatus === "Pending Approval" && isAllowedRole;

    // Always show Master Actions in Master Booking Control view
    if (renderTitle() === "Master Booking Control") {
      return (
        <MasterActions
          bookingStatus={
            data.bookingStatus || { isPaid: false, actionsAllowed: true }
          }
          bookingSource={
            data?.fullTicket?.source ?? data?.bookingResult?.source ?? null
          }
          handleOpenPayment={handleOpenPayment}
          onReleaseSeat={onReleaseSeat}
          onCompletePayment={onCompletePayment}
          isAgencyOwner={isAgencyOwner}
          handleCancelBooking={handleCancelBooking}
          handleRescheduleBooking={handleRescheduleBooking}
        />
      );
    }

    // For other views, check role and actionability
    if (!isActionable || !isAllowedRole) {
      return null;
    }

    // Default to BookingActions for all other cases
    return (
      <BookingActions
        bookingStatus={data.bookingStatus}
        handleOpenPayment={handleOpenPayment}
        onReleaseSeat={onReleaseSeat}
        onCompletePayment={onCompletePayment}
        isAgencyOwner={isAgencyOwner}
        disabled={isQuickHold && !isAgent}
      />
    );
  };

  return (
    <Card className="bg-gray-200 dark:bg-gray-800 border-0 shadow-lg">
      <CardHeader className="pb-3">
        <SectionHeading
          title={
            viewType === "master" ||
            (viewType === "internal" &&
              bookingResult?.status === "BOOKING_CONFIRMED")
              ? "Booking Summary"
              : "Request Summary"
          }
        />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Payment pending verification */}
          <div className="p-4 rounded-lg mb-4 dark:bg-gray-700 bg-gray-100">
            <div className="flex items-center mb-4">
              {data?.bookingResult?.transactionId === null ||
              bookingResult?.[0]?.transactionId === null ||
              bookingResult?.[1]?.transactionId === null ? (
                <AlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
              ) : (
                <CircleCheckBig className="h-5 w-5 text-green-500 mr-2" />
              )}
              <h3 className="font-semibold dark:text-white text-gray-700">
                {data?.bookingResult?.transactionId === null ||
                bookingResult?.[0]?.transactionId === null ||
                bookingResult?.[1]?.transactionId === null
                  ? "Payment Pending Verification"
                  : "Payment Completed"}
              </h3>
            </div>
            <div className="h-px dark:bg-gray-700 bg-gray-300 w-full mb-3"></div>
            <div className="text-sm dark:text-white text-gray-700">
              <p className="flex items-center justify-between mb-2">
                <span className="dark:text-gray-300 text-gray-700">
                  Transaction ID:
                </span>
                <span>
                  {data?.bookingResult?.transactionId ||
                  bookingResult?.[0]?.transactionId ||
                  bookingResult?.[1]?.transactionId
                    ? data?.bookingResult?.transactionId ||
                      bookingResult?.[0]?.transactionId ||
                      bookingResult?.[1]?.transactionId
                    : "Pending"}
                </span>
              </p>
              <p className="flex items-center justify-between">
                <span className="dark:text-gray-300 text-gray-700">
                  Transaction Date:
                </span>
                <span>
                  {data?.bookingResult?.transactionDate ||
                  bookingResult?.[0]?.transactionDate ||
                  bookingResult?.[1]?.transactionDate
                    ? getFormatDateTable(
                        data?.bookingResult?.transactionDate ||
                          bookingResult?.[0]?.transactionDate ||
                          bookingResult?.[1]?.transactionDate
                      )
                    : "Pending"}
                </span>
              </p>
            </div>
          </div>

          {/* Price breakdown */}
          <div className="dark:bg-gray-700 bg-gray-100 space-y-3 p-4 rounded-lg">
            <h3 className="font-semibold mb-2 dark:text-white text-gray-700">
              Price Details
            </h3>
            <div className="h-px dark:bg-gray-600 bg-gray-300 w-full mb-3"></div>

            {/* Get flight class and price information */}
            <>
              {/* OUTBOUND FARE */}
              <div className="flex justify-start mb-2">
                <span className="bg-red-500/20  dark:text-white text-gray-700 px-3 py-1 rounded-md font-medium text-sm inline-block">
                  OUTBOUND
                </span>
              </div>

              {/* Outbound Base Fare */}
              <div className="block mb-3">
                {outboundFareBreakdown.map((item: any) => (
                  <FareBreakdownRow
                    key={`departure-${item.label}`}
                    item={item}
                    currency={currency}
                  />
                ))}
              </div>

              {/* RETURN FARE - Only show if return ticket exists */}
              {hasReturnTicket && (
                <>
                  <div className="flex justify-start mb-2 mt-4">
                    <span className="bg-blue-500/20 dark:text-white text-gray-700 px-3 py-1 rounded-md font-medium text-sm inline-block">
                      RETURN
                    </span>
                  </div>

                  {/* Return Base Fare */}
                  <div className="block mb-3">
                    {returnFareBreakdown.map((item: any) => (
                      <FareBreakdownRow
                        key={`departure-${item.label}`}
                        item={item}
                        currency={currency}
                      />
                    ))}
                  </div>
                </>
              )}
              {/* Taxes */}
              <div className="h-px bg-gray-300 dark:bg-gray-600 w-full my-2"></div>
              <div className="flex justify-between mt-4">
                <span className="dark:text-gray-300 text-gray-700">Taxes</span>
                <span className="dark:text-white text-gray-700">
                  {taxes >= 0 ? `${taxes.toFixed(2)} ${currency}` : "N/A"}
                </span>
              </div>

              {/* Transaction Fee */}
              <div className="flex justify-between">
                <span className="dark:text-gray-300 text-gray-700">
                  Transaction Fee
                </span>
                <span className="dark:text-white text-gray-700">
                  {transactionFee > 0
                    ? `${transactionFee.toFixed(2)} ${currency}`
                    : "0.00 " + currency}
                </span>
              </div>

              {/* Total */}
              <div className="h-px bg-gray-300 dark:bg-gray-600 w-full my-2"></div>
              <div className="pt-1 flex justify-between font-semibold">
                <span className="dark:text-white text-gray-700">Total</span>
                <span className="dark:text-white text-gray-700">
                  {total > 0 ? `${total.toFixed(2)} ${currency}` : "N/A"}
                </span>
              </div>
            </>
          </div>

          {/* Timer Banner - Only show for QUICK_HOLD status */}
          {(data?.bookingStatus === "QUICK_HOLD" ||
            bookingResult?.[0]?.status === "QUICK_HOLD" ||
            bookingResult?.[1]?.status === "QUICK_HOLD" ||
            departureFlight?.status === "QUICK_HOLD" ||
            returnFlight?.status === "QUICK_HOLD") &&
            timeLeft > 0 && (
              <section className="rounded-xl p-4 mb-4 bg-gray-100 dark:bg-gray-700">
                <div className="flex items-start space-x-2">
                  <CircleAlert className="text-red-500 dark:text-red-400 w-4 md:w-8 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-red-500 dark:text-red-400 leading-relaxed font-medium">
                    Please complete booking within{" "}
                    <span className="font-bold">{formatTimer(timeLeft)}</span>
                  </p>
                </div>
              </section>
            )}

          {/* Buttons in their own card - Show when:
              1. View is 'Master Booking Control' OR
              2. Status is QUICK_HOLD OR
              3. View is 'Manage Booking Request' AND status is PENDING_APPROVAL
         */}
          {!loadingStatus &&
            (renderTitle() === "Master Booking Control" ||
            data?.bookingResult?.status === "QUICK_HOLD" ||
            data?.status === "QUICK_HOLD" ||
            departureFlight?.status === "QUICK_HOLD" ||
            returnFlight?.status === "QUICK_HOLD" ||
            (renderTitle() === "Manage Booking Request" &&
              (data?.bookingResult?.status === "PENDING_APPROVAL" ||
                data?.status === "PENDING_APPROVAL" ||
                departureFlight?.status === "PENDING_APPROVAL" ||
                returnFlight?.status === "PENDING_APPROVAL")) ||
            (renderTitle() === "Manage Internal Booking" &&
              (data?.bookingResult?.status === "PENDING_APPROVAL" ||
                data?.bookingResult?.status === "QUICK_HOLD" ||
                data?.status === "PENDING_APPROVAL" ||
                data?.status === "QUICK_HOLD" ||
                departureFlight?.status === "PENDING_APPROVAL" ||
                departureFlight?.status === "QUICK_HOLD" ||
                returnFlight?.status === "PENDING_APPROVAL" ||
                returnFlight?.status === "QUICK_HOLD")) ? (
              <>
                <div className="h-px dark:bg-gray-700 bg-gray-400 w-full my-4"></div>
                <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4">
                  <h3 className="font-semibold mb-3 dark:text-white text-gray-700">
                    {renderActionTitle()}
                  </h3>
                  <div className="h-px bg-gray-300 dark:bg-gray-600 w-full mb-4"></div>
                  <div className="space-y-3">{renderActionButtons()}</div>
                </div>
              </>
            ) : null)}

          <div className="h-px bg-gray-400 dark:bg-gray-700 w-full my-4"></div>

          {/* Travel Documents section */}
          {(data?.status === "BOOKING_CONFIRMED" ||
            data?.bookingResult?.status === "BOOKING_CONFIRMED" ||
            bookingResult?.[0]?.status === "BOOKING_CONFIRMED" ||
            bookingResult?.[1]?.status === "BOOKING_CONFIRMED" ||
            departureFlight?.status === "BOOKING_CONFIRMED" ||
            returnFlight?.status === "BOOKING_CONFIRMED") && (
            <div className="p-5 rounded-lg dark:bg-gray-700 bg-gray-100">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold dark:text-white text-gray-700">
                  Ticket & Receipt
                </h3>
              </div>
              <div className="h-px dark:bg-gray-700 bg-gray-300 w-full mb-4"></div>

              <div
                className="dark:bg-gray-600 bg-white rounded-lg p-4 mb-4"
                id="eticket-section"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Ticket className="h-5 w-5 text-red-500 mr-2" />
                    <span className="dark:text-white text-gray-700 font-medium">
                      E-Ticket & Receipt
                    </span>
                  </div>
                  <div className="text-sm dark:text-gray-400 text-gray-500">
                    PDF Format
                  </div>
                </div>
                <p className="dark:text-gray-300 text-gray-500 text-sm mb-3">
                  This includes your electronic ticket with all flight and
                  booking details, along with the receipt for the flight tickets
                  associated with this transaction.
                </p>

                <div className="flex gap-3">
                  <button
                    onClick={() => handleDocumentAction("receipt", "print")}
                    className="flex-1 flex items-center justify-center py-2 px-3 bg-gray-700 text-white text-sm font-medium rounded-md hover:bg-blue-500 transition-colors duration-300 print-hide"
                  >
                    <Download className="h-3.5 w-3.5 mr-1.5" />
                    Download
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Support */}
          <div className="bg-gray-100 dark:bg-gray-700 space-y-3 p-4 rounded-lg">
            <h3 className="font-semibold mb-2 dark:text-white text-gray-700">
              Need Help?
            </h3>
            <div className="h-px bg-gray-300 dark:bg-gray-600 w-full mb-3"></div>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="h-4 w-4 text-red-500 mr-3" />
                <a
                  href="mailto:<EMAIL>"
                  className="dark:text-white text-gray-700 hover:text-red-500 transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <Headphones className="h-4 w-4 text-red-500 mr-3" />
                <a
                  href="/support"
                  className="dark:text-white text-gray-700 hover:text-red-500 transition-colors"
                >
                  Help & Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      {showPaymentModal && (
        <PaymentDialog
          initialVisibility={true}
          pendingBookingData={pendingBookingData}
          onClose={handleClosePayment}
        />
      )}
    </Card>
  );
};

// Main component
const BookingRequestPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [bookingStatus, setBookingStatus] = useState<{
    isPaid: boolean;
    actionsAllowed: boolean;
    status?: string;
    departureStatus?: string;
    returnStatus?: string;
    isRoundTrip: boolean;
  } | null>(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [isAgencyOwner, setIsAgencyOwner] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Get data directly from Redux
  const dispatch: AppDispatch = useAppDispatch();
  // const bookingResult = useSelector(selectBookingResult);
  const travelerData2 = useSelector(selectTravelerData);
  const fullTicket = useSelector(selectFullTicket);
  const passengerCounts2 = useSelector(selectPassengerCounts);
  const bookingType = useSelector(selectBookingType);
  const itinerary = useSelector(selectItinerary);
  const departureTicket = useSelector(selectDepartureTicket);
  const returnTicket = useSelector(selectReturnTicket);
  const ticketsLoading = useSelector(selectTicketsLoading);
  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );

  const [departureFlight, setDepartureFlight] = useState<any>(null);
  const [returnFlight, setReturnFlight] = useState<any>(null);
  const [isRoundTrip, setIsRoundTrip] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);
  const [error, setError] = useState(null);
  const hasFetchedFlights = useRef(false);

  const bookingId = searchParams.get("bookingId");
  const returnBookingId = searchParams.get("returnBookingId");
  const fromConfirmation = searchParams.get("fromConfirmation") === "true";

  // Helper function to format flight data
  const formatFlightData = (booking: any, type: "departure" | "return") => {
    if (!booking) return null;

    // For departure, we use the ticket data from the booking
    // For return, we use the meta.return data
    const isReturn = type === "return";
    const ticket = isReturn ? booking.meta?.return : booking.ticket || {};

    const segments = booking.ticket?.segments?.[0] || {};
    const flightClasses = booking.ticket?.flightClasses || [];
    const departureAirport = isReturn
      ? ticket.departureAirport
      : booking.ticket?.departure?.airportCode;
    const arrivalAirport = isReturn
      ? ticket.arrivalAirport
      : booking.ticket?.arrival?.airportCode;
    const status = booking?.status;

    return {
      flightDate: booking.ticket?.flightDate,
      departure: {
        airportCode: departureAirport,
        time: booking.ticket?.departureTime,
      },
      arrival: {
        airportCode: arrivalAirport,
        time: booking.ticket?.arrivalTime,
      },
      airline: isReturn
        ? ticket.carrier
        : segments.carrier || booking.ticket?.carrier,
      flightNumber: isReturn ? ticket.flightNumber : segments.flightNumber,
      duration: booking.ticket?.duration || null,
      stops: booking.ticket?.stops || 0,
      // Add any additional fields needed for display
      ticketId: booking.ticketId,
      bookingId: booking.id,
      travelers: booking.travelers || [],
      price: booking.ticket?.flightClasses?.[0]?.price || null,
      flightClasses: flightClasses || [],
      status: status || null,
    };
  };

  // Now you can access the data
  const { bookingResult } = bookingConfirmation;

  // Get the booking data from Redux
  const travelers = bookingResult?.travelers || [];
  const travelerData = travelers.some((t: any) => t.traveler)
    ? travelers.map((t: any) => t.traveler) // Use nested traveler data if available
    : travelerData2 || [];

  // Calculate passenger counts
  const passengerCounts = passengerCounts2 || {
    adults: travelers.filter((t: any) => t?.traveler?.type === "ADULT").length,
    children: travelers.filter((t: any) => t?.traveler?.type === "CHILD")
      .length,
    infants: travelers.filter((t: any) => t?.traveler?.type === "INFANT")
      .length,
    travelClass: (bookingResult as any)?.travelClass || "Economy",
  };

  // Fetch tickets when component mounts or bookingResult changes
  useEffect(() => {
    if (bookingResult) {
      // Type assertion to handle the thunk action
      (dispatch as AppDispatch)(
        fetchTickets({
          departureTicketId: bookingResult.departureTicketId,
          returnTicketId: bookingResult.returnTicketId,
        })
      );
    }

    // Cleanup function to clear tickets when component unmounts
    return () => {
      (dispatch as AppDispatch)(clearTickets());
    };
  }, [bookingResult, dispatch]);

  // Create a combined booking object for components that expect it
  const booking = {
    bookingResult,
    travelerData,
    fullTicket,
    passengerCounts,
    bookingType,
    itinerary,
    departureTicket,
    returnTicket,
    ticketsLoading,
  };

  // this effect to fetch and set flight data
  useEffect(() => {
    const fetchAndSetFlightData = async () => {
      if (hasFetched) return;
      if (isLoading) return;

      try {
        setIsLoading(true);
        let departure = null;
        let returnFlight = null;

        // Fetch departure booking
        if (bookingId) {
          const depBooking = await getBookingById(bookingId);
          departure = formatFlightData(depBooking, "departure");
        }

        // Fetch return booking if it's a round trip
        if (returnBookingId) {
          const retBooking = await getBookingById(returnBookingId);
          returnFlight = formatFlightData(retBooking, "return");
        }

        setDepartureFlight(departure);
        setReturnFlight(returnFlight);
        setIsRoundTrip(!!returnFlight);
        setHasFetched(true);
      } catch (error) {
        console.error("Error fetching flight data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (fromConfirmation && (bookingId || returnBookingId)) {
      fetchAndSetFlightData();
    } else {
      // Handle non-confirmation flow if needed
      const departure = formatFlightData(
        booking?.bookingResult?.[0],
        "departure"
      );
      const returnFlight = formatFlightData(
        booking?.bookingResult?.[1],
        "return"
      );
      setDepartureFlight(departure);
      setReturnFlight(returnFlight);
      setIsRoundTrip(!!returnFlight);
    }
  }, [
    bookingId,
    returnBookingId,
    fromConfirmation,
    booking,
    hasFetched,
    isLoading,
  ]);

  // Timer state and initialization
  const [timeLeft, setTimeLeft] = useState<number>(() => {
    if (
      bookingResult?.expiresAt ||
      bookingResult?.[0]?.expiresAt ||
      bookingResult?.[1]?.expiresAt
    ) {
      const expiry = new Date(
        bookingResult.expiresAt ||
          bookingResult?.[0]?.expiresAt ||
          bookingResult?.[1]?.expiresAt
      ).getTime();
      const now = Date.now();
      return Math.max(0, Math.floor((expiry - now) / 1000));
    } else if (
      bookingResult?.type ||
      bookingResult?.[0]?.type ||
      bookingResult?.[1]?.type
    ) {
      const minutes = bookingResult.type === "SUBMIT_BOOKING" ? 15 : 60;
      return minutes * 60;
    }
    return 0;
  });

  // Format time for display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Timer effect with useRef to maintain interval reference
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear any existing interval
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Only start the timer if we have time left
    if (timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            router.push("/ticket-hub/myBookings");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    // Cleanup interval on unmount or when timeLeft changes
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [timeLeft]);

  // Initialize status from booking data
  const [status, setStatus] = useState<string>(
    bookingResult?.status ||
      departureFlight?.status ||
      returnFlight?.status ||
      "QUICK_HOLD"
  );

  const [loading, setLoading] = useState(false);
  const [actionsDisabled, setActionsDisabled] = useState(false);
  const [isDocumentProcessing, setIsDocumentProcessing] = useState<{
    type: "e-ticket" | "receipt" | null;
    action: "download" | "print" | null;
  }>({ type: null, action: null });

  // --- Traveler Edit State ---
  // Track if any traveler is being edited or saving
  const [travelerEditState, setTravelerEditState] = useState({
    editing: false,
    saving: false,
  });

  // Fetch booking status when component mounts
  useEffect(() => {
    const fetchBookingStatus = async (bookingId: string) => {
      try {
        setLoadingStatus(true);
        if (!bookingId) {
          console.error("No bookingId provided to getBookingById");
          setLoadingStatus(false);
          return;
        }
        const response = await getBookingById(bookingId);
        if (response) {
          setBookingStatus({
            isPaid: response.status === "PAID",
            actionsAllowed: response.status !== "COMPLETED",
            status: response.status,
            isRoundTrip: false,
          });
        } else {
          console.error("Failed to fetch booking status");
        }
      } catch (error) {
        console.error("Error fetching booking status:", error);
      } finally {
        setLoadingStatus(false);
      }
    };

    if (bookingResult?.id) {
      fetchBookingStatus(bookingResult.id);
    } else {
      setLoadingStatus(false);
    }
  }, [bookingResult]);

  const fetchBookingStatus = async (
    bookingId: string,
    isReturnTrip: boolean = false
  ) => {
    try {
      setLoadingStatus(true);

      if (!bookingId) {
        console.error("No bookingId provided to getBookingById");
        setLoadingStatus(false);
        return null;
      }

      const response = await getBookingById(bookingId);
      if (!response) {
        console.error("Failed to fetch booking status");
        return null;
      }

      return {
        status: response.status,
        isPaid: response.status === "PAID",
        actionsAllowed: response.status !== "COMPLETED",
        bookingData: response,
      };
    } catch (error) {
      console.error("Error fetching booking status:", error);
      return null;
    }
  };

  // Update the useEffect that calls fetchBookingStatus
  useEffect(() => {
    const loadBookingStatus = async () => {
      try {
        setLoadingStatus(true);

        if (
          isRoundTrip &&
          bookingResult?.id &&
          Array.isArray(bookingResult.id) &&
          bookingResult.id.length === 2
        ) {
          // Handle round-trip: fetch status for both departure and return
          const [departureStatus, returnStatus] = await Promise.all([
            fetchBookingStatus(bookingResult.id[0]),
            fetchBookingStatus(bookingResult.id[1]),
          ]);

          if (departureStatus && returnStatus) {
            // Both flights must be paid for the booking to be considered paid
            const isPaid = departureStatus.isPaid && returnStatus.isPaid;
            // Actions are allowed if either flight allows actions
            const actionsAllowed =
              departureStatus.actionsAllowed || returnStatus.actionsAllowed;

            setBookingStatus({
              isPaid,
              actionsAllowed,
              departureStatus: departureStatus.status,
              returnStatus: returnStatus.status,
              isRoundTrip: true,
            });
          }
        } else {
          // Handle single booking
          const bookingId = bookingResult?.id || bookingResult?.booking?.id;
          if (bookingId) {
            const status = await fetchBookingStatus(bookingId);
            if (status) {
              setBookingStatus({
                isPaid: status.isPaid,
                actionsAllowed: status.actionsAllowed,
                departureStatus: status.status,
                isRoundTrip: false,
              });
            }
          }
        }
      } catch (error) {
        console.error("Error loading booking status:", error);
      } finally {
        setLoadingStatus(false);
      }
    };

    loadBookingStatus();
  }, [bookingResult, isRoundTrip]);

  // Handle timer expiry for QUICK_HOLD status
  useEffect(() => {
    if (timeLeft === 0 && status === "QUICK_HOLD") {
      setStatus("Timed Out");
      setActionsDisabled(true);
    }
  }, [timeLeft, status]);

  // --- Action Handlers ---
  const handleCompletePayment = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }
    setLoading(true);
    try {
      const result = await completePayment(bookingResult.id, booking);
      if (
        result &&
        typeof result === "object" &&
        "success" in result &&
        result.success
      ) {
        setStatus("PAYMENT_SUCCESS");
        dispatch(
          setMsg({
            message:
              (result as any).message || "Payment completed successfully",
            success: true,
          })
        );
        setActionsDisabled(true);
        // Navigate to confirmation page after a delay to show the success message
        setTimeout(() => {
          router.push(`/confirmation?bookingId=${bookingResult.id}`);
        }, 2000);
      } else {
        setStatus("PAYMENT_FAILED");
        dispatch(
          setMsg({
            message: (result as any).error || "Failed to complete payment",
            success: false,
          })
        );
      }
    } catch (error: any) {
      setStatus("PAYMENT_FAILED");
      dispatch(
        setMsg({
          message:
            error.message || "An error occurred while completing payment",
          success: false,
        })
      );
    } finally {
      setLoading(false);
    }
  };

  const handleReleaseSeat = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }
    setLoading(true);
    try {
      const res = await releaseSeat(bookingResult.id);
      if (res) {
        dispatch(
          setMsg({
            success: true,
            message: "Your booking seat has been released successfully.",
          })
        );
        setStatus("Timed Out");
        setActionsDisabled(true);

        // Redirect to my-bookings after a short delay to show the success message
        setTimeout(() => {
          router.push("/ticket-hub/myBookings");
        }, 2000);
      }
    } catch (error) {
      console.error("Failed to release seat:", error);
      dispatch(
        setMsg({
          success: false,
          message: (error as Error).message,
        })
      );
      setTimeout(() => {
        router.back();
      }, 2000);
      throw error; // Re-throw the error to maintain the Promise chain
    } finally {
      setLoading(false);
    }
  };

  // State to control the confirmation popup
  const [showApproveConfirmation, setShowApproveConfirmation] = useState(false);

  // Function to show the confirmation popup
  const handleShowApproveConfirmation = () => {
    setShowApproveConfirmation(true);
  };

  // State to control the loading popup
  const [showLoadingPopup, setShowLoadingPopup] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState(
    "Processing your request...\n\nThis may take a few seconds."
  );

  // Function to handle the actual approval after confirmation
  const handleApproveBookingConfirmed = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }

    // Check if booking is already confirmed
    if (bookingResult.status === "BOOKING_CONFIRMED") {
      dispatch(
        setMsg({
          message: "This booking is already confirmed. No action needed.",
          success: false,
        })
      );
      setShowApproveConfirmation(false);
      return;
    }

    // Show loading popup and hide confirmation
    setShowLoadingPopup(true);
    setShowApproveConfirmation(false);

    try {
      // Wait for 5 seconds before processing
      await new Promise((resolve) => setTimeout(resolve, 5000));

      // Execute the approval logic
      await approveBooking(bookingResult.id);

      // Keep the loading state for a total of 15 seconds
      const remainingTime = Math.max(0, 15000 - 5000);
      if (remainingTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingTime));
      }

      // Redirect to booking confirmation page
      router.push(
        `/blockseats/list/${bookingResult.id}/checkout/booking-confirmation`
      );
    } catch (error: any) {
      setShowLoadingPopup(false);
      dispatch(
        setMsg({
          message: error.message || "Failed to approve booking",
          success: false,
        })
      );
    }
  };

  // State to control the rejection confirmation popup
  const [showRejectConfirmation, setShowRejectConfirmation] = useState(false);

  // Function to show the rejection confirmation popup
  const handleShowRejectConfirmation = () => {
    setShowRejectConfirmation(true);
  };

  // This is the function that will be called when the "Reject Request" button is clicked
  const handleRejectBooking = () => {
    // Check if booking is already confirmed or rejected
    if (bookingResult?.status === "BOOKING_CONFIRMED") {
      dispatch(
        setMsg({
          message: "This booking is already confirmed and cannot be rejected.",
          success: false,
        })
      );
      return;
    }

    if (bookingResult?.status === "BOOKING_REJECTED") {
      dispatch(
        setMsg({
          message: "This booking is already rejected. No action needed.",
          success: false,
        })
      );
      return;
    }

    handleShowRejectConfirmation();
  };

  // Function to handle the actual rejection after confirmation
  const handleRejectBookingConfirmed = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }

    // Show loading popup and hide confirmation
    setShowLoadingPopup(true);
    setShowRejectConfirmation(false);

    try {
      // Wait for 5 seconds before processing
      await new Promise((resolve) => setTimeout(resolve, 5000));

      // Execute the rejection logic
      await rejectBooking(bookingResult.id);

      // Keep the loading state for a total of 15 seconds
      const remainingTime = Math.max(0, 15000 - 5000);
      if (remainingTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingTime));
      }

      // Redirect to my bookings page
      router.push("/ticket-hub/myBookings");
    } catch (error: any) {
      setShowLoadingPopup(false);
      dispatch(
        setMsg({
          message: error.message || "Failed to reject booking",
          success: false,
        })
      );
    }
  };

  const handleCancelBooking = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }

    setLoading(true);
    try {
      const result = await cancelBooking(bookingResult.id);

      // If we get here, the cancellation was successful
      const successMessage = "Booking cancelled successfully";
      dispatch(setMsg({ message: successMessage, success: true }));

      // Refresh booking data or redirect after a short delay
      setTimeout(() => {
        router.push("/master-control/global-bookings");
      }, 15000);
    } catch (error: any) {
      dispatch(
        setMsg({
          message: error.message || "An error occurred",
          success: false,
        })
      );
    }
  };

  const handleRefundBooking = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }

    setLoading(true);
    try {
      const result = await refundBooking(bookingResult.id);

      // If we get here, the cancellation was successful
      const successMessage = "Booking refunded successfully";
      dispatch(setMsg({ message: successMessage, success: true }));

      // Refresh booking data or redirect after a short delay
      setTimeout(() => {
        router.push("/master-control/global-bookings");
      }, 15000);
    } catch (error: any) {
      dispatch(
        setMsg({
          message: error.message || "An error occurred",
          success: false,
        })
      );
    }
  };

  const handleRescheduleBooking = async () => {
    if (!bookingResult?.id) {
      dispatch(setMsg({ message: "Booking ID is missing", success: false }));
      return;
    }
    try {
      if (bookingResult?.id) {
        // Get ticket information for rescheduling
        const ticketInfo = await getTicketForReschedule(bookingResult.id);

        if (ticketInfo && ticketInfo.refId) {
          // Navigate to the ticket details page with the refId and edit mode
          // router.push(`/master-control/tickets-overview/${ticketInfo.refId}?edit=true`);
          router.push(`/master-control/tickets-overview/${ticketInfo.refId}`);
        } else {
          dispatch(
            setMsg({
              message: "Failed to get ticket information for rescheduling",
              success: false,
            })
          );
        }
      }
    } catch (error: any) {
      dispatch(
        setMsg({
          message: error.message || "An error occurred",
          success: false,
        })
      );
    }
  };

  // Check if user is agency owner
  const currentUser = useSelector((state: RootState) => state.auth.value);
  useEffect(() => {
    const checkAgencyOwner = () => {
      try {
        // Safely access user ID by checking for different possible structures
        let currentUserId = "unknown";
        if (currentUser && typeof currentUser === "object") {
          if (
            "userId" in currentUser &&
            typeof currentUser.userId === "string"
          ) {
            currentUserId = currentUser.userId;
          } else if (
            "id" in currentUser &&
            typeof currentUser.id === "string"
          ) {
            currentUserId = currentUser.id;
          } else if (
            "user" in currentUser &&
            currentUser.user &&
            typeof currentUser.user === "object" &&
            "id" in currentUser.user &&
            typeof currentUser.user.id === "string"
          ) {
            currentUserId = currentUser.user.id;
          }
        }
        const bookingData =
          typeof bookingResult !== "undefined" ? bookingResult : null; // Adjust based on available variables
        if (bookingData) {
          const booking = bookingData;
          // Assuming booking has an ownerId or similar field
          const ownerId =
            booking.ownerId || booking.userId || booking.id || "unknown";
          setIsAgencyOwner(
            ownerId === currentUserId && currentUserId !== "unknown"
          );
        } else {
          setIsAgencyOwner(false);
        }
      } catch (error) {
        console.error("Error checking agency owner:", error);
        setIsAgencyOwner(false);
      }
    };
    checkAgencyOwner();
  }, [currentUser]); // Depend on currentUser to re-check if it changes

  // --- Centralized Role/Status UI Logic ---
  // currentUser already declared above. Do not redeclare.
  const [isAdmin, setIsAdmin] = useState(false);
  const [isAgent, setIsAgent] = useState(false);

  const allowedRoles = ["master", "agency"];

  const notAllowedRoles = ["affiliate"];

  const allowedSubRoles = ["admin", "accountant", "admin", "accountant"];
  const notAllowedSubRoles = ["moderator", "sales", "operation"];

  const allowedRoleTypes = [
    "master_owner",
    "master_admin",
    "master_accountant",
    "master_moderator",
    "agency_owner",
    "agency_admin",
    "agency_accountant",
    "agency_operation",
    "agency_sales",
  ];

  const notAllowedRoleTypes = [];

  useEffect(() => {
    // Determine admin/agent roles (expand as needed for your auth model)
    if (currentUser && typeof currentUser === "object") {
      setIsAdmin(
        typeof currentUser === "object" &&
          currentUser !== null &&
          "role" in currentUser &&
          allowedRoles.includes((currentUser as any).role) &&
          "subRole" in currentUser &&
          allowedSubRoles.includes((currentUser as any).subRole) &&
          "roleType" in currentUser &&
          allowedRoleTypes.includes((currentUser as any).roleType)
      );
      // Agent: the booking creator
      let currentUserId: string | undefined = undefined;
      if ("userId" in currentUser && typeof currentUser.userId === "string") {
        currentUserId = currentUser.userId;
      } else if ("id" in currentUser && typeof currentUser.id === "string") {
        currentUserId = currentUser.id;
      } else if (
        "user" in currentUser &&
        currentUser.user &&
        typeof currentUser.user === "object" &&
        "id" in currentUser.user &&
        typeof currentUser.user.id === "string"
      ) {
        currentUserId = currentUser.user.id;
      }
      setIsAgent(
        bookingResult &&
          currentUserId &&
          (bookingResult.agentId === currentUserId ||
            bookingResult.createdBy === currentUserId ||
            bookingResult.userId === currentUserId ||
            // Then check array elements if they exist
            (Array.isArray(bookingResult) &&
              bookingResult.some(
                (result) =>
                  result?.agentId === currentUserId ||
                  result?.createdBy === currentUserId ||
                  result?.userId === currentUserId
              )))
      );
    }
  }, [currentUser, bookingResult]);

  // UI Matrix - Initialize all to false
  let showTimer = false;
  let showCompletePayment = false;
  let showReleaseSeat = false;
  let showApprove = false;
  let showReject = false;
  let travelerEditable = false;

  // Get current booking status
  const currentStatus = bookingResult?.status || "QUICK_HOLD";

  // Hide actions for these statuses for all users
  const nonActionableStatuses = [
    "TIMED_OUT",
    "PENDING_APPROVAL",
    "BOOKING_CONFIRMED",
    "BOOKING_REJECTED",
    "CANCELLED_BY_USER",
    "CANCELLED_BY_SYSTEM",
  ];
  const nonEditableStatuses = [
    "TIMED_OUT",
    "BOOKING_REJECTED",
    "CANCELLED_BY_USER",
    "CANCELLED_BY_SYSTEM",
  ];

  // Check if user is master_admin
  const isMasterAdmin =
    currentUser &&
    typeof currentUser === "object" &&
    "roleType" in currentUser &&
    allowedRoleTypes.includes((currentUser as any).roleType);

  // For QUICK_HOLD status
  if (currentStatus === "QUICK_HOLD") {
    if (isMasterAdmin) {
      // Only master_admin can see booking actions for QUICK_HOLD
      showCompletePayment = true;
      showReleaseSeat = true;
      showTimer = true;
      travelerEditable = isAgent;
    }
  }
  // For PENDING_APPROVAL status
  else if (currentStatus === "PENDING_APPROVAL" && isMasterAdmin) {
    if (allowedRoleTypes.includes((currentUser as any).roleType)) {
      // Allowed roles can see approve and reject actions for PENDING_APPROVAL
      showApprove = true;
      showReject = true;
      showTimer = true;
    }
  }
  // For other statuses, no actions are shown for any user
  else if (nonEditableStatuses.includes(currentStatus)) {
    // No actions to show for these statuses for any user
    showCompletePayment = false;
    showReleaseSeat = false;
    showApprove = false;
    showReject = false;
    showTimer = true;
    travelerEditable = false;
  }

  // Get entryPoint from URL query parameters
  const entryPoint = searchParams.get("entryPoint") || "my-bookings"; // Default to 'my-bookings' if not specified

  // Add this function to determine the view type based on role, roleType, and entryPoint
  const getViewType = () => {
    if (
      "role" in currentUser &&
      currentUser.role === "master" &&
      "roleType" in currentUser &&
      allowedRoleTypes.includes((currentUser as any).roleType) &&
      entryPoint === "global-bookings"
    ) {
      return "master";
    } else if (
      (("role" in currentUser && (currentUser as any).role === "master") ||
        (currentUser as any).role === "agency") &&
      "roleType" in currentUser &&
      allowedRoleTypes.includes((currentUser as any).roleType) &&
      entryPoint === "booking-requests"
    ) {
      return "request";
    } else if (entryPoint === "my-bookings") {
      return "internal";
    }
    return "internal"; // Fallback to internal view
  };

  const viewType = getViewType();

  // Add this function to render the appropriate title based on view type
  const renderTitle = () => {
    switch (viewType) {
      case "internal":
        return "Manage Internal Booking";
      case "request":
        return "Manage Booking Request";
      case "master":
        return "Master Booking Control";
      default:
        return "Manage Internal Booking";
    }
  };

  const handleDocumentAction = async (
    type: "e-ticket" | "receipt",
    action: "download" | "print"
  ): Promise<void> => {
    try {
      // Set document processing state
      setIsDocumentProcessing({ type, action });

      // Get the container element that wraps the booking confirmation content
      // Try multiple selectors to find the booking content
      const containerSelectors = [
        ".print-container",
        ".receipt-container",
        ".booking-container",
        ".booking-content",
        "body > div", // Fallback to first div in body
        "body", // Last resort use body
      ];

      let container: HTMLElement | null = null;
      for (const selector of containerSelectors) {
        container = document.querySelector(selector) as HTMLElement;
        if (container) {
          break;
        }
      }

      if (!container) {
        console.error(
          "Container element not found. Tried selectors:",
          containerSelectors
        );
        dispatch(
          setMsg({
            message:
              "Failed to generate document: Could not find booking content",
            success: false,
          })
        );
        setIsDocumentProcessing({ type: null, action: null });
        return;
      }

      // Get flight data from the correct structure
      const ticket = booking?.fullTicket || {};
      const bookingResult = booking?.bookingResult || {};

      // Handle API response structure when coming from tables
      const apiTicket = bookingResult?.ticket || {};

      // Determine if we're using data from Redux or from API response
      const effectiveTicket =
        Object.keys(ticket).length > 0 ? ticket : apiTicket;

      const segments = effectiveTicket?.segments || [];
      // Get the first segment for flight details
      const firstSegment = segments[0] || {};

      // Get baggage allowance from flight class
      const carryOnAllowed = firstSegment?.flightClass?.carryOnAllowed || 0;
      const carryOnWeight = firstSegment?.flightClass?.carryOnWeight || 0;
      const checkedAllowed = firstSegment?.flightClass?.checkedAllowed || 0;
      const checkedWeight = firstSegment?.flightClass?.checkedWeight || 0;

      // Format baggage display
      const baggageDisplay = [];
      if (checkedAllowed > 0) {
        baggageDisplay.push(`${checkedAllowed} x ${checkedWeight} kg Checked`);
      }
      if (carryOnAllowed > 0) {
        baggageDisplay.push(`${carryOnAllowed} x ${carryOnWeight} kg Carry-on`);
      }

      if (action === "download") {
        const html2pdf = (await import("html2pdf.js")).default;

        // Clone the container to avoid modifying the original
        const element = container.cloneNode(true) as HTMLElement;
        // element.style.padding = "20px"; // Add some padding for better print layout

        const travelerDataArr = Array.isArray(travelerData)
          ? travelerData.map((item: any) => item.traveler || {})
          : [];

        let travelerCards = "";

        travelerDataArr.forEach((traveler: TravelerDto, index: number) => {
          const t = traveler || {};
          travelerCards += `<div class="card">
        <div class="card-header">
          <h2 class="card-title">Traveler Details</h2>
        </div>
        <div class="card-content">
          <div class="passenger-section">
            <div class="section-header">
              <span style="font-size: 18px; font-weight: 600;">Traveler ${
                index + 1
              }</span>
            </div>
            <div class="divider"></div>
  
            <div style="margin-bottom: 24px;">
              <h3 class="section-title">Personal Information</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Title</label>
                  <div class="field-value">${capitalizeFirst(t.title)}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Gender</label>
                  <div class="field-value">${capitalizeFirst(t.gender)}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">First Name</label>
                  <div class="field-value">${t.firstName}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Last Name</label>
                  <div class="field-value">${t.lastName}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Date of Birth</label>
                  <div class="field-value">${getFormatDateTable(
                    t.dateOfBirth
                  )}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Nationality</label>
                  <div class="field-value">${t.nationality}</div>
                </div>
              </div>
            </div>
  
            <div class="divider"></div>
  
            <div style="margin-bottom: 24px;">
              <h3 class="section-title">Identification Documents</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Passport Number</label>
                  <div class="field-value">${t.documentNumber}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Passport Issuing Country</label>
                  <div class="field-value">${t.issuingCountry}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Passport Expiry</label>
                <div class="field-value">${getFormatDateTable(
                  t.expirationDate
                )}</div>
              </div>
            </div>
  
            <div class="divider"></div>
  
            <div>
              <h3 class="section-title">Contact Information</h3>
              <div style="margin-bottom: 16px;">
                <div class="form-field">
                  <label class="field-label">Email</label>
                  <div class="field-value">${t.contactEmail}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Phone Number</label>
                <div class="field-value">${t.contactPhone}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
        });

        // Set the HTML content
        element.innerHTML = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flight Booking Receipt - Airvilla Charters</title>
  <style>
    /* Print-optimized styles with light colors to preserve ink */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: Arial, sans-serif;
    }
    
    body {
      background-color: white;
      color: #333;
      line-height: 1.5;
    }
    
    .container {
      max-width: 1024px;
      margin: 0 auto;
      padding: 20px;
    }
    
    @media print {
      .container {
        padding: 0;
      }
    }
    
    /* Header styles */
    .receipt-header {
      border: 1px solid #ddd;
      border-radius: 12px;
      background-color: #ffffff;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .header-row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    .company-info {
      display: flex;
      align-items: center;
    }
    
    .company-details {
      margin-left: 12px;
    }
    
    .company-name {
      font-weight: bold;
      font-size: 24px;
    }
    
    .company-address {
      color: #555;
      font-size: 14px;
    }
    
    .receipt-label {
      text-align: right;
      padding: 8px 16px;
    }
    
    .receipt-label h2 {
      font-weight: bold;
      font-size: 20px;
    }
    
    .receipt-label p {
      color: #555;
      font-size: 14px;
    }
    
    .metadata-row {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
    }
    
    .metadata-box {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .metadata-label {
      color: #555;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .metadata-value {
      font-weight: 500;
      font-size: 14px;
    }
    
    /* Grid layout */
    .content-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 32px;
    }
    
    .left-column, .right-column {
      background-color: #f8f8f8;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 24px;
      background-color: #fff;
    }
    
    .card-header {
      border-bottom: 1px solid #eee;
      padding: 16px;
    }
    
    .card-title {
      font-weight: bold;
      font-size: 20px;
    }
    
    .card-content {
      padding: 16px;
    }
    
    /* Booking information */
    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }
    
    .info-box {
      padding: 16px;
      border-radius: 8px;
      background-color: #f0f0f0;
    }
    
    .info-label {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: #555;
    }
    
    .info-label svg {
      margin-right: 8px;
    }
    
    .info-value {
      font-weight: 600;
      font-size: 18px;
    }
    
    /* Flight card */
    .flight-card {
      padding: 16px;
      border-radius: 8px;
      background-color: #f0f0f0;
      margin-bottom: 24px;
    }
    
    .flight-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .flight-type {
      padding: 4px 12px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
    }
    
    .outbound {
      background-color: #ffe6e6;
      color: #c73232;
    }
    
    .return {
      background-color: #e6f0ff;
      color: #2952a3;
    }
    
    .flight-date {
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    
    .flight-date svg {
      margin-right: 8px;
    }
    
    .flight-route {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
    }
    
    .flight-codes {
      display: flex;
      align-items: center;
    }
    
    .flight-code {
      font-size: 20px;
      font-weight: bold;
    }
    
    .flight-arrow {
      margin: 0 8px;
      color: #777;
    }
    
    .flight-airline {
      text-align: right;
      font-weight: 500;
    }
    
    .flight-meta {
      color: #777;
      font-size: 14px;
      margin-top: 4px;
    }
    
    .flight-times {
      display: flex;
      font-size: 14px;
    }
    
    .time-item {
      display: flex;
      align-items: center;
      margin-right: 16px;
    }
    
    .time-item svg {
      margin-right: 4px;
    }
    
    .time-separator {
      margin: 0 16px;
    }
    
    /* Flight separator */
    .flight-separator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 0;
      margin-bottom: 12px;
    }
    
    .separator-line {
      height: 1px;
      background-color: #ddd;
      flex-grow: 1;
    }
    
    .separator-circle {
      margin: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #eee;
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
    
    /* Passenger section */
    .passenger-section {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 1px;
      background-color: #ddd;
      width: 100%;
      margin: 12px 0;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .form-field {
      margin-bottom: 8px;
    }
    
    .field-label {
      display: block;
      font-size: 14px;
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    .field-value {
      width: 100%;
      background-color: #fff;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .section-title {
      font-weight: bold;
      margin-bottom: 12px;
      font-size: 18px;
    }
    
    /* Payment details */
    .payment-status {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .payment-status svg {
      margin-right: 8px;
      color: #22c55e;
    }
    
    .price-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .price-label {
      color: #555;
    }
    
    .price-value {
      font-weight: 500;
    }
    
    .price-total {
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      padding-top: 4px;
    }
    
    .document-section {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .document-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    
    .document-title {
      display: flex;
      align-items: center;
    }
    
    .document-title svg {
      margin-right: 8px;
      color: #c73232;
    }
    
    .document-format {
      font-size: 14px;
      color: #777;
    }
    
    .document-description {
      color: #555;
      font-size: 14px;
      margin-bottom: 12px;
    }
    
    .button-row {
      display: flex;
      gap: 12px;
    }
    
    .button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
    
    .button:hover {
      background-color: #eee;
    }
    
    .button svg {
      margin-right: 6px;
    }
    
    .support-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .support-item svg {
      margin-right: 12px;
      color: #c73232;
    }
    
    .support-link {
      color: #333;
      text-decoration: none;
    }
    
    .support-link:hover {
      color: #c73232;
    }

    /* Icon classes */
    .icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }

    .icon-sm {
      width: 14px;
      height: 14px;
    }

    .icon-lg {
      width: 20px;
      height: 20px;
    }
    
    /* Receipt Footer */
    .receipt-footer {
      margin-top: 40px;
      border-top: 1px solid #ddd;
      padding-top: 24px;
      font-size: 14px;
    }
    
    .footer-content {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .footer-logo {
      flex: 1;
    }
    
    .footer-info {
      flex: 3;
      padding-left: 20px;
    }
    
    .footer-legal {
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    
    .footer-text {
      color: #555;
      margin-bottom: 4px;
    }
    
    .footer-validation {
      flex: 2;
      text-align: right;
    }
    
    .verification-seal {
      display: inline-flex;
      align-items: center;
      background-color: #f8f8f8;
      padding: 8px 14px;
      border-radius: 4px;
      border: 1px dashed #c73232;
      margin-bottom: 12px;
    }
    
    .verification-seal span {
      font-weight: 600;
      margin-left: 8px;
      color: #c73232;
    }
    
    .footer-date {
      color: #555;
      font-size: 13px;
    }
    
    .footer-disclaimer {
      border-top: 1px solid #eee;
      padding-top: 16px;
      padding-bottom: 6px;
      font-size: 12px;
      color: #777;
      text-align: center;
    }
    
    .footer-disclaimer p {
      margin-bottom: 6px;
    }
    
    .footer-contact-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-top: 16px;
      margin-bottom: 16px;
    }
    
    .footer-contact-item {
      display: flex;
      align-items: center;
    }
    
    .footer-contact-label {
      margin-left: 8px;
      font-weight: 500;
    }
    
    .footer-badges {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .footer-badge {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      background-color: #f8f8f8;
      border-radius: 4px;
      font-size: 12px;
      color: #555;
      font-weight: 500;
    }
    
    .footer-badge svg {
      margin-right: 6px;
    }

    /* For printing */
    @media print {
      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      
      .card, .receipt-header, .left-column, .right-column {
        break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Receipt Header -->
    <div class="receipt-header">
      <div class="header-row">
        <div class="company-info">
          <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="48">
          <div class="company-details">
            <h1 class="company-name">Airvilla Charters</h1>
            <p class="company-address">Wasfi At-Tall St. 101, Amman, Jordan</p>
          </div>
        </div>
        <div class="receipt-label">
          <h2>RECEIPT</h2>
          <p>#
          ${bookingResult?.receipt?.receiptNumber}
          </p>
        </div>
      </div>
      
      <div class="metadata-row">
        <div class="metadata-box">
          <p class="metadata-label">Receipt Date</p>
          <p class="metadata-value info-value">${getFormatDateTable(
            bookingResult?.receipt?.createdAt
          )}</p>
        </div>
        <div class="metadata-box">
          <p class="metadata-label">Transaction Time</p>
          <p class="metadata-value info-value">${getFormatTime(
            bookingResult?.receipt?.createdAt
          )}</p>
        </div>
        <div class="metadata-box">
          <p class="metadata-label">Transaction ID</p>
          <p class="metadata-value info-value">${
            bookingResult?.booking?.transactionId ?? "N/A"
          }</p>
        </div>
      </div>
    </div>
    
    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Booking Information -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Booking Information</h2>
          </div>
          <div class="card-content">
            <div class="info-grid">
              <div class="info-box">
                <div class="info-label">
                  Booking ID
                </div>
                <div class="info-value">${
                  bookingResult?.eTicket?.bookingId
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Booking Reference Number
                </div>
                <div class="info-value">${
                  bookingResult?.referenceNumber ||
                  bookingResult?.eTicket?.eTicketNumber
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Payment Method
                </div>
                <div class="info-value">${
                  bookingResult?.booking?.source === "THIRD_PARTY" ||
                  booking?.bookingType === "THIRD_PARTY"
                    ? "Airvilla Wallet"
                    : normalizePaymentMethod(
                        bookingResult?.paymentMethod ||
                          bookingResult?.payment?.paymentMethod ||
                          bookingResult?.data?.fullTicket?.payment
                            ?.paymentMethod ||
                          "N/A"
                      )
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Booking Date
                </div>
                <div class="info-value">${getFormatDateTable(
                  bookingResult?.createdAt || "N/A"
                )}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Purchased Itinerary -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Purchased Itinerary</h2>
          </div>
          <div class="card-content">
            <!-- Outbound Flight -->
            <div class="flight-card">
              <div class="flight-header">
                <div class="flight-type outbound">OUTBOUND</div>
                <div class="flight-date">
                  ${getFormatDateTable(
                    bookingResult?.booking?.ticket?.flightDate ||
                      booking?.fullTicket?.ticket?.flightDate ||
                      "N/A"
                  )}
                </div>
              </div>
              
              <div class="flight-route">
                <div>
                  <div class="flight-codes">
                    <span class="flight-code">
                      ${bookingResult?.booking?.departure?.airportCode || "N/A"}
                    </span>
                    <span class="flight-arrow">→</span>
                    <span class="flight-code">${
                      bookingResult?.booking?.arrival?.airportCode || "N/A"
                    }</span>
                  </div>
                  <div class="flight-meta">
                    ${
                      bookingResult?.booking?.ticket?.stops === "0"
                        ? "Direct Flight"
                        : `${bookingResult?.booking?.ticket?.stops} Stops`
                    } • ${bookingResult?.booking?.ticket?.duration || "N/A"}  
                  </div>
                </div>
                <div class="flight-airline">
                  ${bookingResult?.booking?.airline?.name || "N/A"} (${
          bookingResult?.booking?.flightNumber || "N/A"
        })
                </div>
              </div>
              
              <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                <div class="time-item" style="margin-right: 0;">
                  Departure: ${getFormatTime(
                    bookingResult?.booking?.departureTime ||
                      booking?.fullTicket?.ticket?.departureTime ||
                      "N/A"
                  )}
                </div>
                <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                <div class="time-item" style="margin-left: 0;">
                  Arrival: ${getFormatTime(
                    bookingResult?.booking?.arrivalTime ||
                      booking?.fullTicket?.ticket?.arrivalTime ||
                      "N/A"
                  )}
                </div>
              </div>
            </div>
            
            <!-- Flight Separator -->
            <div class="flight-separator">
              <div class="separator-line"></div>
              <div class="separator-circle">↕</div>
              <div class="separator-line"></div>
            </div>
            
            <!-- Return Flight -->
            <div class="flight-card">
              <div class="flight-header">
                <div class="flight-type return">RETURN</div>
                <div class="flight-date">
                  ${getFormatDateTable(
                    bookingResult?.booking?.ticket?.flightDate
                  )}
                </div>
              </div>
              
              <div class="flight-route">
                <div>
                  <div class="flight-codes">
                    <span class="flight-code">${
                      bookingResult?.booking?.arrivalAirport?.iataCode || "N/A"
                    }</span>
                    <span class="flight-arrow">→</span>
                    <span class="flight-code">${
                      bookingResult?.booking?.departureAirport?.iataCode ||
                      "N/A"
                    }</span>
                  </div>
                  <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            } 
                  </div>
                </div>
                <div class="flight-airline">
                  ${bookingResult?.booking?.airline?.name || "N/A"} (${
          bookingResult?.booking?.flightNumber || "N/A"
        })
                </div>
              </div>
              
              <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                <div class="time-item" style="margin-right: 0;">
                  Departure: ${getFormatTime(
                    bookingResult?.booking?.arrivalTime ||
                      booking?.fullTicket?.ticket?.arrivalTime ||
                      "N/A"
                  )}
                </div>
                <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                <div class="time-item" style="margin-left: 0;">
                  Arrival: ${getFormatTime(
                    bookingResult?.booking?.departureTime ||
                      booking?.fullTicket?.ticket?.departureTime ||
                      "N/A"
                  )}
                </div>
              </div>
            </div>
            
            <!-- Passenger & Baggage -->
            <div class="flight-card">
              <h3 class="section-title" style="margin-bottom: 8px;">Passenger & Baggage</h3>
              <div class="divider"></div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; padding-bottom: 4px;">
                <div style="font-weight: 500;">${
                  bookingResult?.passengerCounts?.adults
                } Adult</div>
                <div style="font-size: 14px; color: #555;">${capitalizeFirst(
                  bookingResult?.passengerCounts?.[0]?.travelClass || "Economy"
                )}
                <span>${baggageDisplay.length > 0 ? " • " : ""}${
          baggageDisplay.length > 0
            ? baggageDisplay.map((item) => item).join(" • ")
            : "No baggage included"
        }</span></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Traveler Details -->
        ${travelerCards}
      </div>
      
      <!-- Right Column -->
      <div class="right-column">
        <!-- Payment Details -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Payment Details</h2>
          </div>
          <div class="card-content">
            <!-- Payment confirmation card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <div class="payment-status">
                <h3 style="font-weight: 600; font-size: 16px;">Payment Successful</h3>
              </div>
              <div class="divider"></div>
              <div style="font-size: 14px;">
                <div class="price-item">
                  <span class="price-label">Transaction ID:</span>
                  <div class="info-value" style="font-size: 12px;">${
                    booking?.bookingResult?.booking?.transactionId || "N/A"
                  }</div>
                </div>
                <div>
                  <span class="price-label">Transaction Date:</span>
                  <div class="info-value" style="font-size: 12px;">${getFormatDateTable(
                    booking?.bookingResult?.booking?.transactionDate || "N/A"
                  )}</div>
                </div>
              </div>
            </div>
            
            <!-- Price breakdown card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Price Details</h3>
              <div class="divider"></div>
              
              <!-- Price details will be populated dynamically -->
              <div class="price-item">
                <span class="price-label" style="font-weight: 600; font-size: 16px;">Total</span>
                <span>
                ${
                  booking?.bookingResult?.totalAmount ||
                  bookingResult?.bookedSeats?.[0]?.totalPrice ||
                  "0.00"
                } ${
          booking?.bookingResult?.currency ||
          bookingResult?.meta?.pricing?.departure?.currency ||
          "JOD"
        }
                </span>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Receipt Information card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Receipt Information</h3>
              <div class="divider"></div>
              <div style="margin-top: 16px;">
                <div>
                  <span class="price-label" style="font-size: 12px;">Receipt Number:</span>
                  <div class="info-value" style="font-size: 12px;">${
                    bookingResult?.receipt?.receiptNumber
                  }</div>
                </div>
                <div>
                  <span class="price-label" style="font-size: 12px;">Booking ID:</span>
                  <div class="info-value" style="font-size: 12px;">${
                    bookingResult?.receipt?.bookingId
                  }</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Footer Section - Placed properly after content-grid but before closing container div -->
    <div class="receipt-footer">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="72">
        </div>
        <div class="footer-info">
          <p class="footer-legal">OFFICIAL RECEIPT DOCUMENT</p>
          <p class="footer-text">Thank you for choosing Airvilla Charters. This document serves as your official receipt and booking confirmation.</p>
          <p class="footer-text">Please retain this document for your records and present it when required.</p>
        </div>
        <div class="footer-validation">
          <div class="verification-seal">
            <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="#c73232" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span>Verified & Approved</span>
          </div>
          <div class="footer-date">Generated on: ${getFormatDate(
            new Date().toString()
          )} • ${getFormatTime(new Date().toString())}</div>
        </div>
      </div>
      
      <div class="footer-contact-grid">
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
          <span class="footer-contact-label"><EMAIL></span>
        </div>
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="9" y1="21" x2="9" y2="9"></line>
          </svg>
          <span class="footer-contact-label">www.airvilla-charters.travel</span>
        </div>
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
          <span class="footer-contact-label">Secure Booking Platform</span>
        </div>
      </div>
      
      <div class="footer-badges">
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          Secure Transaction
        </div>
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 6v6l4 2"></path>
          </svg>
          24/7 Support
        </div>
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
          </svg>
          IATA Certified
        </div>
      </div>
      
      <div class="footer-disclaimer">
        <p>This document contains confidential information and is intended for the named recipient only and Authorized Agents. Unauthorized reproduction is strictly prohibited.</p>
        <p> 2025 Airvilla LLC. All rights reserved.</p>
      </div>
    </div>
  </div>`;
        // Add print-specific styles
        const style = document.createElement("style");
        style.textContent = `
          @page {
            size: A4;
          }
          @media print {
            body { 
              margin: 0; 
              padding: 0; 
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print { 
              display: none !important; 
            }
            .container { 
              width: 100% !important; 
              max-width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              box-shadow: none !important;
            }
            .card {
              page-break-inside: avoid;
              break-inside: avoid;
            }
          }
          body { 
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        `;
        element.prepend(style);

        // Set options for PDF generation
        const opt = {
          margin: [10, 0, 10, 0], // top, right, bottom, left
          filename: `booking-confirmation-${
            booking.bookingResult?.id || "ticket"
          }.pdf`,
          image: {
            type: "jpeg",
            quality: 0.98,
          },
          html2canvas: {
            scale: 2,
            useCORS: true,
            logging: true,
            letterRendering: true,
            scrollY: 0,
            scrollX: 0,
            windowWidth: document.documentElement.offsetWidth,
            allowTaint: true,
          },
          jsPDF: {
            unit: "mm",
            format: "a4",
            orientation: "portrait",
          },
          pagebreak: {
            mode: ["avoid-all", "css", "legacy"],
          },
        };

        // Create a temporary container for PDF generation
        const tempContainer = document.createElement("div");
        // tempContainer.style.position = "absolute";
        // tempContainer.style.left = "-9999px";
        tempContainer.appendChild(element);
        document.body.appendChild(tempContainer);

        try {
          // Generate and download PDF
          await html2pdf()
            .set(opt)
            .from(element)
            .save()
            .then(() => {
              dispatch(
                setMsg({
                  message: "Document downloaded successfully",
                  success: true,
                })
              );
            });
        } catch (error: any) {
          console.error("Error generating PDF:", error);
          dispatch(
            setMsg({
              message: `Failed to generate PDF: ${error.message}`,
              success: false,
            })
          );
        } finally {
          // Clean up the temporary container and reset processing state
          if (document.body.contains(tempContainer)) {
            document.body.removeChild(tempContainer);
          }
          setIsDocumentProcessing({ type: null, action: null });
        }
      } else if (action === "print") {
        // Declare printWindow at the function scope level
        let printWindow: Window | null = null;

        // Get flight data from the correct structure
        const ticket = booking?.fullTicket || {};
        const bookingResult = booking?.bookingResult || {};

        // Handle API response structure when coming from tables
        const apiTicket = bookingResult?.ticket || {};

        // Determine if we're using data from Redux or from API response
        const effectiveTicket =
          Object.keys(ticket).length > 0 ? ticket : apiTicket;

        const segments = effectiveTicket?.segments || [];
        // Get the first segment for flight details
        const firstSegment = segments[0] || {};

        const baggages = bookingResult?.ticket?.flightClasses?.[0];

        // Get baggage allowance from flight class
        const carryOnAllowed =
          firstSegment?.flightClass?.carryOnAllowed ??
          baggages?.carryOnAllowed ??
          0;
        const carryOnWeight =
          firstSegment?.flightClass?.carryOnWeight ??
          baggages?.carryOnWeight ??
          0;
        const checkedAllowed =
          firstSegment?.flightClass?.checkedAllowed ??
          baggages?.checkedAllowed ??
          0;
        const checkedWeight =
          firstSegment?.flightClass?.checkedWeight ??
          baggages?.checkedWeight ??
          0;

        // Format baggage display
        const baggageDisplay = [];
        if (checkedAllowed > 0) {
          baggageDisplay.push(
            `${checkedAllowed} x ${checkedWeight} kg Checked`
          );
        }
        if (carryOnAllowed > 0) {
          baggageDisplay.push(
            `${carryOnAllowed} x ${carryOnWeight} kg Carry-on`
          );
        }

        const travelerData = Array.isArray(booking.travelerData)
          ? booking.travelerData.map((item) => item.traveler || {})
          : [];

        const isRoundTrip =
          (bookingResult?.tripType === "ROUND_TRIP" ||
            booking?.fullTicket?.tripType === "ROUND_TRIP" ||
            bookingResult?.meta?.tripType === "ROUND_TRIP") &&
          Array.isArray(bookingResult?.ticket);

        const flightClasses =
          bookingConfirmation?.ticket?.ticket?.flightClasses ||
          bookingConfirmation?.fullTicket?.[0]?.ticket?.flightClasses ||
          bookingConfirmation?.fullTicket?.ticket?.flightClasses ||
          effectiveTicket?.flightClasses ||
          departureFlight?.flightClasses ||
          [];
        const returnFlightClasses =
          bookingConfirmation?.ticket?.ticket?.returnFlightClasses ||
          bookingConfirmation?.fullTicket?.ticket?.returnFlightClasses ||
          effectiveTicket?.returnFlightClasses ||
          bookingConfirmation?.fullTicket?.[1]?.ticket?.returnFlightClasses ||
          returnFlight?.returnFlightClasses ||
          [];
        const flightClass = flightClasses[0] || {};
        const returnFlightClass = returnFlightClasses[0] || {};
        const price = flightClass?.price || {};
        const returnPrice = returnFlightClass?.price || {};

        // Get passenger counts from booking data or default to 1 adult
        const passengerCounts = booking?.travelerData || {};

        const calculatedPassengerCounts = (() => {
          // Calculate from booking travelers if available
          const travelers = booking?.bookingResult?.travelers || [];

          if (travelers.length > 0) {
            const now = new Date();

            const getAge = (birthDate: string) => {
              const birth = new Date(birthDate);
              let age = now.getFullYear() - birth.getFullYear();
              const monthDiff = now.getMonth() - birth.getMonth();
              if (
                monthDiff < 0 ||
                (monthDiff === 0 && now.getDate() < birth.getDate())
              ) {
                age--;
              }
              return age;
            };

            let adults = 0;
            let children = 0;
            let infants = 0;

            travelers.forEach((traveler: any) => {
              const dob = traveler?.traveler?.dateOfBirth;
              if (!dob) {
                // If no DOB, assume adult
                adults++;
                return;
              }

              const age = getAge(dob);
              if (age < 2) {
                infants++;
              } else if (age < 12) {
                children++;
              } else {
                adults++;
              }
            });

            return {
              adults,
              children,
              infants,
              travelClass:
                booking?.bookingResult?.travelClass ||
                booking?.fullTicket?.ticket?.flightClasses[0]?.type ||
                "Economy",
            };
          }

          // Fallback to 1 adult by default
          return {
            adults: 1,
            children: 0,
            infants: 0,
            travelClass: "Economy",
          };
          // }, [passengerCounts, booking]);
        })();

        // Check if return ticket exists
        const hasReturnTicket = returnFlightClasses.length > 0;

        // Get currency and prices
        const currency = price?.currency || "JOD";
        const adultPrice = price?.adult || 0;
        const childPrice = price?.child || 0;
        const infantPrice = price?.infant || 0;
        const returnAdultPrice = returnPrice?.adult || 0;
        const returnChildPrice = returnPrice?.child || 0;
        const returnInfantPrice = returnPrice?.infant || 0;
        const departureTaxRate = price?.tax / 100 || 0;
        const returnTaxRate = returnPrice?.tax / 100 || 0;

        // Calculate fares based on passenger counts
        const adultFare = adultPrice * (calculatedPassengerCounts?.adults || 0);
        const childFare =
          childPrice * (calculatedPassengerCounts?.children || 0);
        const infantFare =
          infantPrice * (calculatedPassengerCounts?.infants || 0);
        const returnAdultFare =
          returnAdultPrice * (calculatedPassengerCounts?.adults || 0);
        const returnChildFare =
          returnChildPrice * (calculatedPassengerCounts?.children || 0);
        const returnInfantFare =
          returnInfantPrice * (calculatedPassengerCounts?.infants || 0);

        // Calculate base fares
        const outboundBaseFare = adultFare + childFare + infantFare;
        const returnBaseFare =
          returnAdultFare + returnChildFare + returnInfantFare;

        // Calculate taxes separately for departure and return
        const departureTax = outboundBaseFare * departureTaxRate;
        const returnTax = hasReturnTicket ? returnBaseFare * returnTaxRate : 0;

        // Calculate taxes (for both outbound and return if applicable)
        const totalBaseFare = outboundBaseFare + returnBaseFare;
        const taxes = departureTax + returnTax;

        console.log("booking", booking);

        // Fixed transaction fee
        const transactionFee = 0;
        // Calculate total with fallback to bookedSeats totalPrice if available
        const total =
          totalBaseFare + taxes + transactionFee ||
          (bookingResult?.bookedSeats && bookingResult.bookedSeats.length > 0
            ? parseFloat(bookingResult.bookedSeats[0].totalPrice)
            : 0);

        const outboundFareBreakdown = [
          {
            label: "Adult",
            count: calculatedPassengerCounts.adults,
            perPersonValue: adultPrice,
            value: adultFare,
          },
          {
            label: "Child",
            count: calculatedPassengerCounts.children,
            perPersonValue: childPrice,
            value: childFare,
          },
          {
            label: "Infant",
            count: calculatedPassengerCounts.infants,
            perPersonValue: infantPrice,
            value: infantFare,
          },
        ].filter((i) => i.count > 0);

        const returnFareBreakdown = hasReturnTicket
          ? [
              {
                label: "Adult",
                count: calculatedPassengerCounts.adults,
                perPersonValue: returnAdultPrice,
                value: returnAdultFare,
              },
              {
                label: "Child",
                count: calculatedPassengerCounts.children,
                perPersonValue: returnChildPrice,
                value: returnChildFare,
              },
              {
                label: "Infant",
                count: calculatedPassengerCounts.infants,
                perPersonValue: returnInfantPrice,
                value: returnInfantFare,
              },
            ].filter((i) => i.count > 0)
          : [];

        let travelerCards = "";

        try {
          // Open the print window
          printWindow = window.open("", "_blank");

          if (!printWindow) {
            throw new Error(
              "Could not open print window. Please allow popups for this site."
            );
          }

          // Get the container element that wraps the booking confirmation content
          // const container = document.querySelector(".container");
          for (const selector of containerSelectors) {
            container = document.querySelector(selector);
            if (container) break;
          }

          if (!container) {
            throw new Error("Booking details container not found");
          }

          // Clone the container and its styles
          const element = container.cloneNode(true) as HTMLElement;
          element.style.padding = "20px"; // Add some padding for better print layout

          // Add print-specific styles
          const style = document.createElement("style");
          style.textContent = `
            @page {
              size: A4;
              margin: 10mm;
            }
            body { 
              margin: 0; 
              padding: 20px; 
              font-family: Arial, sans-serif;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print { 
              display: none !important; 
            }
            .container { 
              width: 100% !important; 
              max-width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              box-shadow: none !important;
            }
            .card {
              page-break-inside: avoid;
              break-inside: avoid;
              margin-bottom: 20px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              overflow: hidden;
            }
            .card-header {
              border-bottom: 1px solid #eee;
              padding: 16px;
            }
            .card-title {
              margin: 0;
              font-size: 20px;
              font-weight: 600;
            }
            .card-content {
              padding: 16px;
            }
            @media print {
              body { 
                margin: 0; 
                padding: 0; 
              }
              .no-print { 
                display: none !important; 
              }
              .container { 
                width: 100% !important; 
                margin: 0 !important;
                padding: 0 !important;
              }
              .card {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 10mm;
              }
              .card:last-child {
                margin-bottom: 0;
              }
            }
          `;

          // Create a new document for printing
          const printDocument = printWindow.document;
          printDocument.open();
          printDocument.write(`<!DOCTYPE html>
            <html>
              <head>
                <title>Booking Confirmation - ${
                  booking.bookingResult?.id || ""
                }</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>${style.textContent}</style>
              </head>
              <body>
                ${element.outerHTML}
                <script>
                  // Close the print window after printing
                  window.onload = function() {
                    setTimeout(function() {
                      window.print();
                      setTimeout(function() {
                        window.close();
                      }, 100);
                    }, 200);
                  };
                </script>
              </body>
            </html>
          `);

          printDocument.close();
          printWindow.focus();
        } catch (error: any) {
          console.error("Error preparing print:", error);
          dispatch(
            setMsg({
              message: `Failed to prepare print: ${error.message}`,
              success: false,
            })
          );
          setIsDocumentProcessing({ type: null, action: null });
        }
        travelers?.forEach((traveler: any, index: number) => {
          const t = traveler?.traveler || {};

          travelerCards += `
        <div class="card-content">
          <div class="passenger-section">
            <div class="section-header">
              <span style="font-size: 18px; font-weight: 600;">Traveler ${
                index + 1
              }: ${t?.firstName || "N/A"} ${t?.lastName || "N/A"}
              </span>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Personal Information</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Title</label>
                  <div class="field-value">${capitalizeFirst(t.title)}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Gender</label>
                  <div class="field-value">${capitalizeFirst(t.gender)}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">First Name</label>
                  <div class="field-value">${t.firstName}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Last Name</label>
                  <div class="field-value">${t.lastName}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Date of Birth</label>
                  <div class="field-value">${getFormatDateTable(
                    t.dateOfBirth
                  )}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Nationality</label>
                  <div class="field-value">${t.nationality}</div>
                </div>
              </div>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Identification Documents</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Passport Number</label>
                  <div class="field-value">${t.documentNumber}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Passport Issuing Country</label>
                  <div class="field-value">${t.issuingCountry}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Passport Expiry</label>
                <div class="field-value">${getFormatDateTable(
                  t.expirationDate
                )}</div>
              </div>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Contact Information</h3>
              <div style="margin-bottom: 16px;">
                <div class="form-field">
                  <label class="field-label">Email</label>
                  <div class="field-value">${t.contactEmail}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Phone Number</label>
                <div class="field-value">${t.contactPhone}</div>
              </div>
            </div>
          </div>
        </div>
    `;
        });

        if (!printWindow) {
          throw new Error("Print window is not available");
        }

        // Write the closing HTML and close the document
        printWindow.document.write(`<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Booking Receipt - Airvilla Charters</title>
    <style>
      /* Print-optimized styles with light colors to preserve ink */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: Arial, sans-serif;
      }
      
      body {
        background-color: white;
        color: #333;
        line-height: 1.5;
      }
      
      .container {
        max-width: 1024px;
        margin: 0 auto;
        padding: 20px;
      }
      
      @media print {
        .container {
          padding: 0;
        }
      }
      
      /* Header styles */
      .receipt-header {
        border: 1px solid #ddd;
        border-radius: 12px;
        background-color: #ffffff;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      
      .header-row {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
      }
      
      .company-info {
        display: flex;
        align-items: center;
      }
      
      .company-details {
        margin-left: 12px;
      }
      
      .company-name {
        font-weight: bold;
        font-size: 24px;
      }
      
      .company-address {
        color: #555;
        font-size: 14px;
      }
      
      .receipt-label {
        text-align: right;
        padding: 8px 16px;
      }
      
      .receipt-label h2 {
        font-weight: bold;
        font-size: 20px;
      }
      
      .receipt-label p {
        color: #555;
        font-size: 14px;
      }
      
      .metadata-row {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
      }
      
      .metadata-box {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .metadata-label {
        color: #555;
        font-size: 14px;
        margin-bottom: 8px;
      }
      
      .metadata-value {
        font-weight: 500;
        font-size: 14px;
      }
      
      /* Grid layout */
      .content-grid {
        display: flex;
        flex-direction: column-reverse;
        gap: 24px;
        width: 100%;
        max-width: 100%;
      }
      
      .left-column, .right-column {
        background-color: #f8f8f8;
        border-radius: 12px;
        padding: 48px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      
      .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 8px;
        background-color: #fff;
      }
      
      .card-header {
        border-bottom: 1px solid #eee;
        padding: 16px;
      }
      
      .card-title {
        font-weight: bold;
        font-size: 20px;
      }
      
      .card-content {
        padding: 16px;
      }
      
      /* Booking information */
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
      }
      
      .info-box {
        padding: 16px;
        border-radius: 8px;
        background-color: #f0f0f0;
      }
      
      .info-label {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: #555;
      }
      
      .info-label svg {
        margin-right: 8px;
      }
      
      .info-value {
        font-weight: 600;
        font-size: 18px;
      }
      
      /* Flight card */
      .flight-card {
        padding: 16px;
        border-radius: 8px;
        background-color: #f0f0f0;
        margin-bottom: 24px;
      }
      
      .flight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .flight-type {
        padding: 4px 12px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
      }
      
      .outbound {
        background-color: #ffe6e6;
        color: #c73232;
      }
      
      .return {
        background-color: #e6f0ff;
        color: #2952a3;
      }
      
      .flight-date {
        font-weight: 500;
        display: flex;
        align-items: center;
      }
      
      .flight-date svg {
        margin-right: 8px;
      }
      
      .flight-route {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }
      
      .flight-codes {
        display: flex;
        align-items: center;
      }
      
      .flight-code {
        font-size: 20px;
        font-weight: bold;
      }
      
      .flight-arrow {
        margin: 0 8px;
        color: #777;
      }
      
      .flight-airline {
        text-align: right;
        font-weight: 500;
      }
      
      .flight-meta {
        color: #777;
        font-size: 14px;
        margin-top: 4px;
      }
      
      .flight-times {
        display: flex;
        font-size: 14px;
      }
      
      .time-item {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      
      .time-item svg {
        margin-right: 4px;
      }
      
      .time-separator {
        margin: 0 16px;
      }
      
      /* Flight separator */
      .flight-separator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 0;
        margin-bottom: 12px;
      }
      
      .separator-line {
        height: 1px;
        background-color: #ddd;
        flex-grow: 1;
      }
      
      .separator-circle {
        margin: 0 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #eee;
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }
      
      /* Passenger section */
      .passenger-section {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .divider {
        height: 1px;
        background-color: #ddd;
        width: 100%;
        margin: 12px 0;
      }
      
      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .form-field {
        margin-bottom: 8px;
      }
      
      .field-label {
        display: block;
        font-size: 14px;
        margin-bottom: 4px;
        font-weight: 500;
      }
      
      .field-value {
        width: 100%;
        background-color: #eee;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
      }
      
      .section-title {
        font-weight: bold;
        margin-bottom: 12px;
        font-size: 18px;
      }
      
      /* Payment details */
      .payment-status {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .payment-status svg {
        margin-right: 8px;
        color: #22c55e;
      }
      
      .price-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      
      .price-label {
        color: #555;
      }
      
      .price-value {
        font-weight: 500;
      }
      
      .price-total {
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        padding-top: 4px;
      }
      
      .document-section {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .document-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
      }
      
      .document-title {
        display: flex;
        align-items: center;
      }
      
      .document-title svg {
        margin-right: 8px;
        color: #c73232;
      }
      
      .document-format {
        font-size: 14px;
        color: #777;
      }
      
      .document-description {
        color: #555;
        font-size: 14px;
        margin-bottom: 12px;
      }
      
      .button-row {
        display: flex;
        gap: 12px;
      }
      
      .button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        background-color: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
      }
      
      .button:hover {
        background-color: #eee;
      }
      
      .button svg {
        margin-right: 6px;
      }
      
      .support-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
      }
      
      .support-item svg {
        margin-right: 12px;
        color: #c73232;
      }
      
      .support-link {
        color: #333;
        text-decoration: none;
      }
      
      .support-link:hover {
        color: #c73232;
      }
  
      /* Icon classes */
      .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin-right: 4px;
      }
  
      .icon-sm {
        width: 14px;
        height: 14px;
      }
  
      .icon-lg {
        width: 20px;
        height: 20px;
      }
      
      /* Receipt Footer */
      .receipt-footer {
        margin-top: 40px;
        border-top: 1px solid #ddd;
        padding-top: 24px;
        font-size: 14px;
      }
      
      .footer-content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      
      .footer-logo {
        flex: 1;
      }
      
      .footer-info {
        flex: 3;
        padding-left: 20px;
      }
      
      .footer-legal {
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }
      
      .footer-text {
        color: #555;
        margin-bottom: 4px;
      }
      
      .footer-validation {
        flex: 2;
        text-align: right;
      }
      
      .verification-seal {
        display: inline-flex;
        align-items: center;
        background-color: #f8f8f8;
        padding: 8px 14px;
        border-radius: 4px;
        border: 1px dashed #c73232;
        margin-bottom: 12px;
      }
      
      .verification-seal span {
        font-weight: 600;
        margin-left: 8px;
        color: #c73232;
      }
      
      .footer-date {
        color: #555;
        font-size: 13px;
      }
      
      .footer-disclaimer {
        border-top: 1px solid #eee;
        padding-top: 16px;
        padding-bottom: 6px;
        font-size: 12px;
        color: #777;
        text-align: center;
      }
      
      .footer-disclaimer p {
        margin-bottom: 6px;
      }
      
      .footer-contact-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin-top: 16px;
        margin-bottom: 16px;
      }
      
      .footer-contact-item {
        display: flex;
        align-items: center;
      }
      
      .footer-contact-label {
        margin-left: 8px;
        font-weight: 500;
      }
      
      .footer-badges {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .footer-badge {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        background-color: #f8f8f8;
        border-radius: 4px;
        font-size: 12px;
        color: #555;
        font-weight: 500;
      }
      
      .footer-badge svg {
        margin-right: 6px;
      }
  
      /* For printing */
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        
        .card, .receipt-header, .left-column, .right-column {
          break-inside: avoid;
        }
      }
    </style>
  </head>
  <body>
    <div class="container print-container">
      <!-- Receipt Header -->
      <div class="receipt-header">
        <div class="header-row">
          <div class="company-info">
            <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="48">
            <div class="company-details">
              <h1 class="company-name">Airvilla Charters</h1>
              <p class="company-address">Wasfi At-Tall St. 101, Amman, Jordan</p>
            </div>
          </div>
          <div class="receipt-label">
            <h2>ELECTRONIC TICKET RECEIPT</h2>
            <p>#
            ${
              bookingResult?.Receipt?.[0]?.receiptNumber ||
              bookingResult?.receipt?.receiptNumber
            }
            </p>
          </div>
        </div>
        
        <div class="metadata-row">
          <div class="metadata-box">
            <p class="metadata-label">Receipt Date</p>
            <p class="metadata-value info-value">${getFormatDateTable(
              bookingResult?.Receipt?.[0]?.createdAt ||
                bookingResult?.receipt?.createdAt ||
                booking?.fullTicket?.createdAt
            )}</p>
          </div>
          <div class="metadata-box">
            <p class="metadata-label">Transaction Time</p>
            <p class="metadata-value info-value">${getFormatTime(
              booking?.fullTicket?.transactionDate ||
                bookingResult?.Receipt?.[0]?.createdAt ||
                bookingResult?.receipt?.createdAt
            )}</p>
          </div>
          <div class="metadata-box">
            <p class="metadata-label">Transaction ID</p>
            <p class="metadata-value info-value">${
              bookingResult?.booking?.transactionId ||
              booking?.fullTicket?.transactionId ||
              "N/A"
            }</p>
          </div>
        </div>
      </div>
      
      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Left Column -->
        <div class="left-column">
          <!-- Booking Information -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Booking Information</h2>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-box">
                  <div class="info-label">
                    Booking ID
                  </div>
                  <div class="info-value">${
                    bookingResult?.eTickets?.[0]?.bookingId
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Booking Reference Number
                  </div>
                  <div class="info-value">${
                    bookingResult?.referenceNumber ||
                    bookingResult?.eTickets?.[0]?.eTicketNumber
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Payment Method
                  </div>
                  <div class="info-value">${
                    bookingResult?.booking?.source === "THIRD_PARTY" ||
                    booking?.bookingType === "THIRD_PARTY" ||
                    bookingResult?.source === "THIRD_PARTY"
                      ? "Airvilla Wallet"
                      : normalizePaymentMethod(
                          bookingResult?.paymentMethod ||
                            bookingResult?.payment?.paymentMethod ||
                            bookingResult?.data?.fullTicket?.payment
                              ?.paymentMethod ||
                            "N/A"
                        )
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Booking Date
                  </div>
                  <div class="info-value">${getFormatDateTable(
                    bookingResult?.createdAt || "N/A"
                  )}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Purchased Itinerary -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Purchased Itinerary</h2>
            </div>
            <div class="card-content">
              <!-- Outbound Flight -->
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-type outbound">OUTBOUND</div>
                  <div class="flight-date">
                    ${getFormatDateTable(
                      booking?.fullTicket?.ticket?.flightDate ||
                        bookingResult?.booking?.ticket?.flightDate
                    )}
                  </div>
                </div>
                
                <div class="flight-route">
                  <div>
                    <div class="flight-codes">
                      <span class="flight-code">
                        ${
                          booking?.fullTicket?.ticket?.departure?.airportCode ||
                          bookingResult?.booking?.departure?.airportCode ||
                          booking?.fullTicket?.meta?.departure
                            ?.departureAirport ||
                          "N/A"
                        }
                      </span>
                      <span class="flight-arrow">→</span>
                      <span class="flight-code">${
                        booking?.fullTicket?.ticket?.arrival?.airportCode ||
                        bookingResult?.booking?.arrival?.airportCode ||
                        booking?.fullTicket?.meta?.departure?.arrivalAirport ||
                        "N/A"
                      }</span>
                    </div>
                    <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            } 
                    </div>
                  </div>
                  <div class="flight-airline">
                    ${
                      booking?.fullTicket?.ticket?.segments?.[0]?.carrier ||
                      bookingResult?.booking?.airline?.name ||
                      bookingResult?.booking?.airline?.name ||
                      bookingResult?.meta?.departure?.carrier ||
                      "N/A"
                    } (${
          booking?.fullTicket?.ticket?.segments?.[0]?.flightNumber ||
          bookingResult?.booking?.flightNumber ||
          bookingResult?.meta?.departure?.flightNumber ||
          "N/A"
        })
                  </div>
                </div>
                
                <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                  <div class="time-item" style="margin-right: 0;">
                    Departure: ${getFormatTime(
                      bookingResult?.booking?.departureTime ||
                        booking?.fullTicket?.ticket?.departureTime ||
                        "N/A"
                    )}
                  </div>
                  <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                  <div class="time-item" style="margin-left: 0;">
                    Arrival: ${getFormatTime(
                      bookingResult?.booking?.arrivalTime ||
                        booking?.fullTicket?.ticket?.arrivalTime ||
                        "N/A"
                    )}
                  </div>
                </div>
              </div>
              
              ${
                isRoundTrip
                  ? `
              <!-- Flight Separator -->
              <div class="flight-separator">
                <div class="separator-line"></div>
                <div class="separator-circle">↕</div>
                <div class="separator-line"></div>
              </div>
              
              <!-- Return Flight -->
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-type return">RETURN</div>
                  <div class="flight-date">
                    ${getFormatDateTable(
                      bookingResult?.booking?.ticket?.flightDate
                    )}
                  </div>
                </div>
                
                <div class="flight-route">
                  <div>
                    <div class="flight-codes">
                      <span class="flight-code">${
                        bookingResult?.booking?.arrivalAirport?.iataCode ||
                        "N/A"
                      }</span>
                      <span class="flight-arrow">→</span>
                      <span class="flight-code">${
                        bookingResult?.booking?.departureAirport?.iataCode ||
                        "N/A"
                      }</span>
                    </div>
                    <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            }
                    </div>
                  </div>
                  <div class="flight-airline">
                    ${bookingResult?.booking?.airline?.name || "N/A"} (${
                      bookingResult?.booking?.flightNumber || "N/A"
                    })
                  </div>
                </div>
                
                <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                  <div class="time-item" style="margin-right: 0;">
                    Departure: ${getFormatTime(
                      bookingResult?.booking?.arrivalTime ||
                        booking?.fullTicket?.ticket?.arrivalTime ||
                        "N/A"
                    )}
                  </div>
                  <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                  <div class="time-item" style="margin-left: 0;">
                    Arrival: ${getFormatTime(
                      bookingResult?.booking?.departureTime ||
                        booking?.fullTicket?.ticket?.departureTime ||
                        "N/A"
                    )}
                  </div>
                </div>
              </div>`
                  : ""
              }
              
              <!-- Passenger & Baggage -->
              <div class="flight-card">
                <h3 class="section-title" style="margin-bottom: 8px;">Passenger & Baggage</h3>
                <div class="divider"></div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; padding-bottom: 4px;">
                  <div style="font-weight: 500;">${
                    calculatedPassengerCounts.adults > 0
                      ? `${calculatedPassengerCounts.adults} ${
                          calculatedPassengerCounts.adults === 1
                            ? "Adult"
                            : "Adults"
                        }`
                      : ""
                  }
                ${
                  calculatedPassengerCounts.children > 0
                    ? ` • ${calculatedPassengerCounts.children} ${
                        calculatedPassengerCounts.children === 1
                          ? "Child"
                          : "Children"
                      }`
                    : ""
                }
                ${
                  calculatedPassengerCounts.infants > 0
                    ? ` • ${calculatedPassengerCounts.infants} ${
                        calculatedPassengerCounts.infants === 1
                          ? "Infant"
                          : "Infants"
                      }`
                    : ""
                }</div>
                  <div style="font-size: 14px; color: #555;">
                   <p>
                  ${
                    capitalizeFirst(
                      bookingResult?.passengerCounts?.[0]?.travelClass
                    ) ||
                    capitalizeFirst(flightClass?.type) ||
                    (bookingResult?.bookedSeats &&
                    bookingResult.bookedSeats.length > 0
                      ? capitalizeFirst(
                          bookingResult.bookedSeats[0].flightClass
                        )
                      : "Economy")
                  }
                  ${
                    baggageDisplay.length > 0
                      ? ` • ${baggageDisplay.join(" • ")}`
                      : " • No baggage included"
                  }
                </p>
                </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Traveler Details -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Traveler Details</h2>
            <div class="dark:text-white text-gray-700 text-sm">
              ${travelers.length}
              ${travelers.length === 1 ? "Traveler" : "Travelers"}
            </div>
          </div>
          ${travelerCards}
          </div>
        </div>
        
        <!-- Right Column -->
        <div class="right-column">
          <!-- Payment Details -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Payment Details</h2>
            </div>
            <div class="card-content">
              <!-- Payment confirmation card -->
              <div class="info-box" style="margin-bottom: 16px;">
                <div class="payment-status">
                  <h3 style="font-weight: 600; font-size: 16px;">Payment Successful</h3>
                </div>
                <div class="divider"></div>
                <div style="font-size: 18px; display: grid; grid-template-columns: 3fr 1fr;">
                <section>  
                <div>
                    <span class="price-label" style="font-size: 14px;">Transaction ID:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      bookingResult?.transactionId ||
                      booking?.bookingResult?.booking?.transactionId ||
                      bookingResult?.booking?.transactionId ||
                      booking?.fullTicket?.transactionId ||
                      "N/A"
                    }</div>
                  </div>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Transaction Date:</span>
                    <div class="price-value" style="font-size: 16px;">${getFormatDateTable(
                      booking?.bookingResult?.booking?.transactionDate ||
                        bookingResult?.booking?.transactionDate ||
                        booking?.fullTicket?.transactionDate ||
                        bookingResult?.transactionDate ||
                        bookingResult?.payment?.createdAt ||
                        "N/A"
                    )}</div>
                  </div>
                  </section>
                  <section>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Receipt Number:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      bookingResult?.receipt?.receiptNumber ||
                      bookingResult?.Receipt?.[0]?.receiptNumber
                    }</div>
                  </div>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Booking ID:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      bookingResult?.receipt?.bookingId ||
                      bookingResult?.Receipt?.[0]?.bookingId
                    }</div>
                  </div>
                  </section>
                </div>
              </div>
              
                <!-- Price breakdown card -->
                <div class="info-box" style="margin-bottom: 16px;">
                  <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Price Details</h3>
                  <div class="divider"></div>
                  
                  <!-- Price details will be populated dynamically -->
                  <span class="flight-type outbound" style="display: inline-block; margin-bottom: 8px;">OUTBOUND</span>
                  <div className="block mb-3">
                  ${outboundFareBreakdown
                    .map(
                      (item) => `
                    <div class="price-item">
                      <span class="price-label">
                        ${item.count} ${item.label}${
                        item.count > 1 ? "s" : ""
                      } ×
                        ${item.perPersonValue.toFixed(2)} ${currency}
                      </span>
                      <span>${item.value.toFixed(2)} ${currency}</span>
                    </div>`
                    )
                    .join("")}
                  </div>
                  ${
                    isRoundTrip
                      ? `<span class="flight-type return" style="display: inline-block; margin-bottom: 8px;">RETURN</span>
                  <div class="price-item">
                  <span class="price-label">Base Fare</span>
                  <span>${
                    returnBaseFare >= 0
                      ? `${returnBaseFare.toFixed(2)} ${currency}`
                      : "N/A"
                  }</span>
                  </div>`
                      : ""
                  }
                  <div class="divider"></div>
                  <div class="price-item">
                  <span class="price-label">Taxes</span>
                  <span>${
                    taxes >= 0 ? `${taxes.toFixed(2)} ${currency}` : "N/A"
                  }</span>
                  </div>
                  <div class="price-item">
                  <span class="price-label">Transaction Fee</span>
                  <span>${
                    transactionFee >= 0
                      ? `${transactionFee.toFixed(2)} ${currency}`
                      : "N/A"
                  }</span>
                  </div>
                  <div class="divider"></div>
                  <div class="price-item"  style="font-weight: 600; font-size: 16px;">
                    <span class="price-label">Total</span>
                    <span>${
                      total ||
                      Number(
                        bookingResult?.bookedSeats?.[0]?.totalPrice ||
                          booking?.bookingResult?.totalAmount
                      ).toFixed(2) ||
                      "0.00"
                    } 
                    ${
                      booking?.bookingResult?.currency ||
                      bookingResult?.meta?.pricing?.departure?.currency ||
                      "JOD"
                    }
                    </span>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer Section - Placed properly after content-grid but before closing container div -->
      <div class="receipt-footer">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="72">
          </div>
          <div class="footer-info">
            <p class="footer-legal">ELECTRONIC TICKET RECEIPT</p>
            <p class="footer-text">Thank you for choosing Airvilla Charters. This document serves as your official receipt and booking confirmation.</p>
            <p class="footer-text">Please retain this document for your records and present it when required.</p>
          </div>
          <div class="footer-validation">
            <div class="verification-seal">
              <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="#c73232" stroke-width="2">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              <span>Verified & Approved</span>
            </div>
            <div class="footer-date">Generated on: ${getFormatDate(
              new Date().toString()
            )} • ${getFormatTime(new Date().toString())}</div>
          </div>
        </div>
        
        <div class="footer-contact-grid">
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <span class="footer-contact-label"><EMAIL></span>
          </div>
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
            <span class="footer-contact-label">www.airvilla-charters.travel</span>
          </div>
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            <span class="footer-contact-label">Secure Booking Platform</span>
          </div>
        </div>
        
        <div class="footer-badges">
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            Secure Transaction
          </div>
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
            24/7 Support
          </div>
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            IATA Certified
          </div>
        </div>
        
        <div class="footer-disclaimer">
          <p>This document contains confidential information and is intended for the named recipient only and Authorized Agents. Unauthorized reproduction is strictly prohibited.</p>
          <p> 2025 Airvilla LLC. All rights reserved.</p>
        </div>
      </div>
    </div>`);
      }
    } catch (error) {
      console.error("Error generating receipt:", error);
      dispatch(
        setMsg({
          message: `Error generating document: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
          success: false,
        })
      );
    } finally {
      setIsDocumentProcessing({ type: null, action: null });
    }
    return; // Explicitly return void
  };
  return (
    <div className="min-h-screen dark:bg-gray-900 dark:text-white p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center gap-3">
            <h1 className="font-bold text-gray-700 text-2xl dark:text-white leading-8">
              {renderTitle()}
            </h1>
          </div>
        </div>
        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-2">
            <div className="bg-gray-200 dark:bg-gray-800 p-6 rounded-xl shadow-lg">
              <RequestInformation data={booking} viewType={viewType} />
              {renderTitle() === "Master Booking Control" && (
                <TicketSource data={booking} />
              )}
              {bookingResult.source === "INTERNAL" &&
                (bookingResult.status === "BOOKING_CONFIRMED" ||
                  bookingResult.status === "PENDING_APPROVAL") && (
                  <AgentPaymentDetails data={booking} />
                )}
              <ItineraryCard
                data={booking}
                departureFlight={departureFlight}
                returnFlight={returnFlight}
                isRoundTrip={isRoundTrip}
              />
              <TravelerDetails
                data={booking}
                onEditStateChange={setTravelerEditState}
                bookingStatus={{
                  isPaid: bookingStatus?.isPaid || false,
                  actionsAllowed: bookingStatus?.actionsAllowed || false,
                  status: currentStatus,
                }}
                loadingStatus={loadingStatus}
                isAgencyOwner={isAgencyOwner}
                editable={travelerEditable && isAgencyOwner}
                nonEditableStatuses={nonEditableStatuses}
              />
              {renderTitle() !== "Master Booking Control" && (
                <AdditionalInformation />
              )}
            </div>
          </div>
          <div className="col-span-1">
            <PaymentSummary
              viewType={viewType}
              data={{
                bookingStatus: currentStatus,
                currentUser: currentUser,
                bookingResult: booking.bookingResult,
                travelerData: booking.travelerData,
                fullTicket: booking.fullTicket,
                passengerCounts: booking.passengerCounts,
                bookingType: booking.bookingType,
                itinerary: booking.itinerary,
              }}
              onReleaseSeat={
                showReleaseSeat
                  ? handleReleaseSeat
                  : showReject
                  ? handleRejectBooking
                  : () => {}
              }
              onCompletePayment={
                showCompletePayment
                  ? handleCompletePayment
                  : showApprove
                  ? handleShowApproveConfirmation
                  : () => {}
              }
              isAgencyOwner={isAgencyOwner}
              handleCancelBooking={handleCancelBooking}
              handleRefundBooking={handleRefundBooking}
              handleRescheduleBooking={handleRescheduleBooking}
              nonActionableStatuses={nonActionableStatuses}
              nonEditableStatuses={nonEditableStatuses}
              timeLeft={timeLeft}
              formatTimer={formatTimer}
              handleDocumentAction={handleDocumentAction}
              isAgent={isAgent}
              departureFlight={departureFlight}
              returnFlight={returnFlight}
              isRoundTrip={isRoundTrip}
            />
          </div>
        </div>
      </div>

      {/* Loading Popup */}
      {showLoadingPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 text-center py-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 flex flex-col items-center gap-4 max-w-md w-full">
            <Loader className="animate-spin text-red-500" size={48} />
            <span className="text-lg font-semibold text-gray-800 dark:text-gray-100 text-center whitespace-pre-line">
              {loadingMessage}
            </span>
          </div>
        </div>
      )}

      {/* Approval Confirmation Popup */}
      {showApproveConfirmation && (
        <BookingActionConfirmation
          onConfirm={handleApproveBookingConfirmed}
          onCancel={() => setShowApproveConfirmation(false)}
          title="Confirm Action"
          message="Are you sure you want to Approve this request? This cannot be undone."
          actionType="approve"
          isLoading={loading}
        />
      )}

      {/* Rejection Confirmation Popup */}
      {showRejectConfirmation && (
        <BookingActionConfirmation
          onConfirm={handleRejectBookingConfirmed}
          onCancel={() => setShowRejectConfirmation(false)}
          title="Confirm Action"
          message="Are you sure you want to Reject this request? This cannot be undone."
          actionType="reject"
          isLoading={loading}
        />
      )}
    </div>
  );
};

// Default export with booking data from Redux
const ManageBooking = () => {
  const router = useRouter();

  // Get booking data from Redux store
  const bookingResult = useSelector(selectBookingResult);

  // Check if we have data in Redux
  const hasReduxData = bookingResult && Object.keys(bookingResult).length > 0;

  // Show error state if no data in Redux
  if (!hasReduxData) {
    return (
      <div className="min-h-screen dark:bg-gray-900 dark:text-white p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">No booking data found</span>
          </div>
          <button
            onClick={() => router.back()}
            className="mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // If we have data in Redux, render the component
  return <BookingRequestPage />;
};

export default ManageBooking;
