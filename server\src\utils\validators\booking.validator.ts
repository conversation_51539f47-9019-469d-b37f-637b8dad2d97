import Joi from 'joi';

export const seatInputSchema = Joi.object({
  seatNumber: Joi.string().optional().allow(null, ''),
  flightClass: Joi.string().required(),
  totalPrice: Joi.number().required(),
  travelerId: Joi.string().optional().allow(null, ''),
});

export const travelerInputSchema = Joi.object({
  title: Joi.string().required(),
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  nationality: Joi.string().required(),
  // Require ISO date format (YYYY-MM-DD) for dateOfBirth and expirationDate
  dateOfBirth: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
  gender: Joi.string().required(),
  documentType: Joi.string().required(),
  documentNumber: Joi.string().required(),
  issuingCountry: Joi.string().required(),
  expirationDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
  // contactEmail: Joi.string().required(),
  // contactPhone: Joi.string().required(),
  travelerId: Joi.string().optional(), // Optional for existing travelers
  contactEmail: Joi.string().email({ tlds: { allow: false } }).optional().allow('', null),
  contactPhone: Joi.string()
    .pattern(/^[0-9+\s-]+$/)
    .min(8)
    .max(20)
    .optional()
    .allow('', null),
});

export const createBookingSchema = Joi.object({
  ticketId: Joi.string().required(),
  returnTicketId: Joi.string().optional().allow(null, ''),
  userId: Joi.string().required(),
  agencyAgentId: Joi.string().optional().allow(null, ''),
  teamMemberId: Joi.string().optional().allow(null, ''),
  source: Joi.string().valid('INTERNAL', 'THIRD_PARTY').optional().allow(null, ''),
  type: Joi.string().valid('QUICK_HOLD', 'SUBMIT_BOOKING').required(),
  tripType: Joi.string().valid('ONE_WAY', 'ROUND_TRIP').required(),
  // Travelers are optional at booking creation (step 5), required at payment/confirmation (step 6)
  travelers: Joi.array().items(travelerInputSchema).optional().default([]),
  referenceNumber: Joi.string().optional().allow(null, ''),
  seats: Joi.array().items(seatInputSchema).min(1).required(),
});