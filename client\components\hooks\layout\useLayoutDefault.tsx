"use client";

import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { loginUser, logoutUser, selectUser } from "@/redux/features/AuthSlice";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import { fetchUserProfile } from "@/lib/data/userProfileData";
import { useSocket } from "@/context/SocketContext";
import { selectActionMsg, setMsg } from "@/redux/features/ActionMsgSlice";
import { fetchLogout } from "@/lib/data/authData";
import Cookies from "js-cookie";
import { setupSessionBroadcast, broadcastSessionExpiration } from '@/utils/session';
import { SESSION_EXPIRED_EVENT } from "@/utils/constants/sessionExpiredConstants";

export default function useLayoutDefault() {
  // redux
  const user = useAppSelector(selectUser);
  const actionMsg = useAppSelector(selectActionMsg);

  // hooks
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { socket } = useSocket();

  // states
  const timeoutIdRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [loading, setLoading] = useState(true);
  const [userLoading, setUserLoading] = useState(true); // To track user info loading
  const [isSessionExpiredPopupOpen, setIsSessionExpiredPopupOpen] =
    useState(false);

  // functions
  const handleLogout = async () => {
    setLoading(true);
    // clean all cookies
    const logout = await fetchLogout();

    // send a success msg
    if (logout?.success) {
      setIsSessionExpiredPopupOpen(true);
      broadcastSessionExpiration(); // Notify other tabs
    }
    // dispatch(setMsg({ success: logout.success, message: logout.message }));
    setLoading(false);
  };

  // timer
  const resetTimer = useCallback(() => {
    // Clear the previous timeout
    if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);

    // Set a new 20-minute timer (120,000 milliseconds)
    timeoutIdRef.current = setTimeout(() => {
      handleLogout();
    }, SESSION_EXPIRED_EVENT); // 20 minutes
  }, []);

  // useEffects
  useEffect(() => {
    const fetchUserInfo = async (): Promise<void> => {
      setLoading(true);

      // Fetch user info from the server
      const userInfo = await fetchUserProfile();

      // If the fetch was successful and the user info is present, update the state
      if (userInfo?.success && userInfo.results) {
        dispatch(loginUser(userInfo.results));
      }

      setUserLoading(false);
      setLoading(false);
    };

    fetchUserInfo();
  }, []);

  // update the user state when update it from master
  useEffect(() => {
    if (socket) {
      socket.on("updateUserProfile", () => {
        dispatch(
          setMsg({
            success: true,
            message: "Your account info has been updated by master user",
          })
        );
        // fetchUserInfo();
      });

      socket.on("sessionExpiration", () => {
        setIsSessionExpiredPopupOpen(true);
      });

      socket.on("userAccepted", (data) => {
        setIsSessionExpiredPopupOpen(true);
        console.log("userAccepted:", data);
        console.log("userAccepted:", data.accountStatus);
        if (data.accountStatus === "accepted") {
          // Automatically log the user in or redirect to the dashboard
          router.push("/blockseats"); // or any other logic to log the user in
        }
      });
    }

    // Cleanup the event listener when the component unmounts
    return () => {
      if (socket) {
        socket.off("updateUserProfile");
        socket.off("sessionExpiration");
        socket.off("userAccepted");
      }
    };
  }, [socket]);

  // if user is unauthorized
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const userInfo = await fetchUserProfile();
        if (!userInfo?.success || !userInfo.results) {
          // Clear any existing session data
          Cookies.remove("token");
          dispatch(logoutUser());
          // Redirect to signin immediately
          router.push("/signin");
          return;
        }
        dispatch(loginUser(userInfo.results));
      } catch (error) {
        console.error("Auth check failed:", error);
        Cookies.remove("token");
        dispatch(logoutUser());
        router.push("/signin");
      } finally {
        setLoading(false);
        setUserLoading(false);
      }
    };

    checkAuth();
  }, [dispatch]);

  useEffect(() => {
    if (actionMsg.message === "Unauthorized") {
      setIsSessionExpiredPopupOpen(true);
      // dispatch(logoutUser());
    }
  }, [actionMsg]);

  // Redirect to signin page if already not logged in
  useEffect(() => {
    if (userLoading) return;

    // Get current path
    const currentPath = window.location.pathname;

    // If user is not logged in and not on signin page, redirect to signin
    if (!user.isLogin) {
      if (currentPath !== "/signin") {
        console.log(
          "User is not logged in in use layout default. Redirecting to signin page."
        );
        router.push("/signin");
      }
      return;
    }

    // Get user status and verification
    const userStatus = (user as StoredUser).accountStatus;
    const isVerified = (user as StoredUser).verified;

    // Handle suspended/rejected users
    if (
      userStatus === "suspended" ||
      userStatus === "rejected" ||
      userStatus === "deactivated" ||
      userStatus === "disabled"
    ) {
      // Only redirect if not already on signin page
      if (currentPath !== "/signin") {
        // Clear auth state and token
        Cookies.remove("token");
        dispatch(logoutUser());

        const message =
          userStatus === "suspended"
            ? "Your account has been suspended. Please contact support for assistance."
            : "Your account has been rejected. Please contact support for assistance.";

        router.push(`/signin?message=${encodeURIComponent(message)}`);
        // router.push(`/signin`);
      }
      return;
    }

    // Handle unverified users
    if (!isVerified) {
      if (currentPath !== "/signup-process/not-verified") {
        router.push("/signup-process/not-verified");
      }
      return;
    }

    // Handle pending users
    if (userStatus === "pending") {
      if (currentPath !== "/signup-process/not-accepted") {
        router.push("/signup-process/not-accepted");
      }
      return;
    }

    // If we get here, user is verified and accepted
    setLoading(false);
  }, [user, userLoading, router, dispatch]);

  useEffect(() => {
    // Events to detect user activity
    const events = ["keydown", "click"];

    // Add event listeners to reset the timer on user activity
    events.forEach((event) => window.addEventListener(event, resetTimer));

    // Start the inactivity timer when the component mounts
    resetTimer();

    // Cleanup: remove event listeners and clear timeout on unmount
    return () => {
      events.forEach((event) => window.removeEventListener(event, resetTimer));
      if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);
    };
  }, [resetTimer]);

  useEffect(() => {
    const cleanup = setupSessionBroadcast(() => {
      setIsSessionExpiredPopupOpen(true);
    });
  
    return () => {
      cleanup?.();
    };
  }, []);

  return { loading, isSessionExpiredPopupOpen };
}
