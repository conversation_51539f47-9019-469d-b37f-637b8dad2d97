/**
 * Utility function to redirect to a URL while preserving specified URL parameters
 * 
 * @param targetPath - The path to redirect to (e.g., '/blockseats/list')
 * @param delay - Optional delay in milliseconds before redirecting
 */
export const redirectWithParams = (targetPath: string, delay: number = 0) => {
  // Parameters to preserve (add or remove as needed)
  const paramsToPreserve = [
    'itinerary', 'travelClass', 'departureId', 'departureCode', 
    'departureCity', 'departureCountry', 'departureAirport', 
    'arrivalId', 'arrivalCode', 'arrivalCity', 'arrivalCountry', 
    'arrivalAirport', 'flightDate', 'adults', 'children', 'infants',
    'returnDate'
  ];

  const redirectNow = () => {
    if (typeof window === 'undefined') return;
    
    // Extract search parameters that should be preserved
    const currentUrl = new URL(window.location.href);
    const searchParams = currentUrl.searchParams;
    
    // Build the new URL with preserved parameters
    const newUrl = new URL(targetPath, window.location.origin);
    
    // Add preserved parameters to the new URL
    paramsToPreserve.forEach(param => {
      const value = searchParams.get(param);
      if (value) {
        newUrl.searchParams.append(param, value);
      }
    });
    
    // Redirect to the new URL
    window.location.href = newUrl.toString();
  };

  if (delay > 0) {
    setTimeout(redirectNow, delay);
  } else {
    redirectNow();
  }
};
