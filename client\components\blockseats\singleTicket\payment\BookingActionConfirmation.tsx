import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Loader } from "lucide-react";

/**
 * Booking Action Confirmation Component Props
 */
interface BookingActionConfirmationProps {
  /**
   * Callback function when user confirms the action
   */
  onConfirm: () => void;

  /**
   * Callback function when user cancels the action
   */
  onCancel: () => void;

  /**
   * Type of action being confirmed
   */
  actionType: "approve" | "reject";

  /**
   * Custom confirmation message
   * @default "Are you sure you want to perform this action? This cannot be undone."
   */
  message?: string;

  /**
   * Custom confirmation title
   * @default "Confirm Action"
   */
  title?: string;

  /**
   * Loading state
   */
  isLoading?: boolean;
}

/**
 * Booking Action Confirmation Component
 * Displays a modal dialog for confirming booking actions
 */
const BookingActionConfirmation: React.FC<BookingActionConfirmationProps> = ({
  onConfirm,
  onCancel,
  actionType,
  message = "Are you sure you want to perform this action? This cannot be undone.",
  title = "Confirm Action",
  isLoading = false,
}) => {
  return (
    <div className="p-8">
      <div className="fixed inset-0 flex items-center justify-center z-50">
        {/* Background overlay - click events blocked during loading */}
        <div
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={isLoading ? undefined : onCancel}
        ></div>

        {/* Popup content */}
        <div className="relative rounded-xl shadow-lg max-w-md w-full mx-4 p-6 z-10 bg-white dark:bg-gray-800">
          {isLoading ? (
            /* Loading state overlay */
            <div className="flex flex-col items-center text-center py-8 h-64 justify-center">
              {/* Loading spinner */}
              <div className="mb-4">
                <Loader className="h-12 w-12 text-red-500 animate-spin" />
              </div>

              {/* Loading message */}
              <p className="text-gray-700 dark:text-gray-300 text-lg font-medium">
                Processing your request...
              </p>
              <p className="text-gray-700 dark:text-gray-300 mt-2">
                This may take a few seconds.
              </p>
            </div>
          ) : (
            /* Confirmation content */
            <div className="flex flex-col items-center text-center h-64 justify-center">
              {/* Icon */}
              <div className="h-16 w-16 bg-red-500/10 rounded-full flex items-center justify-center mb-4">
                {/* Warning icon for reject */}
                {actionType === "reject" && (
                  <AlertCircle className="h-10 w-10 text-red-500" />
                )}
                {/* Success icon for approve */}
                {actionType === "approve" && (
                  <CheckCircle className="h-10 w-10 text-red-500" />
                )}
              </div>

              {/* Title */}
              <h2 className="text-2xl font-bold text-gray-700 dark:text-gray-300 mb-2">
                {title}
              </h2>

              {/* Message */}
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                {message}
              </p>

              {/* Action buttons */}
              <div className="flex w-full space-x-4">
                <button
                  className="w-1/2 py-3 px-6 rounded-lg font-semibold bg-gray-700 dark:bg-gray-600 text-white dark:text-gray-300 hover:bg-gray-600 dark:hover:bg-gray-700"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </button>
                <button
                  className={`w-1/2 py-3 px-6 rounded-lg font-semibold ${
                    actionType === "approve"
                      ? "bg-red-500 dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700"
                      : "bg-red-500 dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700"
                  } text-white dark:text-gray-300`}
                  onClick={onConfirm}
                  disabled={isLoading}
                >
                  Confirm
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookingActionConfirmation;
