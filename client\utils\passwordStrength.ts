import { PasswordStrength } from "./definitions/agentsDefinitions";

/**
 * Evaluates the strength of a given password and returns a `PasswordStrength` object.
 * The evaluation is based on various criteria such as length, uppercase, lowercase,
 * numeric and special character presence, as well as common pattern avoidance.
 *
 * @param password - The password string to evaluate.
 * @param isDarkMode - A boolean indicating if dark mode is enabled, affecting the color output.
 * @returns A `PasswordStrength` object containing the score, feedback, color, label, and message.
 */

export const checkPasswordStrength = (
  password: string,
  isDarkMode: boolean
): PasswordStrength => {
  const strength: PasswordStrength = {
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  };

  if (!password) {
    strength.feedback.push("Please enter your password");
    return strength;
  }

  // Length check - up to 2 points
  if (password.length >= 8) {
    strength.score += 1;
    if (password.length >= 12) {
      strength.score += 1;
    }
  } else {
    strength.feedback.push(
      "Please make sure your password is at least 8 characters long"
    );
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    strength.feedback.push("include uppercase letters");
  } else {
    strength.score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    strength.feedback.push("include lowercase letters");
  } else {
    strength.score += 1;
  }

  // Numbers check
  if (!/\d/.test(password)) {
    strength.feedback.push("include numbers");
  } else {
    strength.score += 1;
  }

  // Special characters check - up to 2 points
  const specialChars = (password.match(/[!@#$%^&*(),.?":{}|<>]/g) || []).length;
  if (specialChars === 0) {
    strength.feedback.push("special characters");
  } else {
    strength.score += 1;
    if (specialChars >= 2) {
      strength.score += 1; // Extra point for 2 or more special characters
    }
  }

  // Common patterns check
  const commonPatterns = [
    "password",
    "123456",
    "qwerty",
    "admin",
    "letmein",
    "welcome",
  ];
  if (
    commonPatterns.some((pattern) => password.toLowerCase().includes(pattern))
  ) {
    strength.feedback.push("Avoid common password patterns");
    strength.score = Math.max(0, strength.score - 2); // Bigger penalty for common patterns
  }

  // Normalize score to 0-5 range
  strength.score = Math.min(5, strength.score);

  switch (strength.score) {
    case 0:
      strength.color = isDarkMode ? "#FF5252" : "#D32F2F";
      strength.label = "Very Weak";
      strength.message = "Too easy to guess. Add more letters and numbers.";
      break;
    case 1:
      strength.color = isDarkMode ? "#FFAB40" : "#F57C00";
      strength.label = "Weak";
      strength.message =
        "Still too simple. Try adding special characters like ! or @.";
      break;
    case 2:
      strength.color = isDarkMode ? "#FFEE58" : "#FBC02D";
      strength.label = "Fair";
      strength.message = "Getting better. Mix in some capital letters too.";
      break;
    case 3:
      strength.color = isDarkMode ? "#40C4FF" : "#0288D1";
      strength.label = "Good";
      strength.message = "Nice! Make it longer for even better security.";
      break;
    case 4:
      strength.color = isDarkMode ? "#69F0AE" : "#00C853";
      strength.label = "Strong";
      strength.message = "Great job! Your password is very hard to crack.";
      break;
    case 5:
      strength.color = isDarkMode ? "#00E676" : "#2E7D32";
      strength.label = "Very Strong";
      strength.message = "Perfect! Your password is super secure.";
      break;
  }

  return strength;
};
