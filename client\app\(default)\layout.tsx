"use client";
import Sidebar from "@/components/ui/sidebar";
import Header from "@/components/ui/header";
import ProgressLoading from "@/components/utils/ProgressLoading";
import SessionExpirationPopup from "@/components/extra-components/SessionExpirationPopup";
import useLayoutDefault from "@/components/hooks/layout/useLayoutDefault";
import LoginLoadingProgress from "@/components/extra-components/LoginLoadingProgress";
import { NotificationProvider } from "@/context/NotificationContext";
import { useUser } from "@/components/hooks/useUser";

export default function DefaultLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { loading, isSessionExpiredPopupOpen } = useLayoutDefault();
  const user = useUser();

  if (loading) return <ProgressLoading />;
  
  // Wrap the content with NotificationProvider if user is authenticated
  const content = (
    <div className="flex h-[100dvh] overflow-hidden">
      <SessionExpirationPopup isOpen={isSessionExpiredPopupOpen} />
      <LoginLoadingProgress />
      {/* Sidebar */}
      {/* <Sidebar /> */}

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden custom-scrollbar">
        {/*  Site header */}
        <Header />

        <main className="grow [&>*:first-child]:scroll-mt-16">{children}</main>
      </div>
    </div>
  );

  // If user is not authenticated, return content without NotificationProvider
  if (!user) {
    return content;
  }

  // If user is authenticated, wrap content with NotificationProvider
  // Only wrap with NotificationProvider if user is authenticated
  return user ? (
    <NotificationProvider userId={user.id}>{content}</NotificationProvider>
  ) : (
    content
  );
}
