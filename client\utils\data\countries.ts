interface Country {
  code: string;
  name: string;
}

export const countries: Country[] = [
  { code: "ARE", name: "United Arab Emirates" },
  { code: "BHR", name: "Bahrain" },
  { code: "DJ<PERSON>", name: "Djibouti" },
  { code: "DZA", name: "Algeria" },
  { code: "EGY", name: "Egypt" },
  { code: "IRN", name: "Iran" },
  { code: "IRQ", name: "Iraq" },
  { code: "PS", name: "Palestine" },
  { code: "J<PERSON>", name: "Jordan" },
  { code: "KWT", name: "Kuwait" },
  { code: "LBN", name: "Lebanon" },
  { code: "LBY", name: "Libya" },
  { code: "MAR", name: "Morocco" },
  { code: "MLT", name: "Malta" },
  { code: "OMN", name: "Oman" },
  { code: "QAT", name: "Qatar" },
  { code: "SAU", name: "Saudi Arabia" },
  { code: "SYR", name: "Syria" },
  { code: "TUN", name: "Tunisia" },
  { code: "Y<PERSON>", name: "Yemen" },
];

export const countryCodes = [
  { name: "Jordan", code: "+962" },
  { name: "United Arab Emirates", code: "+971" },
  { name: "Bahrain", code: "+973" },
  { name: "Djibouti", code: "+253" },
  { name: "Algeria", code: "+213" },
  { name: "Egypt", code: "+20" },
  { name: "Iran", code: "+98" },
  { name: "Iraq", code: "+964" },
  { name: "Palestine", code: "+970" },
  { name: "Kuwait", code: "+965" },
  { name: "Lebanon", code: "+961" },
  { name: "Libya", code: "+218" },
  { name: "Morocco", code: "+212" },
  { name: "Malta", code: "+356" },
  { name: "Oman", code: "+968" },
  { name: "Qatar", code: "+974" },
  { name: "Saudi Arabia", code: "+966" },
  { name: "Syria", code: "+963" },
  { name: "Tunisia", code: "+216" },
  { name: "Yemen", code: "+967" },
];
