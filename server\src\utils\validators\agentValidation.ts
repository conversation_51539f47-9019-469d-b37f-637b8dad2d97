// src/validations/agent-schema.ts
import <PERSON><PERSON> from "joi";
import { Department, Role, AgentRole, RoleType } from "@prisma/client";
import { baseUserValidation, commonPatterns } from "./baseUserValidation";

export const agentValidation = Joi.object({
  ...baseUserValidation,
  email: Joi.string().email().required(),
  password: Joi.string()
    .min(8)
    .max(128)
    .disallow(...commonPatterns)
    .pattern(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+|~=`{}\[\]:";'<>?,./])[A-Za-z\d!@#$%^&*()_+|~=`{}\[\]:";'<>?,./]{8,128}$/
    )
    .messages({
      "string.pattern.base":
        "Password must contain at least one uppercase, lowercase, number, and special character",
    }),
  role: Joi.string().valid(Role.agency).required(),
  department: Joi.string()
    .valid(...Object.values(Department))
    .required(),
  subRole: Joi.string()
    .valid(...Object.values(AgentRole))
    .required(),
  roleType: Joi.string().valid(...Object.values(RoleType)),
}).options({ abortEarly: false });
