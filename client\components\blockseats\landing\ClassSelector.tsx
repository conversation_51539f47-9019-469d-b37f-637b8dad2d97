"use client";
import React, { useState, useRef, useEffect } from "react";
import { Check, ChevronDown } from "lucide-react";

export const ClassSelector = ({
  travelClass,
  setTravelClass,
  error,
}: {
  travelClass: string;
  setTravelClass: (travelClass: string) => void;
  error?: string;
}) => {
  const [menuActive, setMenuActive] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);

  const travelClasses = [
    "economy",
    "premium economy",
    "business class",
    "first class",
  ];

  // Filter classes based on search input
  const filteredClasses =
    searchValue.trim() === ""
      ? travelClasses
      : travelClasses.filter((cls) =>
          cls.toLowerCase().includes(searchValue.toLowerCase())
        );

  // Handle click outside to close dropdown
  const handleClickOutside = (event: MouseEvent) => {
    if (
      menuRef.current &&
      !menuRef.current.contains(event.target as Node) &&
      !(event.target instanceof HTMLInputElement)
    ) {
      setMenuActive(false);
      setSearchValue("");
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative mb-6" ref={menuRef}>
      <div className="relative inline-flex w-full">
        <div
          onClick={() => {
            setMenuActive(!menuActive);
            setSearchValue("");
          }}
          className={`btn py-0 pl-0 w-full justify-between min-w-52 h-[45px] bg-gray-100 dark:bg-gray-700 hover:border hover:border-red-500 hover:ring-1 hover:ring-red-500 text-gray-700 hover:text-gray-900 dark:text-white dark:hover:text-gray-200 rounded-lg ${
            menuActive ? "border border-red-500 ring-1 ring-red-500" : ""
          }`}
        >
          <span className="flex-1 items-center capitalize px-4 py-3">
            {travelClass || "Select Class"}
          </span>
          <ChevronDown
            className="text-gray-500 dark:text-gray-400 mr-4"
            size={20}
          />
        </div>

        {/* Dropdown Menu */}
        {menuActive && (
          <div className="z-20 absolute top-full left-0 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1">
            <div className="font-medium text-sm text-gray-600 dark:text-gray-300 focus:outline-none max-h-40 overflow-auto custom-scrollbar">
              {filteredClasses.length === 0 && (
                <div className="text-center py-3">
                  <span>No Results</span>
                </div>
              )}
              {filteredClasses.length > 0 &&
                filteredClasses.map((cls) => {
                  const isSelected = travelClass === cls;

                  return (
                    <button
                      key={cls}
                      type="button"
                      className={`flex items-center justify-between text-sm md:text-base w-full py-2 px-3 cursor-pointer capitalize border-b border-gray-200 dark:border-gray-700 ${
                        isSelected ? "text-red-500" : ""
                      }`}
                      onClick={() => {
                        setTravelClass(cls);
                        setMenuActive(false);
                      }}
                    >
                      <div className="text-start text-base">
                        <div className="font-medium">{cls}</div>
                      </div>
                      <Check
                        className={`shrink-0 mr-2 text-red-500 ${
                          !isSelected && "invisible"
                        }`}
                        size={20}
                      />
                    </button>
                  );
                })}
            </div>
          </div>
        )}
      </div>
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
};
