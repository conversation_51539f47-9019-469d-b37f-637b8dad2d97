import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useDebouncedCallback } from "use-debounce";
import { useSocket } from "./SocketContext";
import {
  getNotifications,
  markAsReadNotification,
  markAllAsReadNotification,
  deleteNotification as deleteNotificationApi,
} from "@/lib/data/notificationData";

export interface Notification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  relatedId?: string;
  link?: string;
  priority: number;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | null>(null);

// Helper function to get stored notification states from localStorage
const getStoredNotificationStates = (
  userId: string
): Record<string, boolean> => {
  try {
    const stored = localStorage.getItem(`notification_states_${userId}`);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error(
      "Error reading notification states from localStorage:",
      error
    );
    return {};
  }
};

// Helper function to save notification states to localStorage
const saveNotificationStates = (
  userId: string,
  states: Record<string, boolean>
): void => {
  try {
    localStorage.setItem(
      `notification_states_${userId}`,
      JSON.stringify(states)
    );
  } catch (error) {
    console.error("Error saving notification states to localStorage:", error);
  }
};

export const NotificationProvider = ({
  children,
  userId,
}: {
  children: ReactNode;
  userId: string;
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { socket } = useSocket();
  // Track deleted notification IDs locally to filter out ghosts
  const [deletedIds, setDeletedIds] = useState<Set<string>>(new Set());
  // Load stored notification states on mount
  const [notificationStates, setNotificationStates] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    if (userId) {
      const storedStates = getStoredNotificationStates(userId);
      setNotificationStates(storedStates);
    }
  }, [userId]);

  // Debounced fetchNotifications
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const data = await getNotifications(userId);
      // Filter out any notifications that have been deleted locally
      const filteredData = data.filter(
        (notification: Notification) => !deletedIds.has(notification.id)
      );
      // Apply stored read states from localStorage
      const storedStates = getStoredNotificationStates(userId);
      // Merge server data with stored states
      const mergedData = filteredData.map((notification: Notification) => ({
        ...notification,
        read:
          storedStates[notification.id] !== undefined
            ? storedStates[notification.id]
            : notification.read,
      }));
      // De-duplication: merge and keep most recent by id
      setNotifications((prev) => {
        const seen = new Set();
        return [...mergedData, ...prev].filter((n) => {
          if (seen.has(n.id)) return false;
          seen.add(n.id);
          return true;
        });
      });
      // Clean up deletedIds for notifications that are no longer returned by backend
      setDeletedIds((prev) => {
        const currentIds = new Set(data.map((n: Notification) => n.id));
        const updated = new Set(
          Array.from(prev).filter((id) => currentIds.has(id))
        );
        return updated;
      });
      setError(null);
    } catch (err) {
      setError("Failed to fetch notifications");
    } finally {
      setLoading(false);
    }
  };

  // Debounced fetchNotifications to avoid rapid API calls
  const debouncedFetchNotifications = useDebouncedCallback(
    fetchNotifications,
    500
  );

  useEffect(() => {
    debouncedFetchNotifications();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  useEffect(() => {
    if (!socket || !userId) return;

    // Always join room on connect/reconnect
    const handleConnect = () => {
      socket.emit("joinRoom", userId);
      debouncedFetchNotifications(); // Fetch missed notifications on reconnect
    };
    socket.on("connect", handleConnect);

    // Listen for notification events
    socket.on("new-notification", (notification: Notification) => {
      setNotifications((prev) => {
        // De-duplication: merge and keep most recent by id
        const seen = new Set();
        return [notification, ...prev].filter((n) => {
          if (seen.has(n.id)) return false;
          seen.add(n.id);
          return true;
        });
      });
      fetchNotifications();
    });
    socket.on("notification-read", (notificationId: string) => {
      setNotifications((prev) =>
        prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n))
      );

      // Update stored state
      setNotificationStates((prev) => {
        const updated = { ...prev, [notificationId]: true };
        saveNotificationStates(userId, updated);
        return updated;
      });
    });
    socket.on("notification-deleted", (notificationId: string) => {
      setNotifications((prev) => prev.filter((n) => n.id !== notificationId));

      // Remove from stored states
      setNotificationStates((prev) => {
        const updated = { ...prev };
        delete updated[notificationId];
        saveNotificationStates(userId, updated);
        return updated;
      });

      deleteNotification(notificationId);
    });

    return () => {
      socket.off("connect", handleConnect);
      socket.off("new-notification");
      socket.off("notification-read");
      socket.off("notification-deleted");
    };
  }, [socket, userId]);

  const markAsRead = async (notificationId: string) => {
    try {
      // Check if notification exists in local state
      const notification = notifications.find((n) => n.id === notificationId);
      if (!notification) {
        console.log(
          "Notification not found in local state, skipping mark as read"
        );
        return;
      }

      const result = await markAsReadNotification(notificationId);
      // If the notification was deleted on the server, remove it from local state
      if (
        (result && result.error === "Notification not found") ||
        (result && "deleted" in result && result.deleted)
      ) {
        setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
        setDeletedIds((prev) => new Set(prev).add(notificationId));
        const storedStates = JSON.parse(
          localStorage.getItem("notificationStates") || "{}"
        );
        delete storedStates[notificationId];
        localStorage.setItem(
          "notificationStates",
          JSON.stringify(storedStates)
        );
        return;
      }

      // Update local state
      setNotifications((prev) =>
        prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n))
      );

      // Update stored notification states
      const storedStates = JSON.parse(
        localStorage.getItem("notificationStates") || "{}"
      );
      storedStates[notificationId] = { read: true };
      localStorage.setItem("notificationStates", JSON.stringify(storedStates));

      // Emit socket event to notify other clients
      if (socket) {
        socket.emit("notification_read", { notificationId });
      }
    } catch (error: any) {
      console.error("Error marking notification as read:", error);
      // If we get a 404 error or "Notification not found", remove the notification from local state
      if (error?.status === 404 || error?.error === "Notification not found") {
        setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
        setDeletedIds((prev) => new Set(prev).add(notificationId));
        const storedStates = JSON.parse(
          localStorage.getItem("notificationStates") || "{}"
        );
        delete storedStates[notificationId];
        localStorage.setItem(
          "notificationStates",
          JSON.stringify(storedStates)
        );
      }
    }
  };

  const deleteNotification = async (notificationId: string) => {
    // Optimistically remove notification from state immediately
    setNotifications((prevNotifications) =>
      prevNotifications.filter(
        (notification) => notification.id !== notificationId
      )
    );
    setDeletedIds((prev) => new Set(prev).add(notificationId));
    setNotificationStates((prev) => {
      const updated = { ...prev };
      delete updated[notificationId];
      saveNotificationStates(userId, updated);
      return updated;
    });
    try {
      await deleteNotificationApi(notificationId);
      setError(null);
    } catch (err) {
      setError("Failed to delete notification");
    }
  };

  const markAllAsRead = async () => {
    try {
      await markAllAsReadNotification(userId);
      // Update local state
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
      // Update all stored states to read
      setNotificationStates((prev) => {
        const allReadStates: Record<string, boolean> = {};
        notifications.forEach((notification) => {
          allReadStates[notification.id] = true;
        });
        saveNotificationStates(userId, allReadStates);
        return allReadStates;
      });
    } catch (err) {
      setError("Failed to mark all notifications as read");
    }
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        loading,
        error,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        refreshNotifications: fetchNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    console.warn("useNotifications must be used within a NotificationProvider");
    return {
      notifications: [],
      unreadCount: 0,
      markAsRead: () => {},
      markAllAsRead: () => {},
      deleteNotification: () => {},
      refreshNotifications: () => {},
      loading: false,
      error: null,
    };
    // throw new Error("useNotifications must be used within a NotificationProvider");
  }
  return context;
};
