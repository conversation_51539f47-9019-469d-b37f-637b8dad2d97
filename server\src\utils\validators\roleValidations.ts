// src/validations/role-validations.ts
import { TeamM<PERSON>berRole, Role, AgentRole } from "@prisma/client";
// import { TeamMemberRole, Role, AgentRole } from "../../types/prismaEnums";

export class RoleValidations {
  static validateTeamMemberRole(
    currentRole?: TeamMemberRole | Role,
    newSubRole?: TeamMemberRole
  ) {
    const errors: string[] = [];

    if (!currentRole) return errors;

    if (newSubRole === TeamMemberRole.admin) {
      if (![Role.master, TeamMemberRole.admin].includes(currentRole as any)) {
        errors.push("Only Admin users can modify Admin accounts");
      }
    }

    if (currentRole === TeamMemberRole.moderator) {
      if (newSubRole === TeamMemberRole.admin) {
        errors.push("Moderators cannot modify Admin accounts");
      }
      if (
        newSubRole !== TeamMemberRole.moderator &&
        newSubRole !== TeamMemberRole.accountant
      ) {
        errors.push(
          "Moderators can only manage Moderator and Accountant accounts"
        );
      }
    }

    if (currentRole === TeamMemberRole.accountant) {
      errors.push("Accountants cannot modify any user accounts");
    }

    return errors;
  }

  static validateAgentRole(currentRole?: Role, newSubRole?: AgentRole) {
    const errors: string[] = [];

    if (currentRole && newSubRole === AgentRole.admin) {
      if (currentRole !== Role.master && currentRole !== Role.agency) {
        errors.push("Only agency users can assign admin role to agents");
      }
    }

    return errors;
  }
}
