import { Router } from "express";
import {
  validateTrave<PERSON>,
  createTraveler,
  getTraveler<PERSON>yId,
  updateTraveler,
  deleteTraveler,
} from "../controllers/travelerController";
import userAuth from "../middlewares/userAuth";

const router = Router();

/**
 * @openapi
 * /traveler/validate:
 *   post:
 *     tags:
 *       - Traveler
 *     summary: Validate traveler data without saving
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Traveler data validated
 */
// Validate traveler data without saving
router.post("/validate", validateTraveler);

/**
 * @openapi
 * /traveler:
 *   post:
 *     tags:
 *       - Traveler
 *     summary: Create a new traveler
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Traveler created
 */
// Create a new traveler
router.post("/", userAuth, createTraveler);

/**
 * @openapi
 * /traveler/{id}:
 *   get:
 *     tags:
 *       - Traveler
 *     summary: Get a traveler by ID
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Traveler details
 */
// Get a traveler by ID
router.get("/:id", userAuth, getTravelerById);

/**
 * @openapi
 * /traveler/{id}:
 *   put:
 *     tags:
 *       - Traveler
 *     summary: Update a traveler
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Traveler updated
 */
// Update a traveler
router.put("/:id", userAuth, updateTraveler);

/**
 * @openapi
 * /traveler/{id}:
 *   delete:
 *     tags:
 *       - Traveler
 *     summary: Delete a traveler
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Traveler deleted
 */
// Delete a traveler
router.delete("/:id", userAuth, deleteTraveler);

export default router;
