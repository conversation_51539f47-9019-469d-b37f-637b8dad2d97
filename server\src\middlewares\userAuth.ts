import { Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
import {
  calculateDeactivationDays,
  DELETION_PERIOD,
  isTestMode,
  REACTIVATION_PERIOD,
  unitOfTime,
} from "../utils/constants/timeVariables";
import { AccountStatus } from "@prisma/client";
import { handleAgencyAcceptation } from "../utils/triggers/handleAgencyAcceptation";
import user from "../routes/user";

const userAuth = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.cookies.token;

    if (!token) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // decode the access token
    const secretKey = process.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Secret key not configured");
    }

    const decoded = jwt.verify(token, secretKey) as {
      id: string;
      type:
        | "masterUser"
        | "masterOwner"
        | "agencyOwner"
        | "agencyUser"
        | "affiliate";
    };

    if (!decoded || !decoded.id) {
      return res.status(401).json({ message: "Invalid token" });
    }

    // Set the user ID and account type in the request
    req.userId = decoded.id;
    req.accountType = decoded.type;

    // Verify the account exists
    let account;
    if (
      decoded.type === "masterUser" ||
      decoded.type === "agencyOwner" ||
      decoded.type === "masterOwner" ||
      decoded.type === "affiliate"
    ) {
      try {
        const userAcc = await prisma.user.findUnique({
          where: { id: decoded.id },
        });
        const teamAcc = await prisma.teamMember.findUnique({
          where: { id: decoded.id },
        });

        if (
          decoded.type === "masterUser" ||
          decoded.type === "agencyOwner" ||
          decoded.type === "affiliate"
        ) {
          if (userAcc) {
            req.user = userAcc;
            account = userAcc;
          } else if (teamAcc) {
            // Convert null to undefined for properties that might be null from database
            if (teamAcc.refId === null) teamAcc.refId = undefined as any;
            if (teamAcc.agencyName === null)
              teamAcc.agencyName = undefined as any;
            req.teamMember = teamAcc as any;
            account = teamAcc;
          }
        } else {
          // decoded.type === "masterUser"
          if (teamAcc) {
            // Convert null to undefined for properties that might be null from database
            if (teamAcc.refId === null) teamAcc.refId = undefined as any;
            if (teamAcc.agencyName === null)
              teamAcc.agencyName = undefined as any;
            req.teamMember = teamAcc as any;
            account = teamAcc;
          } else if (userAcc) {
            req.user = userAcc;
            account = userAcc;
          }
        }
      } catch (error: any) {
        console.error("Prisma error:", error);
        return res.status(500).json({ message: "Internal server error" });
      }
    } else {
      try {
        const agentAcc = await prisma.agencyAgent.findUnique({
          where: { id: decoded.id },
        });

        if (agentAcc) {
          // First, fetch the user data
          let userData = null;
          if (agentAcc.agencyId) {
            try {
              userData = await prisma.user.findUnique({
                where: { id: agentAcc.agencyId },
              });
            } catch (error) {
              console.error("Error fetching user data:", error);
            }
          }

          // Create the agencyAgent object
          const agencyAgentData: any = {
            id: agentAcc.id,
            agencyId: agentAcc.agencyId,
            role: agentAcc.role,
            roleType: agentAcc.roleType,
            createdAt: agentAcc.createdAt,
            updatedAt: agentAcc.updatedAt,
            agency: {
              id: agentAcc.agencyId,
              refId: agentAcc.refId || undefined,
              agencyName: agentAcc.agencyName || undefined,
            },
          };

          // Add status if it exists
          if (agentAcc.status) {
            agencyAgentData.status = agentAcc.status;
          }

          // Add user data if available
          if (userData) {
            agencyAgentData.user = userData;
            req.user = userData; // Also set the user in the request
          }

          req.agencyAgent = agencyAgentData;

          // Set account for backward compatibility
          account = agentAcc;
        }
      } catch (error: any) {
        console.error("Prisma error:", error);
        return res.status(500).json({ message: "Internal server error" });
      }
    }

    // If account was not found in prisma.user, try prisma.teamMember
    if (!account) {
      try {
        account = await prisma.teamMember.findUnique({
          where: { id: decoded.id },
        });
        if (account) {
          // Convert null to undefined for properties that might be null from database
          if (account.refId === null) account.refId = undefined as any;
          if (account.agencyName === null)
            account.agencyName = undefined as any;
          req.teamMember = account as any;
        }
      } catch (error: any) {
        console.error("Prisma error:", error);
        return res.status(500).json({ message: "Internal server error" });
      }
    }

    // If still no account, unauthorized
    if (!account) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Handle deactivated accounts for users (similar to login flow)
    if (
      account &&
      typeof account === "object" &&
      account !== null &&
      "accountStatus" in account &&
      account.accountStatus === AccountStatus.deactivated &&
      "deactivationDate" in account &&
      account.deactivationDate
    ) {
      // Ensure deactivationDate is a valid date
      let deactivationDate: Date;
      try {
        // Try to create a date from the deactivationDate
        if (account.deactivationDate instanceof Date) {
          deactivationDate = account.deactivationDate;
        } else if (
          typeof account.deactivationDate === "string" ||
          typeof account.deactivationDate === "number"
        ) {
          deactivationDate = new Date(account.deactivationDate);
        } else {
          console.error(
            "Invalid deactivation date format:",
            account.deactivationDate
          );
          return res.status(400).json({ message: "Invalid account status" });
        }

        if (isNaN(deactivationDate.getTime())) {
          throw new Error("Invalid date");
        }
        // Calculate the number of days since deactivation
        const daysDeactivated = calculateDeactivationDays(deactivationDate);

        // Within reactivation period - allow self-service reactivation
        if (daysDeactivated <= REACTIVATION_PERIOD) {
          const updatedUser = await prisma.user.update({
            where: { id: account.id },
            data: {
              accountStatus: AccountStatus.accepted,
              deactivationDate: null,
            },
          });

          // Update the user with reactivated status
          req.user = updatedUser;
          // Ensure account type is set to 'user' for reactivated accounts
          req.accountType = "affiliate";

          // Create a new token with the correct account type
          const secretKey = process.env.SECRET_KEY;
          if (secretKey) {
            const newToken = jwt.sign(
              { id: updatedUser.id, type: "affiliate" },
              secretKey,
              { expiresIn: "1h" }
            );
            res.cookie("token", newToken, {
              httpOnly: true,
              secure: true,
              sameSite: "none",
              maxAge: 1000 * 60 * 60, // 1 hour
            });
          }

          handleAgencyAcceptation(updatedUser.id);
        } else if (daysDeactivated <= DELETION_PERIOD) {
          // Between reactivation and deletion period - require admin assistance
          const errorMessage = `Your account has been deactivated for ${daysDeactivated} ${isTestMode ? "minutes" : "days"}. Contact Airvilla Support to restore access.`;
          return res.status(401).json({
            success: false,
            message: errorMessage,
            forceLogout: true,
          });
        } else {
          // Past deletion period - account should be deleted
          const errorMessage =
            "Your account has been permanently deleted due to prolonged inactivity.";
          return res.status(401).json({
            success: false,
            message: errorMessage,
            forceLogout: true,
          });
        }
      } catch (error) {
        console.error("Error processing deactivation date:", error);
        return res.status(400).json({ message: "Invalid account status" });
      }
    }

    // Check suspension/status after handling deactivation
    const accountStatus =
      (account as any).accountStatus || (account as any).status;

    if (
      accountStatus === "suspended" ||
      accountStatus === "inactive" ||
      accountStatus === "rejected"
    ) {
      res.clearCookie("token");
      return res.status(401).json({
        success: false,
        message: `Your access has been ${accountStatus}. Please contact Airvilla Support for assistance${accountStatus === "rejected" ? "." : " in restoring your access."}`,
        forceLogout: true,
      });
    }

    return next();
  } catch (error) {
    console.error("JWT verification error:", error);
    return res.status(401).json({ message: "Unauthorized" });
  }
};

export default userAuth;
