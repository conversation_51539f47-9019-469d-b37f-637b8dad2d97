import { logoutUser } from "@/redux/features/AuthSlice";
// import { makeStore } from "@/redux/store";
import { store } from "@/redux/store";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { useRouter } from "next/navigation";

export const handleApiError = (error: any) => {
  // const store = makeStore();

  console.error("API Error:", error);
  // Check if it's a rate limit error
  if (error?.response?.status === 429) {
    const rateLimitError = error.response.data?.error;
    const retryAfter = rateLimitError?.details?.retryAfter || 0;
    const resetTime = new Date(rateLimitError?.details?.rateLimitReset);

    const message =
      retryAfter > 60
        ? `Rate limit exceeded. Please try again in ${Math.ceil(
            retryAfter / 60
          )} minutes.`
        : `Rate limit exceeded. Please try again in ${retryAfter} seconds.`;

    store.dispatch(
      setMsg({
        message,
        success: false,
      })
    );
    return;
  }

  // Check if the error response indicates a force logout
  if (error?.response?.data?.forceLogout) {
    // Dispatch logout action
    store.dispatch(logoutUser());

    // Show message to user
    store.dispatch(
      setMsg({
        message:
          error.response.data.message ||
          "Your session has expired. Please log in again.",
        success: false,
      })
    );

    // Redirect to login page
    window.location.href = "/login";
    return;
  }

  // Handle other API errors
  console.error("API Error:", error);
  store.dispatch(
    setMsg({
      message:
        error?.response?.data?.message ||
        "An error occurred. Please try again.",
      success: false,
    })
  );
};
