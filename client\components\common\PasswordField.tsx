import { PasswordFieldProps } from "@/utils/definitions/agentsDefinitions";
import { EyeIcon, EyeOffIcon, Lock } from "lucide-react";
import ToolTip from "./ToolTip";

const PasswordField = ({
  label,
  name,
  value,
  placeholder,
  onChange,
  showPassword,
  onToggleVisibility,
  strength,
  formErrors,
  required,
  tooltip,
  labelClassName,
  inputClassName,
  strengthClassName,
}: PasswordFieldProps) => {
  return (
    <div>
      <label className={labelClassName}>
        <span>{label}</span>
        {required && <span className="text-red-500">*</span>}
        {tooltip && <ToolTip children={tooltip} size="lg" />}
      </label>
      <div className="relative">
        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
          <Lock size={18} />
        </span>
        <input
          type={showPassword ? "text" : "password"}
          name={name}
          value={value}
          placeholder={placeholder}
          onChange={onChange}
          className={inputClassName}
          required={required}
        />
        <button
          type="button"
          onClick={onToggleVisibility}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        >
          {showPassword ? <EyeOffIcon size={18} /> : <EyeIcon size={18} />}
        </button>
      </div>
      {strength && strength!.score !== 0 && (
        <div className="mt-2">
          <div className="flex justify-between items-center mb-1">
            <span
              className="text-sm font-medium"
              style={{ color: strength!.color }}
            >
              {strength!.label}
            </span>
          </div>
          <div className="flex gap-1">
            {Array.from({ length: 5 }).map((_, index) => {
              // Only fill blocks if password has content and score is sufficient
              const isFilled = value && index < strength!.score;
              return (
                <div
                  key={index}
                  className={`h-2 rounded-sm flex-1 transition-colors duration-300 ${
                    !isFilled ? strengthClassName : ""
                  }`}
                  style={{
                    backgroundColor: isFilled ? strength!.color : undefined,
                    transition: "all 0.3s ease",
                  }}
                />
              );
            })}
          </div>
          <div className="mt-1 text-sm" style={{ color: strength!.color }}>
            {strength!.message}
          </div>
        </div>
      )}
      {formErrors && (
        <p className="text-red-500 text-sm mt-1 flex items-center">
          {formErrors}
        </p>
      )}
    </div>
  );
};

export default PasswordField;
