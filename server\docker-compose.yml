version: "3.8"
services:
  db:
    container_name: postgres-db
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    ports:
      - ${DB_PORT}:5432
    volumes:
      - db-data:/var/lib/postgresql/data

  db-2:
    container_name: postgres-test-db
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: ${TEST_DB_USER}
      POSTGRES_PASSWORD: ${TEST_DB_PASSWORD}
      POSTGRES_DB: ${TEST_DB_NAME}
    ports:
      - ${TEST_DB_PORT}:5432
    volumes:
      - test-db-data:/var/lib/postgresql/data

volumes:
  db-data:
  test-db-data:
