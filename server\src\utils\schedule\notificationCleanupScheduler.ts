/**
 * "When and how often to clean"
 * @module notificationCleanupScheduler
 * Scheduled job to clean up old notifications.
 * - Runs every Saturday at 4 AM (cron: 0 4 * * 6)
 * - Deletes read notifications older than 7 days (configurable)
 * - Implements in-memory locking to prevent concurrent runs
 * - Includes error handling and logging
 */

import cron from "node-cron";
import logger from "../logger";
import { cleanupOldNotifications, type CleanupOptions } from "../cleanup/notificationCleanup";

// Environment configuration
const isTestingEnv = process.env.CLEANUP_MODE === 'testing' || process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'test';
const CLEANUP_MODE: 'testing' | 'production' = isTestingEnv ? 'testing' : 'production';
const IS_TEST = CLEANUP_MODE === 'testing';
const JOB_NAME = "notificationCleanupScheduler";
// Run more frequently in test/dev environments
const CRON_SCHEDULE = IS_TEST ? "*/5 * * * *" : "0 4 * * 6"; // Every Saturday at 4 AM in production
const BATCH_SIZE = IS_TEST ? 100 : 2000; // Smaller batch size for testing
const TIMEZONE = 'America/New_York';

// Enable debug logging for test/dev
if (IS_TEST) {
  logger.level = 'debug';
  logger.info(`[${JOB_NAME}] Running in ${CLEANUP_MODE} mode with schedule: ${CRON_SCHEDULE}`);
}

// In-memory lock to prevent concurrent job runs
let isLocked = false;

/**
 * Acquires a lock for the job.
 * @returns {boolean} True if lock acquired, false otherwise
 */
function acquireLock(): boolean {
  if (isLocked) {
    logger.warn(`[${JOB_NAME}] Job is already running, skipping this run.`);
    return false;
  }
  isLocked = true;
  return true;
}

/**
 * Releases the job lock.
 */
function releaseLock(): void {
  isLocked = false;
  logger.debug(`[${JOB_NAME}] Lock released.`);
}

/**
 * Performs the notification cleanup task.
 * @param options Configuration options for the cleanup
 */
async function runNotificationCleanup(options: Omit<CleanupOptions, 'batchSize'> = {}): Promise<void> {
  if (!acquireLock()) {
    return;
  }

  const jobOptions: CleanupOptions = {
    mode: CLEANUP_MODE,
    dryRun: false, // Always perform actual cleanup
    batchSize: BATCH_SIZE,
    ...options
  };
  
  logger.debug(`[${JOB_NAME}] Cleanup options:`, jobOptions);

  logger.info(`[${JOB_NAME}] Starting notification cleanup job`, { options: jobOptions });

  try {
    const result = await cleanupOldNotifications(jobOptions);
    
    if (result.success) {
      logger.info(
        `[${JOB_NAME}] Successfully processed ${result.deletedCount} old notifications`,
        { mode: result.mode, dryRun: result.dryRun }
      );
    } else {
      logger.error(
        `[${JOB_NAME}] Failed to clean up notifications: ${result.error}`,
        { error: result.error }
      );
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(
      `[${JOB_NAME}] Unhandled error during notification cleanup: ${errorMessage}`,
      { error, stack: error instanceof Error ? error.stack : undefined }
    );
  } finally {
    releaseLock();
  }
}

// Schedule the job based on environment
const scheduleCleanup = () => {
  const job = cron.schedule(
    CRON_SCHEDULE,
    () =>
      runNotificationCleanup({
        mode: CLEANUP_MODE,
        dryRun: false,
      }),
    { timezone: TIMEZONE }
  );

  // Log scheduling info
  logger.info(`[${JOB_NAME}] Scheduled with cron: ${CRON_SCHEDULE}`, { 
    environment: CLEANUP_MODE,
    schedule: CRON_SCHEDULE,
    isTest: IS_TEST,
    batchSize: BATCH_SIZE
  });

  return job;
};

// Initialize the scheduler
const job = scheduleCleanup();

// For manual triggering during testing
if (IS_TEST) {
  // Run immediately in test mode with actual cleanup
  runNotificationCleanup({ 
    mode: 'testing',
    dryRun: false // Perform actual cleanup in test mode
  });
}

// Export for testing purposes
export {
  runNotificationCleanup,
  acquireLock,
  releaseLock,
  JOB_NAME,
  CRON_SCHEDULE,
  BATCH_SIZE,
};