# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# package-lock.json

# dependencies
node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
package-lock.json



# testing
/coverage

# next.js
.next
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
server/app.log
server/logs/combined.log
server/logs/error.log
memory-usage.log
heap-snapshots/

# local env files
.env*.local
.env
.env.prod
.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# docker 
docker-data

# prisma 
migrations

# others
*.http
notes.txt

# vscode
.vscode



# build folder 
dist

temp_root.crt
server/yarn.lock
server/app.log
server/logs/*.log
