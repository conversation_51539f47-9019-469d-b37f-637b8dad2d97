"use client";
import { useEffect, useRef, useState } from "react";
import { Check, ChevronDown } from "lucide-react";

interface SupportDropdownProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: string[];
  required?: boolean;
  description?: string;
  error?: string;
}

const SupportDropdown = ({
  id,
  label,
  value,
  onChange,
  options,
  required = false,
  description,
  error,
}: SupportDropdownProps) => {
  const [menuActive, setMenuActive] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      setMenuActive(false);
      setSearchValue("");
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Handle option selection
  const handleSelectOption = (option: string) => {
    onChange(option);
    setMenuActive(false);
    setSearchValue("");
  };

  // Filter options based on search input
  const filteredOptions =
    searchValue.trim() === ""
      ? options
      : options.filter((option) =>
          option.toLowerCase().includes(searchValue.toLowerCase())
        );

  return (
    <div ref={menuRef}>
      <div className="relative inline-flex w-full">
        <div
          onClick={() => {
            setMenuActive(!menuActive);
            setSearchValue("");
          }}
          className={`btn py-0 pl-0 w-full justify-between min-w-[11rem] h-[45px] bg-white dark:bg-gray-600 hover:border hover:border-red-500 hover:ring-1 hover:ring-red-500 text-gray-500 hover:text-gray-600 dark:text-white dark:hover:text-gray-200 rounded-md ${
            menuActive ? "border border-red-500 ring-1 ring-red-500" : ""
          }`}
        >
          <span className="flex-1 items-center">
            <input
              id={id}
              className="absolute left-0 top-0 bg-transparent border-hidden focus:ring-0 focus:ring-offset-0 w-full dark:placeholder:text-gray-300 placeholder:text-gray-700 placeholder:text-sm rounded-md px-3 py-2"
              value={searchValue}
              onChange={(e) => {
                setSearchValue(e.target.value);
                setMenuActive(true);
              }}
              placeholder={value || `Select ${label}`}
            />
          </span>
          <ChevronDown
            className="text-gray-500 dark:text-gray-400 ml-3 mr-2"
            size={20}
          />
        </div>
        {menuActive && (
          <div className="z-20 absolute top-full left-0 w-full bg-white dark:bg-gray-800 border border-gray-500 dark:border-gray-700 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1">
            <div
              className="font-medium text-sm text-gray-600 dark:text-gray-300 
             divide-y divide-gray-200 dark:divide-gray-700 focus:outline-none max-h-40 overflow-auto custom-scrollbar"
            >
              {filteredOptions.length === 0 && (
                <div className="text-center py-3">
                  <span>No Results</span>
                </div>
              )}
              {filteredOptions.length > 0 &&
                filteredOptions.map((option) => {
                  const isSelected = option === value;

                  return (
                    <button
                      key={option}
                      type="button"
                      className={`flex items-center justify-between text-sm md:text-base w-full py-2 px-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 ${
                        isSelected ? "text-red-500" : ""
                      }`}
                      onClick={() => handleSelectOption(option)}
                    >
                      <div className="text-start text-base">
                        <div className="font-bold">{option}</div>
                      </div>
                      <Check
                        className={`shrink-0 mr-2 text-red-500 ${
                          !isSelected && "invisible"
                        }`}
                        size={20}
                      />
                    </button>
                  );
                })}
            </div>
          </div>
        )}
      </div>
      {description && value && (
        <p className="mt-2 text-sm text-gray-400">{description}</p>
      )}
      {error && <div className="text-sm mt-1 text-red-500">{error}</div>}
    </div>
  );
};

export default SupportDropdown;
