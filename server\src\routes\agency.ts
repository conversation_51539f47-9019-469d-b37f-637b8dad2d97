import { Router } from "express";
import {
  createAgent,
  updateAgent,
  removeAgent,
  fetchAllAgents,
  searchAgents,
} from "../controllers/agencyController";
import userAuth from "../middlewares/userAuth";
import { requireAgencyAdmin } from "../middlewares/requireAgencyAdmin";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

const router = Router();

// Apply authentication to all routes
router.use(userAuth);
// Apply agency admin requirement to all agent management routes
router.use('/agent', requireAgencyAdmin);
router.use('/agents', requireAgencyAdmin);
// router.use(enterpriseApiLimiter);

/**
 * @openapi
 * /agency/agent:
 *   post:
 *     tags:
 *       - Agency
 *     summary: Create an agent
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Agent created
 *   delete:
 *     tags:
 *       - Agency
 *     summary: Remove an agent
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agent removed
 *   put:
 *     tags:
 *       - Agency
 *     summary: Update an agent
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Agent updated
 *
 * /agency/agents:
 *   get:
 *     tags:
 *       - Agency
 *     summary: Get all agents for the agency
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of agents
 *
 * /agency/agents/search:
 *   get:
 *     tags:
 *       - Agency
 *     summary: Search agents
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agent search results
 */
router.post("/agent", createAgent);
router.put("/agent/:id", updateAgent);
router.get("/agents", fetchAllAgents); // For agency users to fetch their own agents
router.get("/agents/search", searchAgents);
router.delete("/agent/:id", removeAgent);

export default router;
