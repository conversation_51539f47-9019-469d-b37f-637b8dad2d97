import teamMemberUrl from "../endpoints/teamEndpoints";
import axios, { AxiosRequestConfig } from "axios";

const fetchData = async (url: string, options: AxiosRequestConfig = {}) => {
  try {
    // Merge default options with provided options
    const axiosOptions: AxiosRequestConfig = {
      url,
      withCredentials: true,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    const response = await axios.request(axiosOptions);

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw error.response.data;
    } else {
      throw new Error("Network error occurred.");
    }
  }
};

interface TeamMemberData {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role?: string;
  subRole?: string;
  department?: string;
  status?: string;
  [key: string]: any;
}

// Get team ID
const getTeamId = async () => {
  const url = teamMemberUrl.getTeamId;
  return await fetchData(url);
};

// Get InvitedById
const getInvitedById = async () => {
  const url = teamMemberUrl.getInvitedById;
  return await fetchData(url);
};

// Create a new team member
const createTeamMember = async (data: TeamMemberData) => {
  const url = teamMemberUrl.createTeamMember;
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: data,
    withCredentials: true,
  };
  return await fetchData(url, options);
};

// Update a team member
const updateTeamMember = async (memberId: string, data: TeamMemberData) => {
  const url = teamMemberUrl.updateTeamMember(memberId);
  const options = {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: data,
    withCredentials: true,
  };
  return await fetchData(url, options);
};

// Get all team members
const fetchAllTeamMembers = async (cursor?: string) => {
  const url = cursor
    ? `${teamMemberUrl.getAllTeamMembers}?cursor=${cursor}`
    : teamMemberUrl.getAllTeamMembers;
  return await fetchData(url);
};

const fetchAllSearchTeamMembers = async (
  input: string,
  // accountType: string,
  subRole: string,
  cursor?: string,
  pageSize: number = 10
) => {
  // try {
  const response = await axios.get(teamMemberUrl.getAllSearchTeamMembers, {
    headers: {
      "Content-Type": "application/json",
    },
    params: {
      input,
      // accountType,
      subRole,
      cursor, // Pass the cursor parameter
      pageSize, // Pass the pageSize parameter
    },
    withCredentials: true, // Ensure credentials are included
  });

  const data = response.data;

  if (!data.success) {
    throw new Error(data.message);
  }

  return data;
};

// Get a single team member by ID
const getSingleTeamMember = async (memberId: string) => {
  const url = teamMemberUrl.getSingleTeamMember(memberId);
  return await fetchData(url);
};

// Update a team member's role
const updateTeamMemberRole = async (memberId: string, role: string) => {
  const url = teamMemberUrl.updateTeamMemberRole(memberId, role);
  const options = {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    data: { role },
    withCredentials: true,
  };
  return await fetchData(url, options);
};

// Remove a team member
const removeTeamMember = async (memberId: string) => {
  const url = teamMemberUrl.removeTeamMember(memberId);
  const options = {
    method: "DELETE",
  };
  return await fetchData(url, options);
};

// Resend an invitation to a team member
const resendInvitation = async (
  email: string,
  teamId: string,
  memberId: string
) => {
  const url = teamMemberUrl.resendInvitation(email, teamId, memberId);
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ email, teamId, memberId }),
  };
  return await fetchData(url, options);
};

// Create a new team
const createTeam = async () => {
  const url = teamMemberUrl.createTeam;
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  };
  return await fetchData(url, options);
};

export {
  getTeamId,
  createTeamMember,
  updateTeamMember,
  fetchAllTeamMembers,
  fetchAllSearchTeamMembers,
  getSingleTeamMember,
  updateTeamMemberRole,
  removeTeamMember,
  resendInvitation,
  createTeam,
};
