// server base url
import { API_VERSION } from '../constants/apiVersion';
const SERVER_URL = process.env.SERVER_URL;
// BASE URL
const BASE_URL = SERVER_URL + API_VERSION + "/master";

// MASTER USER ENDPOINT
const masterUrl = {
  // users
  getAllUsersForMaster: BASE_URL + "/users",
  getAllSearchUsersForMaster: BASE_URL + "/users/search",
  getSingleUserForMaster: (userId: string) => BASE_URL + `/users/${userId}`,
  getSingleUserRequestForMaster: (userId: string) =>
    BASE_URL + `/users/${userId}/request`,
  getSingleUserPasswordForMaster: (userId: string) =>
    BASE_URL + `/users/${userId}/password`,
  softDeleteUser: (userId: string) => BASE_URL + `/users/${userId}/soft-delete`,
  hardDeleteUser: (userId: string) => BASE_URL + `/users/${userId}/hard-delete`,

  // tickets
  getAllTicketsForMaster: BASE_URL + "/tickets",
  getSingleTicketForMaster: (refId: string) => BASE_URL + `/tickets/${refId}`,
  updateTicketStatusForMaster: (refId: string) =>
    BASE_URL + `/tickets/${refId}/status`,
  getAgencyNamesForMaster: BASE_URL + "/agencyNames",
  getUpdateValidTicketForMaster: (refId: string) =>
    BASE_URL + `/tickets/${refId}/valid`,
  rescheduleTicketForMaster: (refId: string) =>
    BASE_URL + `/tickets/${refId}/reschedule`,

  // dashboard
  getTotalTicketsForMaster: BASE_URL + "/tickets/total",
  getTotalUsersForMaster: BASE_URL + "/users/total",

  // agents
  fetchAllAgentsForMaster: (userId: string) => BASE_URL + `/agents/${userId}`, // For master users
};

export default masterUrl;
