"use client";
import { useEffect, useRef, useState } from "react";
import { Check, ChevronDown } from "lucide-react";
import { formatDepartmentName } from "@/utils/functions/functions";

interface DepartmentDropdownProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  disabled?: boolean;
  options: string[];
  required?: boolean;
  formErrors?: string | undefined;
}

const DepartmentDropdown = ({
  name,
  value,
  onChange,
  disabled,
  options,
  required,
  formErrors,
}: DepartmentDropdownProps) => {
  const [menuActive, setMenuActive] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  const handleClickOutside = (event: MouseEvent) => {
    if (
      menuActive &&
      menuRef.current &&
      !menuRef.current.contains(event.target as Node)
    ) {
      setMenuActive(false);
      setSearchValue("");
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [menuActive]);

  // Handle option selection
  const handleSelectOption = (option: string) => {
    const event = {
      target: { name, value: option },
    } as React.ChangeEvent<HTMLSelectElement>;

    onChange(event);
    setMenuActive(false);
    setSearchValue("");
  };

  // Filter options based on search input
  const filteredOptions =
    searchValue.trim() === ""
      ? options
      : options.filter((option) =>
          formatDepartmentName(option)
            .toLowerCase()
            .includes(searchValue.toLowerCase())
        );

  return (
    <div ref={menuRef}>
      <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
        Department {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative inline-flex w-full">
        <div
          onClick={() => {
            if (!disabled) {
              setMenuActive(!menuActive);
              setSearchValue("");
            }
          }}
          className={`btn py-0 pl-0 w-full justify-between min-w-[11rem] h-[45px] bg-gray-300 dark:bg-gray-800 hover:border hover:border-red-500 hover:ring-1 hover:ring-red-500 text-gray-500 hover:text-gray-600 dark:text-white dark:hover:text-gray-200 rounded-lg capitalize ${
            menuActive ? "border border-red-500 ring-1 ring-red-500" : ""
          } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
        >
          <span className="flex-1 items-center">
            <input
              className="absolute left-0 top-0 bg-transparent border-hidden focus:ring-0 focus:ring-offset-0 w-full dark:placeholder:text-gray-300 placeholder:text-gray-700 placeholder:text-sm"
              value={searchValue}
              onChange={(e) => {
                if (!disabled) {
                  setSearchValue(e.target.value);
                  setMenuActive(true);
                }
              }}
              placeholder={
                value ? formatDepartmentName(value) : "Select Department"
              }
              disabled={disabled}
            />
          </span>
          <ChevronDown
            className="text-gray-500 dark:text-gray-400 ml-3"
            size={20}
          />
        </div>
        {menuActive && (
          <div className="z-20 absolute top-full left-0 w-full bg-white dark:bg-gray-800 border border-gray-500 dark:border-gray-700 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1">
            <div className="font-medium text-sm text-gray-600 dark:text-gray-300 divide-y divide-gray-200 dark:divide-gray-700 focus:outline-none max-h-40 overflow-auto custom-scrollbar">
              {filteredOptions.length === 0 && (
                <div className="text-center py-3">
                  <span>No Results</span>
                </div>
              )}
              {filteredOptions.length > 0 &&
                filteredOptions.map((option) => {
                  const isSelected = option === value;

                  return (
                    <button
                      key={option}
                      type="button"
                      className={`flex items-center justify-between text-sm md:text-base w-full py-2 px-3 cursor-pointer capitalize ${
                        isSelected ? "text-red-500" : ""
                      }`}
                      onClick={() => handleSelectOption(option)}
                    >
                      <div className="text-start text-base">
                        <div className="font-bold">
                          {formatDepartmentName(option)}
                        </div>
                      </div>
                      <Check
                        className={`shrink-0 mr-2 text-red-500 ${
                          !isSelected && "invisible"
                        }`}
                        size={20}
                      />
                    </button>
                  );
                })}
            </div>
          </div>
        )}
      </div>
      {formErrors && (
        <p className="text-red-400 text-sm mt-1 flex items-center">
          {formErrors}
        </p>
      )}
    </div>
  );
};

export default DepartmentDropdown;
