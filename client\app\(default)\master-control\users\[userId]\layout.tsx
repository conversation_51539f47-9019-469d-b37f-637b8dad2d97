"use client";
import AccountHubSidebar from "@/components/account-hub/AccountHubSidebar";
import ContactSupportAccountHub from "@/components/account-hub/ContactSupportAccountHub";
import MasterUsersSettingsSidebar from "@/components/master-control/users/singleUser/MasterUsersSettingsSidebar";
import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import React from "react";

export default function MasterUserSingleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { userId: string };
}) {
  const { userId } = params;
  const user = useAppSelector(selectUser);

  return (
    <div className="dark:bg-gray-900 dark:text-white min-h-screen p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center mb-8">
          <h1 className="text-3xl font-bold">
            <span className="capitalize">{(user as StoredUser).role}</span> Hub
          </h1>
        </div>
        <div className="flex flex-col md:flex-row dark:bg-gray-800 rounded-2xl shadow-2xl">
          <MasterUsersSettingsSidebar userId={userId} />
          <div className="flex-grow p-3 md:p-8 bg-gray-200 dark:bg-gray-800 min-h-dvh">
            {children}
            <ContactSupportAccountHub />
          </div>
        </div>
      </div>
    </div>
  );
}
