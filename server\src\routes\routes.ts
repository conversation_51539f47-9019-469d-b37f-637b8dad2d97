import { Router } from "express";
import user from "./user";
import ticket from "./ticket";
import auth from "./auth";
import master from "./master";
import dev from "./dev";
import team from "./team";
import agency from "./agency";
import invitation from "./invitation";
import notification from "./notification";
import traveler from "./traveler";
import { submitFeedback } from "../controllers/feedbackController";
import userAuth from "../middlewares/userAuth";
import multer from "multer";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";
import bookingRoutes from "./booking.routes";
import bookingSessionRoutes from "./bookingSession.routes";
import checkRoutes from "./check.routes";
import manifestRoutes from "./manifest.routes";
// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit per file
    files: 5 // Max 5 files
  }
});

const router = Router();

// Apply enterpriseApiLimiter globally to all API routes (except /health, handled in middleware)
router.use(enterpriseApiLimiter);

router.use("/user", user);
router.use("/ticket", ticket);
router.use("/auth", auth);
router.use("/master", master);
router.use("/dev", dev);
router.use("/team", team);
router.use("/agency", agency);
router.use("/invitation", invitation);
router.use("/notification", notification);
router.use("/traveler", traveler);
router.use("/booking", bookingRoutes);
router.use("/booking-session", bookingSessionRoutes);
router.use("/manifest", manifestRoutes);
router.use("/check", checkRoutes);

// NOTE: The enterpriseApiLimiter skips /health by default as per its config.
// If you want to customize rate limiting per route/tier, import and use loginRateLimit or apiLimiter as needed.

// Apply enterpriseApiLimiter to /submit-feedback for extra protection
router.post(
  "/submit-feedback",
  upload.array("files") as any,
  userAuth,
  enterpriseApiLimiter,
  submitFeedback
);

export default router;
