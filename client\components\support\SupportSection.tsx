"use client";
import useAffiliateUserAuth from "../hooks/useAffiliateUserAuth";
import ProgressLoading from "../utils/ProgressLoading";
import axios from "axios";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import React, { useState, useEffect, useRef } from "react";
import { CircleAlert, Send, Upload, X } from "lucide-react";
import SupportDropdown from "./SupportDropdown";
import {
  Category,
  categoryFields,
  categories,
  departmentDescriptions,
} from "@/utils/data/supportFormData";
import Image from "next/image";
import imgBanner from "@/public/images/support-banner.png";
import { useSearchParams } from "next/navigation";
import { feedbackService } from "./feedbackService";

const MAX_FILES = 5;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
const ALLOWED_FILE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + " B";
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
  return (bytes / (1024 * 1024)).toFixed(1) + " MB";
};

const SupportForm = (): JSX.Element => {
  // State for loading state
  const [loading, setLoading] = useState(false);

  // Dispatch function for the Redux store
  const dispatch = useAppDispatch();

  // Get URL parameters
  const searchParams = useSearchParams();
  const categoryParam = searchParams.get("category");

  // UI states
  const [subject, setSubject] = useState("");
  const [description, setDescription] = useState("");
  // Set initial category based on URL parameter
  const [category, setCategory] = useState<Category>(() => {
    // If category=ticket is in the URL, select ticket support
    if (categoryParam === "ticket") {
      return "ticket";
    }
    // Default to general (Customer Support) for any other value or if absent
    return "general";
  });
  const [customFields, setCustomFields] = useState<Record<string, string>>({});
  const [files, setFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // fields validation
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({ subject: "", description: "", category: "", customFields: "" });

  // UI functions
  useEffect(() => {
    setCustomFields({});
  }, [category]);

  const handleCustomFieldChange = (name: string, value: string) => {
    setCustomFields((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = e.target.files ? Array.from(e.target.files) : [];
    // Check if adding new files would exceed the limit
    if (files.length + newFiles.length > MAX_FILES) {
      dispatch(
        setMsg({
          success: false,
          message: `Maximum ${MAX_FILES} files allowed`,
        })
      );
      return;
    }
    // Validate each file
    const validFiles = newFiles.filter((file) => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        dispatch(
          setMsg({
            success: false,
            message: `${file.name} exceeds 5MB limit`,
          })
        );
        return false;
      }

      // Check file type
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        dispatch(
          setMsg({
            success: false,
            message: `${file.name} has unsupported file type`,
          })
        );
        return false;
      }

      return true;
    });

    setFiles((prevFiles) => [...prevFiles, ...validFiles]);
  };

  const removeFile = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!subject.trim() || !description.trim()) {
      setError("Please fill in all required fields");
      return;
    }

    try {
      setLoading(true);
      setError("");

      // Use the feedback service to submit the form
      const result = await feedbackService.submitFeedback({
        subject,
        description,
        category,
        customFields,
        files,
      });

      // Handle success
      if (result.success) {
        setSubject("");
        setDescription("");
        setCategory("general");
        setCustomFields({});
        setFiles([]);

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }

        // Show success message
        setSuccess("Your feedback has been submitted successfully!");
        dispatch(
          setMsg({ success: true, message: "Feedback submitted successfully!" })
        );

        // Hide success message after 5 seconds
        setTimeout(() => setSuccess(""), 5000);
      }
    } catch (error) {
      console.error("Error submitting feedback:", error);
      dispatch(
        setMsg({
          success: false,
          message: "Failed to submit feedback. Please try again later.",
        })
      );
      setError("Failed to submit feedback. Please try again later.");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="flex flex-col items-center justify-center max-w-7xl mx-4 xl:mx-auto my-8">
      <div className=" w-full min-w-fit max-w-7xl">
        <h1 className="text-3xl font-bold mb-6 dark:text-white">
          Help & Support
        </h1>
        <div className="mb-6">
          <Image
            src={imgBanner}
            alt="img"
            width={800}
            height={120}
            className="w-full rounded-lg shadow-lg"
          />
        </div>
        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:gap-8 space-y-4 lg:space-y-0">
          <div className="bg-gray-200 dark:bg-gray-800 rounded-lg shadow-xl p-4 md:p-8 col-span-3 md:col-span-2">
            <form
              onSubmit={handleSubmit}
              className="space-y-6 text-slate-700 dark:text-gray-300"
              encType="multipart/form-data"
            >
              {/* Feedback Category */}
              <div className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 md:p-8">
                <label
                  htmlFor="category"
                  className="block mb-2 tracking-tight text-gray-700 dark:text-white font-bold leading-8 text-xl md:text-2xl pb-6"
                >
                  How can we help you today?
                </label>
                <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                  Please select the most appropriate category for your request
                  to help us route it correctly.
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  {categories.map((cat: any) => (
                    <button
                      key={cat.value}
                      type="button"
                      onClick={() => {
                        setCategory(cat.value);
                        setSubject("");
                        setDescription("");
                        setCustomFields({});
                        setFiles([]);
                        // Reset file input
                        if (fileInputRef.current) {
                          (fileInputRef.current as HTMLInputElement).value = "";
                        }
                      }}
                      className={`flex flex-col items-center justify-center p-4 rounded-md transition-colors duration-300 shadow-lg border dark:border-transparent ${
                        category === cat.value
                          ? "bg-white dark:bg-gray-600 ring-2 ring-red-500"
                          : "bg-white dark:bg-gray-600 hover:bg-gray-100 dark:hover:bg-gray-500/50"
                      }`}
                    >
                      <div
                        className={`h-12 w-12 ${
                          cat.value === "general"
                            ? "bg-blue-500/10"
                            : cat.value === "bug"
                            ? "bg-red-500/10"
                            : "bg-gray-500/10 dark:bg-gray-500/30"
                        } rounded-full flex items-center justify-center`}
                      >
                        <cat.icon size={24} className={cat.color} />
                      </div>
                      <span className="mt-2 text-base text-gray-800 dark:text-white">
                        {cat.label}
                      </span>
                    </button>
                  ))}
                </div>
                {validationErrors.category && (
                  <div className="text-sm mt-1 text-red-500">
                    {validationErrors.category}
                  </div>
                )}
              </div>

              {/* Severity */}
              <section className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 md:p-8 space-y-4">
                <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white">
                  Additional Information
                </h3>
                {category &&
                  categoryFields[category].map((field) => (
                    <div key={field.name}>
                      {/* Label is now handled by the SupportDropdown component */}
                      {/* {field.type !== "select" && ( */}
                      <label
                        htmlFor={field.name}
                        className="block text-sm font-medium leading-6  mb-2 dark:text-white"
                      >
                        {field.label} <span className="text-red-500">*</span>
                      </label>
                      {/* )} */}
                      {field.type === "select" && (
                        <div>
                          <SupportDropdown
                            id={field.name}
                            label={field.label}
                            value={customFields[field.name] || ""}
                            onChange={(value) =>
                              handleCustomFieldChange(field.name, value)
                            }
                            options={field.options || []}
                            required={field.required}
                            description={
                              field.name === "department" &&
                              customFields[field.name]
                                ? departmentDescriptions[
                                    customFields[field.name]
                                  ]
                                : undefined
                            }
                            error={validationErrors.customFields}
                          />
                        </div>
                      )}
                      {field.type === "text" && (
                        <div>
                          <input
                            id={field.name}
                            type="text"
                            value={customFields[field.name] || ""}
                            onChange={(e) =>
                              handleCustomFieldChange(
                                field.name,
                                e.target.value
                              )
                            }
                            className="w-full border-none rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-none bg-white dark:bg-gray-600 placeholder:text-gray-600 dark:placeholder:text-gray-400"
                            placeholder={field.placeholder ?? ""}
                            required={field.required ?? false}
                          />
                        </div>
                      )}
                      {/* Error is now handled by the SupportDropdown component for select fields */}
                      {field.type === "text" &&
                        validationErrors.customFields && (
                          <div className="text-sm mt-1 text-red-500">
                            {validationErrors.customFields}
                          </div>
                        )}
                    </div>
                  ))}
              </section>

              {/* Subject  */}
              <section className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 md:p-8 space-y-4">
                <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white">
                  Feedback Details
                </h3>
                <div>
                  <label
                    htmlFor="subject"
                    className="block text-sm font-medium mb-2 dark:text-white"
                  >
                    Subject <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="subject"
                    type="text"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    className="w-full dark:bg-gray-600 border-none rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-none placeholder:text-gray-600 dark:placeholder:text-gray-400"
                    placeholder="Enter the subject of your feedback"
                    required
                  />
                  {validationErrors.subject && (
                    <div className="text-sm mt-1 text-red-500">
                      {validationErrors.subject}
                    </div>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium mb-2 dark:text-white"
                  >
                    Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full dark:bg-gray-600 border-none rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-none h-32 custom-scrollbar placeholder:text-gray-600 dark:placeholder:text-gray-400"
                    placeholder="Provide details about your feedback"
                    maxLength={2000}
                    required
                  />
                  <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
                    {description.length}/2000 characters
                  </p>
                  {validationErrors.description && (
                    <div className="text-sm mt-1 text-red-500">
                      {validationErrors.description}
                    </div>
                  )}
                </div>
              </section>

              {/* Attachments */}
              <section className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 md:p-8 space-y-4">
                <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white">
                  Attachments
                </h3>
                <div>
                  <label
                    htmlFor="file-upload"
                    className="block text-sm mb-3 text-gray-700 dark:text-gray-300"
                  >
                    Upload screenshots or documents to help us understand your
                    issue better (Max: {MAX_FILES} files,{" "}
                    {formatFileSize(MAX_FILE_SIZE)} each).
                  </label>
                  <div className="flex  flex-col md:flex-row md:items-center space-x-2">
                    <label
                      htmlFor="file-upload"
                      className={`border border-gray-100 dark:border-transparent dark:bg-gray-600 dark:text-white hover:text-red-400 dark:hover:text-red-500 cursor-pointer bg-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600/70 rounded-lg font-medium text-gray-800 px-4 py-2 text-sm flex items-center justify-center transition-colors duration-200 ${
                        files.length >= MAX_FILES
                          ? "opacity-50 cursor-not-allowed"
                          : ""
                      }`}
                    >
                      <Upload size={18} className="mr-2" />
                      Upload Files
                      <input
                        id="file-upload"
                        name="files"
                        type="file"
                        className="sr-only"
                        multiple
                        onChange={handleFileChange}
                        ref={fileInputRef}
                        accept={ALLOWED_FILE_TYPES.join(",")}
                        disabled={files.length >= MAX_FILES}
                      />
                    </label>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {files.length} of {MAX_FILES} file(s) selected
                    </span>
                  </div>
                  {Boolean(files.length) && (
                    <ul className="mt-2 space-y-1 max-h-24 overflow-y-auto custom-scrollbar">
                      {files.map((file, index) => (
                        <li
                          key={index}
                          className="flex items-center justify-between bg-gray-200 dark:bg-gray-600 rounded-md px-4 py-2 text-sm font-semibold text-red-500"
                        >
                          <div className="flex items-center space-x-2 truncate max-w-[calc(100%-2rem)]">
                            <span className="font-semibold text-red-500 truncate">
                              {file.name}
                            </span>
                            <span className="text-gray-500 dark:text-gray-400 text-xs">
                              ({formatFileSize(file.size)})
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-400 ml-2 flex-shrink-0"
                            title="Remove file"
                          >
                            <X size={14} />
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </section>

              {/* Banner */}
              <section className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-4 md:p-8 flex flex-col md:flex-row items-start justify-start gap-3">
                <CircleAlert className="text-green-500 w-5 md:w-10" />
                <p className="text-lg text-gray-700 dark:text-gray-300">
                  Our support team strives to respond within 24 hours. For
                  urgent travel issues, please select the appropriate priority
                  level. Your information is secure and used solely to resolve
                  your request.
                </p>
              </section>

              {/* Submit Button */}
              <section className="flex justify-end">
                <button
                  type="submit"
                  className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300  text-lg font-semibold"
                >
                  {loading ? (
                    <svg
                      className="animate-spin w-4 h-4 fill-current shrink-0"
                      viewBox="0 0 16 16"
                    >
                      <path d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z" />
                    </svg>
                  ) : (
                    <>Submit</>
                  )}
                </button>
              </section>
            </form>
          </div>
          {/* Live Chat */}
          <div className="col-span-1 space-y-6">
            <div className="bg-card text-card-foreground rounded-xl border-0 shadow-lg bg-gray-200 dark:bg-gray-800">
              <div className="p-6 pt-6">
                <div className="rounded-xl p-4 mb-4 bg-gray-100 dark:bg-gray-700">
                  <div className="flex flex-col md:flex-row items-start gap-2 md:space-x-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-message-circle text-blue-500 flex-shrink-0 mt-0.5"
                    >
                      <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                    </svg>
                    <p className="text-sm dark:text-white leading-relaxed font-medium">
                      {/* Need immediate assistance? Our live chat agents are
                      available 24/7 to help with urgent travel concerns and
                      quick questions. */}
                      For urgent travel issues or time-sensitive requests,
                      please select the appropriate priority level when
                      submitting your ticket. To help us assist you quickly and
                      efficiently, make sure to provide accurate and complete
                      information.
                    </p>
                  </div>
                </div>
                {/* <button
                  type="button"
                  className="w-full py-3 px-6 rounded-lg font-semibold transition-colors duration-300 bg-blue-600 text-white hover:bg-blue-700"
                >
                  <span className="flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-message-circle mr-2"
                    >
                      <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                    </svg>
                    Start Live Chat
                  </span>
                </button> */}
                <p className="text-xs text-gray-700 dark:text-gray-300 text-center mt-3">
                  Typical response time:{" "}
                  <span className="text-green-500">Within 24 hours</span>
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default function SupportSection() {
  const loading = useAffiliateUserAuth();

  if (loading) {
    return <ProgressLoading />;
  }
  return <SupportForm />;
}
