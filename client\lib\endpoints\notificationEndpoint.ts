// server base url
import { API_VERSION } from '../constants/apiVersion';
const SERVER_URL = process.env.SERVER_URL;
// BASE URL
const BASE_URL = SERVER_URL + API_VERSION + "/notification";

// NOTIFICATION ENDPOINT
const notificationUrl = {
  createNotification: BASE_URL,
  getUserNotifications: (userId: string) =>
    BASE_URL + `/user/${userId}`,
  getNotification: (id: string) => BASE_URL + `/${id}`,
  markAsRead: (id: string) => BASE_URL + `/${id}/read`,
  markAllAsRead: (userId: string) => BASE_URL + `/user/${userId}/read`,
  deleteNotification: (id: string) => BASE_URL + `/${id}`,
};

export default notificationUrl;
