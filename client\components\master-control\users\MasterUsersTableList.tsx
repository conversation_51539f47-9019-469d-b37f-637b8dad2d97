import React, { useEffect, useState } from "react";
import { getFormatDateTable, getFormatTime } from "@/utils/functions/functions";
import Link from "next/link";
import {
  MasterUserDataType,
  MasterUserResultType,
  AccountStatusEnum,
} from "@/utils/definitions/masterDefinitions";
import { AccountStatus } from "@/components/common/AccountStatus";
import {
  Edit,
  EyeIcon,
  Trash2,
  UserX,
  CheckCircle,
  UserCheck,
  Ban,
} from "lucide-react";
import {
  fetchSingleUserForMaster,
  fetchUserRequestForMaster,
} from "@/lib/data/masterUsersData";
import { FlightTicketRes } from "@/utils/definitions/blockSeatsDefinitions";
import AlertWindow from "@/components/alert/AlertWindow";
import { setLoading } from "@/redux/features/LoadingSlice";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { ViewTeamMemberModal } from "./MasterUsersViewModal";

type StatusColor =
  | "yellow"
  | "red"
  | "green"
  | "blue"
  | "purple"
  | "gray"
  | "orange";

type StatusConfig = {
  [key: string]: StatusColor;
};

// Define status aliases
type StatusAliases = {
  [key: string]: string[];
};

const STATUS_COLORS: StatusConfig = {
  suspended: "orange",
  accepted: "green",
  pending: "blue",
  rejected: "gray",
  deactivated: "red",
} as const;

const STATUS_ALIASES: StatusAliases = {
  accepted: ["active"],
  deactivated: ["deleted", "removed", "inactive"],
};

const DEFAULT_STATUS = AccountStatusEnum.rejected;

const getStatusConfig = (status: string) => {
  const normalizedStatus = status?.toLowerCase();

  // Check direct status
  if (normalizedStatus in STATUS_COLORS) {
    return STATUS_COLORS[normalizedStatus as keyof typeof STATUS_COLORS];
  }

  // Check aliases
  for (const [mainStatus, aliases] of Object.entries(STATUS_ALIASES)) {
    if (aliases.includes(normalizedStatus)) {
      return STATUS_COLORS[mainStatus as keyof typeof STATUS_COLORS];
    }
  }

  // Return default
  return STATUS_COLORS[DEFAULT_STATUS];
};

const getStatusStyle = (status: string) => {
  const color = getStatusConfig(status);
  const baseStyle =
    "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium";
  return `${baseStyle} bg-${color}-100 text-${color}-800`;
};

const getStatusDot = (status: string) => {
  const color = getStatusConfig(status);
  return `bg-${color}-400`;
};

interface UserStatus {
  userId: string;
  status: string;
  value: string;
}

export default function MasterUsersTableList({
  user,
  setUserStatus,
}: {
  user: MasterUserResultType;
  setUserStatus: React.Dispatch<React.SetStateAction<UserStatus>>;
}) {
  // hash value url
  const hash = window.location.hash.replace("#", "");
  // loading
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userInfo, setUserInfo] = useState<MasterUserResultType | {}>({});
  const [editMode, setEditMode] = useState<boolean>(() =>
    hash === "edit" ? true : false
  );
  const [selectedRequest, setSelectedRequest] = useState<string>("");
  const [dangerModalOpen, setDangerModalOpen] = useState<boolean>(false);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<MasterUserResultType | null>(
    null
  );
  const [tickets, setTickets] = useState(0);

  // filter data
  const [filterData, setFilterData] = useState<MasterUserDataType>({
    accountType: "all",
    subscriptionStatus: "all",
    registrationDateFilter: "all time",
    lastLoginFilter: "all time",
  });

  // FETCH
  useEffect(() => {
    const fetchMyTickets = async () => {
      const userTickets = await fetchSingleUserForMaster(user.id);

      const availableTicketsCount = userTickets?.results?.myTickets?.filter(
        (ticket: FlightTicketRes) => ticket.ticketStatus === "available"
      ).length;

      setTickets(availableTicketsCount);
    };
    fetchMyTickets();
  }, [filterData]);

  // hooks
  const dispatch = useAppDispatch();

  // handle user request
  const handleUserRequest = async () => {
    const newStatus =
      selectedRequest === "accept"
        ? "accepted"
        : selectedRequest === "suspend"
        ? "suspended"
        : selectedRequest === "reject"
        ? "rejected"
        : "";

    dispatch(setLoading(true));
    const updateData = await fetchUserRequestForMaster(user.id, {
      accountStatus: newStatus,
    });

    // update user info with client if success
    if (updateData.success) {
      // Update the user info in the parent component
      setUserStatus({
        userId: user.id,
        status: "update",
        value: newStatus,
      });
      // Update local state with the returned data
      setUserInfo(updateData.results);
    }

    // display message
    dispatch(
      setMsg({ success: updateData.success, message: updateData.message })
    );

    // close the alert window
    setDangerModalOpen(false);

    dispatch(setLoading(false));
  };

  const mappingStatus = (status: string) => {
    switch (status) {
      case "suspended":
        return "disabled";
      case "disabled":
        return "soft delete";
      default:
        return status;
    }
  };
  return (
    <>
      <td className="hidden">
        <AlertWindow
          title={`${selectedRequest} User Request`}
          content={`Are you sure you want to ${selectedRequest} this user?`}
          yesBtnText={selectedRequest}
          noBtnText="Cancel"
          handleYesBtnClick={handleUserRequest}
          dangerModalOpen={dangerModalOpen}
          setDangerModalOpen={setDangerModalOpen}
        />
      </td>
      {/* User ID */}
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
        <div className="font-medium text-blue-400">#{user.refId}</div>
      </td>
      {/* Full Name */}
      <td className="table-list-field">
        <div className="group">
          {/* Truncated Name */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {user?.role === 'affiliate' 
              ? `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
              : user?.agencyName || 'N/A'}
          </div>
          <div className="relative">
            {/* Tooltip with Full Name */}
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {user?.role === 'affiliate'
                ? `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
                : user?.agencyName || 'N/A'}
            </div>
          </div>
        </div>
      </td>

      {/* Email */}
      <td className="table-list-field">
        <div className="group">
          {/* Truncated Email */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {user.email?.split("@")[0]}...
          </div>
          {/* Tooltip with Full Email */}
          <div className="relative">
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {user.email}
            </div>
          </div>
        </div>
      </td>

      {/* Account Type */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100 capitalize">
          <AccountStatus status={user.role || "user"} />
        </div>
      </td>

      {/* Subscription Type */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100 capitalize">
          <AccountStatus status={"Standard"} />
        </div>
      </td>

      {/* Status */}
      <td className="table-list-field">
        <span className={`capitalize ${getStatusStyle(user.accountStatus)}`}>
          <span
            className={`inline-block w-1.5 h-1.5 rounded-full mr-1.5 ${getStatusDot(
              user.accountStatus 
            )}`}
          ></span>
          {mappingStatus(user.accountStatus)}
        </span>
      </td>

      {/* Subscription Status */}
      <td className="table-list-field">
        <span
          className={`capitalize ${getStatusStyle(user.subscriptionStatus)}`}
        >
          <span
            className={`inline-block w-1.5 h-1.5 rounded-full mr-1.5 ${getStatusDot(
              user.subscriptionStatus
            )}`}
          ></span>
          {user.subscriptionStatus}
        </span>
      </td>

      {/* Last Login */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {user.lastLogin && user.accountStatus === "accepted"
            ? `${getFormatDateTable(user.lastLogin)} | ${getFormatTime(
                user.lastLogin
              )}`
            : "Never signed in"}
        </div>
      </td>

      {/* Current Wallet Balance */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {user.creditBalance
            ? `${parseFloat(user.creditBalance).toFixed(2)}`
            : "0.00"}
        </div>
      </td>

      {/* Active Tickets */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {tickets ?? "N/A"}
        </div>
      </td>

      {/* Actions */}
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setSelectedUser(user);
              setModalOpen(true);
            }}
            className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300"
          >
            <Edit size={18} />
          </button>
        </div>
      </td>

      {/* View/Edit User Modal */}
      <td>
        <ViewTeamMemberModal
          isOpen={modalOpen}
          setIsOpen={setModalOpen}
          user={selectedUser}
          onSuccess={() => {
            // Update the user info in the parent component
            if (selectedUser) {
              // For delete action
              if (selectedUser.accountStatus === "disabled") {
                setUserStatus({
                  userId: selectedUser.id,
                  status: "delete",
                  value: selectedUser.accountStatus,
                });
              }
              // For update or suspend/activate actions
              else {
                setUserStatus({
                  userId: selectedUser.id,
                  status: "update",
                  value: selectedUser.accountStatus,
                });
              }
            }
            setModalOpen(false);
          }}
        />
      </td>
    </>
  );
}
