import { Response } from "express";
import { prisma } from "../prisma";
import {
  Role,
  Department,
  RoleType,
  TeamMemberStatus,
  TeamMemberRole as SubRole,
} from "@prisma/client";
import { generateRoleType } from "../models/team.model";
import bcrypt from "bcrypt";
import { sendInvitationEmail } from "../utils/email/sendInvitation";
import {
  generateInvitationToken,
  getCreatedTimeRange,
  getRoleType,
} from "../utils/functions";
import { AuthRequest } from "../utils/definitions";
import getMasterAccess from "../utils/access-check/getMasterAccess";
import generateUserRefId from "../utils/generateUserRefId";
import {
  hasPermission,
  getUserType,
  UserRole,
  PermissionCategory,
  PERMISSIONS,
  hasAnyPermissionInCategory,
} from "../utils/types/auth";
import { getIO } from "../socket";
import { clearSessionExpiration } from "../utils/schedule/trackSessionExpiration";
import { InputSanitizer } from "../utils/sanitizers/inputSanitizer";
import { validateTeamMember } from "../utils/validators/teamValidation";
import { validatePassword } from "../utils/validators/passwordValidator";

interface CreateTeamMemberRequest {
  refId: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  role: Role;
  subRole: SubRole;
  department: Department;
  teamId: string;
  invitedById: string;
  status?: TeamMemberStatus;
}

interface DateRange {
  from?: string;
  to?: string;
}

export const getTeamId = async (req: AuthRequest, res: Response) => {
  try {
    // Get the first team (assuming one team for now)
    const team = await prisma.team.findFirst();

    if (!team) {
      // const masterOwner = await prisma.user.findFirst({
      //   where: {
      //     role: "master",
      //   },
      // });
      // if (!masterOwner) {
      //   return res.status(500).json({
      //     success: false,
      //     message: "No master owner found to process the invitation",
      //   });
      // }
      // If no team exists, create one
      const newTeam = await prisma.team.create({
        data: {
          // id: masterOwner.id,
          name: "Default Team",
        },
      });
      return res.json({ success: true, teamId: newTeam.id });
    }

    return res.json({ success: true, teamId: team.id });
  } catch (error: any) {
    console.error("Error getting team ID:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while getting the team ID",
    });
  }
};

export const createTeamMember = async (req: AuthRequest, res: Response) => {
  try {
    const {
      firstName,
      lastName,
      username,
      email,
      password,
      role, // Get role from request body
      subRole,
      department,
      teamId,
      invitedById,
    } = req.body as CreateTeamMemberRequest;

    // Validate password strength
    if (password) {
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: "Password validation failed",
          errors: passwordValidation.errors.map((error) => ({
            field: "password",
            message: error,
          })),
        });
      }
    }

    // Use role from request body, fallback to creator's role if not provided
    const memberRole = role || (req.user?.role as Role);

    // Validate and sanitize team member input
    const validatedData = validateTeamMember(
      {
        firstName: firstName || "",
        lastName: lastName || "",
        email: email || "",
        password,
        role: memberRole as Role,
        subRole: subRole as SubRole,
        department: department || Department.customer_support,
        teamId: teamId || "",
      },
      req.user?.role as Role
    );

    if (validatedData.errors.length > 0) {
      return res.status(400).json({
        message: validatedData.errors[0].message,
        errors: validatedData.errors,
      });
    }

    const currentUser = req.user || req.teamMember;
    if (!currentUser) {
      return res.status(401).json({ message: "User not authenticated." });
    }

    const userRoleType = currentUser.roleType as UserRole;

    const requiredPermission =
      userRoleType !== UserRole.MASTER_MODERATOR
        ? PERMISSIONS.MANAGE_TEAM.name
        : PERMISSIONS.MANAGE_MODERATOR_ACCOUNTANT.name;

    if (!hasPermission(userRoleType, requiredPermission)) {
      return res.status(403).json({
        message: "Forbidden: You don't have permission to perform this action.",
      });
    }

    // Look for a default admin user if no inviter is specified
    const defaultAdmin = await prisma.user.findFirst({
      where: {
        role: "master",
      },
    });
    if (!defaultAdmin) {
      return res.status(500).json({
        message: "No admin user found to process the invitation",
      });
    }

    const effectiveInviterId = currentUser.id || defaultAdmin.id;

    if (!teamId) {
      return res.status(400).json({
        message: "Missing required field: teamId is required",
      });
    }

    // Check if user already exists with sanitized email
    const existingMember = await prisma.teamMember.findUnique({
      where: { email: validatedData.email },
    });

    if (existingMember) {
      return res.status(400).json({
        message:
          "Team member account with this email already exists, please use a different email",
      });
    }

    // Check if team exists
    const team = await prisma.team.findUnique({
      where: { id: teamId },
    });

    if (!team) {
      return res.status(400).json({
        message: "Team not found",
      });
    }

    let inviterAgencyName: string | null = null;
    let inviterIsUser = false;
    let inviterIsTeamMember = false;

    if (effectiveInviterId) {
      // Check if inviter exists and has permission

      const inviterUser = await prisma.user.findUnique({
        where: { id: effectiveInviterId },
      });
      if (inviterUser) inviterIsUser = true;

      const inviterTeamMember = await prisma.teamMember.findUnique({
        where: { id: effectiveInviterId },
      });
      if (inviterTeamMember) inviterIsTeamMember = true;

      const inviter = inviterUser || inviterTeamMember;

      if (!inviter) {
        return res.status(400).json({
          message: "Inviter not found",
        });
      }

      inviterAgencyName = inviter.agencyName;
      if (!inviterAgencyName) {
        return res.status(400).json({
          message: "Inviter's agency name not found",
        });
      }
    } else {
      return res.status(400).json({
        message: "Inviter ID is required",
      });
    }

    // Check if username already exists
    const existingUser = await prisma.teamMember.findFirst({
      where: {
        username: `${validatedData.firstName.toLowerCase()}_${validatedData.lastName.toLowerCase()}`,
      },
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message:
          "Username already exists. Please use a different name combination.",
      });
    }

    // Create everything in a transaction
    const result = await prisma.$transaction(async (tx: any) => {
      // Hash password
      if (!validatedData.password) {
        throw new Error("Please enter your password");
      }
      const hashedPassword = await bcrypt.hash(validatedData.password, 10);

      // Generate roleType using both role and subRole as selected by the creator
      const roleType = generateRoleType(memberRole as Role, subRole as SubRole);

      // Generate refId for the new team member
      const refId = await generateUserRefId();

      // Create team member with lowercase username and inherited user
      const teamMember = await tx.teamMember.create({
        data: {
          refId,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          username: `${validatedData.firstName.toLowerCase()}_${validatedData.lastName.toLowerCase()}`,
          email: validatedData.email,
          password: hashedPassword,
          role: validatedData.role as Role,
          subRole: validatedData.subRole as SubRole,
          roleType: roleType as RoleType,
          department: validatedData.department as Department,
          agencyName: inviterAgencyName,
          team: {
            connect: { id: teamId },
          },
          createdBy: inviterIsUser
            ? { connect: { id: effectiveInviterId } }
            : undefined,
          createdByTeamMember: inviterIsTeamMember
            ? { connect: { id: effectiveInviterId } }
            : undefined,
        },
      });

      // Generate invitation token (still needed for record keeping)
      const newToken = await generateInvitationToken();

      // Create invitation with accepted status
      const invitation = await tx.invitation.create({
        data: {
          token: newToken,
          email: validatedData.email,
          role: roleType, // Use the generated roleType instead of just the role
          department: validatedData.department as Department,
          status: "accepted", // Set as accepted immediately
          acceptedAt: new Date(), // Set acceptance timestamp
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          teamMember: {
            connect: { id: teamMember.id },
          },
          team: {
            connect: { id: teamId },
          },
          invitedBy: { connect: { id: defaultAdmin.id } },
        },
      });

      return { teamMember, invitation };
    });

    // Still send invitation email to notify team member of their account creation
    await sendInvitationEmail(validatedData.email, req.body);

    res.status(201).json({
      message: "Team member added successfully",
      teamMember: result.teamMember,
    });
  } catch (error: any) {
    console.error("Error creating team member:", error);
    return res.status(400).json({
      success: false,
      message:
        error.message || "An error occurred while creating the team member",
    });
  }
};

export const updateTeamMember = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params; // Assume the ID is passed as a URL parameter
    const {
      refId,
      firstName,
      lastName,
      username,
      email,
      password,
      role,
      subRole,
      department,
      teamId,
      status,
    } = req.body as Partial<CreateTeamMemberRequest>; // Partial to allow optional updates

    if (!id) {
      return res.status(400).json({ message: "Team member ID is required" });
    }

    const currentUser = req.user || req.teamMember;
    if (!currentUser) {
      return res.status(401).json({ message: "User not authenticated." });
    }

    // Use role from request body, fallback to creator's role if not provided
    const memberRole = role || (req.user?.role as Role);

    // Validate and sanitize team member input
    const validatedData = validateTeamMember(
      {
        firstName: firstName || "",
        lastName: lastName || "",
        email: email || "",
        // password,
        role: memberRole,
        subRole,
        department: department || Department.customer_support,
        teamId: teamId || "",
      },
      req.user?.role as Role
    );

    if (validatedData.errors.length > 0) {
      return res.status(400).json({
        message: validatedData.errors[0].message,
        errors: validatedData.errors,
      });
    }

    // Get the team member being modified
    const targetMember = await prisma.teamMember.findUnique({
      where: { id },
    });

    if (!targetMember) {
      return res.status(404).json({ message: "Team member not found" });
    }

    const userRoleType = currentUser.roleType as UserRole;

    // Check permissions based on target member's role
    if (targetMember.subRole === SubRole.admin) {
      if (!hasPermission(userRoleType, PERMISSIONS.EDIT_ADMIN.name)) {
        const action =
          targetMember.status === "inactive"
            ? "activate"
            : targetMember.status === "active"
              ? "deactivate"
              : "modify";
        return res.status(403).json({
          message: `You are not authorized to ${targetMember.subRole === SubRole.admin && action} this account`,
        });
      }
    } else if (
      userRoleType === UserRole.MASTER_MODERATOR &&
      targetMember.subRole !== SubRole.moderator &&
      targetMember.subRole !== SubRole.accountant
    ) {
      return res.status(403).json({
        message: "Moderators can only modify moderator and accountant accounts",
      });
    } else if (userRoleType === UserRole.MASTER_ACCOUNTANT) {
      return res.status(403).json({
        message: "You are not authorized to modify this account",
      });
    }

    // Validate email if being updated
    if (email && email !== targetMember.email) {
      const emailResult = InputSanitizer.sanitizeTeamMemberEmail(email);
      if (!emailResult.isValid) {
        return res.status(400).json({
          success: false,
          message: emailResult.error || "Invalid email format",
        });
      }

      // Check if email already exists
      const existingMember = await prisma.teamMember.findUnique({
        where: { email: emailResult.value },
      });

      if (existingMember && existingMember.id !== id) {
        return res.status(400).json({
          message:
            "Team member account with this email already exists, please use a different email",
        });
      }
    }

    let hashedPassword: string | undefined;

    // Hash the password if provided and validate it
    if (password) {
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: "Password validation failed",
          errors: passwordValidation.errors.map((error) => ({
            field: "password",
            message: error,
          })),
        });
      }
      hashedPassword = await bcrypt.hash(password, 10);
    }

    // Update the team member
    const updatedMember = await prisma.teamMember.update({
      where: { id },
      data: {
        refId: refId ?? targetMember.refId,
        firstName: validatedData.firstName ?? targetMember.firstName,
        lastName: validatedData.lastName ?? targetMember.lastName,
        username: username ?? targetMember.username,
        email: validatedData.email ?? targetMember.email,
        ...(hashedPassword ? { password: hashedPassword } : {}), // Only update password if provided
        subRole: subRole ?? targetMember.subRole,
        department: department ?? targetMember.department,
        status: status ?? targetMember.status,
        team: teamId ? { connect: { id: teamId } } : undefined, // Update team if teamId is provided
        role: memberRole as Role, // Update role field to match Prisma Role enum
      },
    });

    if (updatedMember.status === "inactive") {
      const io = getIO();
      io.to(updatedMember.id).emit("sessionExpiration", {
        message: "Your agency account has been suspended.",
      });

      // Cancel the session expiration timer
      clearSessionExpiration(updatedMember.id as string);
    }

    res.status(200).json({
      message: "Team member updated successfully",
      data: updatedMember,
    });
  } catch (error: any) {
    console.error("Error updating team member:", error);
    return res.status(500).json({
      success: false,
      message:
        // error.message ||
        "An error occurred while updating the team member",
    });
  }
};

export const getTeamMembers = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  try {
    const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
    const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;

    // Get filter parameters from query
    const accountType = req.query.accountType as SubRole | undefined;
    const department = req.query.department as Department | undefined;
    const registrationDateRange = req.query.registrationDate
      ? (JSON.parse(req.query.registrationDate as string) as DateRange)
      : undefined;
    const lastLoginRange = req.query.lastLogin
      ? (JSON.parse(req.query.lastLogin as string) as DateRange)
      : undefined;

    // Build filter conditions
    const filters: any = {
      NOT: [{ id: req.teamMember?.id }],
      AND: [{ verified: true }],
    };

    // Add account type filter if valid status is provided
    if (accountType && Object.values(SubRole).includes(accountType)) {
      filters.AND.push({ subRole: accountType });
    }

    // Add department filter if valid department is provided
    if (department && Object.values(Department).includes(department)) {
      filters.AND.push({ department });
    }

    // Add registration date range filter
    if (registrationDateRange) {
      const dateFilter: any = {};
      if (registrationDateRange.from) {
        dateFilter.gte = new Date(registrationDateRange.from);
      }
      if (registrationDateRange.to) {
        dateFilter.lte = new Date(registrationDateRange.to);
      }
      if (Object.keys(dateFilter).length > 0) {
        filters.AND.push({ createdAt: dateFilter });
      }
    }

    // Add last login range filter
    if (lastLoginRange) {
      const loginFilter: any = {};
      if (lastLoginRange.from) {
        loginFilter.gte = new Date(lastLoginRange.from);
      }
      if (lastLoginRange.to) {
        loginFilter.lte = new Date(lastLoginRange.to);
      }
      if (Object.keys(loginFilter).length > 0) {
        filters.AND.push({ lastLogin: loginFilter });
      }
    }

    // get all users except master user with filters
    const [users, usersTotal] = await Promise.all([
      prisma.teamMember.findMany({
        where: filters,
        take: pageSize,
        // skip the number of users based on the cursor
        skip: cursor ? 1 : 0,
        // set the cursor to the id of the last user
        cursor: cursor ? { id: cursor } : undefined,
        // sort the users by id in ascending order
        orderBy: {
          id: "asc",
        },
        // Include all fields including lastLogin
        select: {
          id: true,
          refId: true,
          firstName: true,
          lastName: true,
          username: true,
          email: true,
          role: true,
          subRole: true,
          department: true,
          status: true,
          accountStatus: true,
          verified: true,
          lastLogin: true,
          createdAt: true,
          updatedAt: true,
          invitation: true,
          team: true,
        },
      }),
      // get the total number of users
      prisma.teamMember.count({
        where: filters,
      }),
    ]);

    // Handle generating `refId` if required
    const refIdUpdates = users
      .filter((user: any) => !user.refId)
      .map(async (user: any) => {
        const refId = await generateUserRefId();
        return prisma.teamMember.update({
          where: { id: user.id },
          data: { refId },
        });
      });

    await Promise.all(refIdUpdates);

    const nextCursor =
      users.length === pageSize ? users[users.length - 1].id : null;

    // return users
    return res.status(200).json({
      success: true,
      results: {
        users,
        usersTotal,
        nextCursor,
      },
    });
  } catch (error: any) {
    console.error("Error fetching team members:", error);
    return res.status(400).json({
      success: false,
      message: error.message || "An error occurred while fetching team members",
    });
  }
};

export const searchTeamMembers = async (req: AuthRequest, res: Response) => {
  try {
    const { input } = req.query;
    const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
    const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;

    let searchData = {
      searchQuery: "",
      accountType: "all",
      department: "all",
      registrationDateFilter: "all time",
      lastLoginFilter: "all time",
    };

    if (!input) {
      return res.status(400).json({
        success: false,
        message: "Search input is required",
      });
    }

    // let searchData;
    if (input) {
      try {
        searchData = JSON.parse(input as string);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: "Invalid search input format",
        });
      }
    }

    const {
      searchQuery,
      accountType,
      department,
      registrationDateFilter,
      lastLoginFilter,
    } = searchData;

    const registrationDateRange = registrationDateFilter
      ? getCreatedTimeRange(registrationDateFilter)
      : {};

    const lastLoginDateRange = lastLoginFilter
      ? getCreatedTimeRange(lastLoginFilter)
      : {};

    // Build the where clause
    const whereClause: any = {
      NOT: [{ id: req.teamMember?.id }],
      AND: [{ verified: true }],
    };

    // Add search query filter if provided
    if (searchQuery) {
      whereClause.AND.push({
        OR: [
          { firstName: { contains: searchQuery, mode: "insensitive" } },
          { lastName: { contains: searchQuery, mode: "insensitive" } },
          { email: { contains: searchQuery, mode: "insensitive" } },
        ],
      });
    }

    // Add account status filter if specified (using Role enum)
    if (accountType && accountType !== "all") {
      whereClause.AND.push({ subRole: accountType as SubRole });
      // } else {
      //   whereClause.AND.push({
      //     subRole: { in: ["admin", "moderator", "accountant"] },
      //   });
    }

    // Add department filter if specified (using Department enum)
    if (department && department !== "all") {
      whereClause.AND.push({ department: department as Department });
    }

    // Add registration date range filter
    if (
      registrationDateRange &&
      Object.keys(registrationDateRange).length > 0
    ) {
      whereClause.AND.push({ createdAt: registrationDateRange });
    }

    // Add last login date range filter
    if (lastLoginDateRange && Object.keys(lastLoginDateRange).length > 0) {
      whereClause.AND.push({ lastLogin: lastLoginDateRange });
    }

    // Add NOT filter for current user only if req.teamMember exists
    if (req.teamMember?.id) {
      whereClause.NOT = [{ id: req.teamMember.id }];
    }

    // Fetch team members
    const [users, usersTotal] = await Promise.all([
      prisma.teamMember.findMany({
        where: whereClause,
        take: pageSize,
        skip: cursor ? 1 : 0,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: {
          id: "asc",
        },
        select: {
          id: true,
          refId: true,
          firstName: true,
          lastName: true,
          username: true,
          email: true,
          role: true,
          subRole: true,
          department: true,
          status: true,
          accountStatus: true,
          verified: true,
          lastLogin: true,
          createdAt: true,
          updatedAt: true,
          invitation: true,
          team: true,
        },
      }),
      prisma.teamMember.count({
        where: whereClause,
      }),
    ]);

    // Handle generating `refId` if required
    const refIdUpdates = users
      .filter((user: any) => !user.refId)
      .map(async (user: any) => {
        const refId = await generateUserRefId();
        return prisma.teamMember.update({
          where: { id: user.id },
          data: { refId },
        });
      });

    await Promise.all(refIdUpdates);

    const nextCursor =
      users.length === pageSize ? users[users.length - 1].id : null;

    return res.status(200).json({
      success: true,
      results: {
        users,
        usersTotal,
        nextCursor,
      },
    });
  } catch (error: any) {
    console.error("Error searching team members:", error);
    return res.status(400).json({
      success: false,
      message:
        error.message || "An error occurred while searching team members",
    });
  }
};

export const removeTeamMember = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const currentUser = req.user || req.teamMember;
    if (!currentUser) {
      return res.status(401).json({ message: "User not authenticated." });
    }

    // Get the team member being deleted
    const targetMember = await prisma.teamMember.findUnique({
      where: { id },
    });

    if (!targetMember) {
      return res.status(404).json({ message: "Team member not found" });
    }

    const userRoleType = currentUser.roleType as UserRole;

    // Check permissions based on target member's role
    if (targetMember.subRole === SubRole.admin) {
      if (!hasPermission(userRoleType, PERMISSIONS.DELETE_ADMIN.name)) {
        return res.status(403).json({
          message: "You don't have permission to delete admin accounts",
        });
      }
    } else if (
      userRoleType === UserRole.MASTER_MODERATOR &&
      targetMember.subRole !== SubRole.moderator &&
      targetMember.subRole !== SubRole.accountant
    ) {
      return res.status(403).json({
        message: "Moderators can only delete moderator and accountant accounts",
      });
    } else if (userRoleType === UserRole.MASTER_ACCOUNTANT) {
      return res.status(403).json({
        message: "Accountants cannot delete any user accounts",
      });
    }

    // Delete related invitations first due to onDelete: Restrict
    await prisma.invitation.deleteMany({
      where: { teamMemberId: id },
    });

    // Now delete the team member
    await prisma.teamMember.delete({
      where: { id },
    });

    const io = getIO();
    io.to(targetMember.id).emit("sessionExpiration", {
      message: "Your agency account has been rejected.",
    });

    // Cancel the session expiration timer
    clearSessionExpiration(targetMember.id as string);

    res.json({ message: "Team member removed successfully" });
  } catch (error: any) {
    console.error("Error removing team member:", error);
    return res.status(400).json({
      success: false,
      message:
        error.message || "An error occurred while removing the team member",
    });
  }
};

export const resendInvitation = async (req: AuthRequest, res: Response) => {
  try {
    const { email, teamId, invitedById } = req.body;

    if (!teamId || !invitedById) {
      return res.status(400).json({
        message: "Missing required fields: teamId and invitedById are required",
      });
    }

    const teamMember = await prisma.teamMember.findUnique({
      where: { email },
      include: {
        invitation: true,
        team: true,
      },
    });

    if (!teamMember || teamMember.status === "active") {
      return res.status(400).json({
        message: "Invalid invitation request",
      });
    }

    // Revoke existing invitations if any
    if (teamMember.invitation.length > 0) {
      await prisma.invitation.updateMany({
        where: {
          teamMemberId: teamMember.id,
          status: "pending",
        },
        data: {
          status: "revoked",
          revokedAt: new Date(),
        },
      });
    }

    const newToken = await generateInvitationToken();

    // Ensure roleType is not null
    if (!teamMember.role || !teamMember.roleType || !teamMember.subRole) {
      return res.status(400).json({
        success: false,
        message: "Team member role, role type, and subRole is required",
      });
    }

    // Create new invitation with accepted status
    const invitation = await prisma.invitation.create({
      data: {
        token: newToken,
        email,
        role: teamMember.role as UserRole,
        department: teamMember.department as Department,
        status: "accepted", // Set as accepted immediately
        acceptedAt: new Date(), // Set acceptance timestamp
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        teamMember: {
          connect: { id: teamMember.id },
        },
        team: {
          connect: { id: teamId },
        },
        invitedBy: {
          connect: { id: invitedById },
        },
      },
    });

    // Still send invitation email to notify team member
    await sendInvitationEmail(email, req.body); // Password hidden in resend

    res.json({
      message: "Team member status updated and notification sent",
      invitation,
    });
  } catch (error: any) {
    console.error("Error updating team member status:", error);
    return res.status(400).json({
      success: false,
      message:
        error.message ||
        "An error occurred while updating the team member status",
    });
  }
};
