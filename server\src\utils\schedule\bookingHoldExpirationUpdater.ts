import cron from "node-cron";
import moment from "moment";
import logger from "../logger";
import { BookingService } from "../../services/booking.service";
import { acquireLock, releaseLock } from "../locks/jobLock";
import NotificationService from "../services/notification.service";
import { getActiveMasterUsers } from "../access-check/getActiveMasterUsers";
import { prisma } from "../../prisma";

type ExpiredBooking = {
  id: string;
  requestId: string;
  ticketId: string;
  userId: string | null;
  agencyAgentId: string | null;
};

const bookingService = new BookingService();

const JOB_NAME = "bookingHoldExpiration";
/**
 * Checks for bookings whose QUICK_HOLD status has expired and updates them to TIMED_OUT.
 */
const BATCH_SIZE = 100;
const MAX_RETRIES = 3;

const checkExpiredBookingHolds = async () => {
  if (!(await acquireLock(JOB_NAME))) {
    // Another instance is running, skip this run
    logger.info(`[${JOB_NAME}] Lock not acquired, skipping this run.`);
    return;
  }
  const startTime = Date.now();
  let processed = 0;
  let errorCount = 0;
  let errors: { bookingId: string; error: any }[] = [];
  let hasMore = true;
  let lastId: string | undefined = undefined;
  const now = moment().toDate();

  try {
    logger.info("Running booking hold expiration check...");
    while (hasMore) {
      const expiredBookings: ExpiredBooking[] = await prisma.booking.findMany({
        where: {
          status: { equals: "QUICK_HOLD" },
          expiresAt: { lte: now },
          ...(lastId ? { id: { gt: lastId } } : {}),
        },
        orderBy: { id: "asc" },
        take: BATCH_SIZE,
        select: {
          id: true,
          requestId: true,
          ticketId: true,
          userId: true,
          agencyAgentId: true,
        },
      });
      if (expiredBookings.length === 0) break;
      for (const b of expiredBookings) {
        let attempt = 0;
        let success = false;
        while (attempt < MAX_RETRIES && !success) {
          try {
            await bookingService.timeoutBooking(b.id);
            // Notify agent if userId exists
            if (b.userId) {
              await NotificationService.createNotification({
                userId: b.userId,
                type: "INTERNAL",
                title: "Booking Timed Out",
                message: `Your internal booking ( ${b.requestId} ) has timed out and seats released.`,
                relatedId: b.id,
                link: `/booking/${b.id}`,
                priority: 1,
              });
            }
            // Notify all active master users
            const masterUsers = await getActiveMasterUsers();
            await Promise.all(
              masterUsers.map((master) =>
                NotificationService.createNotification({
                  userId: master.id,
                  type: "INTERNAL",
                  title: "Booking Timed Out",
                  message: `Internal booking ( ${b.requestId} ) (ID: ${b.id}) has timed out and seats released.`,
                  relatedId: b.id,
                  link: `/booking/${b.id}`,
                  priority: 1,
                })
              )
            );
            logger.info(
              `Internal booking ( ${b.requestId} ) timed out and seats released.`
            );
            processed++;
            success = true;
          } catch (err) {
            attempt++;
            if (attempt >= MAX_RETRIES) {
              logger.error(
                `Failed to timeout internal booking ( ${b.requestId} ) after [ ${MAX_RETRIES} ] attempts:`,
                err
              );
              errors.push({ bookingId: b.id, error: err });
              errorCount++;
            } else {
              logger.warn(
                `Retrying internal booking ( ${b.requestId} ) (attempt ${attempt + 1})...`
              );
              await new Promise((res) => setTimeout(res, 500 * attempt)); // exponential backoff
            }
          }
        }
      }
      hasMore = expiredBookings.length === BATCH_SIZE;
      lastId = expiredBookings[expiredBookings.length - 1]?.id;
    }
    const duration = Date.now() - startTime;
    logger.info(
      `[${JOB_NAME}] Processed: ${processed}, Errors: ${errorCount}, Duration: ${duration}ms.`
    );
    if (errors.length > 0) {
      logger.error(`[${JOB_NAME}] Error details:`, errors);
    }
  } catch (error) {
    logger.error(`[${JOB_NAME}] Fatal error:`, error);
  } finally {
    await releaseLock(JOB_NAME);
  }
};

// Schedule the job to run every minute
// Adjust schedule as needed (e.g., '*/5 * * * *' for every 5 minutes)
const scheduledJob = cron.schedule("* * * * *", checkExpiredBookingHolds);

logger.info("Booking hold expiration checker scheduled to run every minute.");

// Export the job if you need to manage it elsewhere (e.g., stop it)
export { scheduledJob, checkExpiredBookingHolds };
