import "./utils/logger";
import "dotenv/config";

// --- Monitoring & Observability Imports ---
// import expressPrometheusMiddleware from "express-prometheus-middleware";
// import * as Sentry from "@sentry/node";
import toobusy from "toobusy-js";

import express from "express";
// import compression from "compression";

import routes from "./routes/routes";
import swaggerUi from "swagger-ui-express";
import YAML from "yamljs";
import swaggerJSDoc from "swagger-jsdoc";
import path from "path";
import cors from "cors";
import cookieParser from "cookie-parser";
import bodyParser from "body-parser";
import { createServer } from "http";
import { initSocket } from "./socket";
import helmet from "helmet";
// import { sqlInjectionProtection } from "./middlewares/sqlInjectionProtection";
// import { sanitizeInput } from "./middlewares/sanitization";
// import { validateRequest } from "./middlewares/requestValidation";
import "./utils/schedule/ticketStatusExpirationUpdater"; // check for any ticket that has expired date and make it unavailable
import "./utils/schedule/trackUnverifiedUserCleanup"; // Initialize scheduled tasks
import "./utils/schedule/bookingHoldExpirationUpdater"; // Initialize booking hold expiration check
import "./utils/schedule/notificationCleanupScheduler"; // Clean up old notifications weekly
// import memoryMonitor from "./utils/memoryMonitor"; // Monitor memory usage
import "./utils/DatabaseUrl";
// import { apiLimiter } from "./middlewares/rateLimit";
import { prisma } from "./prisma";
import { startRateLimitCleanup } from "./utils/schedule/rateLimitCleanup";
import { errorHandler } from "./middlewares/errorHandler";

// PORT
const PORT = process.env.PORT || 3000;

// main app
const app = express();

// socket
const httpServer = createServer(app);
initSocket(httpServer);

// middleware
app.set("trust proxy", true);
app.use(
  helmet({
    crossOriginEmbedderPolicy: true,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: { policy: "cross-origin" },
    dnsPrefetchControl: true,
    frameguard: { action: "deny" },
    hidePoweredBy: true,
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
    ieNoOpen: true,
    noSniff: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" },
    xssFilter: true,
  })
);
app.use(
  helmet.contentSecurityPolicy({
    directives:
      process.env.NODE_ENV === "production"
        ? {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'"],
            styleSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: [
              "'self'",
              ...(process.env.CLIENT_DOMAINS?.split(",") || []),
            ],
            objectSrc: ["'none'"],
            upgradeInsecureRequests: [],
          }
        : {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: [
              "'self'",
              ...(process.env.CLIENT_DOMAINS?.split(",") || []),
            ],
          },
  })
);
app.use(
  cors({
    origin: process.env.CLIENT_DOMAINS?.split(","),
    credentials: true,
  })
);
// app.use(express.json());
app.use(bodyParser.json({ limit: "500mb" })); // Set the limit for JSON payloads
app.use(bodyParser.urlencoded({ extended: true, limit: "500mb" })); // Set the limit for URL-encoded payloads
app.use(cookieParser());
app.use(express.urlencoded({ extended: true }));

// // PORT
// const PORT = process.env.PORT || 3000;

// // main app
// const app = express();

// // Body parser middleware should be before any routes or other middleware that read req.body
// app.use(bodyParser.json({ limit: "500mb" }));
// app.use(bodyParser.urlencoded({ limit: "500mb", extended: true }));

// // Enable gzip compression for all responses (force for all sizes)
// app.use(compression({ threshold: 0, filter: () => true }) as any);

// // Set cache headers for static assets (served from /public)
// app.use(express.static('public', {
//   maxAge: '7d', // Cache static assets for 7 days
//   setHeaders: (res, path) => {
//     res.setHeader('Cache-Control', 'public, max-age=604800, immutable');
//   }
// }));

// // Set cache headers for API responses (example: cache GET for 1 min)
// app.use((req, res, next) => {
//   if (req.method === 'GET' && req.path.startsWith('/api/')) {
//     res.setHeader('Cache-Control', 'public, max-age=60'); // 1 minute
//   }
//   next();
// });

// // For best performance, deploy behind a CDN (e.g., Cloudflare, AWS CloudFront, Vercel Edge)
// // CDN should be configured to cache static assets and support gzip/brotli compression.

// // For multi-core scaling in production, use PM2 or Node.js cluster mode.
// // See README.md for cluster instructions.

// // --- Sentry Initialization ---
// if (process.env.SENTRY_DSN) {
//   Sentry.init({
//     dsn: process.env.SENTRY_DSN,
//     tracesSampleRate: 0.5, // Adjust as needed
//     environment: process.env.NODE_ENV || 'development',
//     sendDefaultPii: true,
//   });
// }

// // --- Event Loop Lag Protection ---
// app.use((req, res, next) => {
//   if (toobusy() && process.env.NODE_ENV !== "test") {
//     res.status(503).send("Server too busy. Please try again later.");
//   } else {
//     next();
//   }
// });

// // --- Prometheus Metrics ---
// app.use(expressPrometheusMiddleware({
//   metricsPath: process.env.PROM_METRICS_PATH || "/metrics",
//   collectDefaultMetrics: true,
//   requestDurationBuckets: [0.1, 0.5, 1, 1.5],
//   authenticate: req => true, // No auth by default
//   customLabels: ['method', 'path'],
//   transformLabels: (labels, req, res) => labels,
// }));

// // socket
// const httpServer = createServer(app);
// initSocket(httpServer);

// // middleware
// app.set("trust proxy", true);
// app.use(
//   helmet({
//     crossOriginEmbedderPolicy: true,
//     crossOriginOpenerPolicy: true,
//     crossOriginResourcePolicy: { policy: "cross-origin" },
//     dnsPrefetchControl: true,
//     frameguard: { action: "deny" },
//     hidePoweredBy: true,
//     hsts: {
//       maxAge: 31536000,
//       includeSubDomains: true,
//       preload: true,
//     },
//     ieNoOpen: true,
//     noSniff: true,
//     referrerPolicy: { policy: "strict-origin-when-cross-origin" },
//     xssFilter: true,
//   })
// );
// app.use(
//   helmet.contentSecurityPolicy({
//     directives: process.env.NODE_ENV === "production"
//       ? {
//           defaultSrc: ["'self'"],
//           scriptSrc: ["'self'"],
//           styleSrc: ["'self'"],
//           imgSrc: ["'self'", "data:", "https:"],
//           connectSrc: ["'self'", ...(process.env.CLIENT_DOMAINS?.split(",") || [])],
//           objectSrc: ["'none'"],
//           upgradeInsecureRequests: [],
//         }
//       : {
//           defaultSrc: ["'self'"],
//           scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
//           styleSrc: ["'self'", "'unsafe-inline'"],
//           imgSrc: ["'self'", "data:", "https:"],
//           connectSrc: ["'self'", ...(process.env.CLIENT_DOMAINS?.split(",") || [])],
//         },
//   })
// );
// app.use(
//   cors({
//     origin: process.env.CLIENT_DOMAINS?.split(","),
//     // methods: ["GET", "POST", "PUT", 'PATCH',"DELETE", "OPTIONS"],
//     // allowedHeaders: ["Content-Type", "Authorization"],
//     credentials: true,
//     // maxAge: 600, // 10 minutes
//     // exposedHeaders: ["set-cookie"],
//   })
// );
// app.use(express.json({ limit: "10kb" }));
// app.use(cookieParser());
// app.use(express.urlencoded({ extended: true }));

// // Security and validation middlewares
// // app.use(sqlInjectionProtection); // SQL injection protection
// // app.use(sanitizeInput); // XSS and general input sanitization
// // app.use(validateRequest); // Request validation

// // Add request timeout
// app.use((req, res, next) => {
//   res.setTimeout(30000, () => {
//     res.status(408).send("Request Timeout");
//   });
//   next();
// });

// =============================
// Swagger/OpenAPI Documentation
// =============================
// 1. Serve static YAML docs for backward compatibility
const swaggerPath = path.join(__dirname, "../swagger.yaml");
const swaggerDocument = YAML.load(swaggerPath);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// 2. Auto-generate docs from JSDoc comments using swagger-jsdoc
const jsdocOptions = {
  definition: {
    openapi: "3.0.3",
    info: {
      title: "AirVilla Charter API (Auto-generated)",
      version: "1.0.0",
    },
  },
  apis: [
    path.join(__dirname, "./routes/*.ts"),
    path.join(__dirname, "./controllers/*.ts"),
  ],
};
const jsdocSpec = swaggerJSDoc(jsdocOptions);
app.use("/api/v1/docs", swaggerUi.serve, swaggerUi.setup(jsdocSpec));

// =========================
// Versioned API Mount Point
// =========================
app.use("/api/v1", routes);

// // Sentry error handler (before any other error middleware)
// if (process.env.SENTRY_DSN) {
//   app.use(Sentry.Handlers.errorHandler());
// }
// The error handler must be registered before any other error middleware and after all controllers
// Sentry.setupExpressErrorHandler(app);

// Error handler middleware (should be last)
app.use(errorHandler);

// Start the rate limit cleanup job
startRateLimitCleanup();

// if production is not test the run the server
if (process.env.NODE_ENV !== "test")
  httpServer.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}`);
  });
else console.log("test");

// ==========================
// Graceful Shutdown Handling
// ==========================
// Clean up toobusy-js on shutdown
process.on("exit", () => {
  toobusy.shutdown();
});

const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}, shutting down gracefully...`);

  try {
    await prisma.$disconnect();
    console.log("Database connection closed.");
  } catch (error) {
    console.error("Error during database disconnection:", error);
  }

  process.exit(0);
};

// Handle termination signals
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));

// // Take heap snapshot on SIGUSR2 (useful for debugging in production)
// if (process.env.NODE_ENV === 'production') {
//   process.on('SIGUSR2', async () => {
//     console.log('Received SIGUSR2 signal, taking heap snapshot...');
//     try {
//       const snapshotPath = await memoryMonitor.takeHeapSnapshot('manual-snapshot');
//       console.log(`Heap snapshot saved to: ${snapshotPath}`);
//     } catch (error) {
//       console.error('Failed to take heap snapshot:', error);
//     }
//   });
// }

// Monitor memory and take heap snapshots when memory usage is high
const MONITOR_INTERVAL = 60000; // 1 minute
const MEMORY_THRESHOLD = 0.8; // 80% of max memory

// setInterval(async () => {
//   const memoryUsage = process.memoryUsage();
//   const memoryUsageRatio = memoryUsage.heapUsed / memoryUsage.heapTotal;
  
//   if (memoryUsageRatio > MEMORY_THRESHOLD) {
//     console.warn(`High memory usage detected: ${(memoryUsageRatio * 100).toFixed(2)}%`);
//     try {
//       const snapshotPath = await memoryMonitor.takeHeapSnapshot('high-memory');
//       console.log(`Heap snapshot saved due to high memory usage: ${snapshotPath}`);
//     } catch (error) {
//       console.error('Failed to take heap snapshot:', error);
//     }
//   }
// }, MONITOR_INTERVAL);
