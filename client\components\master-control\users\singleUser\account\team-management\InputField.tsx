const InputField = ({
  label,
  name,
  value,
  placeholder,
  onChange,
  disabled,
  required,
  type = "text",
  className = "",
  formErrors,
}: {
  label: string;
  name: string;
  value: string;
  placeholder: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  type?: string;
  className?: string;
  formErrors?: string | undefined;
}) => (
  <div className={className}>
    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <input
      type={type}
      name={name}
      value={value}
      placeholder={placeholder}
      onChange={onChange}
      disabled={disabled}
      className="w-full bg-gray-300 dark:bg-gray-800 rounded-lg px-3 py-2 border-0 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
    />
    {formErrors && (
      <p className="text-red-500 text-sm mt-1 flex items-center">
        {formErrors}
      </p>
    )}
  </div>
);

export default InputField;
