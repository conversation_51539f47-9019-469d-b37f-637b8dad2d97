"use client";

import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import { AccountStatusEnum } from "@/utils/definitions/masterDefinitions";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const VALID_STATUSES: AccountStatusEnum[] = [
  AccountStatusEnum.accepted,
  AccountStatusEnum.suspended,
  AccountStatusEnum.disabled,
];
const LOGOUT_STATUSES: AccountStatusEnum[] = [
  AccountStatusEnum.suspended,
  AccountStatusEnum.disabled,
];

const useMasterUserAuth = (teamMemberAccess: boolean = false) => {
  const user = useAppSelector(selectUser);
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // redirect the user to signin page if not signed in
    if (
      !user.isLogin ||
      LOGOUT_STATUSES.includes(
        (user as StoredUser).accountStatus as AccountStatusEnum
      )
    ) {
      console.log(
        "User is not logged in or is suspended, disabled, or deactivated in master auth. Redirecting to signin page."
      );
      router.push("/signin");
      return;
    }

    // redirect the user to not verified page if not verified yet
    if (!(user as StoredUser).verified) {
      router.push("/signup-process/not-verified");
      return;
    }

    // redirect the user to not approved page if not approved yet
    if (
      !VALID_STATUSES.includes(
        (user as StoredUser).accountStatus as AccountStatusEnum
      )
    ) {
      router.push("/signup-process/not-accepted");
      return;
    }

    const userRole = (user as StoredUser).role;

    if (teamMemberAccess) {
      // For team member access, check if user has any of the allowed roles
      const allowedRoles = ["master", "admin", "moderator", "accountant"];
      if (!userRole || !allowedRoles.includes(userRole)) {
        router.push("/blockseats");
        return;
      }
    } else {
      // For master-only access, check if user is master
      if (userRole !== "master") {
        router.push("/blockseats");
        return;
      }
    }

    setLoading(false);
  }, [user, router, teamMemberAccess]);

  return { loading };
};

export default useMasterUserAuth;
