import { CreateInternalBookingDto } from "@/utils/types/booking.types";

export function validateBookingData(data: CreateInternalBookingDto): string | null {
  if (!data.ticketId) return 'Missing ticketId.';
  if (!data.userId) return 'Missing userId.';
  if (!data.agencyAgentId) return 'Missing agencyAgentId.';
  if (!data.travelers || !data.travelers.length) return 'At least one traveler is required.';
  if (!data.seats || !data.seats.length) return 'At least one seat is required.';
  if (!data.type) return 'Missing booking type.';
  if (!data.source) return 'Missing booking source.';
  if (data.source === 'THIRD_PARTY' && data.seats.some(seat => !seat.seatNumber)) {
    return 'Seat number is required for all seats in third-party bookings.';
  }
  for (let i = 0; i < data.travelers.length; i++) {
    const t = data.travelers[i];
    if (!/^\d{4}-\d{2}-\d{2}$/.test(t.dateOfBirth)) {
      return `Traveler ${i + 1}: Date of Birth must be in YYYY-MM-DD format.`;
    }
    if (!/^\d{4}-\d{2}-\d{2}$/.test(t.expirationDate)) {
      return `Traveler ${i + 1}: Passport Expiry must be in YYYY-MM-DD format.`;
    }
  }
  return null;
}
