import { Response } from "express";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
import { Prisma, PrismaClient, User, AgencyAgent } from "@prisma/client"; // Import Prisma types
import getAgencyAccess from "../utils/access-check/getAgencyAccess";
import { getCreatedTimeRange } from "../utils/functions";
import moment from "moment";
import { updateTicketValidation } from "../utils/validators/ticketValidation";
import { getActiveMasterUsers } from "../utils/access-check/getActiveMasterUsers";
import notificationService from "../utils/services/notification.service";

interface TrackedFields {
  seats: number;
  remainingSeats: number;
  flightDate: string;
  segments: {
    departureTime: string;
    arrivalTime: string;
  }[];
  flightClasses: {
    price: {
      adult: number;
      child: number;
      infant: number;
      tax: number;
    };
  }[];
}

// Define the paths we want to track for changes (including nested paths)
const trackedFields: (keyof TrackedFields | string)[] = [
  "seats",
  "remainingSeats",
  "flightDate",
  "segments",
  "departureTime",
  "arrivalTime",
  "duration",
  "flightClasses.0.price.adult",
  "flightClasses.0.price.child",
  "flightClasses.0.price.infant",
  "flightClasses.0.price.tax",
];

// Basic selector for list views
const basicTicketSelectors = {
  departure: true,
  arrival: true,
  owner: {
    select: {
      id: true,
      agencyName: true,
      logo: true,
      firstName: true,
      lastName: true,
    },
  },
  agencyAgent: {
    select: {
      firstName: true,
      lastName: true,
    },
  },
  flightClasses: {
    include: {
      price: true,
      extraOffers: true,
    },
  },
  segments: {
    include: {
      departure: true,
      arrival: true,
    },
    orderBy: {
      departureTime: "asc",
    },
  },
  purchasedSeats: true,
};

// Detailed selector including history for single ticket view
const detailedTicketSelectors = {
  ...basicTicketSelectors,
  ticketHistoryLogs: {
    select: {
      oldValue: true,
      newValue: true,
      changeType: true,
      changeDetails: true,
      changedAt: true,
      agency: {
        select: {
          id: true,
          agencyName: true,
          firstName: true,
          lastName: true,
          logo: true,
        },
      },
      agencyAgent: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
    },
    orderBy: {
      changedAt: "asc",
    },
  },
  segments: {
    include: {
      departure: true,
      arrival: true,
    },
    orderBy: {
      departureTime: "asc",
    },
  },
};

/**
 * Searches all flight tickets based on the provided search criteria
 * and returns the results in a paginated format.
 * @param req - The request object containing the search criteria
 * @param res - The response object
 * @returns A JSON response with the success status and
 * the retrieved flight tickets. The response object will have the following
 * structure:
 * {
 *   success: boolean,
 *   results: {
 *     tickets: FlightTicketRes[],
 *     totalTickets: number,
 *     nextCursor: string | null
 *   }
 * }
 * where `FlightTicketRes` is defined in `@/utils/definitions/ticketDefinitions.ts`.
 */
export const getAllUsersTickets = async (req: AuthRequest, res: Response) => {
  try {
    // Extract request body parameters
    const {
      airportCode,
      arrivalAirportCode,
      startDate,
      endDate,
      flightClassType,
      createdTimeFilter,
    } = req.body;

    // Extract query parameters
    const { ticketStatus, updated, pageSize, cursor } = req.query;

    // Sanitize and validate input parameters
    const validatePageSize = Math.max(
      1,
      Math.min(50, parseInt(pageSize as string) || 10)
    );
    const validateCursor = typeof cursor === "string" ? cursor : undefined;

    // Create properly typed search options
    const searchOptions = [];

    // Only add defined filters
    if (updated === "true") {
      searchOptions.push({ updated: true });
    }

    if (ticketStatus && ticketStatus !== "all") {
      searchOptions.push({ ticketStatus: String(ticketStatus) });
    }

    if (airportCode) {
      searchOptions.push({
        departure: {
          is: {
            airportCode: {
              contains: String(airportCode),
              mode: Prisma.QueryMode.insensitive,
            },
          },
        },
      });
    }

    if (arrivalAirportCode) {
      searchOptions.push({
        arrival: {
          is: {
            airportCode: {
              contains: String(arrivalAirportCode),
              mode: Prisma.QueryMode.insensitive,
            },
          },
        },
      });
    }

    if (startDate && endDate) {
      searchOptions.push({
        flightDate: {
          gte: String(startDate),
          lte: String(endDate),
        },
      });
    }

    if (flightClassType) {
      searchOptions.push({
        flightClasses: {
          some: {
            type: String(flightClassType),
          },
        },
      });
    }

    if (createdTimeFilter) {
      const timeRange = getCreatedTimeRange(createdTimeFilter);
      if (Object.keys(timeRange).length > 0) {
        searchOptions.push({ createdAt: timeRange });
      }
    }

    // Build the base where clause based on user type
    let whereClause: Prisma.FlightTicketWhereInput = {};

    if (req.accountType === "agencyOwner" || req.accountType === "agencyUser") {
      // For both agency owners and agents, we need to get the agency ID
      let agencyId: string;

      if (req.accountType === "agencyOwner") {
        // For agency owners, the user ID is the agency ID
        const user = await prisma.user.findUnique({
          where: { id: req.userId },
        });

        if (!user) {
          return res.status(404).json({
            success: false,
            message: "User not found",
          });
        }
        agencyId = user.id;
      } else {
        // For agents, get their agency ID
        const agent = await prisma.agencyAgent.findUnique({
          where: { id: req.userId },
          select: { agencyId: true },
        });

        if (!agent) {
          return res.status(404).json({
            success: false,
            message: "Agent not found",
          });
        }
        agencyId = agent.agencyId;
      }

      // Both agency owners and agents can see:
      // 1. Tickets created by the agency
      // 2. Tickets owned by the agency
      // 3. Tickets created by any agent in the agency
      // 4. Tickets where the agency is the owner
      whereClause = {
        OR: [
          { createdBy: agencyId }, // Tickets created by the agency
          { ownerId: agencyId }, // Tickets owned by the agency
          {
            agencyAgent: {
              agencyId: agencyId, // Tickets created by any agent under the agency
            },
          },
          {
            owner: {
              id: agencyId, // Tickets where the agency is the owner
            },
          },
        ],
        AND: searchOptions,
      };
    }
    if (req.accountType === "masterOwner") {
      const user = await prisma.user.findUnique({
        where: { id: req.userId },
      });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }
      if (user.role === "master") {
        whereClause = {
          OR: [
            { createdBy: user.id }, // Tickets created by the master
            {
              owner: {
                role: "master", // All tickets owned by master accounts
              },
            },
            // Allow master owners to see tickets created by their team members
            {
              createdBy: {
                in: await prisma.teamMember
                  .findMany({
                    where: { role: "master" },
                    select: { id: true },
                  })
                  .then((members) => members.map((m) => m.id)),
              },
            },
          ],
          AND: searchOptions,
        };
      }
    } else if (req.accountType === "masterUser") {
      // Get the team member's details

      const teamMember = await prisma.teamMember.findUnique({
        where: { id: req.userId },
        include: {
          team: true,
        },
      });

      if (!teamMember) {
        return res.status(404).json({
          success: false,
          message: "Team member not found",
        });
      }
      // Team members can see all tickets associated with the master account
      whereClause = {
        OR: [
          { createdBy: req.userId }, // Tickets they created
          {
            owner: {
              role: "master", // All tickets owned by master accounts
            },
          },
          // Allow team members to see tickets created by master owners
          {
            createdBy: {
              in: await prisma.user
                .findMany({
                  where: { role: "master" },
                  select: { id: true },
                })
                .then((users) => users.map((u) => u.id)),
            },
          },
        ],
        AND: searchOptions,
      };
    } else if (req.accountType === "agencyUser") {
      // Get the agent's details including their agency
      const agent = await prisma.agencyAgent.findUnique({
        where: { id: req.userId },
        include: {
          agency: true,
        },
      });
      if (!agent) {
        return res.status(404).json({
          success: false,
          message: "Agency agent not found or access denied",
        });
      }
      // Agency members can see all tickets within their agency
      whereClause = {
        OR: [
          // All tickets created by anyone in the agency
          { createdBy: agent.id },
          // All tickets owned by the agency
          { ownerId: agent.agency.id },
          { agencyAgentId: agent.agency.id },
          // All tickets where any agency agent is assigned
          {
            agencyAgent: {
              agencyId: agent.agencyId,
            },
          },
          // Allow agents to see tickets created by their agency owner
          { createdBy: agent.agencyId },
          // Allow agents to see tickets owned by their agency
          { ownerId: agent.agencyId },
        ],
        AND: searchOptions,
      };
    }

    // Execute the queries
    // Create a new include object that combines basic selectors with ticket history logs
    const ticketInclude = {
      ...basicTicketSelectors,
      ticketHistoryLogs: {
        select: {
          changeType: true,
        },
        orderBy: {
          changedAt: "desc",
        },
        take: 1, // Only get the most recent log
      },
    };

    const [tickets, totalTickets] = await Promise.all([
      prisma.flightTicket.findMany({
        where: whereClause,
        include: ticketInclude as any,
        orderBy: {
          flightDate: "asc",
        },
        take: validatePageSize,
        ...(validateCursor && { cursor: { id: validateCursor } }),
        skip: validateCursor ? 1 : 0,
      }),
      prisma.flightTicket.count({
        where: whereClause,
      }),
    ]);

    const nextCursor =
      tickets.length === validatePageSize
        ? tickets[tickets.length - 1].id
        : null;

    // Return search results
    return res.status(200).json({
      success: true,
      results: {
        tickets,
        totalTickets,
        nextCursor,
      },
    });
  } catch (error: any) {
    // Handle access errors specifically
    if (error.name === "AccessError") {
      return res.status(error.status || 403).json({
        success: false,
        message: error.message,
      });
    }

    // Handle other errors
    console.error("Search User Tickets error", {
      message: error.message,
      stack: error.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to search for flight tickets. Please try again later.",
    });
  }
};

/**
 * Retrieves a single flight ticket for a user by its refId.
 *
 * @param req - The request object containing the ticket refId.
 * @param res - The response object to send the result.
 * @returns The response with the retrieved ticket or an error message. The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: flightTicketRes,
 * }
 */
export const getSingleUsersTicket = async (req: AuthRequest, res: Response) => {
  const { ticketId } = req.params;
  try {
    // Authorize the user and check their role
    const user = await getAgencyAccess(req, res);

    // If user is null, getAgencyAccess has already sent the response
    if (!user) {
      return; // Exit early without sending another response
    }

    // Get the agency ID based on account type
    let agencyId = user.id;
    let isTeamMember = false;

    // Handle different account types
    const accountType = req.accountType;

    if (accountType === "agencyUser") {
      const agencyUser = user as any;

      if (agencyUser.role === "agency") {
        agencyId = agencyUser.id;
      } else {
        return res.status(404).json({
          success: false,
          message: "Invalid user role for agency access",
        });
      }

      // Check the agency status
      if (agencyUser.accountStatus !== "accepted") {
        return res.status(403).json({
          success: false,
          message: "The agency account is not active",
        });
      }
    } else if (accountType === "masterUser") {
      // For team members, get the agency through the createdBy relationship
      const teamMember = await prisma.teamMember.findUnique({
        where: { id: user.id },
        include: {
          createdBy: {
            select: { id: true },
          },
        },
      });

      if (!teamMember?.createdBy) {
        return res.status(403).json({
          success: false,
          message: "Team member not associated with an agency",
        });
      }
      // The createdBy user is the agency
      agencyId = teamMember.createdBy.id;
      isTeamMember = true;
    }

    // Get single users ticket include all fields
    const ticket = await prisma.flightTicket.findFirst({
      where: {
        refId: ticketId,
        OR: [
          // Tickets owned by the agency/agent
          { ownerId: agencyId },
          // Tickets created by the user
          { createdBy: user.id },
          // Tickets created by any agent in the agency
          {
            agencyAgent: {
              agency: {
                id: agencyId,
              },
            },
          },
          // For team members, also check if they have explicit access
          ...(isTeamMember
            ? [
                {
                  ticketAccess: {
                    some: {
                      teamMemberId: user.id,
                    },
                  },
                },
              ]
            : []),
        ],
      },
      include: {
        ...(detailedTicketSelectors as any),
        // Include ticket access for team members
        ...(isTeamMember
          ? {
              ticketAccess: {
                where: {
                  teamMemberId: user.id,
                },
                select: {
                  id: true,
                  ticketId: true,
                  teamMemberId: true,
                  accessLevel: true,
                  createdAt: true,
                  updatedAt: true,
                },
              },
            }
          : {}),
      },
    });

    // if ticket not found
    if (!ticket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    // Return a single tickets
    return res.status(200).json({ success: true, results: ticket });
  } catch (error) {
    const err = error as Error;
    console.error("Fetch Single User Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    if (!res.headersSent) {
      return res.status(500).json({
        success: false,
        message: "Failed to fetch flight ticket. Please try again later.",
      });
    }
  }
};

/**
 * Delete a user's ticket by its reference ID.
 *
 * @param req - The authenticated request object.
 * @param res - The response object.
 * @returns The response with the deleted ticket or an error message.
 * @returns The response with the deleted ticket or an error message. The response object will have the following structure:
 * {
 *   success: boolean,
 *   message: string,
 *   results: FlightTicketRes,
 * }
 * where `FlightTicketRes` is defined in `@/utils/definitions/ticketDefinitions.ts`.
 */
export const deleteUsersTicket = async (req: AuthRequest, res: Response) => {
  const { ticketId } = req.params;

  try {
    // Authorize the user
    const user = await getAgencyAccess(req, res);

    // Find the flight ticket along with its relations
    const flightTicket = await prisma.flightTicket.findUnique({
      where: {
        refId: ticketId,
        ownerId:
          (user as { agencyId: string }).agencyId ||
          (user as { id: string }).id,
      },
      include: basicTicketSelectors as any,
    });

    // If the ticket is not found
    if (!flightTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    const validTicketStatus = ["pending", "rejected", "blocked"];

    if (!validTicketStatus.includes(flightTicket.ticketStatus)) {
      return res
        .status(400)
        .json({ success: false, message: "Cannot delete valid ticket" });
    }

    // Delete the ticket and its related data
    const deleteTicket = await prisma.$transaction(async (transaction: any) => {
      // First, delete all related TicketHistoryLog records
      await transaction.ticketHistoryLog.deleteMany({
        where: { ticketId: flightTicket.id },
      });

      // First, get all flight classes for this ticket with their relations
      const flightClasses = await transaction.flightClass.findMany({
        where: { flightTicketId: flightTicket.id },
        include: {
          price: true,
          extraOffers: true,
        },
      });

      // Delete all related records
      for (const flightClass of flightClasses) {
        // Delete FlightExtraOffer records first
        if (flightClass.extraOffers && flightClass.extraOffers.length > 0) {
          await transaction.flightExtraOffer.deleteMany({
            where: { flightClassId: flightClass.id },
          });
        }

        // Delete FlightPrice record if it exists
        if (flightClass.price) {
          await transaction.flightPrice.delete({
            where: { flightClassId: flightClass.id },
          });
        }
      }

      // Now it's safe to delete all flight classes
      await transaction.flightClass.deleteMany({
        where: { flightTicketId: flightTicket.id },
      });

      // Delete the ticket's segments and their related locations
      for (const segment of flightTicket.segments as any) {
        // Delete the segment
        await transaction.flightSegment.delete({
          where: { id: segment.id },
        });

        // Delete the departure location if it's not used by other segments
        const departureInUse = await transaction.flightSegment.findFirst({
          where: {
            OR: [
              { departureId: segment.departureId },
              { arrivalId: segment.departureId },
            ],
            id: { not: segment.id },
          },
        });

        if (!departureInUse) {
          await transaction.flightLocation.delete({
            where: { id: segment.departureId },
          });
        }

        // Delete the arrival location if it's not used by other segments
        const arrivalInUse = await transaction.flightSegment.findFirst({
          where: {
            OR: [
              { departureId: segment.arrivalId },
              { arrivalId: segment.arrivalId },
            ],
            id: { not: segment.id },
          },
        });

        if (!arrivalInUse) {
          await transaction.flightLocation.delete({
            where: { id: segment.arrivalId },
          });
        }
      }

      // Finally, delete the ticket itself
      await transaction.flightTicket.delete({
        where: { id: flightTicket.id },
      });

      return flightTicket;
    });

    // Return the deleted ticket
    return res.status(200).json({
      success: true,
      message: "The ticket was deleted successfully",
      results: deleteTicket,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Delete Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to delete flight ticket. Please try again later.",
    });
  }
};

/**
 * Updates a single flight ticket's status. The user making the request must be
 * the owner of the ticket.
 *
 * @param {AuthRequest} req - The request object from Express
 * @param {Response} res - The response object from Express
 * @returns {Promise<Response>} A JSON response with the success status and the
 * updated ticket. The response object will have the following structure:
 * {
 *   success: boolean,
 *   message: string,
 *   results: FlightTicketRes,
 * }
 * where `FlightTicketRes` is defined in `@/utils/definitions/ticketDefinitions.ts`.
 */
export const updateTicketStatus = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { refId } = req.params;
  const { status, seats } = req.body;
  try {
    // authorize the user
    const user = await getAgencyAccess(req, res);

    if (!status) {
      return res
        .status(400)
        .json({ success: false, message: "Status is required" });
    }

    const ticketStatusOptions = ["available", "unavailable"];
    if (!ticketStatusOptions.includes(status)) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid status" });
    }

    // Find the flight ticket along with its relations
    const flightTicket = await prisma.flightTicket.findUnique({
      where: {
        refId: refId,
        ownerId:
          (user as { agencyId: string }).agencyId ||
          (user as { id: string }).id,
      },
      include: basicTicketSelectors as any,
    });

    // if ticket not found
    if (!flightTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    const date = new Date();
    const currentDate = moment(date).format("YYYY-MM-DDTHH:mm:ss.SSS");

    // Check if the flight date has passed
    if (
      flightTicket.departureTime &&
      flightTicket.departureTime < currentDate
    ) {
      return res
        .status(400)
        .json({ success: false, message: "Flight date has passed" });
    }

    // Check if the ticket's previous status was "pending"
    const wasPending = flightTicket.ticketStatus === "pending";

    // Determine the new ticket status
    const getTicketStatus = (): string => {
      if (status === "available" && wasPending) {
        return "accepted";
      }
      return status;
    };

    const updateTicket = await prisma.$transaction(async (transaction: any) => {
      if (!user) {
        throw new Error("User not found");
      }

      // Get the latest ticket history log that has newValue
      const latestHistoryLog = await transaction.ticketHistoryLog.findFirst({
        where: {
          ticketId: flightTicket.id,
          changeType: "update request",
          NOT: {
            newValue: { equals: null },
          },
        },
        orderBy: {
          changedAt: "asc",
        },
      });

      // Prepare the history log data
      const historyLogData: any = {
        changeType: getTicketStatus(),
      };

      // Only add newValue if latestHistoryLog exists and has newValue
      if (latestHistoryLog?.newValue) {
        historyLogData.newValue = latestHistoryLog.newValue;
      }

      // If the action is triggered by an agency agent (`accountType` === "agencyUser")
      // connect the `agencyAgent` relation; otherwise connect the parent `agency`.
      if (req.accountType === "agencyUser" && (req as any).agencyAgent) {
        // Use the authenticated agent's ID (not the parent agency user)
        historyLogData.agencyAgent = {
          connect: { id: (req as any).agencyAgent.id },
        };
      } else {
        historyLogData.agency = { connect: { id: user.id } };
      }

      // Update the ticket
      const updatedTicket = await transaction.flightTicket.update({
        where: { id: flightTicket.id },
        data: {
          ticketStatus: status,
          // Add a new ticket history log
          ticketHistoryLogs: {
            create: historyLogData,
          },
        },
      });
      return updatedTicket;
    });

    // return a single tickets
    return res.status(200).json({
      success: true,
      message: "The ticket was updated successfully",
      results: updateTicket,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Update Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update flight ticket. Please try again later.",
    });
  }
};

/**
 * Updates a single flight ticket with the given input.
 * The user making the request must be the owner of the ticket.
 * The input must contain the refId, the new data for the ticket,
 * and the new segments data.
 * This function will first validate the input, then authorize the user,
 * and finally update the ticket and its segments.
 * @param {AuthRequest} req - The request object containing the refId and the new data.
 * @param {Response} res - The response object to send the result.
 * @returns The response with the updated ticket or an error message.
 * The response object will have the following structure:
 * {
 *   success: boolean,
 *   message: string,
 *   results: FlightTicketRes,
 * }
 * where `FlightTicketRes` is the shape of a flight ticket object returned by Prisma.
 */
export const updateValidTicket = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { refId } = req.params;
  const { updatedTicketReq } = req.body;
  try {
    // authorize the user
    const user = await getAgencyAccess(req, res);

    // Check if user is a master admin
    const isMasterAdmin = (user as any)?.roleType === "master_admin";

    // For master admin, we don't need to check the owner
    const whereClause = isMasterAdmin
      ? { refId: refId }
      : {
          refId: refId,
          ownerId: (user as AgencyAgent)?.agencyId || (user as User)?.id,
        };

    // Find the flight ticket along with its relations
    const flightTicket = await prisma.flightTicket.findUnique({
      where: whereClause,
      include: detailedTicketSelectors as any,
    });

    // if ticket not found
    if (!flightTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    const date = new Date();
    const currentDate = moment(date).format("YYYY-MM-DDTHH:mm:ss.SSS");

    // Check if the flight date has passed
    if (
      flightTicket.departureTime &&
      flightTicket.departureTime < currentDate
    ) {
      return res
        .status(400)
        .json({ success: false, message: "Flight date has passed" });
    }

    // check if the ticket is valid
    const ticketStatusOptions = ["available", "unavailable"];
    if (!ticketStatusOptions.includes(flightTicket.ticketStatus)) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid Ticket" });
    }

    // Validate the inputs
    const { error } = updateTicketValidation.validate(updatedTicketReq, {
      context: {
        isMasterAccepted: flightTicket.manifestId !== null && flightTicket.manifestId !== ""
      },
      abortEarly: false,
    });

    // Return a list of errors
    if (error) {
      const errorDetails = error.details.reduce(
        (acc, detail) => {
          acc[detail.path.join(".")] = detail.message;
          return acc;
        },
        {} as Record<string, string>
      );

      return res
        .status(400)
        .json({ success: false, validationErrors: errorDetails });
    }

    const updatedTicket = await prisma.$transaction(
      async (transaction: any) => {
        // 0. Update the ticket with the updated status
        await transaction.flightTicket.update({
          where: { id: flightTicket.id },
          data: {
            updated: true,
          },
        });

        // 1. Convert numeric fields in the request
        const convertedUpdateReq = convertNumericFields(
          updatedTicketReq,
          trackedFields
        );

        // 2. Get tracked values before update
        const oldValues = pick(flightTicket, trackedFields);

        // 3. Get tracked values after update
        const newValues = pick(convertedUpdateReq, trackedFields);

        // 4. Find only changed fields
        const changes = getDifferences(oldValues, newValues);

        // 5. Create optimized log
        await transaction.ticketHistoryLog.create({
          data: {
            ticketId: flightTicket.id,
            changeType: "update request",
            changeDetails: JSON.stringify(changes),
            oldValue: JSON.stringify(oldValues), // Only tracked fields
            newValue: JSON.stringify(newValues), // Only tracked fields
            agencyId: (user as { id: string }).id,
          },
        });
      }
    );

    // Send notifications to all Master Users about the ticket update
    try {
      const masterOwner = await prisma.user.findFirst({
        where: {
          roleType: "master_owner",
        },
      });

      if (!masterOwner) {
        throw new Error("No Master User found");
      }

      const masterUsers = await prisma.teamMember.findMany({
        where: {
          roleType: { in: ["master_admin", "master_moderator"] },
          status: "active",
        },
        select: {
          id: true,
          email: true,
          roleType: true,
        },
      });

      if (
        (!masterOwner || !masterOwner.id) &&
        (!masterUsers || masterUsers.length === 0)
      ) {
        throw new Error("No Master Users found");
      }

      // Collect all user IDs to notify
      const userIdsToNotify = [
        ...(masterOwner?.id ? [masterOwner.id] : []),
        ...(masterUsers?.map((member) => member.id).filter(Boolean) || []),
      ].filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates

      if (userIdsToNotify.length === 0) {
        return res
          .status(400)
          .json({ success: false, message: "No valid user IDs to notify" });
      }

      // Create notifications for each user
      const notificationPromises = userIdsToNotify.map((userId) => {
        if (!userId) return Promise.resolve();
        return notificationService.createNotification({
          userId: userId, // Assuming userId is the field that links to User
          title: "Ticket Update Request",
          message: `A ticket update request has been submitted by ${user?.firstName} ${user?.lastName} (${user?.agencyName}).`,
          type: "TICKET_REQUEST_UPDATE",
          link: `/master-control/ticket-requests/${flightTicket.refId}`,
          priority: 1,
        });
      });

      // Wait for all notifications to be created
      await Promise.all(notificationPromises);
    } catch (notificationError) {
      // Log the error but don't fail the ticket update
      console.error(
        "Failed to send update notifications to Master Users:",
        notificationError
      );
    }

    // return a single tickets
    return res.status(200).json({
      success: true,
      message: "The ticket was updated successfully",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Update Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update flight ticket. Please try again later.",
    });
  }
};

/**
 * Withdraws a user's update request for a valid ticket.
 *
 * @param {AuthRequest} req - The request object containing the ticket refId.
 * @param {Response} res - The response object to send the result.
 * @returns The response with the updated ticket or an error message.
 * The response object will have the following structure:
 * {
 *   success: boolean,
 *   message: string,
 *   results: FlightTicketRes,
 * }
 * where `FlightTicketRes` is the shape of a flight ticket object returned by Prisma.
 */
export const withdrawUpdateReqValidTicket = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { refId } = req.params;
  try {
    // authorize the user
    const user = await getAgencyAccess(req, res);

    // Get the user's ID and check if they are a master admin
    const userId = (user as any)?.id;
    const isMasterAdmin = (user as any)?.roleType === "master_admin";

    // For master admin, we don't need to check the owner
    const whereClause = isMasterAdmin
      ? { refId: refId }
      : {
          refId: refId,
          ownerId: (user as any)?.agencyId || userId,
        };

    // Find the flight ticket along with its relations
    const flightTicket = await prisma.flightTicket.findUnique({
      where: whereClause,
      include: basicTicketSelectors as any,
    });

    // if ticket not found
    if (!flightTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    const date = new Date();
    const currentDate = moment(date).format("YYYY-MM-DDTHH:mm:ss.SSS");

    // Check if the flight date has passed
    if (
      flightTicket.departureTime &&
      flightTicket.departureTime < currentDate
    ) {
      return res
        .status(400)
        .json({ success: false, message: "Flight date has passed" });
    }

    // check if the ticket is valid
    const ticketStatusOptions = ["available", "unavailable"];
    if (!ticketStatusOptions.includes(flightTicket.ticketStatus)) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid Ticket" });
    }

    const updatedTicket = await prisma.$transaction(
      async (transaction: any) => {
        // update the ticket
        await transaction.flightTicket.update({
          where: { id: flightTicket.id },
          data: {
            updated: false,
          },
        });

        // Log detailed changes to the ticket
        await transaction.ticketHistoryLog.create({
          data: {
            ticketId: flightTicket.id,
            changeType: "withdraw request",
            changeDetails: JSON.stringify({
              comment: "Withdraw update request",
            }),
            agencyId: (user as { id: string }).id,
          },
        });
      }
    );

    // return a single tickets
    return res.status(200).json({
      success: true,
      message: "The ticket was withdrawn successfully",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Update Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update flight ticket. Please try again later.",
    });
  }
};

/**
 * Deeply compares two objects and returns an object showing the differences.
 * @param {Object} oldObj - The original object.
 * @param {Object} newObj - The updated object.
 * @returns {Object} - An object showing the differences.
 */
function getDifferences(oldObj: any, newObj: any): any {
  const changes: any = {};

  function compareObjects(oldVal: any, newVal: any, path: string[] = []) {
    if (
      typeof oldVal !== "object" ||
      oldVal === null ||
      typeof newVal !== "object" ||
      newVal === null
    ) {
      if (oldVal !== newVal) {
        changes[path.join(".")] = { oldValue: oldVal, newValue: newVal };
      }
      return;
    }

    // Compare arrays
    if (Array.isArray(oldVal) && Array.isArray(newVal)) {
      if (oldVal.length !== newVal.length) {
        changes[path.join(".")] = { oldValue: oldVal, newValue: newVal };
      } else {
        for (let i = 0; i < oldVal.length; i++) {
          compareObjects(oldVal[i], newVal[i], [...path, `[${i}]`]);
        }
      }
      return;
    }

    // Compare objects
    for (const key of new Set([
      ...Object.keys(oldVal),
      ...Object.keys(newVal),
    ])) {
      compareObjects(oldVal[key], newVal[key], [...path, key]);
    }
  }

  compareObjects(oldObj, newObj);
  return changes;
}

// 2. Add utility function to pick essential fields
const pick = (obj: any, keys: string[]): Record<string, any> => {
  return keys.reduce(
    (acc, key) => {
      const parts = key.split(".");
      let value = obj;

      for (const part of parts) {
        if (value === undefined || value === null) break;
        if (Array.isArray(value) && /^\d+$/.test(part)) {
          value = value[parseInt(part, 10)];
        } else {
          value = value[part];
        }
      }

      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    },
    {} as Record<string, any>
  );
};

// Add this type conversion helper
const convertNumericFields = (obj: any, paths: string[]) => {
  const converted = { ...obj };
  paths.forEach((path) => {
    const parts = path.split(".");
    let current = converted;

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (i === parts.length - 1 && current[part] !== undefined) {
        // Convert only leaf nodes that are numbers
        const num = Number(current[part]);
        current[part] = isNaN(num) ? current[part] : num;
      }
      current = current[part];
      if (current === undefined) break;
    }
  });
  return converted;
};
