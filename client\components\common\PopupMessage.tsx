"use client";

import React, { useState } from "react";
import { X } from "lucide-react";

interface PopupMessageProps {
  icon: React.ReactElement;
  message: string;
  hideCloseButton?: boolean;
  onClose?: () => void;
}

const PopupMessage = React.memo(({
  icon,
  message,
  hideCloseButton = false,
  onClose,
}: PopupMessageProps) => {
  const [isVisible, setIsVisible] = useState(true);

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) onClose();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="relative rounded-xl shadow-lg max-w-md w-full mx-4 p-6 z-10 bg-white dark:bg-gray-800">
        {!hideCloseButton && (
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-5 w-5" />
          </button>
        )}
        <div className="flex flex-col items-center text-center py-8 h-64 justify-center">
          <div className="mb-4">{icon}</div>
          <p className="text-gray-700 dark:text-gray-300 text-lg font-medium whitespace-pre-line">
            {message}
          </p>
        </div>
      </div>
    </div>
  );
});

export default PopupMessage;
