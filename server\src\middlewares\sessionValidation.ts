import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { prisma } from "../prisma";
import { AuthRequest } from "../utils/definitions";

export const validateSession = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get token from cookie
    const token = req.cookies.token;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "No token provided",
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as {
      id: string;
      type: "user" | "teamMember" | "agencyAgent";
    };

    // Check user/team member status
    const user =
      decoded.type === "user"
        ? await prisma.user.findUnique({
            where: { id: decoded.id },
            select: { accountStatus: true },
          })
        : decoded.type === "teamMember"
          ? await prisma.teamMember.findUnique({
              where: { id: decoded.id },
              select: { status: true },
            })
          : await prisma.agencyAgent.findUnique({
              where: { id: decoded.id },
              select: { status: true },
            });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if user/team member is suspended or inactive
    const status =
      decoded.type === "user"
        ? (user as any).accountStatus
        : (user as any).status;

    if (status === "suspended" || status === "inactive") {
      // Clear the token cookie
      res.clearCookie("token");

      return res.status(401).json({
        success: false,
        message: `Your access has been ${status}. Please contact Airvilla Support for assistance in restoring your access.`,
        forceLogout: true,
      });
    }

    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      res.clearCookie("token");
      return res.status(401).json({
        success: false,
        message: "Token expired",
        forceLogout: true,
      });
    }

    return res.status(401).json({
      success: false,
      message: "Invalid token",
    });
  }
};
