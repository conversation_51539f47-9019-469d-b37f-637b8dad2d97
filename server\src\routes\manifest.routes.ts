import { Router } from 'express';
import { sendManifestEmail, getManifestTickets, downloadManifestPdf, handleDownloadPdf } from '../controllers/manifestController';
import userAuth from "../middlewares/userAuth";

const router = Router();

// Get all tickets with manifest data
router.get('/', userAuth, getManifestTickets);
router.get('/download/:id', downloadManifestPdf);
// router.get('/download/:manifestId', handleDownloadPdf);
router.post('/send-email', sendManifestEmail);

export default router;