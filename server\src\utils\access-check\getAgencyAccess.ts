import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, User } from "@prisma/client";
import { Response } from "express";
import checkUserAuth from "../authorization/checkUserAuth";
import { AuthRequest } from "../definitions";

interface UserWithRole {
  role?: string;
  roleType?: string;
}

// Type for user status response
type UserStatusResponse =
  | { success: true; message: string }
  | { success: false; message: string; status: number };

// Define a type for the status check response
interface StatusCheckResponse {
  success: boolean;
  message: string;
  status?: number;
}

// Function to check the status of a user
const checkUserStatus = (
  user: User | AgencyAgent | TeamMember | null | undefined
): StatusCheckResponse => {
  // Check if the user object is null or undefined
  if (!user) {
    return { success: false, message: "User not found", status: 404 };
  }

  // Check if the user is active (using type assertion for now)
  if ("isActive" in user && user.isActive === false) {
    return { success: false, message: "User is inactive", status: 403 };
  }

  // Check if the user is deleted (using type assertion for now)
  if ("isDeleted" in user && user.isDeleted === true) {
    return { success: false, message: "User is deleted", status: 403 };
  }

  // Check if the user is verified
  if ("verified" in user && !user.verified) {
    return { success: false, message: "User not verified", status: 401 };
  }

  // Check if the user's account status is accepted
  if ("accountStatus" in user && user.accountStatus !== "accepted") {
    return { success: false, message: "User not approved", status: 403 };
  }

  // Check if the user is an affiliate
  if (user.role === "affiliate") {
    return {
      success: false,
      message: "Affiliate access not allowed",
      status: 403,
    };
  }

  // If all checks pass, return success with message
  return { success: true, message: "User is active" };
};

// Function to handle affiliate access based on user status
const getAgencyAccess = async (
  req: AuthRequest,
  res: Response
): Promise<User | AgencyAgent | TeamMember | null> => {
  try {
    // C heck if the user is authenticated
    let user: User | AgencyAgent | TeamMember | null = null;
    // Get the user from the request if available
    const requestUser = req.user;

    if (req.accountType === "masterOwner") {
     user = (await checkUserAuth(req, res, "masterOwner")) as User;
    } else if (req.accountType === "masterUser") {
      user = (await checkUserAuth(req, res, "masterUser")) as TeamMember;
    } else if (req.accountType === "agencyOwner") {
      user = (await checkUserAuth(req, res, "agencyOwner")) as User;
    } else if (req.accountType === "agencyUser") {
      user = (await checkUserAuth(req, res, "agencyUser")) as AgencyAgent;
    } else {
      res.status(403).json({
        success: false,
        message: "Invalid account type for agency access",
      });
      return null;
    }

    // Get the authenticated user based on account type
    try {
      let authenticatedUser: User | TeamMember | AgencyAgent | null = null;

      // First, handle the case where we already have the user from the request
      if (requestUser) {
        authenticatedUser = requestUser;
      } else {
        // Normalize account type for comparison
        const normalizedAccountType = req.accountType;

        // Use the correct checkUserAuth overload based on account type
        if (normalizedAccountType === "agencyOwner") {
          authenticatedUser = await checkUserAuth(req, res, "agencyOwner");
        } else if (normalizedAccountType === "masterOwner") {
          authenticatedUser = await checkUserAuth(req, res, "masterOwner");
        } else if (normalizedAccountType === "masterUser") {
          authenticatedUser = await checkUserAuth(req, res, "masterUser");
        } else if (normalizedAccountType === "agencyUser") {
          authenticatedUser = await checkUserAuth(req, res, "agencyUser");
        } else {
          res.status(403).json({
            success: false,
            message: `Invalid account type for agency access. Received: ${JSON.stringify(
              {
                normalizedAccountType,
                reqAccountType: req.accountType,
                userType: (req as any)?.user?.type,
                userRole: (req as any)?.user?.role,
              }
            )}`,
          });
          return null;
        }
      }

      if (!authenticatedUser) {
        throw new Error(
          "Failed to authenticate user: No user returned from authentication"
        );
      }

      if (!authenticatedUser.id) {
        throw new Error("Authenticated user has no ID");
      }

      user = authenticatedUser;
    } catch (error) {
      // Log the error and send a 500 internal server error response
      console.error(error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Internal server error",
          // debug: {
          //   hasAuthHeader: !!req.headers.authorization,
          //   token: req.headers.authorization?.split(' ')[1] ? 'Token present' : 'No token'
          // }
        });
      }
      return null;
    }

    // Check the user's status
    const status = checkUserStatus(user);
    if (!status.success) {
      if (!res.headersSent) {
        const statusCode =
          status && "status" in status && status.status ? status.status : 403;
        res.status(statusCode).json({
          success: false,
          message: status?.message || "Access denied",
        });
      }
      return null;
    }

    // If all checks pass, return the user
    return user;
  } catch (error) {
    // Log the error and send a 500 internal server error response
    console.error(error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
    return null;
  }
};

export default getAgencyAccess;
