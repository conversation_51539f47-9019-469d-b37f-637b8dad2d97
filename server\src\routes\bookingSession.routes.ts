import express, { Request, Response } from "express";
import userAuth from "../middlewares/userAuth";

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}
import {
  startBookingSession,
  validateBookingSession,
} from "../utils/schedule/bookingSessionTracker";

const router = express.Router();

/**
 * @openapi
 * /booking-session/start:
 *   post:
 *     tags:
 *       - BookingSession
 *     summary: Start a new booking session
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Booking session started
 */
router.post("/start", userAuth, async (req: Request, res: Response) => {
  try {
    const authenticatedReq = req as AuthenticatedRequest;
    const { token, expiryTime } = startBookingSession(authenticatedReq.user.id);
    res.json({ success: true, token, expiryTime });
  } catch (error) {
    res
      .status(500)
      .json({ success: false, message: "Failed to start booking session" });
  }
});

/**
 * @openapi
 * /booking-session/validate:
 *   post:
 *     tags:
 *       - BookingSession
 *     summary: Validate a booking session
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Booking session validated
 */
router.post("/validate", userAuth, async (req, res) => {
  try {
    const token = req.body.token || req.headers.authorization?.split(" ")[1];
    const isValid = validateBookingSession(token);
    res.json({ success: true, isValid });
  } catch (error) {
    res
      .status(500)
      .json({ success: false, message: "Failed to validate booking session" });
  }
});

export default router;
