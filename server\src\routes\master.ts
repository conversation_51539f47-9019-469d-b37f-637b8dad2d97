import { Router } from "express";
const router = Router();
import {
  getAllUsers,
  getSingleUserById,
  softDeleteSingleUser,
  hardDeleteSingleUser,
  searchAllUsers,
  userAccountRequest,
  updateUserInfoForMaster,
  updateUserPasswordForMaster,
} from "../controllers/masterUsersController";
import userAuth from "../middlewares/userAuth";
import {
  deleteSingleMasterTicket,
  getAllMasterTickets,
  getSingleMasterTicket,
  updateTicketStatus,
  updateValidTicket,
  rescheduleTicket,
} from "../controllers/masterTicketsController";
import {
  getAgencyNames,
  getTotalTickets,
  getTotalUsers,
} from "../controllers/masterDashboardController";
import { fetchAllAgents } from "../controllers/agencyController";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

// authorize the user
router.use(userAuth);
// router.use(enterpriseApiLimiter);

// master dashboard
/**
 * @openapi
 * /master/tickets/total:
 *   get:
 *     tags:
 *       - Master
 *     summary: Get total tickets
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Total tickets
 *
 * /master/users/total:
 *   get:
 *     tags:
 *       - Master
 *     summary: Get total users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Total users
 *
 * /master/agencyNames:
 *   get:
 *     tags:
 *       - Master
 *     summary: Get agency names
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agency names
 */
router.get("/tickets/total", getTotalTickets);
router.get("/users/total", getTotalUsers);
router.get("/agencyNames", getAgencyNames);

// master users
router.get("/users", getAllUsers);
router.get("/users/search", searchAllUsers);
router.get("/users/:userId", getSingleUserById);
router.put("/users/:userId", updateUserInfoForMaster);
router.put("/users/:userId/request", userAccountRequest);
router.put("/users/:userId/password", updateUserPasswordForMaster);
router.delete("/users/:userId/hard-delete", hardDeleteSingleUser);
router.delete("/users/:userId/soft-delete", softDeleteSingleUser);

//master tickets
router.post("/tickets", getAllMasterTickets);
router.get("/tickets/:refId", getSingleMasterTicket);
router.put("/tickets/:refId/status", updateTicketStatus);
router.put("/tickets/:refId/valid", updateValidTicket);
router.put("/tickets/:refId/reschedule", rescheduleTicket);
router.delete("/tickets/:refId", deleteSingleMasterTicket);

// master agents of an agency
router.get("/agents/:userId", fetchAllAgents); // For master users to fetch specific agency's agents

export default router;
