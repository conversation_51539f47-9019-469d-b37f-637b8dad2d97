"use client";
import "./css/style.css";

import { Inter } from "next/font/google";
import Theme from "./theme-provider";
import AppProvider from "./app-provider";
import StoreProvider from "../redux/StoreProvider";
import MessageBox from "@/components/extra-components/MessageBox";
import Loading from "@/components/common/Loading";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v13-appRouter";
import { SocketProvider } from "@/context/SocketContext";
import { NotificationProvider } from "@/context/NotificationContext";
import { useUser } from "@/components/hooks/useUser";
import LoginLoadingProgress from "@/components/extra-components/LoginLoadingProgress";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

// export const metadata = {
//   title: "Airvilla Charter",
//   description: "Airvilla Charter Flight App",
// };

function NotificationProviderWrapper({ children }: { children: React.ReactNode }) {
  const user = useUser();
  if (!user) return <>{children}</>;
  return <NotificationProvider userId={user.id}>{children}</NotificationProvider>;
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* suppressHydrationWarning: https://github.com/vercel/next.js/issues/44343 */}
      <body
        suppressHydrationWarning
        className="antialiased bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"
      >
        <StoreProvider>
          <Theme>
            <AppProvider>
              <AppRouterCacheProvider>
                <SocketProvider>
                  <NotificationProviderWrapper>
                    <Loading />
                    <LoginLoadingProgress />
                    {children}
                    <MessageBox />
                  </NotificationProviderWrapper>
                </SocketProvider>
              </AppRouterCacheProvider>
            </AppProvider>
          </Theme>
        </StoreProvider>
      </body>
    </html>
  );
}
