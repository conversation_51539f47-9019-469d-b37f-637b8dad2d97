import { Router } from "express";
import {
  createInvitation,
  updateInvitation,
  revokeInvitation,
  acceptInvitation,
} from "../controllers/invitationController";

const router = Router();

import userAuth from "../middlewares/userAuth";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

// router.use(userAuth);
// router.use(enterpriseApiLimiter);

/**
 * @openapi
 * /invitation/invitations:
 *   post:
 *     tags:
 *       - Invitation
 *     summary: Create invitation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Invitation created
 *   put:
 *     tags:
 *       - Invitation
 *     summary: Update invitation status
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Invitation updated
 *   delete:
 *     tags:
 *       - Invitation
 *     summary: Revoke invitation
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Invitation revoked
 *
 * /invitation/accept:
 *   post:
 *     tags:
 *       - Invitation
 *     summary: Accept invitation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Invitation accepted
 */
router.post("/invitations", createInvitation); // Create invitation
router.post("/accept", acceptInvitation);
router.put("/invitations/:id", updateInvitation); // Update invitation status
router.delete("/invitations/:id", revokeInvitation); // Revoke invitation

export default router;
