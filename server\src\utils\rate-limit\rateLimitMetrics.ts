// Prometheus metrics for rate limiting
import client from 'prom-client';

export const rateLimitHits = new client.Counter({
  name: 'rate_limit_hits_total',
  help: 'Total number of rate limit hits',
  labelNames: ['tier', 'endpoint', 'ip', 'userId'],
});

export const rateLimitBlocks = new client.Counter({
  name: 'rate_limit_blocks_total',
  help: 'Total number of rate limit blocks (429)',
  labelNames: ['tier', 'endpoint', 'ip', 'userId'],
});

export const rateLimitErrors = new client.Counter({
  name: 'rate_limit_errors_total',
  help: 'Total number of rate limit store errors',
  labelNames: ['tier', 'endpoint', 'ip', 'userId'],
});
