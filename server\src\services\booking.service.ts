import {
  BookingType,
  BookingStatus,
  Booking,
  BookedFlightSeatStatus,
  ActorType,
  CustomerInfoTitle,
  CustomerDocumentType,
  BookingSource,
  ETicket,
  Receipt,
  PrismaClient,
} from "@prisma/client";
import {
  BookingMeta,
  BookingTravelerInput,
  CreateInternalBookingDto,
} from "../types/booking.types";
import { customAlphabet, nanoid } from "nanoid";
import { createBookingSchema } from "../utils/validators/booking.validator";
import { createId } from "@paralleldrive/cuid2";
import NotificationService from "../utils/services/notification.service";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
import { CreditTransactionType } from "../utils/constants/booking";
import { nanoid as cuidNanoId } from "nanoid"; // For placeholder refund transaction IDs if not already aliased
import { mapClientPaymentToDbPayment } from "../utils/mappers/payment.mapper";
import { getAssociatedAccountIds } from "../utils/association";
import memoryMonitor from "../utils/memoryMonitor";
import { getCreatedTimeRange } from "../utils/functions";
import { getAgentAgencyIdFromRequest } from "../utils/agent.utils";

// Placeholder for actual payment gateway interaction
async function processPaymentGatewayRefund(
  bookingId: string,
  amount: number
): Promise<{ success: boolean; transactionId?: string; error?: string }> {
  console.log(
    `Attempting to refund ${amount} for booking ${bookingId} via payment gateway.`
  );
  // In a real scenario, this would involve API calls to Stripe, PayPal, etc.
  // For now, simulate success.
  await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate async delay
  return { success: true, transactionId: `pg_refund_${cuidNanoId()}` };
}

const nanoidAlphaNum = customAlphabet(
  "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
  10
);

export class BookingService {
  // Helper function to check if a flight date is in the past
  private isFlightDateInPast(flightDate: Date | string): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate date comparison

    const flightDateObj =
      typeof flightDate === "string" ? new Date(flightDate) : flightDate;

    // Compare dates (ignoring time)
    return flightDateObj < today;
  }

  // Helper function to calculate traveler type based on date of birth
  private calculateTravelerType(
    dateOfBirth: string
  ): "INFANT" | "CHILD" | "ADULT" {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();

    // Calculate age in years
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    // Adjust age if birthday hasn't occurred yet this year
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    if (age < 2) return "INFANT";
    if (age < 5) return "CHILD";
    return "ADULT";
  }
  /**
   * Creates a new internal booking request for a flight ticket.
   * - Validates input data and ticket existence
   * - Ensures atomic seat assignment and prevents double-booking
   * - Handles seat decrement, notifications, and booking history logs
   * @param agentId - ID of the booking agent
   * @param bookingData - Data required to create the booking (travelers, seats, etc.)
   * @returns The created booking object
   * @throws Error if validation fails or seats are unavailable
   */
  async createInternalBooking(
    agentId: string,
    bookingData: CreateInternalBookingDto
  ): Promise<{
    booking: Booking;
    eTicket: ETicket | null;
    receipt: Receipt | null;
  }> {
    if (!agentId) throw new Error("Agent ID is required.");
    if (!bookingData) throw new Error("Booking data is required.");

    const {
      ticketId,
      returnTicketId,
      userId,
      agencyAgentId,
      teamMemberId,
      type,
      travelers,
      seats,
      tripType,
    } = bookingData;
    // Validate booking data using Joi schema
    const { error } = createBookingSchema.validate(bookingData, {
      abortEarly: false,
    });
    if (error) {
      const messages = error.details.map((d) => d.message).join("; ");
      throw new Error(`Booking validation error: ${messages}`);
    }

    // Validate return ticket for round-trip bookings
    if (tripType === "ROUND_TRIP" && !returnTicketId) {
      throw new Error("Return ticket ID is required for round-trip bookings");
    }

    // --- Business Logic & DB Actions ---
    try {
      // Start a transaction for atomic seat management
      // Calculate total price of all seats
      // const totalAmount = seats.reduce((sum, seat) => sum + seat.totalPrice, 0);
      const internalBooking = await prisma.$transaction(async (tx: any) => {
        let agentName = await tx.user.findUnique({
          where: { id: userId },
          select: { firstName: true, lastName: true },
        });
        if (!agentName) {
          agentName = await tx.teamMember.findUnique({
            where: { id: userId },
            select: { firstName: true, lastName: true },
          });
          if (!agentName) {
            agentName = await tx.agencyAgent.findUnique({
              where: { id: userId },
              select: { firstName: true, lastName: true },
            });
          }
        }

        const wallet = await tx.user.findUnique({
          where: { id: userId },
        });
        // // 0. Wallet balance check
        // const wallet = await tx.user.findUnique({ where: { id: agentId } });
        // if (!wallet || wallet.creditBalance.lt(totalAmount)) {
        //   const err: any = new Error("Insufficient wallet balance.");
        //   err.code = "INSUFFICIENT_WALLET";
        //   err.status = 402;
        //   throw err;
        // }
        // await tx.user.update({
        //   where: { id: agentId },
        //   data: { creditBalance: { decrement: totalAmount } },
        // });
        // 1. Validate ticket existence and fetch seat info with row-level locking
        const ticket = await tx.flightTicket.findUnique({
          where: { id: ticketId },
          select: {
            id: true,
            seats: true,
            remainingSeats: true,
            segments: true,
            departure: true,
            arrival: true,
            duration: true,
            stops: true,
            owner: true,
            ticketStatus: true,
            flightDate: true,
            departureTime: true,
          },
        });
        if (!ticket) throw new Error("Flight ticket not found.");

        // Check if flight date is in the past
        if (this.isFlightDateInPast(ticket.flightDate)) {
          const error: any = new Error(
            `Cannot book a flight that has already departed. The flight date ${new Date(ticket.flightDate).toLocaleDateString()} is in the past.`
          );
          error.code = "FLIGHT_DEPARTED";
          error.status = 400;
          throw error;
        }

        // Check if flight is within 6 hours from now
        const now = new Date();
        const sixHoursFromNow = new Date(now.getTime() + 6 * 60 * 60 * 1000);
        const departureTime = new Date(ticket.departureTime);

        if (departureTime < sixHoursFromNow) {
          const error: any = new Error(
            `Cannot book a flight that departs in less than 6 hours. The flight departs at ${departureTime.toLocaleString()}.`
          );
          error.code = "FLIGHT_TOO_SOON";
          error.status = 400;
          throw error;
        }

        let returnTicket;
        if (tripType === "ROUND_TRIP") {
          returnTicket = await tx.flightTicket.findUnique({
            where: { id: returnTicketId },
            select: {
              id: true,
              seats: true,
              remainingSeats: true,
              segments: true,
              departure: true,
              arrival: true,
              duration: true,
              stops: true,
              owner: true,
              ticketStatus: true,
              flightDate: true,
              departureTime: true,
            },
          });
          if (!returnTicket) throw new Error("Return flight ticket not found.");

          // Check if return flight date is in the past
          if (this.isFlightDateInPast(returnTicket.flightDate)) {
            const error: any = new Error(
              `Cannot book a return flight that has already departed. The return flight date ${new Date(returnTicket.flightDate).toLocaleDateString()} is in the past.`
            );
            error.code = "RETURN_FLIGHT_DEPARTED";
            error.status = 400;
            throw error;
          }
          // Check if return flight is within 6 hours from now
          if (returnTicket) {
            const returnDepartureTime = new Date(returnTicket.departureTime);
            if (returnDepartureTime < sixHoursFromNow) {
              const error: any = new Error(
                `Cannot book a return flight that departs in less than 6 hours. The return flight departs at ${returnDepartureTime.toLocaleString()}.`
              );
              error.code = "RETURN_FLIGHT_TOO_SOON";
              error.status = 400;
              throw error;
            }
          }
        }

        // 2. Validate ticket status
        if (ticket.ticketStatus === "unavailable") {
          const error: any = new Error(
            "This ticket is no longer available for booking."
          );
          error.code = "TICKET_UNAVAILABLE";
          error.status = 410; // Gone
          throw error;
        }

        if (
          tripType === "ROUND_TRIP" &&
          returnTicket.ticketStatus === "unavailable"
        ) {
          const error: any = new Error(
            "This return ticket is no longer available for booking."
          );
          error.code = "RETURN_TICKET_UNAVAILABLE";
          error.status = 410; // Gone
          throw error;
        }

        // 3. Validate seat availability for this booking
        const requestedSeatCount = Array.isArray(travelers)
          ? travelers.length
          : 0;

        if (requestedSeatCount === 0) {
          const error: any = new Error("No seats selected for booking.");
          error.code = "NO_SEATS_SELECTED";
          error.status = 400;
          throw error;
        }

        // 4. Check if we have enough seats available
        if (ticket.remainingSeats < requestedSeatCount) {
          const error: any = new Error(
            `Not enough available seats for this booking. Requested ${requestedSeatCount} but only ${ticket.remainingSeats} available.`
          );
          error.code = "NOT_ENOUGH_SEATS";
          error.status = 409;
          error.availableSeats = ticket.remainingSeats;
          error.requestedSeats = requestedSeatCount;
          throw error;
        }

        if (tripType === "ROUND_TRIP") {
          if (returnTicket.remainingSeats < requestedSeatCount) {
            const error: any = new Error(
              `Not enough available seats for this booking. Requested ${requestedSeatCount} but only ${returnTicket.remainingSeats} available.`
            );
            error.code = "NOT_ENOUGH_SEATS";
            error.status = 409;
            error.availableSeats = returnTicket.remainingSeats;
            error.requestedSeats = requestedSeatCount;
            throw error;
          }
        }

        // 5. Set timer based on booking type (15 or 60 minutes)
        const timerDuration =
          (bookingData.type as any) === BookingType.SUBMIT_BOOKING ? 15 : 60;
        const expiresAt = new Date(Date.now() + timerDuration * 60 * 1000);

        // 6. Atomically update remainingSeats and check ticket status
        const maxRetries = 3;
        let retryCount = 0;
        let updatedTicket;
        let lastError;

        while (retryCount < maxRetries) {
          try {
            updatedTicket = await tx.flightTicket.update({
              where: {
                id: ticketId,
                // Ensure the remainingSeats hasn't changed since we checked
                remainingSeats: { gte: requestedSeatCount },
                // Ensure ticket is still available
                ticketStatus: { not: "unavailable" },
              },
              data: {
                remainingSeats: {
                  decrement: requestedSeatCount,
                },
                // If this update would make remainingSeats 0, also update status
                ...(ticket.remainingSeats === requestedSeatCount
                  ? { ticketStatus: "unavailable" }
                  : {}),
              },
              select: {
                remainingSeats: true,
                ticketStatus: true,
              },
            });
            // If we get here, the update was successful
            break;
          } catch (error: any) {
            lastError = error;
            retryCount++;

            // If it's not a connection error or we've reached max retries, rethrow
            if (
              (error.code !== "ECONNRESET" && error.code !== "P2024") ||
              retryCount >= maxRetries
            ) {
              // Log the error for debugging
              console.error(
                `Failed to update flight ticket after ${retryCount} attempts:`,
                error
              );
              throw error;
            }

            // Wait for a short delay before retrying (exponential backoff)
            const delay = Math.min(500 * Math.pow(2, retryCount), 5000);
            await new Promise((resolve) => setTimeout(resolve, delay));
          }
        }
        if (!updatedTicket) {
          throw (
            lastError ||
            new Error("Failed to update flight ticket after multiple attempts")
          );
        }

        let updatedReturnTicket;
        if (tripType === "ROUND_TRIP") {
          const maxRetries = 3;
          let retryCount = 0;
          let lastError;

          while (retryCount < maxRetries) {
            try {
              updatedReturnTicket = await tx.flightTicket.update({
                where: {
                  id: returnTicketId,
                  // Ensure the remainingSeats hasn't changed since we checked
                  remainingSeats: { gte: requestedSeatCount },
                  // Ensure ticket is still available
                  ticketStatus: { not: "unavailable" },
                },
                data: {
                  remainingSeats: {
                    decrement: requestedSeatCount,
                  },
                  // If this update would make remainingSeats 0, also update status
                  ...(returnTicket.remainingSeats === requestedSeatCount
                    ? { ticketStatus: "unavailable" }
                    : {}),
                },
                select: {
                  remainingSeats: true,
                  ticketStatus: true,
                },
              });
              // If we get here, the update was successful
              break;
            } catch (error: any) {
              lastError = error;
              retryCount++;

              // If it's not a connection error or we've reached max retries, rethrow
              if (
                (error.code !== "ECONNRESET" && error.code !== "P2024") ||
                retryCount >= maxRetries
              ) {
                // Log the error for debugging
                console.error(
                  `Failed to update flight ticket after ${retryCount} attempts:`,
                  error
                );
                throw error;
              }

              // Wait for a short delay before retrying (exponential backoff)
              const delay = Math.min(500 * Math.pow(2, retryCount), 5000);
              await new Promise((resolve) => setTimeout(resolve, delay));
            }
          }
          // if (!updatedReturnTicket) {
          //   throw lastError || new Error('Failed to update flight ticket after multiple attempts');
          // }

          // If no rows were updated, it means the ticket was modified by another transaction
          if (!updatedReturnTicket) {
            // Get the latest ticket info for a more accurate error message
            const currentReturnTicket = await tx.flightTicket.findUnique({
              where: { id: returnTicketId },
              select: { remainingSeats: true, ticketStatus: true },
            });

            const availableSeats = currentReturnTicket?.remainingSeats || 0;
            const requestedSeats = requestedSeatCount;

            const error: any = new Error(
              `Not enough available seats for this booking. Requested ${requestedSeats} but only ${availableSeats} available.`
            );
            error.code = "NOT_ENOUGH_SEATS";
            error.status = 409;
            error.availableSeats = availableSeats;
            error.requestedSeats = requestedSeats;
            throw error;
          }
        }

        // If no rows were updated, it means the ticket was modified by another transaction
        if (!updatedTicket) {
          // Get the latest ticket info for a more accurate error message
          const currentTicket = await tx.flightTicket.findUnique({
            where: { id: ticketId },
            select: { remainingSeats: true, ticketStatus: true },
          });

          const availableSeats = currentTicket?.remainingSeats || 0;
          const error: any = new Error(
            `Not enough available seats for this booking. Requested ${requestedSeatCount} but only ${availableSeats} available.`
          );
          error.code = "NOT_ENOUGH_SEATS";
          error.status = 409;
          error.availableSeats = availableSeats;
          error.requestedSeats = requestedSeatCount;
          throw error;
        }

        // 5. Create travelers if needed
        let travelerIds: string[] = [];
        if (Array.isArray(travelers) && travelers.length > 0) {
          for (const t of travelers) {
            let travelerId = t.travelerId;
            if (!travelerId) {
              // Defensive date validation and type narrowing
              const isIsoDate = (date: string) =>
                typeof date === "string" && /^\d{4}-\d{2}-\d{2}$/.test(date);
              if (
                !t.dateOfBirth ||
                !t.expirationDate ||
                !isIsoDate(t.dateOfBirth) ||
                !isIsoDate(t.expirationDate)
              ) {
                throw new Error(
                  "Traveler dateOfBirth and expirationDate must be valid ISO dates (YYYY-MM-DD)."
                );
              }
              // Convert 'YYYY-MM-DD' to JS Date object (ISO 8601)
              const toIsoDateTime = (date: string) =>
                new Date(date + "T00:00:00.000Z");
              const newTraveler = await tx.traveler.create({
                data: {
                  title: t.title,
                  firstName: t.firstName,
                  lastName: t.lastName,
                  nationality: t.nationality,
                  dateOfBirth: toIsoDateTime(t.dateOfBirth),
                  gender: t.gender,
                  documentType: "passport",
                  documentNumber: t.documentNumber,
                  issuingCountry: t.issuingCountry,
                  expirationDate: toIsoDateTime(t.expirationDate),
                  contactEmail: t.contactEmail,
                  contactPhone: t.contactPhone,
                },
              });
              travelerId = newTraveler.id;
            }
            if (travelerId) {
              travelerIds.push(travelerId);
            }
          }
        }

        // 0. Wallet balance check - Fetch from associated agency owner
        let walletOwnerId = agentId;
        const teamMember = await tx.teamMember.findUnique({
          where: { id: agentId },
          select: { createdById: true },
        });
        if (teamMember && teamMember.createdById) {
          walletOwnerId = teamMember.createdById;
        } else {
          const agencyAgent = await tx.agencyAgent.findUnique({
            where: { id: agentId },
            select: { agencyId: true },
          });
          if (agencyAgent && agencyAgent.agencyId) {
            walletOwnerId = agencyAgent.agencyId;
          }
        }
        // 6. Create Booking with nested travelers and bookedSeats
        const booking = await tx.booking.create({
          data: {
            id: `IB-${nanoidAlphaNum()}`,
            ticketId,
            userId,
            teamMemberId,
            source: bookingData.source,
            agencyAgentId: agentId,
            sellerAgencyId: ticket.ownerId || walletOwnerId, // Set seller agency
            buyerAgencyId: walletOwnerId,
            requestId: `BR-${nanoidAlphaNum()}`,
            referenceNumber: `BR-${nanoidAlphaNum()}`,
            type,
            tripType,
            status: BookingStatus.QUICK_HOLD,
            timerDuration,
            timerStartedAt: new Date(),
            expiresAt,
            // meta: {
            //   carrier: ticket.segments[0].carrier,
            //   flightNumber: ticket.segments[0].flightNumber,
            //   departureAirport: ticket.departure?.airportCode,
            //   arrivalAirport: ticket.arrival?.airportCode,
            //   buyerAgencyName: wallet?.agencyName,
            //   sellerName: `${ticket.owner?.firstName} ${ticket.owner?.lastName}`,
            //   buyerAgentName: `${agentName?.firstName} ${agentName?.lastName}`,
            //   sellerAgencyName: ticket.owner?.agencyName,
            // },
            meta: this.generateBookingMeta(
              ticket, // Departure ticket
              tripType === "ROUND_TRIP" ? returnTicket : null, // Return ticket if exists
              seats, // All seats (they should have legType)
              tripType, // 'ONE_WAY' or 'ROUND_TRIP'
              wallet,
              walletOwnerId,
              agentName
            ),
            ...(travelerIds.length > 0 && {
              travelers: {
                create: travelerIds.map((travelerId) => ({
                  traveler: { connect: { id: travelerId } },
                })),
              },
            }),
            bookedSeats: {
              create: seats.map((seat, idx) => ({
                seatNumber: seat.seatNumber,
                flightClass: seat.flightClass,
                seatStatus: BookedFlightSeatStatus.booked,
                totalPrice: seat.totalPrice,
                bookedByAgency: agencyAgentId ?? userId,
                flightTicket: { connect: { id: ticketId } },
                traveler: { connect: { id: travelerIds[idx] } },
              })),
            },
          },
          include: {
            travelers: true,
            bookedSeats: true,
          },
        });

        let returnBooking;
        if (tripType === "ROUND_TRIP") {
          // // Create a new array of traveler IDs for the return booking to avoid unique constraint violation
          // const returnTravelerIds = await Promise.all(
          //   travelerIds.map(async (travelerId) => {
          //     const traveler = await tx.traveler.findUnique({
          //       where: { id: travelerId },
          //     });

          //     // Create a new traveler record with the same details but a new ID
          //     const newTraveler = await tx.traveler.create({
          //       data: {
          //         ...traveler,
          //         id: undefined, // Let Prisma generate a new ID
          //         createdAt: new Date(),
          //         updatedAt: new Date(),
          //       },
          //     });

          //     return newTraveler.id;
          //   })
          // );

          // Create new traveler records for the return booking first
          const createdTravelers = await Promise.all(
            (travelers || []).map((traveler, idx) => {
              // Convert date strings to Date objects and then to ISO strings
              const dob = new Date(traveler.dateOfBirth);
              const expDate = traveler.expirationDate
                ? new Date(traveler.expirationDate)
                : null;
              const travelerType = this.calculateTravelerType(
                dob.toISOString()
              );
              return tx.traveler.create({
                data: {
                  id: `temp-return-traveler-${idx}-${Date.now()}`,
                  firstName: traveler.firstName,
                  lastName: traveler.lastName,
                  documentNumber: traveler.documentNumber,
                  contactEmail: traveler.contactEmail,
                  contactPhone: traveler.contactPhone,
                  dateOfBirth: dob.toISOString(), // Convert to ISO string
                  gender: traveler.gender,
                  nationality: traveler.nationality,
                  expirationDate: expDate ? expDate.toISOString() : null,
                  title: traveler.title,
                  documentType: traveler.documentType.toLowerCase() as any,
                  issuingCountry: traveler.issuingCountry,
                  primaryContact: traveler.isPrimary || false,
                  type: travelerType,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              });
            })
          );

          // Generate a single request ID for both outbound and return bookings
          const roundTripReferenceNumber = `BC-${nanoidAlphaNum()}`;

          // Update the outbound booking with the round-trip request ID
          await tx.booking.update({
            where: { id: booking.id },
            data: { referenceNumber: roundTripReferenceNumber },
          });

          returnBooking = await tx.booking.create({
            data: {
              id: `IB-${nanoidAlphaNum()}`,
              ticketId: returnTicketId,
              userId,
              teamMemberId,
              source: bookingData.source,
              agencyAgentId: agentId,
              sellerAgencyId: returnTicket.ownerId || walletOwnerId, // Set seller agency
              buyerAgencyId: walletOwnerId,
              requestId: `BR-${nanoidAlphaNum()}`,
              referenceNumber: roundTripReferenceNumber,
              type,
              tripType,
              status: BookingStatus.QUICK_HOLD,
              timerDuration,
              timerStartedAt: new Date(),
              expiresAt,
              meta: this.generateBookingMeta(
                ticket, // Departure ticket
                returnTicket, // Return ticket if exists
                seats, // All seats (they should have legType)
                tripType, // 'ONE_WAY' or 'ROUND_TRIP'
                wallet,
                walletOwnerId,
                agentName
              ),
              // Connect to existing travelers for the return booking
              // Connect travelers through the junction table
              travelers: {
                create: createdTravelers.map((traveler) => ({
                  traveler: {
                    connect: { id: traveler.id },
                  },
                })),
              },
              bookedSeats: {
                create: seats.map((seat, idx) => ({
                  seatNumber: seat.seatNumber,
                  flightClass: seat.flightClass,
                  seatStatus: BookedFlightSeatStatus.booked,
                  totalPrice: seat.totalPrice,
                  bookedByAgency: agencyAgentId ?? userId,
                  flightTicket: { connect: { id: returnTicketId } },
                  traveler: { connect: { id: createdTravelers[idx].id } },
                })),
              },
            },
            include: {
              travelers: true,
              bookedSeats: true,
            },
          });
        }

        // 7. Emit notification
        await tx.notification.create({
          data: {
            id: createId(),
            createdAt: new Date(),
            title: "Booking Created",
            userId: booking.userId,
            bookingId: booking.id,
            type: booking.type,
            message: `Your internal booking ( ${booking.requestId} ) has been created.`,
            priority: 1,
            link: `/booking/${booking.id}`,
            relatedId: booking.id,
            agencyAgentId: null,
            read: false,
            teamMemberId: null,
          },
        });
        // 9. Log action
        await tx.bookingHistoryLog.create({
          data: {
            bookingId: booking.id,
            changeType: "CREATE",
            reason: "The booking was created successfully.",
            actorId: null,
            actorType: ActorType.SYSTEM,
            oldStatus: null,
            newStatus: booking.status,
          },
        });

        // 11. Log ticket action
        await tx.ticketHistoryLog.create({
          data: {
            ticketId: ticketId,
            changeType: "Seats Updated",
            changeDetails: JSON.stringify({
              comment: `Seats temporarily held via internal booking process ( ${booking.requestId} ) \n by ${agentName.firstName} ${agentName.lastName}.`,
            }),
            oldValue: JSON.stringify({
              remainingSeats: ticket.remainingSeats,
              seats: ticket.seats,
            }),
            newValue: JSON.stringify({
              remainingSeats: updatedTicket.remainingSeats,
              seats: updatedTicket.seats,
            }),
            agencyId: booking.buyerAgencyId,
            agencyAgentId: agentId,
          },
        });

        if (tripType === "ROUND_TRIP") {
          await tx.ticketHistoryLog.create({
            data: {
              ticketId: returnTicketId,
              changeType: "Seats Updated",
              changeDetails: JSON.stringify({
                comment: `Seats temporarily held via internal booking process ( ${booking.requestId} ) \n by ${agentName.firstName} ${agentName.lastName}.`,
              }),
              oldValue: JSON.stringify({
                remainingSeats: returnTicket.remainingSeats,
                seats: returnTicket.seats,
              }),
              newValue: JSON.stringify({
                remainingSeats: updatedReturnTicket.remainingSeats,
                seats: updatedReturnTicket.seats,
              }),
              agencyId: booking.buyerAgencyId,
              agencyAgentId: agentId,
            },
          });
        }
        if (tripType === "ROUND_TRIP" && returnBooking) {
          const bookingData = [booking, returnBooking];

          return {
            booking: bookingData,
            eTicket: null,
            receipt: null,
          };
        }
        return { booking, eTicket: null, receipt: null };
      });
      return internalBooking;
    } catch (err: any) {
      // If this is an insufficient wallet error, propagate as is
      if (err && err.code === "INSUFFICIENT_WALLET") throw err;
      if (err && err.code === "FLIGHT_TOO_SOON") throw err;
      if (err && err.code === "FLIGHT_DEPARTED") throw err;
      // Log for diagnostics
      console.error("Booking creation failed:", err);
      // Throw a generic error for the client
      throw new Error("Failed to create booking. Please try again later.");
    }
  }

  // Get a single booking by its ID (with all relations)
  /**
   * Retrieves a booking by its ID.
   * @param bookingId - The booking's unique identifier
   * @returns The booking or null if not found
   */
  async getBookingById(bookingId: string): Promise<any | null> {
    if (!bookingId) throw new Error("bookingId is required.");
    const booking = await prisma.booking.findFirst({
      where: { OR: [{ id: bookingId }, { requestId: bookingId }] },
      include: {
        travelers: { include: { traveler: true } },
        bookedSeats: true,
        ticket: {
          include: {
            segments: true,
            flightClasses: {
              select: {
                type: true,
                carryOnAllowed: true,
                carryOnWeight: true,
                checkedAllowed: true,
                checkedWeight: true,
                checkedFee: true,
                additionalFee: true,
                price: true,
              },
            },
            departure: true,
            arrival: true,
          },
        },
        payment: true,
        eTickets: true,
        Receipt: true,
        bookingHistoryLogs: true,
        notifications: true,
      },
    });
    if (!booking) throw new Error("Booking not found.");
    return booking;
  }

  // Get all bookings for a user (optionally filter by status)
  /**
   * Retrieves all bookings for a user, optionally filtered by status.
   * @param userId - The user's unique identifier
   * @param status - Optional status filter
   * @returns An array of bookings
   */
  async getBookingsByUser(userId: string, status?: string): Promise<any[]> {
    if (!userId) throw new Error("userId is required.");
    let statusFilter: BookingStatus | undefined = undefined;
    if (status) {
      // Validate and cast the status string to the enum
      if (
        Object.values(BookingStatus).includes(
          status as unknown as BookingStatus
        )
      ) {
        statusFilter = status as unknown as BookingStatus;
      } else {
        throw new Error("Invalid booking status.");
      }
    }
    const bookings = await prisma.booking.findMany({
      where: {
        userId,
        ...(statusFilter ? { status: statusFilter } : {}),
      },
      orderBy: { timerStartedAt: "desc" },
      include: {
        travelers: { include: { traveler: true } },
        bookedSeats: true,
        ticket: true,
        payment: true,
      },
    });
    return bookings;
  }

  // Get all bookings by status (e.g., for admin dashboard)
  /**
   * Retrieves all bookings by status.
   * @param status - The status of the bookings
   * @returns An array of bookings
   */
  async getBookingsByStatus(status: string): Promise<any[]> {
    if (!status) throw new Error("status is required.");
    let statusFilter: BookingStatus | undefined = undefined;
    if (status) {
      // Validate and cast the status string to the enum
      if (
        Object.values(BookingStatus).includes(
          status as unknown as BookingStatus
        )
      ) {
        statusFilter = status as unknown as BookingStatus;
      } else {
        throw new Error("Invalid booking status.");
      }
    }
    const bookings = await prisma.booking.findMany({
      where: { status: statusFilter },
      orderBy: { timerStartedAt: "desc" },
      include: {
        travelers: { include: { traveler: true } },
        bookedSeats: true,
        ticket: true,
        payment: true,
      },
    });
    return bookings;
  }

  // Get all bookings with cursor-based pagination
  /**
   * Retrieves all bookings with cursor-based pagination.
   * @param page - Page number (default: 1)
   * @param pageSize - Number of bookings per page (default: 20)
   * @param cursor - Optional cursor for pagination
   * @param userId - Optional user ID filter
   * @param accountType - Optional account type filter
   * @param agencyId - Optional agency ID filter
   * @param bookingType - Optional booking type filter ('buyer' or 'seller')
   * @returns An array of bookings
   */
  async getAllBookings(
    page = 1,
    pageSize = 20,
    cursor?: string,
    userId?: string,
    accountType?: string,
    agencyId?: string,
    bookingType?: string,
    // New filter parameters
    status?: string | string[],
    source?: string | string[],
    agentName?: string | string[],
    travelDateRange?: string,
    searchQuery?: string
  ): Promise<any[]> {
    if (!userId) {
      throw new Error("userId is required to fetch associated account IDs.");
    }
    if (!accountType) {
      throw new Error(
        "accountType is required to fetch associated account IDs."
      );
    }

    // 2. Build query based on bookingType
    const filters: any[] = [];

    // Only apply user/agency filters for non-admin users
    if (accountType !== "masterUser" && accountType !== "masterOwner") {
      // 1. Get all IDs for this user context
      const relevantIds = await getAssociatedAccountIds(userId, accountType);

      // If bookingType is not provided, default to 'buyer' logic
      const type = bookingType || "buyer";
      if (type === "buyer") {
        filters.push({
          OR: [
            { buyerAgencyId: { in: relevantIds } },
            { userId: { in: relevantIds } }, // for affiliates or direct user bookings
          ],
        });
      } else if (type === "seller") {
        filters.push({
          OR: [
            { sellerAgencyId: { in: relevantIds } },
            { userId: { in: relevantIds } }, // for affiliates or direct user bookings
          ],
        });
      }
    }

    // Add status filter if provided
    if (status) {
      const statuses = Array.isArray(status) ? status : [status];
      filters.push({ status: { in: statuses } });
    }

    // Add source filter if provided
    if (source) {
      const sources = Array.isArray(source) ? source : [source];
      filters.push({ source: { in: sources } });
    }

    // Add agent name filter if provided
    if (agentName) {
      const agentNames = Array.isArray(agentName) ? agentName : [agentName];
      const agentNameConditions = agentNames.flatMap((name) => [
        { meta: { path: ["agentName"], equals: name } },
        { meta: { path: ["bookedByAgentName"], equals: name } },
        { meta: { path: ["buyerAgentName"], equals: name } },
      ]);
      filters.push({ OR: agentNameConditions });
    }

    // Add date range filter if provided
    if (travelDateRange && typeof travelDateRange === "string") {
      const dateRange = getCreatedTimeRange(travelDateRange.toLowerCase());
      filters.push({ createdAt: dateRange });
    }

    // Add search query if provided
    if (searchQuery) {
      const searchCondition = {
        OR: [
          { requestId: { contains: searchQuery, mode: "insensitive" } },
          { referenceNumber: { contains: searchQuery, mode: "insensitive" } },
          {
            eTickets: {
              some: {
                eTicketNumber: { contains: searchQuery, mode: "insensitive" },
              },
            },
          },
          {
            travelers: {
              some: {
                traveler: {
                  OR: [
                    {
                      firstName: { contains: searchQuery, mode: "insensitive" },
                    },
                    {
                      lastName: { contains: searchQuery, mode: "insensitive" },
                    },
                  ],
                },
              },
            },
          },
          {
            ticket: {
              segments: {
                some: {
                  OR: [
                    {
                      flightNumber: {
                        contains: searchQuery,
                        mode: "insensitive",
                      },
                    },
                    {
                      carrier: {
                        contains: searchQuery,
                        mode: "insensitive",
                      },
                    },
                    {
                      departure: {
                        airportCode: {
                          contains: searchQuery,
                          mode: "insensitive",
                        },
                      },
                    },
                    {
                      arrival: {
                        airportCode: {
                          contains: searchQuery,
                          mode: "insensitive",
                        },
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
      };
      filters.push(searchCondition);
    }

    // 4. Combine all filters into a single where clause
    const whereClause = { AND: filters };

    // First, get the bookings without the user relation to avoid the null constraint
    const bookings = await prisma.booking.findMany({
      where: whereClause,
      take: pageSize,
      skip: (page - 1) * pageSize,
      ...(cursor
        ? {
            skip: 1, // Skip the cursor item
            cursor: { id: cursor },
          }
        : {}),
      orderBy: { timerStartedAt: "desc" },
      include: {
        travelers: { include: { traveler: true } },
        bookedSeats: true,
        ticket: {
          include: {
            flightClasses: {
              select: {
                price: true,
              },
            },
          },
        },
        payment: true,
      },
    });

    // Then, for each booking, fetch the user separately and handle null cases
    const bookingsWithUsers = await Promise.all(
      bookings.map(async (booking) => {
        const user = booking.userId
          ? await prisma.user.findUnique({
              where: { id: booking.userId },
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            })
          : null;

        return {
          ...booking,
          user,
        };
      })
    );

    return bookings;
  }

  // Get all MY bookings with cursor-based pagination
  /**
   * Retrieves all bookings with cursor-based pagination.
   * @param page - Page number (default: 1)
   * @param pageSize - Number of bookings per page (default: 20)
   * @param cursor - Optional cursor for pagination
   * @param userId - Optional user ID filter
   * @param accountType - Optional account type filter
   * @param agencyId - Optional agency ID filter
   * @param bookingType - Optional booking type filter ('buyer' or 'seller')
   * @returns An array of bookings
   */
  async getAllMyBookings(
    page = 1,
    pageSize = 20,
    cursor?: string,
    userId?: string,
    accountType?: string,
    agencyId?: string,
    bookingType?: string,
    // New filter parameters
    status?: string | string[],
    source?: string | string[],
    agentName?: string | string[],
    travelDateRange?: string,
    searchQuery?: string
  ): Promise<any[]> {
    if (!userId) {
      throw new Error("userId is required to fetch associated account IDs.");
    }
    if (!accountType) {
      throw new Error(
        "accountType is required to fetch associated account IDs."
      );
    }

    // 2. Build query based on bookingType
    const filters: any[] = [];

    // Only apply user/agency filters for non-admin users
    // 1. Get all IDs for this user context
    const relevantIds = await getAssociatedAccountIds(userId, accountType);

    // If bookingType is not provided, default to 'buyer' logic
    const type = bookingType || "buyer";
    if (type === "buyer") {
      filters.push({
        OR: [
          { buyerAgencyId: { in: relevantIds } },
          { userId: { in: relevantIds } }, // for affiliates or direct user bookings
        ],
      });
    } else if (type === "seller") {
      filters.push({
        OR: [
          { sellerAgencyId: { in: relevantIds } },
          { userId: { in: relevantIds } }, // for affiliates or direct user bookings
        ],
      });
    }

    // Add status filter if provided
    if (status) {
      const statuses = Array.isArray(status) ? status : [status];
      filters.push({ status: { in: statuses } });
    }

    // Add source filter if provided
    if (source) {
      const sources = Array.isArray(source) ? source : [source];
      filters.push({ source: { in: sources } });
    }

    // Add agent name filter if provided
    // if (agentName) {
    //   const agentNames = Array.isArray(agentName) ? agentName : [agentName];
    //   const agentNameConditions = agentNames.flatMap((name) => [
    //     { meta: { path: ["agentName"], equals: name } },
    //     { meta: { path: ["bookedByAgentName"], equals: name } },
    //     { meta: { path: ["buyerAgentName"], equals: name } },
    //   ]);
    //   filters.push({ OR: agentNameConditions });
    // }

    if (agentName) {
      let agentNames = Array.isArray(agentName) ? agentName : [agentName];
      const externalAgentFilter = agentNames.includes("External Agent");
      agentNames = agentNames.filter((name) => name !== "External Agent"); // Remove it from list
  
      const agentNameConditions: any[] = [];
  
      // If 'External Agent' is a filter, find bookings not in the user's agency context
      if (externalAgentFilter) {
        if (type === "buyer") {
          agentNameConditions.push({ buyerAgencyId: { notIn: relevantIds } });
        } else {
          agentNameConditions.push({ sellerAgencyId: { notIn: relevantIds } });
        }
      }
  
      // If there are specific agent names, find bookings matching them
      if (agentNames.length > 0) {
        const specificAgentConditions = agentNames.flatMap((name) => [
          { meta: { path: ["agentName"], equals: name } },
          { meta: { path: ["bookedByAgentName"], equals: name } },
          { meta: { path: ["buyerAgentName"], equals: name } },
        ]);
        agentNameConditions.push({ OR: specificAgentConditions });
      }
  
      if (agentNameConditions.length > 0) {
        filters.push({ OR: agentNameConditions });
      }
    }

    // Add date range filter if provided
    if (travelDateRange && typeof travelDateRange === "string") {
      const dateRange = getCreatedTimeRange(travelDateRange.toLowerCase());
      filters.push({ createdAt: dateRange });
    }

    // Add search query if provided
    if (searchQuery) {
      const searchCondition = {
        OR: [
          { requestId: { contains: searchQuery, mode: "insensitive" } },
          { referenceNumber: { contains: searchQuery, mode: "insensitive" } },
          {
            eTickets: {
              some: {
                eTicketNumber: { contains: searchQuery, mode: "insensitive" },
              },
            },
          },
          {
            travelers: {
              some: {
                traveler: {
                  OR: [
                    {
                      firstName: { contains: searchQuery, mode: "insensitive" },
                    },
                    {
                      lastName: { contains: searchQuery, mode: "insensitive" },
                    },
                  ],
                },
              },
            },
          },
          {
            ticket: {
              segments: {
                some: {
                  OR: [
                    {
                      flightNumber: {
                        contains: searchQuery,
                        mode: "insensitive",
                      },
                    },
                    {
                      carrier: {
                        contains: searchQuery,
                        mode: "insensitive",
                      },
                    },
                    {
                      departure: {
                        airportCode: {
                          contains: searchQuery,
                          mode: "insensitive",
                        },
                      },
                    },
                    {
                      arrival: {
                        airportCode: {
                          contains: searchQuery,
                          mode: "insensitive",
                        },
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
      };
      filters.push(searchCondition);
    }

    // 4. Combine all filters into a single where clause
    const whereClause = { AND: filters };

    // First, get the bookings without the user relation to avoid the null constraint
    const bookings = await prisma.booking.findMany({
      where: whereClause,
      take: pageSize,
      skip: (page - 1) * pageSize,
      ...(cursor
        ? {
            skip: 1, // Skip the cursor item
            cursor: { id: cursor },
          }
        : {}),
      orderBy: { timerStartedAt: "desc" },
      include: {
        travelers: { include: { traveler: true } },
        bookedSeats: true,
        ticket: {
          include: {
            flightClasses: {
              select: {
                price: true,
              },
            },
          },
        },
        payment: true,
        Receipt: true,
        eTickets: true,
      },
    });

    // Then, for each booking, fetch the user separately and handle null cases
    const bookingsWithUsers = await Promise.all(
      bookings.map(async (booking) => {
        const user = booking.userId
          ? await prisma.user.findUnique({
              where: { id: booking.userId },
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            })
          : null;

        return {
          ...booking,
          user,
        };
      })
    );

    return bookings;
  }

  /**
   * Calculates the payment amounts for a booking based on its type and details
   * @param bookingId - The booking ID
   * @param tx - Prisma transaction client
   * @returns Object containing outboundAmount, returnAmount, and totalAmount
   */
  private async calculatePaymentAmounts(
    bookingId: string | { id: string },
    tx: any
  ): Promise<{
    outboundAmount: number;
    returnAmount: number;
    totalAmount: number;
    isRoundTrip: boolean;
  }> {
    // Extract ID if an object was passed
    const resolvedBookingId =
      typeof bookingId === "string" ? bookingId : bookingId.id;

    // Get booking with related data
    const bookingWithDetails = await tx.booking.findUnique({
      where: { id: resolvedBookingId },
      include: {
        ticket: {
          include: {
            flightClass: {
              include: {
                flightPrice: true,
              },
            },
          },
        },
        bookedSeats: true,
        returnBookedSeats: true,
        travelers: true,
      },
    });

    if (!bookingWithDetails) {
      throw new Error("Booking details not found");
    }

    // Calculate outbound amount from booked seats
    const outboundAmount = bookingWithDetails.bookedSeats.reduce(
      (sum: number, seat: any) => sum + (parseFloat(seat.totalPrice) || 0),
      0
    );

    // Initialize return amount
    let returnAmount = 0;
    const isRoundTrip = bookingWithDetails.tripType === "ROUND_TRIP";

    // For round trips, calculate return amount
    if (isRoundTrip) {
      if (bookingWithDetails.returnBookedSeats?.length > 0) {
        returnAmount = bookingWithDetails.returnBookedSeats.reduce(
          (sum: number, seat: any) => sum + (parseFloat(seat.totalPrice) || 0),
          0
        );
      } else {
        // If no return seats but it's a round trip, use outbound amount as return amount
        // This is a fallback and ideally should be avoided by ensuring return seats are always created
        returnAmount = outboundAmount;
      }
    }

    // Calculate total amount
    const totalAmount = isRoundTrip
      ? outboundAmount + returnAmount
      : outboundAmount;

    return {
      outboundAmount,
      returnAmount: isRoundTrip ? returnAmount : 0,
      totalAmount,
      isRoundTrip,
    };
  }

  // Complete Payment (moves to Pending Approval)
  /**
   * Completes the payment for a booking, moving it to the pending approval state.
   * @param bookingId - The ID of the booking to complete payment for
   * @param paymentDetails - Payment details to be associated with the booking
   * @returns The updated booking object
   */
  async completePayment(bookingId: string, paymentDetails: any) {
    if (!bookingId) throw new Error("bookingId is required.");
    // Validate paymentDetails here
    if (!paymentDetails) throw new Error("Payment details are required.");
    if (
      paymentDetails.amount === undefined ||
      paymentDetails.amount === null ||
      !paymentDetails.currency
    ) {
      throw new Error("Payment amount and currency are required.");
    }
    if (!paymentDetails.referenceNumber) {
      throw new Error("Payment reference number is required.");
    }
    if (!paymentDetails.transactionDate) {
      throw new Error("Payment transaction date is required.");
    }
    if (!paymentDetails.paymentMethod) {
      throw new Error("Payment method is required.");
    }

    return await prisma.$transaction(async (tx: any) => {
      // Get booking with all necessary relations
      const booking = await tx.booking.findUnique({
        where: { id: bookingId },
        include: {
          ticket: true,
          bookedSeats: true,
          travelers: true,
        },
      });

      if (!booking) throw new Error("Booking not found.");

      if (booking.status === BookingStatus.PENDING_APPROVAL) {
        const error: any = new Error("Booking is already pending approval.");
        error.code = "ALREADY_PENDING_APPROVAL";
        error.status = 409;
        throw error;
      }

      // // Calculate expected payment amounts based on booking details
      // const { outboundAmount, returnAmount, totalAmount, isRoundTrip } =
      //   await this.calculatePaymentAmounts(booking, tx);
      // const paymentAmount =
      //   typeof paymentDetails.amount === "number"
      //     ? paymentDetails.amount
      //     : parseFloat(paymentDetails.amount);

      // // Validate payment amount matches expected total amount (with a small tolerance for floating point)
      // if (Math.abs(paymentAmount - totalAmount) > 0.01) {
      //   console.warn(
      //     `Payment amount mismatch for booking ${bookingId}. Expected: ${totalAmount}, Got: ${paymentAmount}`
      //   );
      //   const error: any = new Error(
      //     "Payment amount does not match the expected total for this booking."
      //   );
      //   error.code = "PAYMENT_AMOUNT_MISMATCH";
      //   error.expectedAmount = totalAmount;
      //   error.receivedAmount = paymentAmount;
      //   error.status = 400;
      //   throw error;
      // }

      // // Get seller information for credit adjustment
      // const seller = await tx.user.findUnique({
      //   where: { id: booking.sellerAgencyId },
      //   select: { id: true, creditBalance: true },
      // });

      // if (!seller) {
      //   throw new Error("Seller not found for this booking");
      // }

      // // Process credit adjustments for buyer and seller
      // // 1. Deduct total amount from buyer's credit
      // await tx.user.update({
      //   where: { id: booking.buyerAgencyId || booking.userId },
      //   data: {
      //     creditBalance: {
      //       decrement: totalAmount,
      //     },
      //   },
      // });

      // // 2. Add outbound amount to seller's credit (for outbound leg)
      // await tx.user.update({
      //   where: { id: booking.sellerAgencyId },
      //   data: {
      //     creditBalance: {
      //       increment: outboundAmount,
      //     },
      //   },
      // });

      // // 3. If it's a round trip, add return amount to the return seller's credit
      // if (isRoundTrip && returnAmount > 0) {
      //   // Assuming returnSellerId is stored in the booking, or use the same seller for both legs
      //   const returnSellerId = booking.returnSellerId || booking.sellerAgencyId;
      //   await tx.user.update({
      //     where: { id: returnSellerId },
      //     data: {
      //       creditBalance: {
      //         increment: returnAmount,
      //       },
      //     },
      //   });
      // }

      const dbPaymentData = mapClientPaymentToDbPayment(paymentDetails);
      // Map client payment fields to database payment fields
      // const dbPaymentData = mapClientPaymentToDbPayment({
      //   ...paymentDetails,
      //   amount: totalAmount, // Use server-calculated total amount
      //   metadata: {
      //     ...(paymentDetails.metadata || {}),
      //     outboundAmount,
      //     returnAmount: isRoundTrip ? returnAmount : undefined,
      //     isRoundTrip,
      //   },
      // });

      const updatedBooking = await tx.booking.update({
        where: { id: bookingId },
        data: {
          status: BookingStatus.PENDING_APPROVAL,
          // totalAmount: totalAmount, // Ensure totalAmount is set correctly
          // outboundAmount: outboundAmount,
          // returnAmount: isRoundTrip ? returnAmount : null,
          payment: { create: dbPaymentData },
        },
        include: {
          payment: true,
          ticket: true,
          bookedSeats: true,
        },
      });

      // 1. Emit notification
      await NotificationService.createNotification({
        userId: updatedBooking.userId,
        type: updatedBooking.type,
        title: "Booking Payment Completed",
        message: `Your internal booking ( ${updatedBooking.requestId} ) payment has been completed.`,
        // message:
        //   `Your ${isRoundTrip ? "round-trip" : "one-way"} booking (${updatedBooking.requestId}) payment of ${totalAmount.toFixed(2)} ${paymentDetails.currency} has been completed.` +
        //   (isRoundTrip
        //     ? `\nOutbound: ${outboundAmount.toFixed(2)} ${paymentDetails.currency}\nReturn: ${returnAmount.toFixed(2)} ${paymentDetails.currency}`
        //     : ""),
        relatedId: updatedBooking.id,
        link: `/booking/${bookingId}`,
        priority: 1,
      });

      // 2. Log action with detailed payment info
      await tx.bookingHistoryLog.create({
        data: {
          bookingId,
          changeType: "PAYMENT_COMPLETED",
          reason: "The payment was completed successfully.",
          // reason:
          //   `Payment of ${totalAmount.toFixed(2)} ${paymentDetails.currency} was completed successfully (${isRoundTrip ? "round-trip" : "one-way"}).` +
          //   (isRoundTrip
          //     ? `\nOutbound: ${outboundAmount.toFixed(2)} ${paymentDetails.currency}\nReturn: ${returnAmount.toFixed(2)} ${paymentDetails.currency}`
          //     : ""),
          actorId: null,
          actorType: ActorType.SYSTEM,
          oldStatus: updatedBooking.status,
          newStatus: BookingStatus.PENDING_APPROVAL,
          metadata: {
            // paymentAmount: totalAmount,
            // outboundAmount: outboundAmount,
            // returnAmount: isRoundTrip ? returnAmount : undefined,
            currency: paymentDetails.currency,
            tripType: updatedBooking.tripType,
            outboundSeatCount: updatedBooking.bookedSeats?.length || 0,
            // returnSeatCount: isRoundTrip
            //   ? updatedBooking.returnBookedSeats?.length || 0
            //   : undefined,
            passengerCount: updatedBooking.travelers?.length || 0,
          },
        },
      });

      return updatedBooking;
    });
  }

  // Release Seat (sets booking to Timed Out)
  /**
   * Releases a seat by setting the booking status to timed out.
   * @param bookingId - The ID of the booking to release
   * @returns The updated booking object
   */
  async releaseSeat(bookingId: string) {
    if (!bookingId) throw new Error("bookingId is required.");
    // Fetch the current booking with all possible agent relations
    return await prisma.$transaction(async (tx: any) => {
      const booking = await tx.booking.findUnique({
        where: { id: bookingId },
        include: {
          bookedSeats: true,
          agencyAgent: true,
          user: true,
          TeamMember: true,
          travelers: true,
        },
      });
      if (!booking) throw new Error("Booking not found.");
      if (booking.status === BookingStatus.TIMED_OUT) {
        const error: any = new Error("Booking is already timed out.");
        error.code = "ALREADY_TIMED_OUT";
        error.status = 409;
        throw error;
      }

      const ticket = await tx.flightTicket.findUnique({
        where: { id: booking.ticketId },
      });
      // Update status to TIMED_OUT
      const updatedBooking = await tx.booking.update({
        where: { id: bookingId },
        data: { status: BookingStatus.TIMED_OUT },
      });
      const totalSeats = booking.travelers.length;

      // Increment the flight ticket's remainingSeats by the number of seats released
      const updateTicket = await tx.flightTicket.update({
        where: { id: booking.ticketId },
        data: { remainingSeats: { increment: totalSeats } },
      });

      // 1. Find all booked seats for the booking
      const bookedSeats = await tx.bookedFlightSeat.findMany({
        where: { bookingId },
      });
      // 2. Update each seat (if you want to release all seats for the booking)
      await Promise.all(
        bookedSeats.map((seat: any) =>
          tx.bookedFlightSeat.update({
            where: { id: seat.id },
            data: { seatStatus: BookedFlightSeatStatus.canceled },
          })
        )
      );

      // 3. Emit notification
      await NotificationService.createNotification({
        userId: updatedBooking.userId,
        type: updatedBooking.type,
        title: "Booking Timed Out",
        message: `Your internal booking ( ${updatedBooking.requestId} ) has timed out.`,
        relatedId: updatedBooking.id,
        link: `/booking/${bookingId}`,
        priority: 1,
      });
      // 4. Log action
      await tx.bookingHistoryLog.create({
        data: {
          bookingId,
          changeType: "TIMED_OUT",
          reason: "The booking has timed out.",
          actorId: null,
          actorType: ActorType.SYSTEM,
          oldStatus: updatedBooking.status,
          newStatus: BookingStatus.TIMED_OUT,
        },
      });

      await tx.ticketHistoryLog.create({
        data: {
          ticketId: booking.ticketId,
          changeType: "Seats updated",
          changeDetails: JSON.stringify({
            comment: `Seats released by Booking Agent via Release Seat action ( ${booking.requestId} )\n by ${
              booking?.agencyAgent
                ? `${booking.agencyAgent.firstName} ${booking.agencyAgent.lastName}`
                : booking?.TeamMember
                  ? `${booking.TeamMember.firstName} ${booking.TeamMember.lastName}`
                  : booking?.user
                    ? `${booking.user.firstName} ${booking.user.lastName}`
                    : "System"
            }.`,
          }),
          oldValue: JSON.stringify({
            remainingSeats: ticket?.remainingSeats,
            seats: ticket?.seats,
          }),
          newValue: JSON.stringify({
            remainingSeats: updateTicket?.remainingSeats,
            seats: updateTicket?.seats,
          }),
          agencyId: booking.buyerAgencyId,
          agencyAgentId: booking.teamMemberId,
        },
      });
      return updatedBooking;
    });
  }

  // Admin Approve Booking
  /**
   * Approves a booking by setting its status to confirmed.
   * @param bookingId - The ID of the booking to approve
   * @returns The updated booking object
   */
  async approveBooking(
    bookingId: string
  ): Promise<{ booking: Booking; eTicket: any; receipt: any }> {
    if (!bookingId) throw new Error("bookingId is required.");

    return await prisma.$transaction(async (tx: any) => {
      // Fetch booking and relations
      const bookingData = await tx.booking.findUnique({
        where: { id: bookingId },
        include: {
          bookedSeats: true,
          travelers: true,
        },
      });
      if (!bookingData) throw new Error("Booking not found.");
      if (bookingData.status !== BookingStatus.PENDING_APPROVAL)
        throw new Error("Booking is not in pending approval status.");
      if (bookingData.ticketId === null)
        throw new Error("Booking is not associated with a ticket.");

      // Start a transaction for atomicity
      // Update booking with e-ticket info
      const booking = await tx.booking.update({
        where: { id: bookingId },
        data: {
          status: BookingStatus.BOOKING_CONFIRMED,
          transactionId: `TRN-${nanoidAlphaNum()}`,
          travelers: {
            connect: bookingData.travelers.map((traveler: any) => ({
              id: traveler.id,
            })),
          },
          bookedSeats: {
            connect: bookingData.bookedSeats.map((seat: any) => ({
              id: seat.id,
            })),
          },
          timerDuration: bookingData.timerDuration,
          timerStartedAt: bookingData.timerStartedAt,
          expiresAt: bookingData.expiresAt,
        },
      });

      // 7. Generate e-ticket and receipt (source/type already handled in DTO)
      const eTicket = await this.generateThirdPartyTicket(booking.id, tx);
      const receipt = await this.generateThirdPartyReceipt(
        booking.id,
        eTicket.id,
        bookingData.agentId,
        tx
      );

      const ticket = await tx.flightTicket.findUnique({
        where: { id: booking.ticketId },
      });

      // Update all booked seats to confirmed
      await tx.bookedFlightSeat.updateMany({
        where: { bookingId },
        data: { seatStatus: BookedFlightSeatStatus.booked },
      });

      // 3. Emit notification
      await NotificationService.createNotification({
        userId: booking.userId,
        type: booking.type,
        title: "Booking Approved",
        message: `Your internal booking ( ${booking.requestId} ) has been approved.`,
        relatedId: booking.id,
        link: `/booking/${bookingId}`,
        priority: 1,
      });

      // 4. Log action
      await tx.bookingHistoryLog.create({
        data: {
          bookingId,
          changeType: "Booking Approved",
          reason: `Seats updated due to internal booking approval ( ${booking.requestId} ).`,
          actorId: null,
          actorType: ActorType.SYSTEM,
          oldStatus: booking.status,
          newStatus: BookingStatus.BOOKING_CONFIRMED,
        },
      });

      // 5. Log ticket action
      await tx.ticketHistoryLog.create({
        data: {
          ticketId: ticket.id,
          changeType: "Booking Approved",
          changeDetails: JSON.stringify({
            comment: `Seats updated due to internal booking approval ( ${booking.requestId} ).`,
          }),
          oldValue: JSON.stringify({
            remainingSeats: ticket.remainingSeats,
            seats: ticket.seats,
          }),
          newValue: JSON.stringify({
            remainingSeats: ticket.remainingSeats,
            seats: ticket.seats,
          }),
          agencyId: booking.buyerAgencyId,
          agencyAgentId: booking.teamMemberId,
        },
      });

      return { booking, eTicket, receipt };
    });
  }

  // Admin Reject Booking
  async rejectBooking(bookingId: string) {
    if (!bookingId) throw new Error("bookingId is required.");
    return await prisma.$transaction(async (tx: any) => {
      // First get the count of booked seats
      const bookedSeatsCount = await tx.bookedFlightSeat.count({
        where: {
          bookingId,
          seatStatus: { not: BookedFlightSeatStatus.canceled },
        },
      });

      // First, get basic booking data without relations
      const bookingData = await tx.booking.findUnique({
        where: { id: bookingId },
        select: {
          id: true,
          status: true,
          ticketId: true,
          userId: true,
          agencyAgentId: true,
          teamMemberId: true,
          buyerAgencyId: true,
          requestId: true,
          ticket: true,
          travelers: true,
        },
      });

      if (!bookingData) throw new Error("Booking not found.");
      if (bookingData.status === BookingStatus.BOOKING_REJECTED) {
        throw new Error("The booking has already been rejected.");
      }

      // Check if either user or agencyAgent exists
      if (!bookingData.userId && !bookingData.agencyAgentId) {
        throw new Error("Invalid booking: No associated user or agent found.");
      }

      // Now get the related data we need
      const [user, agencyAgent, teamMember] = await Promise.all([
        bookingData.userId
          ? tx.user.findUnique({
              where: { id: bookingData.userId },
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            })
          : Promise.resolve(null),
        bookingData.agencyAgentId
          ? tx.agencyAgent.findUnique({
              where: { id: bookingData.agencyAgentId },
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            })
          : Promise.resolve(null),
        bookingData.teamMemberId
          ? tx.teamMember.findUnique({
              where: { id: bookingData.teamMemberId },
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            })
          : Promise.resolve(null),
      ]);

      // Combine the data
      const bookingWithRelations = {
        ...bookingData,
        user,
        agencyAgent,
        TeamMember: teamMember,
      };

      // Update booking status
      const booking = await tx.booking.update({
        where: { id: bookingId },
        data: { status: BookingStatus.BOOKING_REJECTED },
      });

      const ticket = await tx.flightTicket.findUnique({
        where: { id: bookingWithRelations.ticket.id },
      });

      if (!ticket) throw new Error("Ticket not found.");
      const totalSeats = bookingData.travelers.length;

      // Update ticket remaining seats with the actual count if ticket exists
      let updateTicket;
      if (bookedSeatsCount > 0 && bookingWithRelations.ticket) {
        updateTicket = await tx.flightTicket.update({
          where: { id: bookingWithRelations.ticket.id },
          data: { remainingSeats: { increment: totalSeats } },
        });
      }

      // 1. Find all booked seats for the booking
      const bookedSeats = await tx.bookedFlightSeat.findMany({
        where: { bookingId },
      });
      // 2. Update each seat (if you want to release all seats for the booking)
      await Promise.all(
        bookedSeats.map((seat: any) =>
          tx.bookedFlightSeat.update({
            where: { id: seat.id },
            data: { seatStatus: BookedFlightSeatStatus.canceled },
          })
        )
      );
      // 3. Emit notification
      try {
        const notificationData = {
          userId:
            bookingWithRelations.userId ||
            bookingWithRelations.agencyAgentId ||
            bookingWithRelations.teamMemberId,
          type: "BOOKING_REJECTED",
          title: "Booking Rejected",
          message: `Your internal booking (${bookingWithRelations.requestId}) has been rejected.`,
          relatedId: bookingWithRelations.id,
          link: `/booking/${bookingId}`,
          priority: 1,
        };

        if (!notificationData.userId) {
          console.warn(
            "No valid user ID found for notification:",
            notificationData
          );
          // Try to get the user ID from the ticket if available
          if (bookingWithRelations.ticket?.userId) {
            notificationData.userId = bookingWithRelations.ticket.userId;
          } else {
            throw new Error("No valid user ID found for notification");
          }
        }

        await NotificationService.createNotification(notificationData);
      } catch (error) {
        console.error(
          "Failed to create notification for booking rejection:",
          error
        );
        // Don't fail the whole operation if notification fails
      }
      // 4. Log action
      await tx.bookingHistoryLog.create({
        data: {
          bookingId,
          changeType: "Booking Rejected",
          reason: `Seats updated due to internal booking rejection ( ${bookingWithRelations.requestId} ).`,
          actorId: null,
          actorType: ActorType.SYSTEM,
          oldStatus: bookingWithRelations.status,
          newStatus: BookingStatus.BOOKING_REJECTED,
        },
      });

      // 5. Log ticket action if ticket exists
      if (updateTicket) {
        await tx.ticketHistoryLog.create({
          data: {
            ticketId: updateTicket.id,
            changeType: "Booking Rejected",
            changeDetails: JSON.stringify({
              comment: `Seats updated due to internal booking rejection ( ${bookingWithRelations.requestId} ).`,
            }),
            oldValue: JSON.stringify({
              remainingSeats: ticket.remainingSeats,
              seats: ticket.seats,
            }),
            newValue: JSON.stringify({
              remainingSeats: updateTicket.remainingSeats,
              seats: updateTicket.seats,
            }),
            agencyId: bookingWithRelations.buyerAgencyId,
            agencyAgentId:
              bookingWithRelations.teamMemberId ||
              bookingWithRelations.agencyAgentId ||
              bookingWithRelations.userId ||
              bookingWithRelations.agencyAgent?.id ||
              bookingWithRelations.TeamMember?.id ||
              bookingWithRelations.user?.id,
          },
        });
      }
      return booking;
    });
  }

  // Admin Cancel Booking// Cancel Booking (agent/admin/user can cancel if not finalized)
  async cancelBooking(bookingId: string, canceledBy: string) {
    if (!bookingId) throw new Error("bookingId is required.");

    return await prisma.$transaction(async (tx: any) => {
      // Fetch booking and check status
      const bookingData = await tx.booking.findUnique({
        where: { id: bookingId },
        include: { bookedSeats: true, travelers: true },
      });

      if (!bookingData) throw new Error("Booking not found.");

      // Disallow cancellation for third-party bookings at any status
      if (
        bookingData.bookingSource === "THIRD_PARTY" ||
        (bookingData as any).source === "THIRD_PARTY"
      ) {
        const error: any = new Error(
          "Cancellation is not permitted for third-party bookings. Please initiate a refund instead."
        );
        error.code = "THIRD_PARTY_BOOKING_CANCELLATION_NOT_ALLOWED";
        error.status = 400;
        throw error;
      }

      // Only allow cancel if not already finalized (for INTERNAL bookings only)
      const nonCancelableStatuses: BookingStatus[] = [
        BookingStatus.CANCELLED_BY_USER,
        BookingStatus.CANCELLED_BY_SYSTEM,
        BookingStatus.BOOKING_REJECTED,
        BookingStatus.PENDING_APPROVAL,
        BookingStatus.QUICK_HOLD,
        BookingStatus.TIMED_OUT,
      ];
      if (nonCancelableStatuses.includes(bookingData.status)) {
        const error: any = new Error(
          "Booking cannot be canceled in its current state."
        );
        error.code = "INVALID_BOOKING_STATE";
        error.status = 400;
        error.details = {
          currentStatus: bookingData.status,
        };
        throw error;
      }

      // Transaction: update booking status, release seats, and increment remainingSeats
      const booking = await tx.booking.update({
        where: { id: bookingId },
        data: {
          status: BookingStatus.CANCELLED_BY_USER,
          cancellationReason: "The user's booking has been canceled.",
        },
      });

      const ticket = await tx.flightTicket.findUnique({
        where: { id: bookingData.ticketId },
      });

      if (!ticket) throw new Error("Ticket not found.");

      const totalSeats = bookingData.travelers.length;

      // Update ticket remaining seats
      // Increment the flight ticket's remainingSeats by the number of seats released
      const updatedTicket = await tx.flightTicket.update({
        where: { id: bookingData.ticketId },
        data: { remainingSeats: { increment: totalSeats } },
      });

      await tx.bookedFlightSeat.updateMany({
        where: { bookingId },
        data: { seatStatus: BookedFlightSeatStatus.canceled },
      });

      // Optionally: log action, emit notification, etc.
      await tx.bookingHistoryLog.create({
        data: {
          bookingId,
          changeType: "Booking Canceled",
          reason: `Booking ${bookingData.requestId} has been canceled.`,
          actorId: canceledBy,
          actorType: ActorType.CUSTOMER,
          oldStatus: bookingData.status,
          newStatus: BookingStatus.CANCELLED_BY_USER,
        },
      });

      await tx.ticketHistoryLog.create({
        data: {
          ticketId: updatedTicket.id,
          changeType: "Booking Canceled",
          changeDetails: JSON.stringify({
            comment: `Booking ${bookingData.requestId} has been canceled.`,
          }),
          oldValue: JSON.stringify({
            remainingSeats: ticket.remainingSeats,
            seats: ticket.seats,
          }),
          newValue: JSON.stringify({
            remainingSeats: updatedTicket.remainingSeats,
            seats: updatedTicket.seats,
          }),
          agencyId: bookingData.buyerAgencyId,
          agencyAgentId: bookingData.teamMemberId,
        },
      });

      // Emit notification
      await NotificationService.createNotification({
        userId: bookingData.userId,
        type: bookingData.type,
        title: "Booking Canceled",
        message: `Your booking ( ${bookingData.requestId} ) has been canceled.`,
        relatedId: bookingData.id,
        link: `/booking/${bookingId}`,
        priority: 1,
      });

      return booking;
    });
  }

  async refundBooking(bookingId: string, canceledBy: string) {
    // Take initial memory snapshot
    const initialMem = process.memoryUsage();
    memoryMonitor.takeSnapshot();
    try {
      if (!bookingId) throw new Error("bookingId is required.");

      return await prisma.$transaction(async (tx: any) => {
        // Fetch booking and check status
        const bookingData = await tx.booking.findUnique({
          where: { id: bookingId },
          include: { bookedSeats: true },
        });

        if (!bookingData) throw new Error("Booking not found.");

        // Disallow cancellation for third-party bookings at any status
        if (
          bookingData.bookingSource === "INTERNAL" ||
          (bookingData as any).source === "INTERNAL"
        ) {
          const error: any = new Error(
            "Refund is not permitted for internal bookings. Please initiate a cancellation instead."
          );
          error.code = "INTERNAL_BOOKING_REFUND_NOT_ALLOWED";
          error.status = 400;
          throw error;
        }

        // Only allow cancel if not already finalized (for INTERNAL bookings only)
        const nonCancelableStatuses: BookingStatus[] = [
          BookingStatus.CANCELLED_BY_USER,
          BookingStatus.CANCELLED_BY_SYSTEM,
          BookingStatus.BOOKING_REJECTED,
          BookingStatus.PENDING_APPROVAL,
          BookingStatus.QUICK_HOLD,
          BookingStatus.TIMED_OUT,
        ];
        if (nonCancelableStatuses.includes(bookingData.status)) {
          const error: any = new Error(
            "Booking cannot be canceled in its current state."
          );
          error.code = "INVALID_BOOKING_STATE";
          error.status = 400;
          error.details = {
            currentStatus: bookingData.status,
          };
          throw error;
        }

        // 1. Get booking with all necessary relations
        // First, get the basic booking with minimal relations
        const booking = await tx.booking.findUnique({
          where: { id: bookingId },
          include: {
            payment: true,
            user: {
              select: {
                id: true,
                creditBalance: true,
              },
            },
            agencyAgent: {
              select: {
                id: true,
                agencyId: true,
                agency: {
                  select: {
                    id: true,
                    creditBalance: true,
                  },
                },
              },
            },
            TeamMember: {
              select: {
                id: true,
                createdById: true,
              },
            },
            travelers: {
              select: {
                id: true,
                traveler: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
            bookedSeats: {
              select: {
                id: true,
                totalPrice: true,
                traveler: {
                  select: {
                    id: true,
                  },
                },
              },
            },
            ticket: {
              select: {
                id: true,
                owner: true,
              },
            },
          },
        });
        if (!booking) throw new Error("Booking not found");

        // 2. Calculate total price from travelers
        let totalPrice = 0;
        let totalSeats = 0;

        // Calculate total price from booked seats
        booking.bookedSeats.forEach((seat: any) => {
          totalPrice += parseFloat(seat.totalPrice.toString());
        });

        // Get total number of travelers
        totalSeats = booking.travelers.length;

        // 3. Process refund if payment exists
        if (totalPrice > 0) {
          // 3.1 Credit the buyer (who made the booking)
          let buyerId = "";
          let buyerType: "user" | "agencyAgent" | "teamMember" | null = null;
          let buyerBalance = 0;
          let buyerOwnerId = "";
          let buyerOwnerType: "user" | null = null;

          // Find the buyer and their current balance
          if (booking.user) {
            // Direct user booking
            buyerId = booking.user.id;
            buyerType = "user";
            buyerBalance = parseFloat(
              booking.user.creditBalance?.toString() || "0"
            );
            buyerOwnerId = buyerId;
            buyerOwnerType = "user";
          } else if (booking.agencyAgent) {
            // Agency agent booking - get the agency's user (owner) details
            buyerId = booking.agencyAgent.id;
            buyerType = "agencyAgent";

            const agencyUser = await tx.user.findUnique({
              where: { id: booking.agencyAgent.agencyId },
              select: {
                id: true,
                creditBalance: true,
              },
            });

            if (agencyUser) {
              buyerOwnerId = agencyUser.id;
              buyerBalance = parseFloat(
                agencyUser.creditBalance?.toString() || "0"
              );
              buyerOwnerType = "user";
            }
          } else if (booking.TeamMember) {
            // Team member booking - get the creator (user) details
            buyerId = booking.TeamMember.id;
            buyerType = "teamMember";

            const creatorUser = await tx.user.findUnique({
              where: { id: booking.TeamMember.createdById },
              select: {
                id: true,
                creditBalance: true,
              },
            });

            if (creatorUser) {
              buyerOwnerId = creatorUser.id;
              buyerBalance = parseFloat(
                creatorUser.creditBalance?.toString() || "0"
              );
              buyerOwnerType = "user";
            }
          }

          if (!buyerType) {
            throw new Error(
              "No valid user, agency agent, or team member found for this booking"
            );
          }

          // 3.2 Debit the seller (ticket owner)
          if (!booking.ticket || !booking.ticket.owner) {
            throw new Error("Ticket owner information is missing");
          }

          const seller = await tx.user.findUnique({
            where: { id: booking.ticket.owner.id },
            select: { creditBalance: true },
          });

          if (!seller) {
            throw new Error("Seller not found for this booking");
          }

          // 3.3 Update buyer's balance with proper error handling
          try {
            await tx.user.update({
              where: { id: buyerOwnerId },
              data: { creditBalance: { increment: totalPrice } },
            });
          } catch (error) {
            console.error("Failed to update buyer balance:", error);
            throw new Error(
              `Failed to process refund: Could not update ${buyerType} balance`
            );
          }

          // Get the updated buyer's balance
          const updatedBuyer = await tx.user.findUnique({
            where: { id: buyerOwnerId },
            select: { creditBalance: true },
          });
          const updatedBuyerBalance = parseFloat(
            updatedBuyer?.creditBalance?.toString() || "0"
          );

          // Debit seller (deduct from seller's balance)
          if (!booking.ticket || !booking.ticket.owner) {
            throw new Error("Ticket owner information is missing");
          }

          // 2. Create credit transaction for buyer (refund)
          await tx.creditTransaction.create({
            data: {
              user: {
                connect: { id: buyerOwnerId },
              },
              amount: totalPrice,
              referenceId: booking.requestId,
              balanceAfter: updatedBuyerBalance,
              type: "REFUND",
            },
          });
          const sellerBalance = parseFloat(
            seller?.creditBalance.toString() || "0"
          );
          const updatedSellerBalance = sellerBalance - totalPrice;
          await tx.user.update({
            where: { id: booking.ticket.owner.id },
            data: {
              creditBalance: {
                decrement: totalPrice,
              },
            },
          });

          // Create credit transaction for buyer (refund)
          await tx.creditTransaction.create({
            data: {
              user: {
                connect: { id: buyerOwnerId },
              },
              amount: totalPrice,
              referenceId: booking.requestId,
              balanceAfter: updatedBuyerBalance,
              type: "REFUND",
            },
          });

          // Create debit transaction for seller (reversal)
          await tx.creditTransaction.create({
            data: {
              user: {
                connect: { id: booking.ticket.owner.id },
              },
              amount: -totalPrice,
              referenceId: booking.id,
              balanceAfter: updatedSellerBalance,
              type: "TRANSFER_REVERSAL",
            },
          });

          // Update payment status if payment exists
          if (booking.payment) {
            await tx.payment.update({
              where: { id: booking.payment.id },
              data: {
                status: "REFUNDED",
                refundedAt: new Date(),
                updatedAt: new Date(),
              },
            });
          }
        }

        // Transaction: update booking status, release seats, and increment remainingSeats
        await tx.booking.update({
          where: { id: bookingId },
          data: {
            status: BookingStatus.CANCELLED_BY_USER,
            cancellationReason: "The user's booking has been canceled.",
          },
        });

        const ticket = await tx.flightTicket.findUnique({
          where: { id: bookingData.ticketId },
        });

        if (!ticket) throw new Error("Ticket not found.");

        // Update ticket remaining seats
        // Increment the flight ticket's remainingSeats by the number of seats released
        const updatedTicket = await tx.flightTicket.update({
          where: { id: bookingData.ticketId },
          data: {
            remainingSeats: { increment: totalSeats },
          },
        });

        await tx.bookedFlightSeat.updateMany({
          where: { bookingId },
          data: { seatStatus: BookedFlightSeatStatus.canceled },
        });

        // Optionally: log action, emit notification, etc.
        await tx.bookingHistoryLog.create({
          data: {
            bookingId,
            changeType: "Booking Refunded",
            reason: `Booking ${bookingData.requestId} has been refunded.`,
            actorId: canceledBy,
            actorType: ActorType.CUSTOMER,
            oldStatus: bookingData.status,
            newStatus: BookingStatus.CANCELLED_BY_USER,
          },
        });

        await tx.ticketHistoryLog.create({
          data: {
            ticketId: updatedTicket.id,
            changeType: "Booking Refunded",
            changeDetails: JSON.stringify({
              comment: `Booking ${bookingData.requestId} has been refunded.`,
            }),
            oldValue: JSON.stringify({
              remainingSeats: ticket.remainingSeats,
              seats: ticket.seats,
            }),
            newValue: JSON.stringify({
              remainingSeats: updatedTicket.remainingSeats,
              seats: updatedTicket.seats,
            }),
            agencyId: bookingData.buyerAgencyId,
            agencyAgentId: bookingData.teamMemberId,
          },
        });

        // Get the buyer's user ID for notification
        const buyerOwnerId = bookingData.userId || bookingData.createdById;

        // Emit notification to buyer if we have a valid buyerOwnerId
        if (buyerOwnerId) {
          try {
            await NotificationService.createNotification({
              userId: buyerOwnerId,
              type: bookingData.type,
              title: "Booking Refunded",
              message: `Your booking (${bookingData.requestId}) has been refunded.`,
              relatedId: bookingData.id,
              link: `/booking/${bookingId}`,
              priority: 1,
            });
          } catch (error) {
            console.error("Failed to send refund notification:", error);
            // Log the error but don't fail the refund process
            await tx.bookingHistoryLog.create({
              data: {
                bookingId,
                changeType: "REFUND_NOTIFICATION_ERROR",
                reason: `Failed to send refund notification to user ${buyerOwnerId}`,
                actorId: null,
                actorType: ActorType.SYSTEM,
                oldStatus: bookingData.status,
                newStatus: BookingStatus.CANCELLED_BY_SYSTEM,
                metadata: {
                  error:
                    error instanceof Error ? error.message : "Unknown error",
                },
              },
            });
          }
        }

        // Emit notification to seller
        if (booking.ticket?.owner?.id) {
          await NotificationService.createNotification({
            userId: booking.ticket.owner.id,
            type: bookingData.type,
            title: "Booking Reversal",
            message: `Reversal for ${totalSeats} seats (Booking #${booking.requestId}).`,
            relatedId: bookingData.id,
            link: `/booking/${bookingId}`,
            priority: 1,
          });
        }

        // After the transaction completes successfully
        const finalMem = process.memoryUsage();
        console.log("Memory after transaction:", {
          rss: `${(finalMem.rss / (1024 * 1024)).toFixed(2)} MB`,
          heapUsed: `${(finalMem.heapUsed / (1024 * 1024)).toFixed(2)} MB`,
          rssDelta: `${((finalMem.rss - initialMem.rss) / (1024 * 1024)).toFixed(2)} MB`,
          heapUsedDelta: `${((finalMem.heapUsed - initialMem.heapUsed) / (1024 * 1024)).toFixed(2)} MB`,
        });

        memoryMonitor.takeSnapshot();

        return booking;
      });
    } catch (error: any) {
      // Log memory on error
      const errorMem = process.memoryUsage();
      console.error("Error during refund - Memory usage:", {
        rss: `${(errorMem.rss / (1024 * 1024)).toFixed(2)} MB`,
        heapUsed: `${(errorMem.heapUsed / (1024 * 1024)).toFixed(2)} MB`,
        error: error.message,
      });
      memoryMonitor.takeSnapshot();
      throw error;
    }
  }

  /**
   * Marks a booking as timed out, releases seats, notifies, and logs.
   * @param bookingId - The ID of the booking to expire
   */
  async timeoutBooking(bookingId: string): Promise<Booking> {
    if (!bookingId) throw new Error("bookingId is required.");

    return await prisma.$transaction(async (tx: any) => {
      // Fetch booking and check status
      const bookingData = await tx.booking.findUnique({
        where: { id: bookingId },
        include: { bookedSeats: true, travelers: true },
      });
      if (!bookingData) throw new Error("Booking not found.");

      // Only allow cancel if not already finalized
      const nonCancelableStatuses: BookingStatus[] = [
        BookingStatus.BOOKING_CONFIRMED,
        BookingStatus.BOOKING_REJECTED,
        BookingStatus.CANCELLED_BY_USER,
        BookingStatus.CANCELLED_BY_SYSTEM,
        BookingStatus.TIMED_OUT,
      ];
      if (nonCancelableStatuses.includes(bookingData.status)) {
        throw new Error("Booking cannot be timed out in its current state.");
      }

      // Transaction: update booking status, release seats, and increment remainingSeats
      const booking = await tx.booking.update({
        where: { id: bookingId },
        data: {
          status: BookingStatus.TIMED_OUT,
          cancellationReason: "The user's booking has timed out.",
        },
      });

      // Update ticket remaining seats
      // Increment the flight ticket's remainingSeats by the number of seats released
      const ticket = await tx.flightTicket.findUnique({
        where: { id: bookingData.ticketId },
      });
      // Get total number of travelers
      const totalSeats = bookingData.travelers.length;

      // Update ticket remaining seats
      // Increment the flight ticket's remainingSeats by the number of seats released
      const updatedTicket = await tx.flightTicket.update({
        where: { id: bookingData.ticketId },
        data: { remainingSeats: { increment: totalSeats } },
      });

      await tx.bookedFlightSeat.updateMany({
        where: { bookingId },
        data: { seatStatus: BookedFlightSeatStatus.canceled },
      });

      // Optionally: log action, emit notification, etc.
      await tx.bookingHistoryLog.create({
        data: {
          bookingId,
          changeType: "Booking Timed Out",
          reason: `Internal booking process expired ( ${bookingData.requestId} ).`,
          actorId: null,
          actorType: ActorType.SYSTEM,
          oldStatus: bookingData.status,
          newStatus: BookingStatus.TIMED_OUT,
        },
      });

      await tx.ticketHistoryLog.create({
        data: {
          ticketId: updatedTicket.id,
          changeType: "Booking Timed Out",
          changeDetails: JSON.stringify({
            comment: `Internal booking process expired ( ${bookingData.requestId} ).`,
          }),
          oldValue: JSON.stringify({
            remainingSeats: ticket.remainingSeats,
            seats: ticket.seats,
          }),
          newValue: JSON.stringify({
            remainingSeats: updatedTicket.remainingSeats,
            seats: updatedTicket.seats,
          }),
          agencyId: bookingData.buyerAgencyId,
          agencyAgentId: bookingData.teamMemberId,
        },
      });

      // Emit notification
      await NotificationService.createNotification({
        userId: bookingData.userId,
        type: bookingData.type,
        title: "Booking Timed Out",
        message: `Your internal booking ( ${bookingData.requestId} ) has timed out.`,
        relatedId: bookingData.id,
        link: `/booking/${bookingId}`,
        priority: 1,
      });

      return booking;
    });
  }

  /**
   * Identify booking type based on agent agency and ticket ownership
   * @param agentAgencyId string
   * @param ticketId string
   * @returns 'INTERNAL' | 'THIRD_PARTY'
   */
  async identifyBookingType(
    agentAgencyId: string,
    ticketId: string
  ): Promise<"INTERNAL" | "THIRD_PARTY"> {
    return await prisma.$transaction(async (tx: any) => {
      const ticket = await tx.flightTicket.findUnique({
        where: { id: ticketId },
        select: { ownerId: true },
      });
      if (!ticket) throw new Error("Ticket not found");

      // Check if the owner is directly the agency
      if (ticket.ownerId === agentAgencyId) {
        return "INTERNAL";
      }

      // Check if the owner is a user belonging to the agency
      const user = await tx.user.findUnique({
        where: { id: ticket.ownerId },
        select: { agencyName: true, agents: true },
      });

      if (
        user &&
        user.agencyName &&
        user.agents.some((agent: any) => agent.agencyId === agentAgencyId)
      ) {
        return "INTERNAL";
      }

      // Check if the owner is an agent belonging to the agency
      const agent = await tx.agencyAgent.findUnique({
        where: { id: ticket.ownerId },
        select: { agencyId: true },
      });

      if (agent && agent.agencyId === agentAgencyId) {
        return "INTERNAL";
      }

      return "THIRD_PARTY";
    });
  }

  /**
   * Confirm a booking by updating its status and generating a requestId
   * @param bookingId string
   * @returns updated booking
   */
  async confirmBooking(bookingId: string): Promise<Booking> {
    return await prisma.$transaction(async (tx: any) => {
      // Find booking, check eligibility, update status, generate requestId if needed
      const booking = await tx.booking.findUnique({
        where: { id: bookingId },
      });
      if (!booking) throw new Error("Booking not found");
      // ... additional checks as needed ...
      const updated = await tx.booking.update({
        where: { id: bookingId },
        data: {
          status: "PENDING_APPROVAL",
          requestId: booking.requestId || nanoid(),
          // ...other updates...
        },
      });
      return updated;
    });
  }

  /**
   * Creates a new third-party booking request for a flight ticket.
   * - Validates input data and ticket existence
   * - Deducts wallet balance
   * - Generates booking, ticket, transaction, and receipt
   * - Returns all details needed for confirmation
   * @param agentId - ID of the booking agent
   * @param bookingData - Data required to create the booking (travelers, seats, etc.)
   * @returns The created booking object with ticket/receipt info
   * @throws Error if validation fails or funds are insufficient
   */
  async createThirdPartyBooking(
    agentId: string,
    bookingData: CreateInternalBookingDto
  ): Promise<{ booking: Booking; eTicket: ETicket; receipt: Receipt }> {
    if (!agentId) throw new Error("Agent ID is required.");
    if (!bookingData) throw new Error("Booking data is required.");
    const {
      ticketId,
      returnTicketId,
      userId,
      travelers,
      seats,
      type,
      source,
      tripType,
    } = bookingData;

    // Validate return ticket for round-trip bookings
    if (tripType === "ROUND_TRIP" && !returnTicketId) {
      throw new Error("Return ticket ID is required for round-trip bookings");
    }

    if (!travelers || travelers.length === 0) {
      throw new Error("Travelers are required.");
    }

    // Validate booking data using Joi schema
    const { error } = createBookingSchema.validate(bookingData, {
      abortEarly: false,
    });
    if (error) {
      const messages = error.details.map((d) => d.message).join("; ");
      throw new Error(`Booking validation error: ${messages}`);
    }
    try {
      const thirdPartyBooking = await prisma.$transaction(async (tx: any) => {
        let agentName = await tx.user.findUnique({
          where: { id: userId },
          select: { firstName: true, lastName: true },
        });
        if (!agentName) {
          agentName = await tx.teamMember.findUnique({
            where: { id: userId },
            select: { firstName: true, lastName: true },
          });
          if (!agentName) {
            agentName = await tx.agencyAgent.findUnique({
              where: { id: userId },
              select: { firstName: true, lastName: true },
            });
          }
        }
        // 1. Validate ticket existence and fetch seat info with row-level locking
        const ticket = await tx.flightTicket.findUnique({
          where: { id: ticketId },
          select: {
            id: true,
            seats: true,
            refId: true,
            remainingSeats: true,
            flightClasses: true,
            ownerId: true,
            segments: true,
            departure: true,
            arrival: true,
            departureTime: true,
            arrivalTime: true,
            duration: true,
            stops: true,
            owner: true,
            ticketStatus: true,
            flightDate: true,
          },
        });
        if (!ticket) throw new Error("Flight ticket not found.");

        // Check if flight date is in the past
        if (this.isFlightDateInPast(ticket.flightDate)) {
          const error: any = new Error(
            `Cannot book a flight that has already departed. The flight date ${new Date(ticket.flightDate).toLocaleDateString()} is in the past.`
          );
          error.code = "FLIGHT_DEPARTED";
          error.status = 400;
          throw error;
        }
        const now = new Date();
        const sixHoursFromNow = new Date(now.getTime() + 6 * 60 * 60 * 1000);
        const departureTime = new Date(ticket.departureTime);
        if (departureTime < sixHoursFromNow) {
          const error: any = new Error(
            `Cannot book a flight that departs in less than 6 hours. The flight departs at ${departureTime.toLocaleString()}.`
          );
          error.code = "FLIGHT_TOO_SOON";
          error.status = 400;
          throw error;
        }

        let returnTicket;
        if (tripType === "ROUND_TRIP") {
          returnTicket = await tx.flightTicket.findUnique({
            where: { id: returnTicketId },
            select: {
              id: true,
              seats: true,
              refId: true,
              remainingSeats: true,
              flightClasses: true,
              ownerId: true,
              segments: true,
              departure: true,
              arrival: true,
              departureTime: true,
              arrivalTime: true,
              duration: true,
              stops: true,
              owner: true,
              ticketStatus: true,
              flightDate: true,
            },
          });
          if (!returnTicket) throw new Error("Return flight ticket not found.");

          // Check if return flight date is in the past
          if (this.isFlightDateInPast(returnTicket.flightDate)) {
            const error: any = new Error(
              `Cannot book a return flight that has already departed. The return flight date ${new Date(returnTicket.flightDate).toLocaleDateString()} is in the past.`
            );
            error.code = "RETURN_FLIGHT_DEPARTED";
            error.status = 400;
            throw error;
          }

          // Check if return flight is within 6 hours from now
          const returnDepartureTime = new Date(returnTicket.departureTime);

          if (returnDepartureTime < sixHoursFromNow) {
            const error: any = new Error(
              `Cannot book a return flight that departs in less than 6 hours. The return flight departs at ${returnDepartureTime.toLocaleString()}.`
            );
            error.code = "RETURN_FLIGHT_TOO_SOON";
            error.status = 400;
            throw error;
          }
        }

        // 2. Validate ticket status
        if (ticket.ticketStatus === "unavailable") {
          const error: any = new Error(
            "This ticket is no longer available for booking."
          );
          error.code = "TICKET_UNAVAILABLE";
          error.status = 410; // Gone
          throw error;
        }

        if (tripType === "ROUND_TRIP") {
          if (returnTicket.ticketStatus === "unavailable") {
            const error: any = new Error(
              "This return ticket is no longer available for booking."
            );
            error.code = "RETURN_TICKET_UNAVAILABLE";
            error.status = 410; // Gone
            throw error;
          }
        }

        // 3. Validate seat availability
        const travelersCount = travelers?.length || 0;
        const requestedSeatCount = Array.isArray(travelers)
          ? travelers.length
          : 0;

        if (travelersCount !== requestedSeatCount) {
          const error: any = new Error(
            "Number of travelers does not match the number of seats selected."
          );
          error.code = "INVALID_TRAVELER_COUNT";
          error.status = 400;
          throw error;
        }

        if (requestedSeatCount === 0 || travelersCount === 0) {
          const error: any = new Error("No seats selected for booking.");
          error.code = "NO_SEATS_SELECTED";
          error.status = 400;
          console.error(`[ThirdPartyBooking] Error: ${error.message}`);
          throw error;
        }

        // 4. Check if we have enough seats available
        if (
          ticket.remainingSeats < requestedSeatCount ||
          ticket.remainingSeats < travelersCount
        ) {
          const error: any = new Error(
            `Not enough available seats for this booking. Requested ${requestedSeatCount} but only ${ticket.remainingSeats} available.`
          );
          console.error(`[ThirdPartyBooking] Error: ${error.message}`);
          error.code = "NOT_ENOUGH_SEATS";
          error.status = 409;
          error.availableSeats = ticket.remainingSeats;
          error.requestedSeats = requestedSeatCount;
          throw error;
        }

        if (tripType === "ROUND_TRIP") {
          if (
            returnTicket.remainingSeats < requestedSeatCount ||
            returnTicket.remainingSeats < travelersCount
          ) {
            const error: any = new Error(
              `Not enough available seats for this booking. Requested ${requestedSeatCount} but only ${returnTicket.remainingSeats} available.`
            );
            console.error(`[ThirdPartyBooking] Error: ${error.message}`);
            error.code = "NOT_ENOUGH_SEATS";
            error.status = 409;
            error.availableSeats = returnTicket.remainingSeats;
            error.requestedSeats = requestedSeatCount;
            throw error;
          }
        }

        // Additional validation - ensure requestedSeatCount is positive
        if (requestedSeatCount <= 0 || travelersCount <= 0) {
          const error: any = new Error("Invalid number of seats requested.");
          error.code = "INVALID_SEAT_COUNT";
          error.status = 400;
          console.error(
            `[ThirdPartyBooking] Error: ${error.message} (${requestedSeatCount} seats)`
          );
          throw error;
        }
        // 3. Calculate total price
        const totalAmount = seats.reduce(
          (sum, seat) => sum + seat.totalPrice,
          0
        );
        // 4. Deduct wallet balance
        // const wallet = await tx.user.findUnique({ where: { id: agentId } });
        // if (!wallet || wallet.creditBalance.lt(totalAmount)) {
        //   const err: any = new Error("Insufficient wallet balance.");
        //   err.code = "INSUFFICIENT_WALLET";
        //   err.status = 402;
        //   throw err;
        // }
        // await tx.user.update({
        //   where: { id: agentId },
        //   data: { creditBalance: { decrement: totalAmount } },
        // });

        // 0. Wallet balance check - Fetch from associated agency owner
        let walletOwnerId = agentId;
        const teamMember = await tx.teamMember.findUnique({
          where: { id: agentId },
          select: { createdById: true },
        });
        if (teamMember && teamMember.createdById) {
          walletOwnerId = teamMember.createdById;
        } else {
          const agencyAgent = await tx.agencyAgent.findUnique({
            where: { id: agentId },
            select: { agencyId: true },
          });
          if (agencyAgent && agencyAgent.agencyId) {
            walletOwnerId = agencyAgent.agencyId;
          }
        }

        const wallet = await tx.user.findUnique({
          where: { id: walletOwnerId },
        });
        if (!wallet || wallet.creditBalance.lt(totalAmount)) {
          const err: any = new Error("Insufficient wallet balance.");
          err.code = "INSUFFICIENT_WALLET";
          err.status = 402;
          throw err;
        }
        await tx.user.update({
          where: { id: walletOwnerId },
          data: { creditBalance: { decrement: totalAmount } },
        });

        // 6. Verify ticket status again right before update
        const currentTicketStatus = await tx.flightTicket.findUnique({
          where: { id: ticketId },
          select: { remainingSeats: true, ticketStatus: true },
        });

        if (
          !currentTicketStatus ||
          currentTicketStatus.ticketStatus === "unavailable" ||
          currentTicketStatus.remainingSeats < requestedSeatCount
        ) {
          const available = currentTicketStatus?.remainingSeats || 0;
          console.error(
            `[ThirdPartyBooking] Ticket status changed. Available: ${available}, Status: ${currentTicketStatus?.ticketStatus}`
          );
          const error: any = new Error(
            `Ticket availability changed. Only ${available} seat${available !== 1 ? "s" : ""} now available.`
          );
          error.code = "SEAT_AVAILABILITY_CHANGED";
          error.status = 409;
          error.availableSeats = available;
          error.requestedSeats = requestedSeatCount;
          throw error;
        }

        if (tripType === "ROUND_TRIP") {
          const currentReturnTicketStatus = await tx.flightTicket.findUnique({
            where: { id: returnTicketId },
            select: { remainingSeats: true, ticketStatus: true },
          });

          if (
            !currentReturnTicketStatus ||
            currentReturnTicketStatus.ticketStatus === "unavailable" ||
            currentReturnTicketStatus.remainingSeats < requestedSeatCount
          ) {
            const available = currentReturnTicketStatus?.remainingSeats || 0;
            console.error(
              `[ThirdPartyBooking] Return ticket status changed. Available: ${available}, Status: ${currentReturnTicketStatus?.ticketStatus}`
            );
            const error: any = new Error(
              `Return ticket availability changed. Only ${available} seat${available !== 1 ? "s" : ""} now available.`
            );
            error.code = "RETURN_TICKET_AVAILABILITY_CHANGED";
            error.status = 409;
            error.availableSeats = available;
            error.requestedSeats = requestedSeatCount;
            throw error;
          }
        }

        // 9. Atomically update remainingSeats and check ticket status
        const updatedTicket = await tx.flightTicket.update({
          where: {
            id: ticketId,
            // Ensure the remainingSeats hasn't changed since we checked
            remainingSeats: { gte: requestedSeatCount },
            // Ensure ticket is still available
            ticketStatus: { not: "unavailable" },
          },
          data: {
            remainingSeats: {
              decrement: requestedSeatCount,
            },
            // If this update would make remainingSeats 0, also update status
            ...(currentTicketStatus.remainingSeats <= requestedSeatCount
              ? {
                  ticketStatus: "unavailable",
                }
              : {}),
          },
          select: {
            remainingSeats: true,
            ticketStatus: true,
          },
        });

        let updatedReturnTicket;
        if (tripType === "ROUND_TRIP") {
          updatedReturnTicket = await tx.flightTicket.update({
            where: {
              id: returnTicketId,
              // Ensure the remainingSeats hasn't changed since we checked
              remainingSeats: { gte: requestedSeatCount },
              // Ensure ticket is still available
              ticketStatus: { not: "unavailable" },
            },
            data: {
              remainingSeats: {
                decrement: requestedSeatCount,
              },
              // If this update would make remainingSeats 0, also update status
              ...(currentTicketStatus.remainingSeats <= requestedSeatCount
                ? {
                    ticketStatus: "unavailable",
                  }
                : {}),
            },
            select: {
              remainingSeats: true,
              ticketStatus: true,
            },
          });
        }

        // If no rows were updated, it means the ticket was modified by another transaction
        if (!updatedTicket) {
          try {
            // Get the latest ticket info for a more accurate error message
            const currentTicket = await tx.flightTicket.findUnique({
              where: { id: ticketId },
              select: {
                remainingSeats: true,
                ticketStatus: true,
                updatedAt: true,
              },
            });

            const availableSeats = currentTicket?.remainingSeats || 0;
            const lastUpdated = currentTicket?.updatedAt
              ? new Date(currentTicket.updatedAt).toISOString()
              : "unknown";

            console.error(
              `[ThirdPartyBooking] Failed to update ticket ${ticketId}. Current state:`,
              {
                availableSeats,
                status: currentTicket?.ticketStatus,
                lastUpdated,
                requestedSeats: requestedSeatCount,
              }
            );

            const error: any = new Error(
              `Seat availability changed. Only ${availableSeats} seat${availableSeats !== 1 ? "s" : ""} now available.`
            );
            error.code = "SEAT_AVAILABILITY_CHANGED";
            error.status = 409;
            error.availableSeats = availableSeats;
            error.requestedSeats = requestedSeatCount;
            error.lastUpdated = lastUpdated;
            throw error;
          } catch (err) {
            console.error(
              `[ThirdPartyBooking] Error while checking ticket status for ${ticketId}:`,
              err
            );
            const error: any = new Error(
              "Unable to verify seat availability. Please try again."
            );
            error.code = "SEAT_VERIFICATION_ERROR";
            error.status = 500;
            throw error;
          }
        }

        if (tripType === "ROUND_TRIP") {
          if (!updatedReturnTicket) {
            try {
              const currentReturnTicketStatus =
                await tx.flightTicket.findUnique({
                  where: { id: returnTicketId },
                  select: {
                    remainingSeats: true,
                    ticketStatus: true,
                    updatedAt: true,
                  },
                });
              const lastUpdated = currentReturnTicketStatus?.updatedAt
                ? new Date(currentReturnTicketStatus.updatedAt)
                : null;
              const availableSeats = currentReturnTicketStatus?.remainingSeats;

              const error: any = new Error(
                `Return ticket availability changed. Only ${availableSeats} seat${availableSeats !== 1 ? "s" : ""} now available.`
              );
              error.code = "RETURN_TICKET_AVAILABILITY_CHANGED";
              error.status = 409;
              error.availableSeats = availableSeats;
              error.requestedSeats = requestedSeatCount;
              error.lastUpdated = lastUpdated;
              throw error;
            } catch (err) {
              console.error(
                `[ThirdPartyBooking] Error while checking return ticket status for ${returnTicketId}:`,
                err
              );
              const error: any = new Error(
                "Unable to verify seat availability. Please try again."
              );
              error.code = "SEAT_VERIFICATION_ERROR";
              error.status = 500;
              throw error;
            }
          }
        }

        // 5. Create travelers if needed
        let travelerIds: string[] = [];
        if (Array.isArray(travelers) && travelers.length > 0) {
          for (const t of travelers) {
            let travelerId = t.travelerId;
            if (!travelerId) {
              // Defensive date validation and type narrowing
              const isIsoDate = (date: string) =>
                typeof date === "string" && /^\d{4}-\d{2}-\d{2}$/.test(date);
              if (
                !t.dateOfBirth ||
                !t.expirationDate ||
                !isIsoDate(t.dateOfBirth) ||
                !isIsoDate(t.expirationDate)
              ) {
                throw new Error(
                  "Traveler dateOfBirth and expirationDate must be valid ISO dates (YYYY-MM-DD)."
                );
              }
              // Convert 'YYYY-MM-DD' to JS Date object (ISO 8601)
              const toIsoDateTime = (date: string) =>
                new Date(date + "T00:00:00.000Z");
              const newTraveler = await tx.traveler.create({
                data: {
                  title: t.title,
                  firstName: t.firstName,
                  lastName: t.lastName,
                  nationality: t.nationality,
                  dateOfBirth: toIsoDateTime(t.dateOfBirth),
                  gender: t.gender,
                  documentType: "passport",
                  documentNumber: t.documentNumber,
                  issuingCountry: t.issuingCountry,
                  expirationDate: toIsoDateTime(t.expirationDate),
                  contactEmail: t.contactEmail,
                  contactPhone: t.contactPhone,
                },
              });
              travelerId = newTraveler.id;
            }
            if (travelerId) {
              travelerIds.push(travelerId);
            }
          }
        }
        // 6. Create Booking with nested travelers and bookedSeats
        const booking = await tx.booking.create({
          data: {
            id: `TPB-${nanoidAlphaNum()}`,
            ticketId,
            userId,
            agencyAgentId: agentId,
            sellerAgencyId: ticket.ownerId, // Set seller agency to departure ticket owner
            buyerAgencyId: walletOwnerId, // Set buyer agency (assuming agentId is agency, else resolve agencyId from agent)
            requestId: `BC-${nanoidAlphaNum()}`,
            referenceNumber: `BC-${nanoidAlphaNum()}`,
            transactionId: `TRN-${nanoidAlphaNum()}`,
            type: BookingType.SUBMIT_BOOKING,
            source: BookingSource.THIRD_PARTY,
            status: BookingStatus.BOOKING_CONFIRMED,
            transactionDate: new Date(),
            paymentCompletedAt: new Date(),
            timerStartedAt: new Date(), // or undefined if allowed
            expiresAt: new Date(), // or undefined if allowed
            timerDuration: 0, // or some default value
            tripType: tripType,
            meta: this.generateBookingMeta(
              ticket, // Departure ticket
              tripType === "ROUND_TRIP" ? returnTicket : null, // Return ticket if exists
              seats, // All seats (they should have legType)
              tripType, // 'ONE_WAY' or 'ROUND_TRIP'
              wallet,
              walletOwnerId,
              agentName
            ),
            travelers: {
              create: travelerIds.map((travelerId) => ({
                traveler: { connect: { id: travelerId } },
              })),
            },
            bookedSeats: {
              create: seats.map((seat, idx) => ({
                seatNumber: seat.seatNumber,
                flightClass: seat.flightClass,
                seatStatus: "booked",
                totalPrice: seat.totalPrice,
                bookedByAgency: agentId,
                flightTicket: { connect: { id: ticketId } },
                traveler: travelerIds[idx]
                  ? { connect: { id: travelerIds[idx] } }
                  : undefined,
              })),
            },
            bookingHistoryLogs: {
              create: [
                {
                  changeType: "CREATE",
                  oldStatus: null,
                  newStatus: BookingStatus.BOOKING_CONFIRMED,
                  actorType: ActorType.CUSTOMER,
                  actorId: agentId,
                  reason: "Third-party booking was created and confirmed.",
                  createdAt: new Date(),
                },
              ],
            },
          },
          include: {
            travelers: { include: { traveler: true } },
            bookedSeats: true,
            ticket: true,
            payment: true,
          },
        });

        let returnBooking;
        if (tripType === "ROUND_TRIP") {
          // Create a new array of traveler IDs for the return booking to avoid unique constraint violation
          const returnTravelerIds = await Promise.all(
            travelerIds.map(async (travelerId) => {
              const traveler = await tx.traveler.findUnique({
                where: { id: travelerId },
              });

              // Create a new traveler record with the same details but a new ID
              const newTraveler = await tx.traveler.create({
                data: {
                  ...traveler,
                  id: undefined, // Let Prisma generate a new ID
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              });

              return newTraveler.id;
            })
          );

          // Generate a single request ID for both outbound and return bookings
          const roundTripReferenceNumber = `BC-${nanoidAlphaNum()}`;

          // Update the outbound booking with the round-trip request ID
          await tx.booking.update({
            where: { id: booking.id },
            data: { referenceNumber: roundTripReferenceNumber },
          });

          returnBooking = await tx.booking.create({
            data: {
              id: `TPB-${nanoidAlphaNum()}`,
              ticketId: returnTicketId,
              userId,
              agencyAgentId: agentId,
              sellerAgencyId: returnTicket?.ownerId, // Set seller agency to departure ticket owner
              buyerAgencyId: walletOwnerId, // Set buyer agency (assuming agentId is agency, else resolve agencyId from agent)
              requestId: `BR-${nanoidAlphaNum()}`,
              referenceNumber: roundTripReferenceNumber, // Same reference number as outbound
              transactionId: `TRN-${nanoidAlphaNum()}`,
              type: BookingType.SUBMIT_BOOKING,
              source: BookingSource.THIRD_PARTY,
              status: BookingStatus.BOOKING_CONFIRMED,
              transactionDate: new Date(),
              paymentCompletedAt: new Date(),
              timerStartedAt: new Date(), // or undefined if allowed
              expiresAt: new Date(), // or undefined if allowed
              timerDuration: 0, // or some default value
              tripType: tripType,
              meta: this.generateBookingMeta(
                ticket, // Departure ticket
                returnTicket, // Return ticket if exists
                seats, // All seats (they should have legType)
                tripType, // 'ONE_WAY' or 'ROUND_TRIP'
                wallet,
                walletOwnerId,
                agentName
              ),
              travelers: {
                create: returnTravelerIds.map((travelerId) => ({
                  traveler: { connect: { id: travelerId } },
                })),
              },
              bookedSeats: {
                create: seats.map((seat, idx) => ({
                  seatNumber: seat.seatNumber,
                  flightClass: seat.flightClass,
                  seatStatus: "booked",
                  totalPrice: seat.totalPrice,
                  bookedByAgency: agentId,
                  flightTicket: { connect: { id: returnTicketId } },
                  traveler: returnTravelerIds[idx]
                    ? { connect: { id: returnTravelerIds[idx] } }
                    : undefined,
                })),
              },
              bookingHistoryLogs: {
                create: [
                  {
                    changeType: "CREATE",
                    oldStatus: null,
                    newStatus: BookingStatus.BOOKING_CONFIRMED,
                    actorType: ActorType.CUSTOMER,
                    actorId: agentId,
                    reason: "Third-party booking was created and confirmed.",
                    createdAt: new Date(),
                  },
                ],
              },
            },
            include: {
              travelers: { include: { traveler: true } },
              bookedSeats: true,
              ticket: true,
              payment: true,
            },
          });
        }

        // credit balance deduction
        const sellerId = ticket.ownerId;
        const returnSellerId = returnTicket?.ownerId;

        // a. Credit the ticket owner's (seller's) balance
        const updatedSeller = await tx.user.update({
          where: { id: sellerId }, // sellerId is the owner of the ticket
          data: { creditBalance: { increment: totalAmount } },
        });

        // b. Credit the return ticket owner's (seller's) balance
        let updatedReturnSeller = null;
        if (returnSellerId) {
          updatedReturnSeller = await tx.user.update({
            where: { id: returnSellerId },
            data: { creditBalance: { increment: totalAmount } },
          });
        }

        // b. Log the credit transaction for the seller
        const user = await tx.user.findUnique({
          where: { id: booking.userId },
          select: { firstName: true, lastName: true },
        });
        const userName =
          `${user?.firstName} ${user?.lastName}` || booking.userId;

        await tx.creditTransaction.create({
          data: {
            userId: sellerId,
            amount: totalAmount,
            referenceId: booking.id, // or ticketId if you prefer
            balanceAfter: updatedSeller.creditBalance,
            type: "transfer",
          },
        });

        // b. Log the credit transaction for the seller
        let returnUser = null;
        if (returnSellerId) {
          returnUser = await tx.user.findUnique({
            where: { id: returnSellerId },
            select: { firstName: true, lastName: true },
          });
        }
        const returnUserName =
          `${returnUser?.firstName} ${returnUser?.lastName}` || returnSellerId;

        if (returnTicket) {
          await tx.creditTransaction.create({
            data: {
              userId: returnSellerId,
              amount: totalAmount,
              referenceId: booking.id, // or ticketId if you prefer
              balanceAfter: updatedReturnSeller.creditBalance,
              type: "transfer",
            },
          });
        }

        // 7. Generate e-ticket and receipt (source/type already handled in DTO)
        const eTicket = await this.generateThirdPartyTicket(booking.id, tx);
        const receipt = await this.generateThirdPartyReceipt(
          booking.id,
          eTicket.id,
          agentId,
          tx
        );

        let returnETicket = null;
        let returnReceipt = null;
        if (tripType === "ROUND_TRIP") {
          returnETicket = await this.generateThirdPartyTicket(
            returnBooking.id,
            tx
          );
          returnReceipt = await this.generateThirdPartyReceipt(
            returnBooking.id,
            returnETicket.id,
            agentId,
            tx
          );
        }

        // Send notifications after transaction commit
        // Seller agent notification
        await NotificationService.createNotification({
          userId: sellerId,
          type: booking.type,
          title: "Third-Party Booking Created",
          message: `Your ticket ( ${ticket.refId} ) has been sold. seats available now: ${updatedTicket.remainingSeats}`,
          relatedId: booking.id,
          link: `/booking/${booking.id}`,
          priority: 2,
        });

        if (returnTicket) {
          // Return seller agent notification
          await NotificationService.createNotification({
            userId: returnSellerId,
            type: returnBooking.type,
            title: "Third-Party Booking Created",
            message: `Your ticket ( ${returnTicket.refId} ) has been sold. seats available now: ${updatedReturnTicket.remainingSeats}`,
            relatedId: returnBooking.id,
            link: `/booking/${returnBooking.id}`,
            priority: 2,
          });
        }

        // Booking agent notification
        await NotificationService.createNotification({
          userId: booking.userId,
          type: booking.type,
          title: "Third-Party Booking Confirmed",
          message: `Booking ( ${booking.requestId} ) has been successfully confirmed.`,
          relatedId: booking.id,
          link: `/booking/${booking.id}`,
          priority: 2,
        });

        // 9. Log action
        await tx.ticketHistoryLog.create({
          data: {
            ticketId: ticketId,
            changeType: "Seats Updated",
            changeDetails: JSON.stringify({
              comment: `Seats updated due to third-party booking ( ${booking.requestId} ).`,
            }),
            oldValue: JSON.stringify({
              remainingSeats: ticket.remainingSeats,
              seats: ticket.seats,
            }),
            newValue: JSON.stringify({
              remainingSeats: updatedTicket.remainingSeats,
              seats: updatedTicket.seats,
            }),
            agencyId: ticket.ownerId,
            agencyAgentId: null,
          },
        });

        if (returnTicket) {
          await tx.ticketHistoryLog.create({
            data: {
              ticketId: returnTicket.id,
              changeType: "Seats Updated",
              changeDetails: JSON.stringify({
                comment: `Seats updated due to third-party booking ( ${booking.requestId} ).`,
              }),
              oldValue: JSON.stringify({
                remainingSeats: returnTicket.remainingSeats,
                seats: returnTicket.seats,
              }),
              newValue: JSON.stringify({
                remainingSeats: updatedReturnTicket.remainingSeats,
                seats: updatedReturnTicket.seats,
              }),
              agencyId: returnTicket.ownerId,
              agencyAgentId: null,
            },
          });
        }

        if (tripType === "ROUND_TRIP" && returnBooking) {
          return {
            booking,
            eTicket,
            receipt,
            returnBooking: returnBooking || null,
            returnETicket,
            returnReceipt,
          };
        }
        return { booking, eTicket, receipt, returnBooking: null };
      });
      return thirdPartyBooking;
    } catch (err: any) {
      console.error("Third-party booking creation failed:", err);
      if (err && err.code === "NOT_ENOUGH_SEATS") throw err;
      if (err && err.code === "INSUFFICIENT_WALLET") throw err;
      if (err && err.code === "FLIGHT_TOO_SOON") throw err;
      if (err && err.code === "FLIGHT_DEPARTED") throw err;
      throw new Error(
        "Failed to create third-party booking. Please try again later."
      );
    }
  }

  // Generate e-ticket for third-party booking
  async generateThirdPartyTicket(bookingId: string, tx: any): Promise<any> {
    // Implement ticket generation logic (PDF or record)
    // Placeholder: create a ticket record
    const ticket = await tx.eTicket.create({
      data: {
        bookingId,
        eTicketNumber: `AVC-${nanoidAlphaNum()}`,
        ticketType: bookingId.startsWith("TPB") ? "THIRD_PARTY" : "INTERNAL",
        issuedAt: new Date(),
      },
    });
    return ticket;
  }

  /**
   * Calculates payment metadata including passenger counts, prices, and totals
   * Handles both one-way and round-trip with separate pricing for each leg
   * @param seats - Array of booked seats with pricing information
   * @param tripType - Type of trip (ONE_WAY or ROUND_TRIP)
   * @returns Object containing calculated payment metadata
   */
  private calculatePaymentMetadata(
    seats: any[],
    tripType: "ONE_WAY" | "ROUND_TRIP"
  ) {
    // Initialize passenger counts with explicit type
    interface PassengerCounts {
      adults: number;
      children: number;
      infants: number;
      total: number;
      [key: string]: number; // Index signature to allow dynamic access
    }

    interface PassengerPricing {
      adult: { price: number; currency: string; taxRate: number };
      child: { price: number; currency: string; taxRate: number };
      infant: { price: number; currency: string; taxRate: number };
    }

    interface TripPricing {
      subtotal: number;
      taxes: number;
      fees: number;
      total: number;
      currency: string;
      passengerCounts: PassengerCounts;
    }
    interface PassengerPricingInfo {
      totalPrice: number;
      count: number;
      taxRate: number;
    }

    type PassengerType = "adult" | "child" | "infant";
    type PricingStructure = {
      [K in PassengerType]: PassengerPricingInfo;
    };

    // Initialize passenger counts for departure
    const departureCounts: PassengerCounts = {
      adults: 0,
      children: 0,
      infants: 0,
      total: 0,
    };

    const returnCounts: PassengerCounts = {
      adults: 0,
      children: 0,
      infants: 0,
      total: 0,
    };

    // Track total prices for each passenger type and leg
    const departurePricing: PricingStructure = {
      adult: { totalPrice: 0, count: 0, taxRate: 0 },
      child: { totalPrice: 0, count: 0, taxRate: 0 },
      infant: { totalPrice: 0, count: 0, taxRate: 0 },
    };

    const returnPricing: PricingStructure = {
      adult: { totalPrice: 0, count: 0, taxRate: 0 },
      child: { totalPrice: 0, count: 0, taxRate: 0 },
      infant: { totalPrice: 0, count: 0, taxRate: 0 },
    };

    // Track currency for each leg
    let departureCurrency = "JOD";
    let returnCurrency = "JOD";
    let departureCurrencySet = false;
    let returnCurrencySet = false;

    // Calculate passenger counts and accumulate prices for each leg
    seats.forEach((seat) => {
      const passengerType =
        (seat.passengerType?.toLowerCase() as PassengerType) || "adult";
      const price = parseFloat(seat.price || "0");
      const taxRate = parseFloat(seat.taxRate || "0");

      if (seat.legType === "return") {
        // Set return currency from first return seat
        if (!returnCurrencySet && seat.currency) {
          returnCurrency = seat.currency;
          returnCurrencySet = true;
        }
        returnCounts[passengerType]++;
        returnCounts.total++;
        returnPricing[passengerType].totalPrice += price;
        returnPricing[passengerType].count++;
        returnPricing[passengerType].taxRate = taxRate; // Assuming same tax rate for all seats of same type on same leg
      } else {
        // Set departure currency from first departure seat
        if (!departureCurrencySet && seat.currency) {
          departureCurrency = seat.currency;
          departureCurrencySet = true;
        }
        departureCounts[passengerType]++;
        departureCounts.total++;
        departurePricing[passengerType].totalPrice += price;
        departurePricing[passengerType].count++;
        departurePricing[passengerType].taxRate = taxRate; // Assuming same tax rate for all seats of same type on same leg
      }
    });

    // Helper function to calculate trip pricing
    const calculateTripPricing = (
      counts: PassengerCounts,
      pricing: PricingStructure,
      currency: string
    ): TripPricing => {
      // Calculate average price per passenger type
      const adultAvg =
        counts.adults > 0 ? pricing.adult.totalPrice / counts.adults : 0;
      const childAvg =
        counts.children > 0 ? pricing.child.totalPrice / counts.children : 0;
      const infantAvg =
        counts.infants > 0 ? pricing.infant.totalPrice / counts.infants : 0;

      const adultTotal = counts.adults * adultAvg;
      const childTotal = counts.children * childAvg;
      const infantTotal = counts.infants * infantAvg;

      const subtotal = adultTotal + childTotal + infantTotal;

      // Calculate weighted average tax rate
      const totalPassengers = counts.total || 1; // Avoid division by zero
      const avgTaxRate =
        (counts.adults * pricing.adult.taxRate +
          counts.children * pricing.child.taxRate +
          counts.infants * pricing.infant.taxRate) /
        totalPassengers;

      const taxes = subtotal * (avgTaxRate / 100);
      const fees = 0; // Add any additional fees here if needed
      const total = subtotal + taxes + fees;

      return {
        subtotal: parseFloat(subtotal.toFixed(2)),
        taxes: parseFloat(taxes.toFixed(2)),
        fees: parseFloat(fees.toFixed(2)),
        total: parseFloat(total.toFixed(2)),
        currency,
        passengerCounts: { ...counts },
      };
    };

    // Calculate pricing for each leg using the actual seat prices
    const departurePricingResult = calculateTripPricing(
      departureCounts,
      departurePricing,
      departureCurrency
    );

    const returnPricingResult =
      tripType === "ROUND_TRIP"
        ? calculateTripPricing(returnCounts, returnPricing, returnCurrency)
        : null;
    // Calculate pricing for departure
    const departure = departurePricingResult;

    // Calculate pricing for return (if round-trip)
    const returnTrip = tripType === "ROUND_TRIP" ? returnPricingResult : null;

    // Calculate grand total (converting all to a common currency if needed)
    // For now, we'll just sum the totals (in a real app, you'd convert currencies)
    const grandTotal = departure.total + (returnTrip?.total || 0);

    // Construct the metadata object
    const paymentMetadata = {
      tripType,
      passengerCounts: departure.passengerCounts, // Total counts across both legs
      pricing: {
        departure: {
          ...departure,
          passengerPricing: departurePricing,
        },
        ...(returnTrip && {
          return: {
            ...returnTrip,
            passengerPricing: returnPricing,
          },
        }),
        grandTotal: parseFloat(grandTotal.toFixed(2)),
        // In a real app, you might want to include currency conversion rates used
        currencyRates: {
          // Example: 'JOD_TO_USD': 1.41
        },
      },
      calculationTimestamp: new Date().toISOString(),
    };

    return paymentMetadata;
  }

  /**
   * Generates the complete meta object for a third-party booking
   * @param ticket - The flight ticket being booked
   * @param seats - Array of booked seats with pricing information
   * @param tripType - Type of trip (ONE_WAY or ROUND_TRIP)
   * @param wallet - The buyer's wallet information
   * @param walletOwnerId - ID of the wallet owner
   * @param agentName - Name of the booking agent
   * @returns Complete meta object with all booking metadata
   */
  private generateBookingMeta(
    departureTicket: any,
    returnTicket: any,
    seats: any[],
    tripType: "ONE_WAY" | "ROUND_TRIP",
    wallet: any,
    walletOwnerId: string,
    agentName: { firstName: string; lastName: string }
  ) {
    // Generate base meta with flight and agency info
    const baseMeta = {
      // Flight information
      // Departure flight info
      departure: {
        carrier: departureTicket.segments[0]?.carrier,
        flightNumber: departureTicket.segments[0]?.flightNumber,
        departureAirport: departureTicket.departure?.airportCode,
        arrivalAirport: departureTicket.arrival?.airportCode,
        departureTime: departureTicket.departureTime,
        arrivalTime: departureTicket.arrivalTime,
      },
      // Add return flight info if it exists
      ...(returnTicket && tripType === "ROUND_TRIP"
        ? {
            return: {
              carrier: returnTicket.segments[0]?.carrier,
              flightNumber: returnTicket.segments[0]?.flightNumber,
              departureAirport: returnTicket.departure?.airportCode,
              arrivalAirport: returnTicket.arrival?.airportCode,
              departureTime: returnTicket.departureTime,
              arrivalTime: returnTicket.arrivalTime,
            },
          }
        : {}),

      // Agency and agent information
      buyerAgencyName: wallet?.agencyName,
      buyerAgencyId: walletOwnerId,
      buyerAgentName: `${agentName?.firstName} ${agentName?.lastName}`,

      // Departure ticket seller info
      departureSeller: {
        name: `${departureTicket.owner?.firstName} ${departureTicket.owner?.lastName}`,
        agencyName: departureTicket.owner?.agencyName,
        agencyId: departureTicket.ownerId,
        ticketId: departureTicket.id,
      },

      // Return ticket seller info (if exists)
      ...(returnTicket && tripType === "ROUND_TRIP"
        ? {
            returnSeller: {
              name: `${returnTicket.owner?.firstName} ${returnTicket.owner?.lastName}`,
              agencyName: returnTicket.owner?.agencyName,
              agencyId: returnTicket.ownerId,
              ticketId: returnTicket.id,
            },
          }
        : {}),

      // Timestamps
      bookingCreatedAt: new Date().toISOString(),
      paymentCompletedAt: new Date().toISOString(),
    };

    // Add payment metadata
    const paymentMeta = this.calculatePaymentMetadata(seats, tripType);

    // Combine and return
    return {
      ...baseMeta,
      ...paymentMeta,
    };
  }

  // Generate receipt for third-party booking
  async generateThirdPartyReceipt(
    bookingId: string,
    eTicketId: string,
    agentId: string,
    tx: any
  ): Promise<any> {
    // Implement receipt generation logic (PDF or record)
    // Placeholder: create a receipt record
    const receipt = await tx.receipt.create({
      data: {
        bookingId,
        eTicketId,
        receiptNumber: `RCT-${nanoidAlphaNum()}`,
        fileUrl: null, // or set to generated PDF url
        meta: {}, // any extra info
        issuedAt: new Date(),
        issuedBy: agentId, // or whoever is issuing
        receiptType: bookingId.startsWith("TPB") ? "THIRD_PARTY" : "INTERNAL", // or "INTERNAL"
        status: "ISSUED",
      },
    });
    return receipt;
  }

  async getBookingStatus(bookingId: string): Promise<any> {
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: {
        status: true,
        payment: true,
        expiresAt: true,
      },
    });
    if (!booking) throw new Error("Booking not found.");

    const isPaid =
      booking.payment?.paymentStatus === "COMPLETED" ||
      (booking.payment?.amount && booking.payment?.amount.gt(0)) ||
      false;
    return {
      status: booking.status,
      isPaid,
      actionsAllowed:
        !isPaid &&
        booking.status !== "TIMED_OUT" &&
        booking.status !== "BOOKING_CONFIRMED" &&
        booking.status !== "BOOKING_REJECTED",
      expiresAt: booking.expiresAt,
    };
  }

  /**
   * Reschedules a booking by updating its ticket and related information.
   * @param bookingId - The ID of the booking to reschedule
   * @returns The updated booking object
   */
  async rescheduleBooking(bookingId: string): Promise<Booking> {
    if (!bookingId) throw new Error("bookingId is required.");

    return await prisma.$transaction(async (tx: any) => {
      // Fetch booking and check status
      const bookingData = await tx.booking.findUnique({
        where: { id: bookingId },
        include: {
          bookedSeats: true,
          ticket: true,
          travelers: { include: { traveler: true } },
        },
      });

      if (!bookingData) throw new Error("Booking not found.");
      if (bookingData.status !== "BOOKING_CONFIRMED")
        throw new Error("Booking must be confirmed to be rescheduled.");

      // Fetch new ticket
      const newTicket = await tx.flightTicket.findUnique({
        where: { id: bookingData.ticketId },
        include: {
          segments: true,
          departure: true,
          arrival: true,
          owner: true,
        },
      });

      if (!newTicket) throw new Error("New ticket not found.");
      if (newTicket.remainingSeats < bookingData.bookedSeats.length) {
        throw new Error("Not enough seats available in the new flight.");
      }

      // Start transaction for atomic updates
      return await tx.$transaction(async (tx: any) => {
        // 1. Update booking with new ticket info
        const updatedBooking = await tx.booking.update({
          where: { id: bookingId },
          data: {
            ticketId: bookingData.ticketId,
            meta: {
              carrier: newTicket.segments[0].carrier,
              flightNumber: newTicket.segments[0].flightNumber,
              departureAirport: newTicket.departure?.airportCode,
              arrivalAirport: newTicket.arrival?.airportCode,
              agencyName: newTicket.owner?.agencyName,
              ownerName: `${newTicket.owner?.firstName} ${newTicket.owner?.lastName}`,
              rescheduledFrom: bookingData.ticketId,
              rescheduledAt: new Date(),
            },
          },
          include: {
            travelers: { include: { traveler: true } },
            bookedSeats: true,
            ticket: true,
          },
        });

        // 2. Update booked seats to point to new ticket
        await tx.bookedFlightSeat.updateMany({
          where: { bookingId },
          data: {
            flightTicketId: bookingData.ticketId,
            updatedAt: new Date(),
          },
        });

        // 3. Increment remaining seats for old ticket
        await tx.flightTicket.update({
          where: { id: bookingData.ticketId },
          data: {
            remainingSeats: { increment: bookingData.bookedSeats.length },
          },
        });

        // 4. Decrement remaining seats for new ticket
        await tx.flightTicket.update({
          where: { id: bookingData.ticketId },
          data: {
            remainingSeats: { decrement: bookingData.bookedSeats.length },
          },
        });

        // 5. Create booking history log
        await tx.bookingHistoryLog.create({
          data: {
            bookingId,
            changeType: "RESCHEDULE",
            reason: "Booking was rescheduled to a new flight.",
            actorId: null,
            actorType: ActorType.SYSTEM,
            oldStatus: bookingData.status,
            newStatus: updatedBooking.status,
          },
        });

        // 6. Create notification
        await NotificationService.createNotification({
          userId: bookingData.userId,
          type: bookingData.type,
          title: "Booking Rescheduled",
          message: `Your booking ( ${bookingData.requestId} ) has been rescheduled to a new flight.`,
          relatedId: bookingData.id,
          link: `/booking/${bookingId}`,
          priority: 2,
        });

        // 7. Update ticket history log
        await tx.ticketHistoryLog.create({
          data: {
            ticketId: bookingData.ticketId,
            changeType: "RESCHEDULE",
            changeDetails: JSON.stringify({
              comment: "Booking was rescheduled to a new flight.",
            }),
            oldValue: JSON.stringify({
              remainingSeats: bookingData.ticket.remainingSeats,
              seats: bookingData.ticket.seats,
              status: bookingData.ticket.status,
              flightDate: bookingData.ticket.flightDate,
              departureTime: bookingData.ticket.departureTime,
              arrivalTime: bookingData.ticket.arrivalTime,
              departureAirport: bookingData.ticket.departure?.airportCode,
              arrivalAirport: bookingData.ticket.arrival?.airportCode,
              carrier: bookingData.ticket.segments[0].carrier,
              flightNumber: bookingData.ticket.flightNumber,
            }),
            newValue: JSON.stringify({
              remainingSeats: bookingData.ticket.remainingSeats,
              seats: bookingData.ticket.seats,
              status: bookingData.ticket.status,
              flightDate: bookingData.ticket.flightDate,
              departureTime: bookingData.ticket.departureTime,
              arrivalTime: bookingData.ticket.arrivalTime,
              departureAirport: bookingData.ticket.departure?.airportCode,
              arrivalAirport: bookingData.ticket.arrival?.airportCode,
              carrier: bookingData.ticket.segments[0].carrier,
              flightNumber: bookingData.ticket.flightNumber,
            }),
            agencyId: bookingData.buyerAgencyId,
            agencyAgentId: bookingData.teamMemberId,
          },
        });

        return updatedBooking;
      });
    });
  }

  /**
   * Gets ticket information for rescheduling a booking.
   * @param bookingId - The ID of the booking to reschedule
   * @returns The ticket information including refId and route details
   */
  async getTicketForReschedule(bookingId: string): Promise<any> {
    if (!bookingId) throw new Error("bookingId is required.");

    // Fetch booking with ticket information
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        ticket: {
          include: {
            departure: true,
            arrival: true,
            segments: true,
          },
        },
      },
    });

    if (!booking) throw new Error("Booking not found.");
    if (!booking.ticket) throw new Error("No ticket found for this booking.");

    return {
      ticketId: booking.ticket.id,
      refId: booking.ticket.refId,
      departure: {
        airportCode: booking.ticket.departure?.airportCode,
        city: booking.ticket.departure?.city,
        country: booking.ticket.departure?.country,
      },
      arrival: {
        airportCode: booking.ticket.arrival?.airportCode,
        city: booking.ticket.arrival?.city,
        country: booking.ticket.arrival?.country,
      },
      departureTime: booking.ticket.departureTime,
      arrivalTime: booking.ticket.arrivalTime,
      carrier: booking.ticket.segments[0]?.carrier,
      flightNumber: booking.ticket.segments[0]?.flightNumber,
      remainingSeats: booking.ticket.remainingSeats,
      bookedSeats: booking.ticket.seats || 0,
    };
  }

  /**
   * Update traveler information for a booking
   * @param bookingId string
   * @param travelerInfo TravelerInfo
   * @returns updated booking
   */
  async updateTravelerInfo(
    bookingId: string,
    travelerInfo: { travelers: Array<{ traveler: any }> }
  ): Promise<Booking> {
    // First, find the booking with its travelers
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        travelers: {
          include: {
            traveler: true,
          },
        },
      },
    });

    if (!booking) {
      throw new Error("Booking not found");
    }

    // Process each traveler in the request
    await Promise.all(
      travelerInfo.travelers.map(async (travelerData) => {
        const traveler = travelerData.traveler;

        // Find the booking traveler by ID if provided, otherwise match by index
        let bookingTraveler;
        if (traveler.id) {
          bookingTraveler = booking.travelers.find(
            (bt) => bt.travelerId === traveler.id
          );
        } else {
          // If no ID provided, match by index (assuming order is preserved)
          const index = travelerInfo.travelers.indexOf(travelerData);
          bookingTraveler = booking.travelers[index];
        }

        if (!bookingTraveler) {
          console.warn(`No matching traveler found in booking ${bookingId}`);
          return;
        }

        // Update the traveler's information
        await prisma.traveler.update({
          where: { id: bookingTraveler.travelerId },
          data: {
            title: traveler.title,
            firstName: traveler.firstName,
            lastName: traveler.lastName,
            gender: traveler.gender,
            dateOfBirth: traveler.dateOfBirth
              ? new Date(traveler.dateOfBirth)
              : undefined,
            nationality: traveler.nationality,
            documentType: traveler.documentType,
            documentNumber: traveler.documentNumber,
            issuingCountry: traveler.issuingCountry,
            expirationDate: traveler.expirationDate
              ? new Date(traveler.expirationDate)
              : undefined,
            contactEmail: traveler.contactEmail,
            contactPhone: traveler.contactPhone,
          },
        });

        // Update the booking traveler info status
        await prisma.bookingTraveler.update({
          where: { id: bookingTraveler.id },
          data: {
            infoStatus: "COMPLETED",
          },
        });
      })
    );

    // Return the updated booking with all travelers
    const updatedBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        travelers: {
          include: {
            traveler: true,
          },
        },
      },
    });

    if (!updatedBooking) {
      throw new Error("Booking not found after update");
    }

    return updatedBooking;
  }
}
// --- helpers --------------------------------------------------
export async function collectAgencyNames(
  prisma: PrismaClient,
  req: AuthRequest
): Promise<string[]> {
  const agencyId = await getAgentAgencyIdFromRequest(req, prisma);
  if (!agencyId || agencyId === "affiliate") return [];

  const bookings = await prisma.booking.findMany({
    where: {
      OR: [
        { agencyAgent: { agencyId } },
        { userId: agencyId },
        { TeamMember: { createdById: agencyId } },
      ],
    },
    select: {
      agencyAgent: { select: { firstName: true, lastName: true } },
      TeamMember: { select: { firstName: true, lastName: true } },
      meta: true,
    },
  });

  const set = new Set<string>();
  bookings.forEach((b: any) => {
    if (b.agencyAgent)
      set.add(`${b.agencyAgent.firstName} ${b.agencyAgent.lastName}`.trim());
    if (b.TeamMember)
      set.add(`${b.TeamMember.firstName} ${b.TeamMember.lastName}`.trim());
    const meta = b.meta as BookingMeta;
    if (meta?.agentName) set.add(meta.agentName);
    if (meta?.buyerAgentName) set.add(meta.buyerAgentName);
  });
  return [...set];
}

export async function collectSellerAgentNames(
  prisma: PrismaClient,
  req: AuthRequest
): Promise<string[]> {
  const agencyId = await getAgentAgencyIdFromRequest(req, prisma);
  if (!agencyId || agencyId === "affiliate") return [];

  const bookings = await prisma.booking.findMany({
    where: {
      sellerAgencyId: agencyId,
      meta: {
        path: ["agentName"],
        not: "",
      },
    },
    select: {
      meta: true,
    },
  });

  const agentNames = bookings.map((b: any) => b.meta?.agentName as string);
  return [...new Set(agentNames)].filter(Boolean);
}

export async function collectGlobalNames(
  prisma: PrismaClient
): Promise<string[]> {
  const bookings = await prisma.booking.findMany({
    where: {
      OR: [
        { agencyAgentId: { not: null } },
        { teamMemberId: { not: null } },
        { meta: { path: ["agentName"], not: "" } },
        { meta: { path: ["buyerAgentName"], not: "" } },
      ],
    },
    select: {
      agencyAgent: { select: { firstName: true, lastName: true } },
      TeamMember: { select: { firstName: true, lastName: true } },
      meta: true,
    },
  });

  const set = new Set<string>();
  bookings.forEach((b: any) => {
    if (b.agencyAgent)
      set.add(`${b.agencyAgent.firstName} ${b.agencyAgent.lastName}`.trim());
    if (b.TeamMember)
      set.add(`${b.TeamMember.firstName} ${b.TeamMember.lastName}`.trim());
    const meta = b.meta as BookingMeta;
    if (meta?.agentName) set.add(meta.agentName);
    if (meta?.buyerAgentName) set.add(meta.buyerAgentName);
  });
  return [...set];
}
// --------------------------------------------------------------
