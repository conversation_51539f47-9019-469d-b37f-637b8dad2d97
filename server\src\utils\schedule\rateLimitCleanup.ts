import cron from "node-cron";
import logger from "../logger";
import { prisma } from "../../prisma"; 

/**
 * Production-grade scheduled job to clean up expired rate limits.
 * - Runs every hour (cron: 0 * * * *)
 * - Deletes expired rate limit entries in batches
 * - Implements locking to prevent concurrent runs
 * - Aggregates and logs errors, retries failed batches
 * - Ensures Prisma connection is closed
 * - Uses UTC and robust error handling
 * - All logic is DRY, readable, and maintainable
 *
 * @module rateLimitCleanup
 */

// Constants for job configuration
const JOB_NAME = "rateLimitCleanup";
const BATCH_SIZE = Number(process.env.RATE_LIMIT_CLEANUP_BATCH_SIZE) || 500; // Can be tuned via env
const CRON_SCHEDULE = "0 * * * *"; // Every hour

// In-memory lock to prevent concurrent job runs (for single-instance)
let isLocked = false;

/**
 * Acquires a lock for the job. Returns true if lock acquired, false otherwise.
 */
function acquireLock(): boolean {
  if (isLocked) return false;
  isLocked = true;
  return true;
}

/**
 * Releases the job lock.
 */
function releaseLock() {
  isLocked = false;
}

/**
 * Cleans up expired rate limit entries in batches, with retries and error aggregation.
 * @returns {Promise<void>}
 */
async function cleanupExpiredRateLimits(): Promise<void> {
  const errors: Error[] = [];
  let totalDeleted = 0;
  let batchNumber = 0;
  try {
    while (true) {
      // Always use UTC for time comparisons
      const nowUtc = new Date();
      // Find expired rate limit IDs in a batch
      // Assumes 'key' is the unique identifier for rateLimit (not 'id')
      const expired = await prisma.rateLimit.findMany({
        where: { expiresAt: { lte: nowUtc } },
        select: { key: true },
        take: BATCH_SIZE,
      });
      if (expired.length === 0) break;
      const keys = expired.map((r: any) => r.key);
      batchNumber++;
      let retryCount = 0;
      let deleted = 0;
      // Retry logic for failed batch deletions
      while (retryCount < 3) {
        try {
          const result = await prisma.rateLimit.deleteMany({
            where: { key: { in: keys } },
          });
          deleted = result.count;
          totalDeleted += deleted;
          logger.info({
            job: JOB_NAME,
            batch: batchNumber,
            deleted,
            totalDeleted,
            batchSize: keys.length,
            timestamp: new Date().toISOString(),
            message: `Batch ${batchNumber}: Deleted ${deleted} expired rate limits.`,
          });
          break;
        } catch (err) {
          retryCount++;
          errors.push(err instanceof Error ? err : new Error(String(err)));
          logger.warn({
            job: JOB_NAME,
            batch: batchNumber,
            retry: retryCount,
            error: err,
            timestamp: new Date().toISOString(),
            message: `Batch ${batchNumber} failed on attempt ${retryCount}.`,
          });
          if (retryCount >= 3) {
            logger.error({
              job: JOB_NAME,
              batch: batchNumber,
              error: err,
              timestamp: new Date().toISOString(),
              message: `Batch ${batchNumber} failed after 3 retries.`,
            });
          }
        }
      }
    }
    if (totalDeleted > 0) {
      logger.info({
        job: JOB_NAME,
        totalDeleted,
        timestamp: new Date().toISOString(),
        message: `🧹 Cleaned up ${totalDeleted} expired rate limits.`,
      });
    } else {
      logger.info({
        job: JOB_NAME,
        timestamp: new Date().toISOString(),
        message: "No expired rate limits found.",
      });
    }
    if (errors.length > 0) {
      logger.error({
        job: JOB_NAME,
        errors: errors.map((e) => e.message),
        timestamp: new Date().toISOString(),
        message: `Encountered ${errors.length} errors during cleanup.`,
      });
    }
  } finally {
    // Ensure Prisma connection is closed regardless of success/failure
    // Prisma disconnection is handled centrally; do not disconnect here.
  }
}

/**
 * Starts the scheduled rate limit cleanup job.
 * - Ensures only one job runs at a time via locking
 * - Handles errors, logging, and lock release
 */
export function startRateLimitCleanup() {
  cron.schedule(CRON_SCHEDULE, async () => {
    if (!acquireLock()) {
      logger.warn({
        job: JOB_NAME,
        message: "Cleanup job already running, skipping this run.",
      });
      return;
    }
    logger.info({
      job: JOB_NAME,
      message: "Starting scheduled rate limit cleanup job.",
    });
    try {
      await cleanupExpiredRateLimits();
    } catch (error) {
      logger.error({
        job: JOB_NAME,
        error,
        message: "Unexpected error in rate limit cleanup job.",
      });
    } finally {
      releaseLock();
      logger.info({ job: JOB_NAME, message: "Cleanup job finished." });
    }
  });
}
