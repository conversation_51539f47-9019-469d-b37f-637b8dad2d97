// server/src/utils/pdf/generateManifestPdf.ts
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

export const generateManifestPdf = async (manifest: any) => {
  const doc = new jsPDF();
  
  // Add logo
  try {
    const logoUrl = '/images/logo/airvilla_logo_symbol_red.png';
    const logoResponse = await fetch(logoUrl);
    const logoBuffer = await logoResponse.arrayBuffer();
    const logoDataUrl = `data:image/png;base64,${Buffer.from(logoBuffer).toString('base64')}`;
    
    doc.addImage(logoDataUrl, 'PNG', 15, 10, 30, 15);
  } catch (error) {
    console.error('Error loading logo:', error);
  }

  // Add flight info
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(16);
  doc.text(`Manifest #${manifest.manifestId || manifest.id}`, 15, 40);
  
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(12);
  doc.text(`Flight: ${manifest.flightNumber}`, 15, 50);
  doc.text(`Date: ${manifest.flightDate}`, 15, 58);
  doc.text(`From: ${manifest.departure?.city} (${manifest.departure?.airportCode})`, 15, 66);
  doc.text(`To: ${manifest.arrival?.city} (${manifest.arrival?.airportCode})`, 15, 74);

  // Prepare passenger data
  const passengerRows = manifest.Booking?.flatMap((booking: any) => 
    booking.travelers?.map((t: any) => [
      t.traveler?.title || '',
      `${t.traveler?.firstName || ''} ${t.traveler?.lastName || ''}`.trim(),
      t.traveler?.dateOfBirth || '',
      t.traveler?.nationality || '',
      t.traveler?.documentNumber || '',
      t.traveler?.documentExpiryDate || '',
      booking.id
    ]) || []
  ) || [];

  // Add passenger table
  (doc as any).autoTable({
    startY: 90,
    head: [['Title', 'Name', 'DOB', 'Nationality', 'Document #', 'Expiry', 'Booking ID']],
    body: passengerRows,
    headStyles: { fillColor: [41, 128, 185], textColor: 255, fontStyle: 'bold' },
    alternateRowStyles: { fillColor: [245, 245, 245] },
    margin: { top: 90 }
  });

  // Add page numbers
  const pageCount = (doc as any).internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10);
  }

  return doc.output('arraybuffer');
};