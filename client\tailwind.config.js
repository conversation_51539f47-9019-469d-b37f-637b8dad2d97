const plugin = require("tailwindcss/plugin");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      boxShadow: {
        DEFAULT:
          "0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.02)",
        md: "0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.02)",
        lg: "0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.01)",
        xl: "0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.01)",
      },
      outline: {
        blue: "2px solid rgba(0, 112, 244, 0.5)",
      },
      fontFamily: {
        inter: ["var(--font-inter)", "sans-serif"],
      },
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1.5" }],
        sm: ["0.875rem", { lineHeight: "1.5715" }],
        base: ["1rem", { lineHeight: "1.5", letterSpacing: "-0.01em" }],
        lg: ["1.125rem", { lineHeight: "1.5", letterSpacing: "-0.01em" }],
        xl: ["1.25rem", { lineHeight: "1.5", letterSpacing: "-0.01em" }],
        "2xl": ["1.5rem", { lineHeight: "1.33", letterSpacing: "-0.01em" }],
        "3xl": ["1.88rem", { lineHeight: "1.33", letterSpacing: "-0.01em" }],
        "4xl": ["2.25rem", { lineHeight: "1.25", letterSpacing: "-0.02em" }],
        "5xl": ["3rem", { lineHeight: "1.25", letterSpacing: "-0.02em" }],
        "6xl": ["3.75rem", { lineHeight: "1.2", letterSpacing: "-0.02em" }],
      },
      screens: {
        xs: "480px",
      },
    },
  },
  plugins: [
    require("@tailwindcss/forms"),
    // add custom variant for expanding sidebar
    plugin(({ addVariant, e }) => {
      addVariant("sidebar-expanded", ({ modifySelectors, separator }) => {
        modifySelectors(
          ({ className }) =>
            `.sidebar-expanded .${e(
              `sidebar-expanded${separator}${className}`
            )}`
        );
      });
    }),
    function ({ addUtilities }) {
      const newUtilities = {
        ".no-arrows::-webkit-outer-spin-button, .no-arrows::-webkit-inner-spin-button":
          {
            "-webkit-appearance": "none",
            margin: "0",
          },
        ".no-arrows": {
          "-moz-appearance": "textfield",
          "-ms-appearance": "textfield",
        },
      };
      addUtilities(newUtilities);
    },
  ],
  safelist: [
    "bg-yellow-100",
    "bg-yellow-400",
    "text-yellow-800",
    "bg-red-100",
    "bg-red-400",
    "text-red-800",
    "bg-green-100",
    "bg-green-400",
    "text-green-800",
    "bg-blue-100",
    "bg-blue-400",
    "text-blue-800",
    "bg-purple-100",
    "bg-purple-400",
    "text-purple-800",
    "bg-gray-100",
    "bg-gray-400",
    "text-gray-800",
    "bg-orange-100",
    "bg-orange-400",
    "text-orange-800",
    "bg-indigo-100",
    "bg-indigo-400",
    "text-indigo-800",
    "bg-pink-100",
    "bg-pink-400",
    "text-pink-800",
    "bg-teal-100",
    "bg-teal-400",
    "text-teal-800",
    "bg-cyan-100",
    "bg-cyan-400",
    "text-cyan-800",
    "bg-lime-100",
    "bg-lime-400",
    "text-lime-800",
    "bg-amber-100",
    "bg-amber-400",
    "text-amber-800",
    "bg-fuchsia-100",
    "bg-fuchsia-400",
    "text-fuchsia-800",
    "bg-violet-100",
    "bg-violet-400",
    "text-violet-800",
    "bg-emerald-100",
    "bg-emerald-400",
    "text-emerald-800",
    "bg-zinc-100",
    "bg-zinc-400",
    "text-zinc-800",
  ],
};
