import validator from "validator";
import xss from "xss";
import { commonPatterns } from "../validators/baseUserValidation";

export interface SanitizedResult {
  value: string;
  isValid: boolean;
  error?: string;
}

export class InputSanitizer {
  /**
   * Sanitizes a string input by:
   * 1. Trimming whitespace
   * 2. Removing any HTML/script tags
   * 3. Escaping special characters
   */
  static sanitizeString(input: string | undefined | null): SanitizedResult {
    if (input === undefined || input === null) {
      return { value: "", isValid: false, error: "Input is required" };
    }

    // Convert to string and trim
    let sanitized = input.toString().trim();

    // Remove any HTML/script tags
    sanitized = xss(sanitized, {
      whiteList: {}, // empty object means no tags are allowed
      stripIgnoreTag: true, // strip all HTML tags
      stripIgnoreTagBody: ["script", "style"], // remove content inside these tags
    });

    // Escape special characters
    sanitized = validator.escape(sanitized);

    return {
      value: sanitized,
      isValid: true,
    };
  }

  /**
   * Validates if an email belongs to the airvilla-charters.travel domain
   */
  static validateTeamMemberDomain(email: string): boolean {
    return email.endsWith("@airvilla-charters.travel");
  }

  /**
   * Sanitizes an email address by:
   * 1. Trimming whitespace
   * 2. Converting to lowercase
   * 3. Validating email format
   */
  static sanitizeEmail(email: string | undefined | null): SanitizedResult {
    if (email === undefined || email === null) {
      return { value: "", isValid: false, error: "Please enter your email" };
    }

    // Convert to string, trim, and lowercase
    let sanitized = email.toString().trim().toLowerCase();

    // Validate email format
    if (!validator.isEmail(sanitized)) {
      return {
        value: sanitized,
        isValid: false,
        error:
          "Please use a valid @airvilla-charters.travel email for team member accounts",
      };
    }

    return {
      value: sanitized,
      isValid: true,
    };
  }

  /**
   * Sanitizes a team member email address by:
   * 1. Sanitizing the email
   * 2. Validating the domain is airvilla-charters.travel
   */
  static sanitizeTeamMemberEmail(
    email: string | undefined | null
  ): SanitizedResult {
    const sanitized = this.sanitizeEmail(email);

    if (!sanitized.isValid) {
      return sanitized;
    }

    if (!this.validateTeamMemberDomain(sanitized.value)) {
      return {
        value: sanitized.value,
        isValid: false,
        error:
          "Please use an @airvilla-charters.travel email for team member accounts",
      };
    }

    return sanitized;
  }

  /**
   * Sanitizes a password by:
   * 1. Checking minimum length
   * 2. Checking for common security issues
   */
  static sanitizePassword(
    password: string | undefined | null
  ): SanitizedResult {
    if (password === undefined || password === null) {
      return { value: "", isValid: false, error: "Please enter your password" };
    }

    const sanitized = password.toString();

    // Check minimum length
    if (sanitized.length < 8) {
      return {
        value: sanitized,
        isValid: false,
        error: "Password must be at least 8 characters long",
      };
    }

    // Check for common security issues
    const lowercasePassword = sanitized.toLowerCase();

    if (
      commonPatterns.some((pattern) =>
        validator.equals(lowercasePassword, pattern)
      )
    ) {
      return {
        value: sanitized,
        isValid: false,
        error: "Password is too common",
      };
    }

    return {
      value: sanitized,
      isValid: true,
    };
  }

  /**
   * Sanitizes an enum value by checking if it's a valid member of the enum
   */
  static sanitizeEnum<T extends { [key: string]: string }>(
    value: string | undefined | null,
    enumObj: T,
    fieldName: string
  ): SanitizedResult {
    if (value === undefined || value === null) {
      return {
        value: "",
        isValid: false,
        error: `${fieldName} is required`,
      };
    }

    const sanitized = value.toString().trim();
    const isValid = Object.values(enumObj).includes(sanitized as any);

    if (!isValid) {
      return {
        value: sanitized,
        isValid: false,
        error: `Invalid ${fieldName.toLowerCase()} value`,
      };
    }

    return {
      value: sanitized,
      isValid: true,
    };
  }
}
