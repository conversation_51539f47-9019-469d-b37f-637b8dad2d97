import { prisma } from "../../prisma";
import { AccountStatus } from "@prisma/client";
import moment from "moment";
import {
  isTestMode,
  REACTIVATION_PERIOD,
  DELETION_PERIOD,
  unitOfTime,
  DELETION_THRESHOLD,
  REACTIVATION_THRESHOLD,
  calculateDeactivationDays,
} from "../constants/timeVariables";
import { Response } from "express";
import { acquireLock, releaseLock } from "../locks/jobLock";
import logger from "../logger";

const JOB_NAME = "deactivationUserCleanup";

/**
 * Supported account types and their corresponding Prisma models.
 * Used for DRY lookup in all batch operations.
 */
/**
 * Supported account types and their corresponding Prisma models.
 * Used for DRY lookup in all batch operations.
 */
type AccountType =
  | "masterOwner"
  | "masterUser"
  | "agencyOwner"
  | "agencyUser"
  | "affiliate";
/**
 * Helper to get the correct strongly-typed Prisma model delegate for an account type.
 */
function getAccountModel(accountType: AccountType) {
  switch (accountType) {
    case "masterOwner":
      return prisma.user;
    case "agencyOwner":
      return prisma.user;
    case "affiliate":
      return prisma.user;
    case "masterUser":
      return prisma.teamMember;
    case "agencyUser":
      return prisma.agencyAgent;
    default:
      throw new Error(`Unknown account type: ${accountType}`);
  }
}

// Batch size for deleting records in chunks
const BATCH_SIZE = 1000; // Adjust this value as needed
const MAX_BATCH_RETRIES = 3;

// Helper to retry a batch operation
async function retryBatch<T>(
  fn: (...args: any[]) => Promise<T>,
  args: any[],
  maxRetries: number,
  loggerContext: string
): Promise<T> {
  let attempt = 0;
  let lastError;
  while (attempt < maxRetries) {
    try {
      return await fn(...args);
    } catch (error) {
      attempt++;
      lastError = error;
      logger.warn(`${loggerContext}: Attempt ${attempt} failed`, error);
      if (attempt >= maxRetries) throw error;
      await new Promise((res) => setTimeout(res, 500 * attempt)); // Exponential backoff
    }
  }
  throw lastError;
}

// Handle 30-day self-service expiration
export const handle30DayMark = async (): Promise<void> => {
  if (!(await acquireLock(JOB_NAME))) {
    logger.info(`[${JOB_NAME}] Lock not acquired, skipping this run.`);
    return;
  }
  try {
    let totalDeleted = 0;
    let totalFailed = 0;

    // Helper for paginated atomic deletion
    async function paginatedAtomicDelete(
      table: "user" | "teamMember" | "agencyAgent"
    ) {
      let lastId: string | undefined = undefined;
      let hasMore = true;
      while (hasMore) {
        let batch: { id: string }[] = [];
        if (table === "user") {
          batch = await prisma.user.findMany({
            where: {
              accountStatus: AccountStatus.deactivated,
              deactivationDate: {
                lt: REACTIVATION_THRESHOLD,
                gt: DELETION_THRESHOLD,
              },
              ...(lastId ? { id: { gt: lastId } } : {}),
            },
            orderBy: { id: "asc" },
            select: { id: true },
            take: BATCH_SIZE,
          });
        } else if (table === "teamMember") {
          batch = await prisma.teamMember.findMany({
            where: {
              accountStatus: AccountStatus.deactivated,
              deactivationDate: {
                lt: REACTIVATION_THRESHOLD,
                gt: DELETION_THRESHOLD,
              },
              ...(lastId ? { id: { gt: lastId } } : {}),
            },
            orderBy: { id: "asc" },
            select: { id: true },
            take: BATCH_SIZE,
          });
        } else if (table === "agencyAgent") {
          batch = await prisma.agencyAgent.findMany({
            where: {
              accountStatus: AccountStatus.deactivated,
              deactivationDate: {
                lt: REACTIVATION_THRESHOLD,
                gt: DELETION_THRESHOLD,
              },
              ...(lastId ? { id: { gt: lastId } } : {}),
            },
            orderBy: { id: "asc" },
            select: { id: true },
            take: BATCH_SIZE,
          });
        }
        if (batch.length === 0) break;
        for (const record of batch) {
          try {
            let deleted = { count: 0 };
            if (table === "user") {
              deleted = await prisma.user.deleteMany({
                where: {
                  id: record.id,
                  accountStatus: AccountStatus.deactivated,
                  deactivationDate: {
                    lt: REACTIVATION_THRESHOLD,
                    gt: DELETION_THRESHOLD,
                  },
                },
              });
            } else if (table === "teamMember") {
              deleted = await prisma.teamMember.deleteMany({
                where: {
                  id: record.id,
                  accountStatus: AccountStatus.deactivated,
                  deactivationDate: {
                    lt: REACTIVATION_THRESHOLD,
                    gt: DELETION_THRESHOLD,
                  },
                },
              });
            } else if (table === "agencyAgent") {
              deleted = await prisma.agencyAgent.deleteMany({
                where: {
                  id: record.id,
                  accountStatus: AccountStatus.deactivated,
                  deactivationDate: {
                    lt: REACTIVATION_THRESHOLD,
                    gt: DELETION_THRESHOLD,
                  },
                },
              });
            }
            if (deleted.count === 1) {
              logger.info(`[${JOB_NAME}] Deleted ${table}: ${record.id}`);
              totalDeleted++;
            } else {
              logger.info(
                `[${JOB_NAME}] Skipped ${table}: ${record.id} (accountStatus or deactivationDate changed)`
              );
            }
          } catch (err) {
            logger.error(
              `[${JOB_NAME}] Failed to delete ${table}: ${record.id}`,
              err
            );
            totalFailed++;
          }
        }
        lastId = batch[batch.length - 1].id;
        hasMore = batch.length === BATCH_SIZE;
      }
    }

    await paginatedAtomicDelete("user");
    await paginatedAtomicDelete("teamMember");
    await paginatedAtomicDelete("agencyAgent");
    logger.info(
      `[${JOB_NAME}] Deletion complete. Deleted: ${totalDeleted}, Failed: ${totalFailed}`
    );
    logger.info(
      `[${JOB_NAME}] Deletion complete. Deleted: ${totalDeleted}, Failed: ${totalFailed}`
    );
  } catch (error) {
    logger.error(`[${JOB_NAME}] Error in handle30DayMark:`, error);
  } finally {
    await releaseLock(JOB_NAME);
  }
};

// Permanent deletion after 180 days
export const handle180DayDeletion = async (): Promise<{
  success: boolean;
  message: string;
  count: number;
}> => {
  const currentTime = moment.utc();
  const deletionThreshold = currentTime
    .clone()
    .subtract(DELETION_PERIOD, unitOfTime)
    .toDate();

  let totalDeleted = 0;
  const accountTypes: AccountType[] = [
    "masterOwner",
    "masterUser",
    "agencyOwner",
    "agencyUser",
    "affiliate",
  ];

  try {
    for (const accountType of accountTypes) {
      let lastId: string | undefined = undefined;
      let hasMore = true;
      while (hasMore) {
        let batch: Array<{ id: string }> = [];
        // Use a switch so TypeScript knows the exact Prisma model type
        switch (accountType) {
          case "masterOwner":
          case "agencyOwner":
          case "affiliate":
            batch = await prisma.user.findMany({
              where: {
                OR: [
                  { accountStatus: AccountStatus.deactivated },
                  { accountStatus: AccountStatus.disabled },
                ],
                deactivationDate: {
                  lt: deletionThreshold,
                },
                ...(lastId ? { id: { gt: lastId } } : {}),
              },
              orderBy: { id: "asc" },
              select: { id: true },
              take: BATCH_SIZE,
            });
            break;
          case "masterUser":
            batch = await prisma.teamMember.findMany({
              where: {
                OR: [
                  { accountStatus: AccountStatus.deactivated },
                  { accountStatus: AccountStatus.disabled },
                ],
                deactivationDate: {
                  lt: deletionThreshold,
                },
                ...(lastId ? { id: { gt: lastId } } : {}),
              },
              orderBy: { id: "asc" },
              select: { id: true },
              take: BATCH_SIZE,
            });
            break;
          case "agencyUser":
            batch = await prisma.agencyAgent.findMany({
              where: {
                OR: [
                  { accountStatus: AccountStatus.deactivated },
                  { accountStatus: AccountStatus.disabled },
                ],
                deactivationDate: {
                  lt: deletionThreshold,
                },
                ...(lastId ? { id: { gt: lastId } } : {}),
              },
              orderBy: { id: "asc" },
              select: { id: true },
              take: BATCH_SIZE,
            });
            break;
        }
        if (batch.length === 0) break;
        for (const record of batch) {
          let deleted = { count: 0 };
          switch (accountType) {
            case "masterOwner":
            case "agencyOwner":
            case "affiliate":
              deleted = await prisma.user.deleteMany({
                where: {
                  id: record.id,
                  OR: [
                    { accountStatus: AccountStatus.deactivated },
                    { accountStatus: AccountStatus.disabled },
                  ],
                  deactivationDate: {
                    lt: deletionThreshold,
                  },
                },
              });
              break;
            case "masterUser":
              deleted = await prisma.teamMember.deleteMany({
                where: {
                  id: record.id,
                  OR: [
                    { accountStatus: AccountStatus.deactivated },
                    { accountStatus: AccountStatus.disabled },
                  ],
                  deactivationDate: {
                    lt: deletionThreshold,
                  },
                },
              });
              break;
            case "agencyUser":
              deleted = await prisma.agencyAgent.deleteMany({
                where: {
                  id: record.id,
                  OR: [
                    { accountStatus: AccountStatus.deactivated },
                    { accountStatus: AccountStatus.disabled },
                  ],
                  deactivationDate: {
                    lt: deletionThreshold,
                  },
                },
              });
              break;
          }
          if (deleted.count === 1) {
            logger.info(
              `[handle180DayDeletion] Deleted ${accountType}: ${record.id}`
            );
            totalDeleted++;
          } else {
            logger.info(
              `[handle180DayDeletion] Skipped ${accountType}: ${record.id} (accountStatus or deactivationDate changed)`
            );
          }
        }
        lastId = batch[batch.length - 1].id;
        hasMore = batch.length === BATCH_SIZE;
      }
    }
    return {
      success: true,
      message: `Deleted ${totalDeleted} accounts older than 180 days`,
      count: totalDeleted,
    };
  } catch (error) {
    logger.error(`[handle180DayDeletion] Error:`, error);
    return {
      success: false,
      message: `Error during 180-day deletion: ${error}`,
      count: totalDeleted,
    };
  }
};

/**
 * Enhanced login handler with tiered reactivation based on deactivation date:
 * - Within 30 days: Self-service reactivation
 * - 31-180 days: Requires admin assistance
 * - After 180 days: Account permanently deleted
 */
export const handleLoginWithReactivation = async (
  email: string,
  res: Response
) => {
  const user =
    (await prisma.user.findUnique({ where: { email } })) ||
    (await prisma.teamMember.findUnique({ where: { email } })) ||
    (await prisma.agencyAgent.findUnique({ where: { email } }));

  if (!user) throw new Error("User not found");

  if (user.accountStatus !== AccountStatus.deactivated) return user;

  // If no deactivation date is set, return the user as is
  if (!user.deactivationDate) return user;

  // Calculate days deactivated
  // const daysDeactivated = Math.floor(
  //   (Date.now() - user.deactivationDate.getTime()) /
  //     (isTestMode ? msInMinute : msInDay) // milliseconds in a day
  // );
  const daysDeactivated = calculateDeactivationDays(user.deactivationDate);

  // Tier 1: Self-service reactivation (within 30 days)
  if (daysDeactivated <= REACTIVATION_PERIOD) {
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        accountStatus: AccountStatus.accepted,
        deactivationDate: null,
      },
    });
    console.log("User reactivated:", updatedUser.email);
    return updatedUser;
  }

  // Tier 2: Admin-required reactivation (31-180 days)
  if (daysDeactivated <= DELETION_PERIOD) {
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        accountStatus: AccountStatus.deactivated,
        email: `${user.email}_${daysDeactivated}`,
      },
    });
    console.log("User reactivated:", updatedUser.email);
    throw new Error(
      `Your account has been deactivated for ${daysDeactivated} ${isTestMode ? "minutes" : "days"}. Contact Airvilla Support to restore access.`
    );
  }

  // Tier 3: Account deletion (after 180 days)
  throw new Error(
    "Your access has been permanently deleted due to extended deactivation period (over 180 days)."
  );
};

/**
 * Handles the cleanup of deactivated accounts based on time thresholds:
 * - After 30 days: Account requires admin help to restore
 * - After 180 days: Account and related data are permanently deleted
 */
export const cleanupDeactivatedUsers = async (
  forceDeactivatedCleanup = false
): Promise<void> => {
  try {
    // Recalculate thresholds every run
    const currentTime = moment.utc();
    const reactivationThreshold = currentTime
      .clone()
      .subtract(REACTIVATION_PERIOD, unitOfTime)
      .toDate();
    const deletionThreshold = currentTime
      .clone()
      .subtract(DELETION_PERIOD, unitOfTime)
      .toDate();

    console.log("Live thresholds:", {
      reactivationThreshold,
      deletionThreshold,
    });

    const totalDeactivated = await prisma.user.count({
      where: { accountStatus: AccountStatus.deactivated },
    });
    const totalDisabled = await prisma.user.count({
      where: { accountStatus: AccountStatus.disabled },
    });
    console.log(`Total deactivated users in system: ${totalDeactivated}`);
    console.log(`Total disabled users in system: ${totalDisabled}`);

    console.log(
      `♻️: Reactivated users ♻️: ${REACTIVATION_PERIOD} ${unitOfTime}`
    );
    console.log(`❌: Deactivated users ❌: ${DELETION_PERIOD} ${unitOfTime}`);

    // Check if there are any deactivated or disabled users at all
    const countDeactivatedOrDisabled = async (status: AccountStatus) => {
      const [users, teamMembers, agents] = await Promise.all([
        prisma.user.count({ where: { accountStatus: status } }),
        prisma.teamMember.count({ where: { accountStatus: status } }),
        prisma.agencyAgent.count({ where: { accountStatus: status } }),
      ]);
      return {
        total: users + teamMembers + agents,
        users,
        teamMembers,
        agents,
      };
    };

    const logAccountCounts = ({
      type,
      counts = { total: 0, users: 0, teamMembers: 0, agents: 0 },
    }: {
      type: string;
      counts: {
        total: number;
        users: number;
        teamMembers: number;
        agents: number;
      };
    }) => {
      console.log(
        `Total ${type} accounts: ${counts.total} (Users: ${counts.users}, TeamMembers: ${counts.teamMembers}, Agents: ${counts.agents})`
      );
    };

    const deactivatedCounts = await countDeactivatedOrDisabled(
      AccountStatus.deactivated
    );
    const disabledCounts = await countDeactivatedOrDisabled(
      AccountStatus.disabled
    );

    logAccountCounts({ type: "deactivated", counts: deactivatedCounts });
    if (deactivatedCounts.total === 0) {
      console.log("No deactivated accounts found, skipping cleanup");
      return;
    }

    logAccountCounts({ type: "disabled", counts: disabledCounts });
    if (disabledCounts.total === 0) {
      console.log("No disabled accounts found, skipping cleanup");
      return;
    }

    // If force cleanup is enabled, process all deactivated users
    const whereClause = forceDeactivatedCleanup
      ? {
          accountStatus: {
            in: [AccountStatus.deactivated, AccountStatus.disabled],
          },
        }
      : {
          accountStatus: {
            in: [AccountStatus.deactivated, AccountStatus.disabled],
          },
          deactivationDate: { lt: deletionThreshold },
        };

    // Common query parameters
    const queryParams = {
      where: whereClause,
      select: {
        id: true,
        email: true,
        addressId: true,
        userAddressId: true,
        createdAt: true,
        deactivationDate: true,
      },
    } as const;

    // Paginated, per-type: collect all deactivated users older than 180 days
    const accountTypes = [
      { table: "user", prismaModel: "user" },
      { table: "teamMember", prismaModel: "teamMember" },
      { table: "agencyAgent", prismaModel: "agencyAgent" },
    ];

    /**
     * Aggregates all deactivated users for deletion, paginated by account type.
     * Handles batch errors robustly and logs per-batch progress.
     */
    const allDeactivatedUsers: Array<{
      id: string;
      email?: string;
      deactivationDate?: Date | null;
      addressId?: string | null;
      userAddressId?: string | null;
      accountType: string;
    }> = [];
    const deletionBatchErrors: Array<{
      accountType: string;
      batchStartId: string | undefined;
      error: any;
    }> = [];
    for (const accountType of [
      "masterOwner",
      "masterUser",
      "agencyOwner",
      "agencyUser",
      "affiliate",
    ] as AccountType[]) {
      let lastId: string | undefined = undefined;
      let hasMore = true;
      let batchCount = 0;
      while (hasMore) {
        batchCount++;
        logger.info(
          `[DELETION] Processing batch #${batchCount} for ${accountType} starting at id ${lastId ?? "beginning"}`
        );
        try {
          let batch: Array<{
            id: string;
            email?: string;
            deactivationDate?: Date | null;
          }> = [];
          // Use a switch so TypeScript knows the exact Prisma model type
          switch (accountType) {
            case "masterOwner":
            case "agencyOwner":
            case "affiliate":
              batch = await prisma.user.findMany({
                where: whereClause,
                orderBy: { id: "asc" },
                select: { id: true, email: true, deactivationDate: true },
                ...(lastId ? { cursor: { id: lastId }, skip: 1 } : {}),
                take: BATCH_SIZE,
              });
              break;
            case "masterUser":
              batch = await prisma.teamMember.findMany({
                where: whereClause,
                orderBy: { id: "asc" },
                select: { id: true, email: true, deactivationDate: true },
                ...(lastId ? { cursor: { id: lastId }, skip: 1 } : {}),
                take: BATCH_SIZE,
              });
              break;
            case "agencyUser":
              batch = await prisma.agencyAgent.findMany({
                where: whereClause,
                orderBy: { id: "asc" },
                select: { id: true, email: true, deactivationDate: true },
                ...(lastId ? { cursor: { id: lastId }, skip: 1 } : {}),
                take: BATCH_SIZE,
              });
              break;
          }
          if (batch.length === 0) break;
          allDeactivatedUsers.push(
            ...batch.map(
              (u: {
                id: string;
                email?: string;
                deactivationDate?: Date | null;
              }) => ({ ...u, accountType })
            )
          );
          lastId = batch[batch.length - 1].id;
          hasMore = batch.length === BATCH_SIZE;
        } catch (error) {
          logger.error(
            `❌ Batch failed for ${accountType} starting at id ${lastId} after ${MAX_BATCH_RETRIES} retries:`,
            error
          );
          deletionBatchErrors.push({
            accountType,
            batchStartId: lastId,
            error,
          });
          // Continue to next batch
          hasMore = false;
        }
      }
    }

    if (deletionBatchErrors.length > 0) {
      logger.warn(`⚠️ Some deletion batches failed:`, deletionBatchErrors);
    }

    console.log(
      "Found deactivated users for processing:",
      allDeactivatedUsers.map((u) => ({
        id: u.id,
        email: u.email,
        deactivationDate: u.deactivationDate,
        accountType: u.accountType,
      }))
    );

    if (allDeactivatedUsers.length === 0) {
      console.log("No deactivated users to cleanup");
      return;
    }

    // Extract IDs for deletion
    const userIds = allDeactivatedUsers.map((user) => user.id);
    const teamMemberIds = allDeactivatedUsers.map((tm) => tm.id);
    const agencyAgentIds = allDeactivatedUsers.map((aa) => aa.id);

    const addressIds = allDeactivatedUsers
      .map((user) => user.addressId || user.userAddressId)
      .filter((id): id is string => id !== null);

    // Start a transaction to ensure all related data is deleted atomically
    await prisma.$transaction(async (tx: any) => {
      // 1. Delete TeamMember-related TicketAccess first
      if (teamMemberIds.length > 0) {
        console.log("🧹 Cleaning up TeamMember TicketAccess...");
        try {
          for (let i = 0; i < teamMemberIds.length; i += BATCH_SIZE) {
            const batch = teamMemberIds.slice(i, i + BATCH_SIZE);
            await tx.ticketAccess.deleteMany({
              where: { teamMemberId: { in: batch } },
            });
          }
        } catch (error) {
          console.error("❌ Failed to delete TeamMember TicketAccess:", error);
          throw error;
        }
      }

      // Step 2: Find and delete FlightTickets (those with no remaining TicketAccess)
      console.log("🔍Finding user-owned FlightTickets...");
      try {
        const flightTickets = await tx.flightTicket.findMany({
          where: {
            OR: [
              { ownerId: { in: userIds } },
              { agencyAgentId: { in: agencyAgentIds } },
            ],
          },
          select: { id: true },
        });
        const flightTicketIds = flightTickets.map((ft: any) => ft.id);

        if (flightTicketIds.length > 0) {
          console.log("🧹 Cleaning up FlightTicket access...");
          // Delete TicketAccess in batches
          for (let i = 0; i < flightTicketIds.length; i += BATCH_SIZE) {
            const batch = flightTicketIds.slice(i, i + BATCH_SIZE);
            await tx.ticketAccess.deleteMany({
              where: { ticketId: { in: batch } },
            });
          }

          console.log("🗑️ Deleting user-owned FlightTickets...");
          // Delete FlightTickets in batches
          for (let i = 0; i < flightTicketIds.length; i += BATCH_SIZE) {
            const batch = flightTicketIds.slice(i, i + BATCH_SIZE);
            await tx.flightTicket.deleteMany({
              where: { id: { in: batch } },
            });
          }
        }
      } catch (error) {
        console.error("❌ Failed to process FlightTickets:", error);
        throw error;
      }

      // 3. Clean up orphaned FlightTickets (optional safety measure)
      console.log("🔍 Checking for orphaned FlightTickets...");
      try {
        const orphanedFlightTickets = await tx.flightTicket.findMany({
          where: { ticketAccess: { none: {} } },
        });

        if (orphanedFlightTickets.length > 0) {
          console.log("🧹 Cleaning up orphaned FlightTickets...");
          const orphanedIds = orphanedFlightTickets.map((ft: any) => ft.id);
          for (let i = 0; i < orphanedIds.length; i += BATCH_SIZE) {
            const batch = orphanedIds.slice(i, i + BATCH_SIZE);
            await tx.flightTicket.deleteMany({
              where: { id: { in: batch } },
            });
          }
        }
      } catch (error) {
        console.error("❌ Failed to clean orphaned FlightTickets:", error);
        throw error;
      }

      // 4. Delete user addresses
      if (addressIds.length > 0) {
        try {
          for (let i = 0; i < addressIds.length; i += BATCH_SIZE) {
            const batch = addressIds.slice(i, i + BATCH_SIZE);
            await tx.userAddress.deleteMany({
              where: { id: { in: batch } },
            });
          }
        } catch (error) {
          console.error("❌ Failed to delete user addresses:", error);
          throw error;
        }
      }

      // 5. Finally delete the users and related entities
      console.log("🧑💼 Deleting user accounts...");
      try {
        // Helper function for batch deletion
        const batchDelete = async (
          model: any,
          ids: string[],
          entityName: string
        ) => {
          if (ids.length === 0) {
            console.log(`⏩ No ${entityName} to delete`);
            return;
          }

          for (let i = 0; i < ids.length; i += BATCH_SIZE) {
            const batch = ids.slice(i, i + BATCH_SIZE);
            console.log(
              `🗑️ Deleting ${entityName} batch ${i / BATCH_SIZE + 1}`
            );
            await model.deleteMany({ where: { id: { in: batch } } });
          }
        };
        await Promise.all([
          tx.user.deleteMany({ where: { id: { in: userIds } } }),
          tx.teamMember.deleteMany({ where: { id: { in: teamMemberIds } } }),
          tx.agencyAgent.deleteMany({ where: { id: { in: agencyAgentIds } } }),
        ]);
      } catch (error) {
        console.error("❌ Failed to delete user accounts:", error);
        throw error;
      }
    });

    console.log(
      `✅ Successfully removed ${allDeactivatedUsers.length} deactivated users`
    );

    // Handle 30-day mark notifications
    const thirtyDayUsers: Array<{
      id: string;
      email?: string;
      accountType: string;
    }> = [];

    const updateBatchErrors: Array<{
      accountType: string;
      batchStartId: string | undefined;
      error: any;
    }> = [];
    for (const { table, prismaModel } of accountTypes) {
      let lastId: string | undefined = undefined;
      let hasMore = true;
      let batchCount = 0;
      while (hasMore) {
        batchCount++;
        logger.info(
          `[UPDATE] Processing batch #${batchCount} for ${table} starting at id ${lastId ?? "beginning"}`
        );
        let batch: Array<{ id: string; email?: string }> = [];
        try {
          const fetchBatch = async () => {
            if (prismaModel === "user") {
              return await prisma.user.findMany({
                where: {
                  accountStatus: AccountStatus.deactivated,
                  deactivationDate: {
                    lt: reactivationThreshold,
                    gt: deletionThreshold,
                  },
                  ...(lastId ? { id: { gt: lastId } } : {}),
                },
                orderBy: { id: "asc" },
                select: { id: true, email: true },
                take: BATCH_SIZE,
              });
            } else if (prismaModel === "teamMember") {
              return await prisma.teamMember.findMany({
                where: {
                  accountStatus: AccountStatus.deactivated,
                  deactivationDate: {
                    lt: reactivationThreshold,
                    gt: deletionThreshold,
                  },
                  ...(lastId ? { id: { gt: lastId } } : {}),
                },
                orderBy: { id: "asc" },
                select: { id: true, email: true },
                take: BATCH_SIZE,
              });
            } else if (prismaModel === "agencyAgent") {
              return await prisma.agencyAgent.findMany({
                where: {
                  accountStatus: AccountStatus.deactivated,
                  deactivationDate: {
                    lt: reactivationThreshold,
                    gt: deletionThreshold,
                  },
                  ...(lastId ? { id: { gt: lastId } } : {}),
                },
                orderBy: { id: "asc" },
                select: { id: true, email: true },
                take: BATCH_SIZE,
              });
            }
            return [];
          };
          batch = await retryBatch(
            fetchBatch,
            [],
            MAX_BATCH_RETRIES,
            `Update batch for ${table} starting at id ${lastId}`
          );
          if (batch.length === 0) break;
          thirtyDayUsers.push(
            ...batch.map((u) => ({
              id: u.id,
              email: u.email,
              accountType: table,
            }))
          );
        } catch (error) {
          logger.error(
            `❌ Update batch failed for ${table} starting at id ${lastId} after ${MAX_BATCH_RETRIES} retries:`,
            error
          );
          updateBatchErrors.push({
            accountType: table,
            batchStartId: lastId,
            error,
          });
        }
        lastId = batch.length > 0 ? batch[batch.length - 1].id : lastId;
        hasMore = batch.length === BATCH_SIZE;
      }
    }
    if (updateBatchErrors.length > 0) {
      logger.warn(`⚠️ Some update batches failed:`, updateBatchErrors);
    }

    // Update these users to require admin assistance
    if (thirtyDayUsers.length > 0) {
      const timestamp = new Date().getTime();

      // Process each user individually to update their email
      const failedUpdates: string[] = [];
      await Promise.all(
        thirtyDayUsers.map(async (user) => {
          if (
            !user.email ||
            typeof user.email !== "string" ||
            user.email.trim() === ""
          ) {
            logger.warn(
              `User ${user.id} (${user.accountType}) has no valid email, skipping email update for admin assistance.`
            );
            return;
          }
          try {
            if (user.accountType === "masterOwner") {
              await prisma.user.update({
                where: { id: user.id },
                data: {
                  email: `${user.email}_${timestamp}`,
                },
              });
            } else if (user.accountType === "masterUser") {
              await prisma.teamMember.update({
                where: { id: user.id },
                data: {
                  email: `${user.email}_${timestamp}`,
                },
              });
            } else if (user.accountType === "agencyUser") {
              await prisma.agencyAgent.update({
                where: { id: user.id },
                data: {
                  email: `${user.email}_${timestamp}`,
                },
              });
            } else {
              logger.warn(
                `Unknown account type '${user.accountType}' for id ${user.id}, skipping.`
              );
              return;
            }
          } catch (error) {
            logger.error(
              `Failed to update email for ${user.accountType} ${user.id}:`,
              error
            );
            failedUpdates.push(user.id);
          }
        })
      );
      if (failedUpdates.length > 0) {
        logger.warn(
          `The following user email updates failed (will retry once): ${failedUpdates.join(", ")}`
        );
        // Retry failed updates once more
        const stillFailed: string[] = [];
        await Promise.all(
          failedUpdates.map(async (userId) => {
            const user = thirtyDayUsers.find((u) => u.id === userId);
            if (
              !user ||
              !user.email ||
              typeof user.email !== "string" ||
              user.email.trim() === ""
            ) {
              logger.warn(
                `User ${user?.id} (${user?.accountType}) has no valid email, skipping retry update.`
              );
              return;
            }
            try {
              if (
                user.accountType === "agencyOwner" ||
                user.accountType === "masterOwner" ||
                user.accountType === "affiliate"
              ) {
                await prisma.user.update({
                  where: { id: user.id },
                  data: {
                    email: `${user.email}_${timestamp}`,
                  },
                });
              } else if (user.accountType === "masterUser") {
                await prisma.teamMember.update({
                  where: { id: user.id },
                  data: {
                    email: `${user.email}_${timestamp}`,
                  },
                });
              } else if (user.accountType === "agencyUser") {
                await prisma.agencyAgent.update({
                  where: { id: user.id },
                  data: {
                    email: `${user.email}_${timestamp}`,
                  },
                });
              } else {
                logger.warn(
                  `Unknown account type '${user.accountType}' for id ${user.id}, skipping.`
                );
                return;
              }
            } catch (error) {
              logger.error(
                `Second attempt failed to update email for ${user.accountType} ${user.id}:`,
                error
              );
              stillFailed.push(user.id);
            }
          })
        );
        if (stillFailed.length > 0) {
          logger.error(
            `The following user email updates failed after retry: ${stillFailed.join(", ")}`
          );
          // Optionally: store stillFailed somewhere for future reprocessing
        }
      }

      console.log(
        `Flagged ${thirtyDayUsers.length} users for admin assistance in reactivating their accounts.`
      );
    }
  } catch (error) {
    console.error("🔥 Critical error during cleanup:", error);
    throw error;
  } finally {
    // Prisma disconnection is handled centrally; do not disconnect here.
  }
};
