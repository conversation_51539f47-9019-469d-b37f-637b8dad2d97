// Types
export interface Agent {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  subRole: string;
  department: string;
  status: "active" | "inactive";
  password?: string;
  confirmPassword?: string;
  lastLogin?: string;
  createdAt?: string;
}

export enum AgentRole {
  ADMIN = "admin",
  ACCOUNTANT = "accountant",
  OPERATION = "operation",
  SALES = "sales",
}

export enum Department {
  CUSTOMER_SUPPORT = "customer_support",
  MANAGEMENT = "management",
  FINANCE = "finance",
  MARKETING = "marketing",
  SALES = "sales",
  IT = "it",
  OPERATIONS = "operations",
}

// Password Field
export interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
  label: string;
  message: string;
}
export interface PasswordFieldProps {
  label: string;
  name: string;
  value: string;
  placeholder: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  showPassword: boolean;
  onToggleVisibility: () => void;
  strength?: PasswordStrength;
  required?: boolean;
  formErrors?: string | undefined;
  tooltip?: string;
  labelClassName?: string;
  inputClassName?: string;
  strengthClassName?: string;
}

// Select Field
export interface SelectFieldProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  disabled?: boolean;
  options: Array<string | { value: string; label: string }>;
  formatOption?: (option: string) => string;
  required?: boolean;
  formErrors?: string | undefined;
}
