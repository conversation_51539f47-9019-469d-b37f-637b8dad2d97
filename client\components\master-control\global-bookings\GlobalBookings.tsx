"use client";
import React, { useEffect, useState, useRef, Suspense } from "react";
import {
  Search,
  Filter,
  Edit,
  Calendar,
  Download,
  ChevronDown,
} from "lucide-react";
import FilterDropdown from "@/components/common/FilterDropdown";
import {
  getAllBookings,
  getBookingById,
  getAllGlobalBookings,
  getGlobalAgentNames,
  getAgencyNames,
  getCarrierNames,
} from "@/lib/data/bookingData";
import { useInView } from "react-intersection-observer";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { setBookingConfirmationData } from "@/redux/features/BookingConfirmationSlice";
import ListLoading from "@/components/flight-tickets/myTickets/ListLoading";
import ProgressLoading from "@/components/utils/ProgressLoading";
import {
  calculatePrice,
  getFormatDateTable,
  normalizePaymentMethod,
} from "@/utils/functions/functions";
import {
  exportToCSV,
  formatBookingsForExport,
} from "@/utils/functions/exportUtils";
import useMasterUserAuth from "@/components/hooks/useMasterUserAuth";
import { PassengerDisplay } from "@/components/ticket-hub/bookings/MyBookings";

type BookingFilters = {
  status: string;
  source: string;
  issuedOn: string;
  flightDate: string;
  tripType: string;
  carrier: string;
  agency: string;
  agent: string;
  paymentMethod: string;
};

// Utility to normalize booking status/action strings for display
function normalizeBookingStatus(status: string): string {
  if (!status) return "-";
  if (status === "BOOKING_CONFIRMED") return "Booking Confirmed";
  if (status === "PENDING_APPROVAL") return "Pending Approval";
  if (status === "TIMED_OUT") return "Timed Out";
  if (status === "CANCELLED_BY_USER" || status === "CANCELLED_BY_SYSTEM")
    return "Cancelled";
  return status
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

// Constants
const TRIP_TYPES = {
  ROUND_TRIP: "Round Trip",
  ONE_WAY: "One Way",
};

// Mapping for DB tripType values to user-friendly labels
const TRIP_TYPE_LABELS: Record<string, string> = {
  ROUND_TRIP: "Round Trip",
  ONE_WAY: "One Way",
};

const TRIP_STATUS = {
  UPCOMING: "Upcoming",
  COMPLETED: "Completed",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
  IN_PROGRESS: "In Progress",
};

const BOOKING_STATUS = {
  BOOKING_CONFIRMED: "BOOKING_CONFIRMED",
  PENDING_APPROVAL: "PENDING_APPROVAL",
  QUICK_HOLD: "QUICK_HOLD",
  BOOKING_REJECTED: "BOOKING_REJECTED",
  TIMED_OUT: "TIMED_OUT",
  CANCELLED_BY_USER: "CANCELLED_BY_USER",
  CANCELLED_BY_SYSTEM: "CANCELLED_BY_SYSTEM",
};

const SALE_STATUS = {
  COMPLETED: "Completed",
  REFUNDED: "Refunded",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
};

// Status styles for visual indication - matching MyBookings
const STATUS_STYLES = {
  [TRIP_STATUS.UPCOMING]: "bg-blue-200 text-blue-800",
  [TRIP_STATUS.COMPLETED]: "bg-green-200 text-green-800",
  [TRIP_STATUS.PENDING]: "bg-yellow-200 text-yellow-800",
  [TRIP_STATUS.NOT_VALID]: "bg-red-200 text-red-800",
  [TRIP_STATUS.IN_PROGRESS]: "bg-purple-200 text-purple-800",
};

// We're using the TRIP_STATUS values directly now, so this mapping is no longer needed
// but keeping it commented for reference
/*
const TRIP_STATUS_LABELS: Record<string, string> = {
  UPCOMING: "Upcoming",
  COMPLETED: "Completed",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
  IN_PROGRESS: "In Progress",
};
*/

// We're using normalizeBookingStatus function instead of this mapping
/*
const BOOKING_ACTION_LABELS: Record<string, string> = {
  CONFIRMED: "Booking Confirmed",
  PENDING_APPROVAL: "Pending Approval",
  QUICK_HOLD: "Quick Hold",
  REJECTED: "Booking Rejected",
  TIMED_OUT: "Timed Out",
  CANCELLED: "Cancelled",
};
*/

const SALE_STATUS_STYLES = {
  [SALE_STATUS.COMPLETED]: "bg-green-200 text-green-800",
  [SALE_STATUS.REFUNDED]: "bg-orange-200 text-orange-800",
  [SALE_STATUS.PENDING]: "bg-yellow-200 text-yellow-800",
  [SALE_STATUS.NOT_VALID]: "bg-red-200 text-red-800",
};

// Using normalizeSourceValue function instead of this mapping
/*
const SOURCE_LABELS: Record<string, string> = {
  THIRD_PARTY: "Third-Party",
  "Third-Party": "Third-Party",
  INTERNAL: "Internal",
  Internal: "Internal",
};
*/
// Source styles for booking source badges
const SOURCE_STYLES = {
  Internal: "bg-blue-200 text-blue-800",
  "Third-Party": "bg-purple-200 text-purple-800",
  INTERNAL: "bg-blue-200 text-blue-800",
  THIRD_PARTY: "bg-purple-200 text-purple-800",
};

function getSourceStyle(source: string): string {
  if (Object.prototype.hasOwnProperty.call(SOURCE_STYLES, source)) {
    return SOURCE_STYLES[source as keyof typeof SOURCE_STYLES];
  }
  return "bg-gray-300 text-gray-800";
}

const BOOKING_STATUS_STYLES = {
  [BOOKING_STATUS.QUICK_HOLD]: "bg-yellow-200 text-yellow-800",
  [BOOKING_STATUS.TIMED_OUT]: "bg-gray-200 text-gray-800",
  [BOOKING_STATUS.PENDING_APPROVAL]: "bg-blue-200 text-blue-800",
  [BOOKING_STATUS.BOOKING_CONFIRMED]: "bg-green-200 text-green-800",
  [BOOKING_STATUS.BOOKING_REJECTED]: "bg-red-200 text-red-800",
  [BOOKING_STATUS.CANCELLED_BY_USER]: "bg-red-200 text-red-800",
  [BOOKING_STATUS.CANCELLED_BY_SYSTEM]: "bg-red-200 text-red-800",
};

// Table column headers
const MASTER_TABLE_HEADERS = [
  "ID",
  "Flight Date",
  "Passenger",
  "Route",
  "Trip Type",
  "Carrier",
  "Price",
  "Trip Status",
  "Booking Status",
  "Sale Status",
  "Booking Source",
  "Seller Agency",
  "Booking Agency",
  "Booking Agent",
  "Payment Method",
  "Issued On",
  "Actions",
];

// Filter options for the dropdown selects
const FILTER_OPTIONS = {
  BookingStatus: [
    "All",
    "Confirmed",
    "Pending Approval",
    "Quick Hold",
    "Rejected",
    "Timed Out",
    "Cancelled",
  ],
  BookingSource: ["All", "Internal", "Third-Party"],
  FlightDate: [
    "All Time",
    "Next 90 Days",
    "Next 30 Days",
    "Next 7 Days",
    "Today",
  ],
  PaymentMethods: [
    "All",
    "Cash Deposit",
    "Cliq",
    "Cheque",
    "Bank Transfer",
    "Credit Card",
    "Airvilla Wallet",
  ],
  AgencyNames: [
    "All",
    "Global Travel LLC",
    "SkyHigh Bookings",
    "TravelWise Agency",
  ],
  AgentNames: [
    "All",
    "Alex Wong",
    "Sophia Miller",
    "William Johnson",
    "Emily Wilson",
    "Daniel Park",
    "Kevin Chen",
  ],
  Carriers: [
    "All",
    "Royal Jordanian",
    "Qatar Airways",
    "Emirates",
    "Turkish Airlines",
    "Lufthansa",
    "Air France",
    "British Airways",
    "Singapore Airlines",
    "Etihad Airways",
    "Japan Airlines",
    "Delta Air Lines",
    "Cathay Pacific",
    "KLM Royal Dutch Airlines",
    "Swiss International Air Lines",
  ],
  IssuedOn: [
    "All Time",
    "Last 12 Months",
    "Last Month",
    "Last 7 Days",
    "Today",
  ],
};

interface Filters {
  status: string;
  source: string;
  issuedOn: string;
  flightDate: string;
  tripType: string;
  carrier: string;
  agency: string;
  agent: string;
  paymentMethod: string;
}

// Dashboard header component
const DashboardHeader = () => (
  <div className="flex justify-between items-center mb-5">
    <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
      Global Bookings
    </h1>
  </div>
);

// Component for dropdown filters
const FilterSelect = ({
  label,
  options = ["All"],
}: {
  label: string;
  options?: string[];
}) => (
  <div className="flex-1">
    <label className="block text-sm font-medium mb-1 text-gray-600 dark:text-white">
      {label}
    </label>
    <select className="h-[45px] bg-gray-100 dark:bg-gray-700 rounded-lg py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-red-500 border-none hover:border-red-500 hover:ring-2 hover:ring-red-500 text-gray-500 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-200">
      {options.map((option) => (
        <option key={option}>{option}</option>
      ))}
    </select>
  </div>
);

// Component for date range filter
const DateRangeFilter = ({ label }: { label: string }) => (
  <div className="flex-1">
    <label className="block text-sm font-medium mb-1 text-gray-600 dark:text-white">
      {label}
    </label>
    <div className="relative">
      <input
        type="text"
        placeholder="Select date range"
        className="h-[45px] rounded-lg py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-red-500 border-none bg-gray-100 dark:bg-gray-700 hover:border-red-500 hover:ring-2 hover:ring-red-500 text-gray-500 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-200"
      />
      <Calendar className="absolute right-3 top-2.5 text-gray-400" size={18} />
    </div>
  </div>
);

// Filter card component with advanced options
const FilterCard: React.FC<{
  filters: BookingFilters;
  setFilters: React.Dispatch<React.SetStateAction<BookingFilters>>;
}> = ({ filters, setFilters }) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("All");
  const [selectedSource, setSelectedSource] = useState("All");
  const [selectedIssuedOn, setSelectedIssuedOn] = useState("All");
  const [selectedFlightDate, setSelectedFlightDate] = useState("All Time");
  const [selectedTripType, setSelectedTripType] = useState("All");
  const [selectedCarrier, setSelectedCarrier] = useState("All");
  const [selectedAgency, setSelectedAgency] = useState("All");
  const [selectedAgent, setSelectedAgent] = useState("All");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("All");
  const [agentNames, setAgentNames] = useState<string[]>([]);
  const [carrierNames, setCarrierNames] = useState<string[]>([]);
  const [agencyNames, setAgencyNames] = useState<string[]>([]);

  // helper to update both the local dropdown state and the master filters map
  const updateFilter = (
    key: keyof typeof filters,
    value: string,
    setter: (v: string) => void
  ) => {
    setter(value);
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleResetFilters = () => {
    setSelectedStatus("All");
    setSelectedSource("All");
    setSelectedIssuedOn("All");
    setSelectedFlightDate("All Time");
    setSelectedTripType("All");
    setSelectedCarrier("All");
    setSelectedAgency("All");
    setSelectedAgent("All");
    setSelectedPaymentMethod("All");
    setFilters({
      status: "All",
      source: "All",
      issuedOn: "All",
      flightDate: "All Time",
      tripType: "All",
      carrier: "All",
      agency: "All",
      agent: "All",
      paymentMethod: "All",
    });
  };

  // Fetch agent names from API
  useEffect(() => {
    const fetchAgentNames = async () => {
      try {
        const agentNames = await getGlobalAgentNames();
        setAgentNames([
          "All",
          ...agentNames.filter((name): name is string => name !== "All"),
        ]);
      } catch (error) {
        console.error("Error fetching agent names:", error);
        setAgentNames(["All"]);
      }
    };
    fetchAgentNames();
  }, []);

  useEffect(() => {
    Promise.all([
      getCarrierNames(),
      getGlobalAgentNames(),
      getAgencyNames(),
    ]).then(([c, a, ag]) => {
      setCarrierNames(c);
      setAgencyNames(a);
      setAgencyNames(ag);
    });
  }, []);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg pt-5 pb-7 mb-5 sm:px-3">
      <div className="flex items-end space-x-4 mb-4">
        <FilterDropdown
          label="Booking Status"
          options={FILTER_OPTIONS.BookingStatus}
          value={selectedStatus}
          onChange={(value) => updateFilter("status", value, setSelectedStatus)}
        />
        <FilterDropdown
          label="Booking Source"
          options={FILTER_OPTIONS.BookingSource}
          value={selectedSource}
          onChange={(value) => updateFilter("source", value, setSelectedSource)}
        />
        <FilterDropdown
          label="Issued On"
          options={FILTER_OPTIONS.IssuedOn}
          value={selectedIssuedOn || "All"}
          onChange={(value) =>
            updateFilter("issuedOn", value, setSelectedIssuedOn)
          }
        />
        <FilterDropdown
          label="Flight Date"
          options={FILTER_OPTIONS.FlightDate}
          value={selectedFlightDate || "All Time"}
          onChange={(value) =>
            updateFilter("flightDate", value, setSelectedFlightDate)
          }
        />
        <button
          className="h-[45px] bg-blue-500 text-white text-sm hover:bg-blue-600 transition duration-300 py-2 px-4 rounded-lg"
          onClick={handleResetFilters}
        >
          Reset Filters
        </button>
      </div>

      <button
        onClick={() => setShowAdvanced(!showAdvanced)}
        className="flex items-center text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white text-sm"
      >
        <Filter size={14} className="mr-1" />
        {showAdvanced ? "Hide Advanced Filters" : "Show Advanced Filters"}
        <ChevronDown
          size={14}
          className={`ml-1 transform transition-transform ${
            showAdvanced ? "rotate-180" : ""
          }`}
        />
      </button>

      {showAdvanced && (
        <div className="mt-4 pt-4 border-t border-gray-300 dark:border-gray-700 grid grid-cols-5 gap-4">
          <FilterDropdown
            label="Trip Type"
            options={["All", "One Way", "Round Trip"]}
            value={selectedTripType || "All"}
            onChange={(value) =>
              updateFilter("tripType", value, setSelectedTripType)
            }
          />
          <FilterDropdown
            label="Carrier"
            options={carrierNames}
            value={selectedCarrier || "All"}
            onChange={(value) =>
              updateFilter("carrier", value, setSelectedCarrier)
            }
          />
          <FilterDropdown
            label="Agency"
            options={agencyNames}
            value={selectedAgency || "All"}
            onChange={(value) =>
              updateFilter("agency", value, setSelectedAgency)
            }
          />
          <FilterDropdown
            label="Agent"
            options={agentNames}
            value={selectedAgent || "All"}
            onChange={(value) => updateFilter("agent", value, setSelectedAgent)}
          />
          <FilterDropdown
            label="Payment Method"
            options={FILTER_OPTIONS.PaymentMethods}
            value={selectedPaymentMethod || "All"}
            onChange={(value) =>
              updateFilter("paymentMethod", value, setSelectedPaymentMethod)
            }
          />
        </div>
      )}
    </div>
  );
};

interface BookingTableRowProps {
  booking: any;
  agencyNameMap: Record<string, string>;
  onEdit: (bookingId: string) => void;
}

// Utility function to determine trip status based on booking status and flight date
const determineTripStatus = (
  bookingStatus: string,
  flightDateStr: string, // Expecting "MM/DD/YYYY"
  fallbackStatus: string, // Still passed but maybe unused if logic is comprehensive
  saleStatus: string,
  source: string // Still passed but maybe unused
) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

  // If sale status is Refunded, trip status is Not Valid (Keep this rule)
  if (saleStatus === SALE_STATUS.REFUNDED) {
    return TRIP_STATUS.NOT_VALID;
  }

  // Handle cancelled/rejected statuses first
  if (
    bookingStatus === BOOKING_STATUS.CANCELLED_BY_USER ||
    bookingStatus === BOOKING_STATUS.CANCELLED_BY_SYSTEM ||
    bookingStatus === BOOKING_STATUS.BOOKING_REJECTED ||
    bookingStatus === BOOKING_STATUS.TIMED_OUT
  ) {
    return TRIP_STATUS.NOT_VALID;
  }

  // Handle pending statuses
  if (
    bookingStatus === BOOKING_STATUS.PENDING_APPROVAL ||
    bookingStatus === BOOKING_STATUS.QUICK_HOLD
  ) {
    return TRIP_STATUS.PENDING;
  }

  // For confirmed bookings, check the date
  if (bookingStatus === BOOKING_STATUS.BOOKING_CONFIRMED) {
    try {
      // Parse the flight date (MM/DD/YYYY)
      const [month, day, year] = flightDateStr.split("/").map(Number);
      const flightDate = new Date(year, month - 1, day);
      flightDate.setHours(0, 0, 0, 0);

      // Calculate the difference in days
      const diffTime = flightDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return TRIP_STATUS.COMPLETED; // Past date
      } else if (diffDays === 0) {
        return TRIP_STATUS.IN_PROGRESS; // Today
      } else {
        return TRIP_STATUS.UPCOMING; // Future date
      }
    } catch (error) {
      console.error("Error parsing flight date:", flightDateStr, error);
      return TRIP_STATUS.NOT_VALID;
    }
  }

  // Default fallback
  return TRIP_STATUS.NOT_VALID;
};

/**
 * Determines the appropriate sale status based on booking status
 * @param {string} bookingStatus - The status of the booking
 * @returns {string} - The appropriate sale status
 */
function determineSaleStatus(bookingStatus: string): string {
  if (!bookingStatus) return SALE_STATUS.COMPLETED;

  // Convert to uppercase for consistent comparison
  const status = bookingStatus.toUpperCase();

  // If Booking Status is Timed Out or Rejected, Sale Status must be Not Valid
  if (
    status === BOOKING_STATUS.TIMED_OUT ||
    status === BOOKING_STATUS.BOOKING_REJECTED
  ) {
    return SALE_STATUS.NOT_VALID;
  }

  // If Booking Status is Quick Hold or Pending Approval, Sale Status is typically Pending
  if (
    status === BOOKING_STATUS.QUICK_HOLD ||
    status === BOOKING_STATUS.PENDING_APPROVAL
  ) {
    return SALE_STATUS.PENDING;
  }

  // If Booking Status is Confirmed, Sale Status is typically Completed
  if (status === BOOKING_STATUS.BOOKING_CONFIRMED) {
    return SALE_STATUS.COMPLETED;
  }

  // If Booking Status is Cancelled, Sale Status may be Refunded
  if (
    status === BOOKING_STATUS.CANCELLED_BY_USER ||
    status === BOOKING_STATUS.CANCELLED_BY_SYSTEM
  ) {
    return SALE_STATUS.REFUNDED;
  }

  // Default to Completed for any other status
  return SALE_STATUS.COMPLETED;
}

// Function to validate sale status based on booking status
const validateSaleStatus = (bookingAction: string, saleStatus: string = "") => {
  // If booking is timed out or rejected, sale status should be Not Valid
  if (
    bookingAction === BOOKING_STATUS.TIMED_OUT ||
    bookingAction === BOOKING_STATUS.BOOKING_REJECTED
  ) {
    return SALE_STATUS.NOT_VALID;
  }

  // For other booking statuses, keep the original sale status
  return saleStatus || SALE_STATUS.COMPLETED;
};

/**
 * Normalizes the source value to ensure consistent usage throughout the application
 * @param {string} source - The original source value
 * @returns {string} - The normalized source value
 */
function normalizeSourceValue(source: string): string {
  if (!source) return "Internal";

  // Convert to uppercase for consistent comparison
  if (source === "INTERNAL") {
    return "Internal";
  } else if (source === "THIRD_PARTY") {
    return "Third-Party";
  }

  return source;
}

// Function to validate booking status based on source
const validateBookingStatus = (bookingAction: string, source: string) => {
  // Third-Party bookings cannot have Rejected status
  if (
    source === "Third-Party" &&
    bookingAction === BOOKING_STATUS.BOOKING_REJECTED
  ) {
    console.warn(
      "Invalid booking state: Third-Party bookings cannot have Rejected status"
    );
    return BOOKING_STATUS.CANCELLED_BY_USER; // Default to cancelled instead
  }
  return bookingAction;
};

interface BookingTableRowProps {
  booking: any;
  agencyNameMap: Record<string, string>;
  onEdit: (bookingId: string) => void;
}

const BookingTableRow = ({
  booking,
  agencyNameMap,
  onEdit,
}: BookingTableRowProps) => {
  const {
    reference,
    date,
    route,
    airline: carrier,
    passenger,
    travelers,
    agent,
    agency,
    tripType,
    status,
    bookingAction,
    source,
    price,
    issuedOn,
    saleStatus,
    paymentMethod,
  } = booking;

  const isRoundTrip =
    tripType?.toString().toUpperCase().replace(/\s+/g, "_") === "ROUND_TRIP";
  // Format route display based on trip type
  const formattedRoute = isRoundTrip
    ? route.replace("→", "↔") // Replace one-way arrow with bidirectional arrow for round trips
    : route.replace("↔", "→"); // Replace bidirectional arrow with one-way arrow for one-way trips

  // Normalize source value
  const normalizedSource = normalizeSourceValue(source);

  // Validate booking status based on source and get the valid booking action
  const validBookingAction = validateBookingStatus(status, normalizedSource);

  // Validate sale status based on booking action
  const validSaleStatus = validateSaleStatus(validBookingAction, saleStatus);

  // Apply status logic
  const calculatedTripStatus = determineTripStatus(
    validBookingAction,
    date,
    status,
    validSaleStatus,
    normalizedSource
  );

  // Removed useEffect hook as it was likely for fetching data at a higher level
  // useEffect(() => { ... }, []);

  // Prepare passengers data for display
  const getPassengersData = () => {
    // If we have travelers array, use it
    if (travelers && Array.isArray(travelers) && travelers.length > 0) {
      return travelers
        .map((t) => {
          if (!t?.traveler) return null;
          const traveler = t.traveler;
          return {
            ...traveler,
            // Fallback to name from traveler object if available
            name:
              traveler.firstName && traveler.lastName
                ? `${traveler.firstName} ${traveler.lastName}`
                : traveler.firstName || traveler.lastName || "Passenger",
          };
        })
        .filter(Boolean); // Remove any null entries
    }

    // Fallback to the passenger string if available
    if (passenger) {
      return [
        {
          name: passenger,
          firstName: passenger.split(" ")[0],
          lastName: passenger.split(" ").slice(1).join(" "),
        },
      ];
    }

    // No passenger data available
    return [];
  };

  const passengersData = getPassengersData();

  return (
    <tr className="border-b border-gray-300 dark:border-gray-700">
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-semibold">
        <span className="text-blue-400">{reference}</span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {getFormatDateTable(date)}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {travelers && travelers?.length > 1 ? (
          <PassengerDisplay passengers={passengersData} />
        ) : (
          <div className="group">
            {/* Truncated Name */}
            <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
              {passenger}
            </div>
            <div className="relative">
              {/* Tooltip with Full Name */}
              <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
                {passenger}
              </div>
            </div>
          </div>
        )}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {formattedRoute}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {TRIP_TYPE_LABELS[tripType] || tripType}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {carrier}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {price}
      </td>
      {/* Trip Status Column */}
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            STATUS_STYLES[calculatedTripStatus] || "bg-gray-300 text-gray-800"
          }`}
        >
          {calculatedTripStatus}
        </span>
      </td>
      {/* Booking Status Column - Use bookingAction */}
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            // Use the normalized status string as the key for the style lookup
            BOOKING_STATUS_STYLES[status] || "bg-gray-300 text-gray-800"
          }`}
        >
          {/* Display the normalized status string */}
          {normalizeBookingStatus(status)}
        </span>
      </td>
      {/* Sale Status Column */}
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            SALE_STATUS_STYLES[validSaleStatus] || "bg-gray-300 text-gray-800"
          }`}
        >
          {validSaleStatus}
        </span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${getSourceStyle(
            normalizedSource
          )}`}
        >
          {normalizedSource}
        </span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <div className="group">
          {/* Truncated Name */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {booking.createdBy || "-"}
          </div>
          <div className="relative">
            {/* Tooltip with Full Name */}
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {booking.createdBy || "-"}
            </div>
          </div>
        </div>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <div className="group">
          {/* Truncated Name */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {agencyNameMap[agency] || agency || "-"}
          </div>
          <div className="relative">
            {/* Tooltip with Full Name */}
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {agencyNameMap[agency] || agency || "-"}
            </div>
          </div>
        </div>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <div className="group">
          {/* Truncated Name */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {agent || "-"}
          </div>
          <div className="relative">
            {/* Tooltip with Full Name */}
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {agent || "-"}
            </div>
          </div>
        </div>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {paymentMethod}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {getFormatDateTable(issuedOn)}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100 flex space-x-2">
        <button
          className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300"
          onClick={() => onEdit(reference)}
        >
          <Edit size={18} />
        </button>
      </td>
    </tr>
  );
};

// Bookings table component
interface BookingsTableProps {
  bookings: any[];
  agencyNameMap: Record<string, string>;
  onEditBooking: (bookingId: string) => void;
  hasMore: boolean;
  loadingMore: boolean;
  observerRef: any; // Using any to accommodate the ref function from useInView
  totalBookings: number;
}

const BookingsTable = ({
  bookings,
  agencyNameMap,
  onEditBooking,
  hasMore,
  loadingMore,
  observerRef,
  // totalBookings is not used in this component but is part of the interface
  totalBookings: _,
}: BookingsTableProps) => {
  return (
    <div className="overflow-x-auto custom-scrollbar max-h-[calc(100vh-400px)]">
      <Suspense
        fallback={
          <div className="py-3 w-full">
            <div className="flex justify-center">
              <ListLoading />
            </div>
          </div>
        }
      >
        <table className="table-auto w-full">
          {/* Table header */}
          <thead className="text-xs font-semibold capitalize text-gray-800 dark:text-gray-50 bg-gray-50 dark:bg-gray-900/20 border-t border-b border-gray-200 dark:border-gray-700 text-left sticky -top-0.5 z-[5]">
            <tr>
              {MASTER_TABLE_HEADERS.map((header) => (
                <th
                  key={header}
                  className="pl-2 p-4 whitespace-nowrap font-semibold text-sm bg-gray-300 dark:bg-gray-700"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          {/* Table body */}
          <tbody className="text-sm divide-y divide-gray-200 dark:divide-gray-700">
            {bookings.map((booking: any, index) => (
              <BookingTableRow
                key={`${booking.reference}-${index}`}
                booking={booking}
                agencyNameMap={agencyNameMap}
                onEdit={onEditBooking}
              />
            ))}
          </tbody>
        </table>

        {bookings && bookings.length === 0 && !loadingMore && (
          <div className="text-center py-8">
            <p className="text-lg dark:text-gray-400">No Bookings Found</p>
          </div>
        )}
        <div className="w-full">
          {loadingMore && (
            <div className="py-4">
              <div className="flex justify-center">
                <ListLoading />
              </div>
            </div>
          )}
          {!loadingMore && hasMore && (
            <div ref={observerRef} className="w-full h-10" />
          )}
        </div>
      </Suspense>
    </div>
  );
};

// Tab navigation item component
const TabItem = ({ label, isActive, onClick }: any) => (
  <span
    className={`${
      isActive
        ? "bg-red-500 text-white"
        : "text-gray-700 hover:bg-gray-300 dark:text-gray-300 dark:hover:bg-gray-700"
    } cursor-pointer py-1 px-2 rounded transition-colors duration-200`}
    onClick={onClick}
  >
    {label}
  </span>
);

// Search and action buttons component
const SearchAndActions = ({
  searchQuery,
  setSearchQuery,
  onExport,
}: {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  onExport: () => void;
}) => (
  <div className="flex items-center space-x-3">
    <button
      className="flex items-center space-x-2 bg-red-500 text-white text-sm hover:bg-red-600 transition duration-300 px-3 py-2 rounded-lg"
      onClick={onExport}
    >
      <Download size={16} />
      <span>Export</span>
    </button>
    <div className="relative">
      <input
        type="text"
        placeholder="Search bookings..."
        className="w-full mx-auto bg-gray-200 dark:bg-gray-700 dark:text-white rounded-lg py-2 pl-10 pr-4 md:w-64 border-none focus:outline-none focus:ring-2 focus:ring-red-500 hover:ring-2 hover:ring-red-500 hover:border-red-500"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      <Search className="absolute left-3 top-2.5 text-gray-400" size={20} />
    </div>
  </div>
);

// Main dashboard component
const GlobalBookingsDashboard = () => {
  // Define booking interface
  interface Booking {
    reference: string;
    date: string;
    route: string;
    airline: string;
    passenger: string;
    agent: string;
    tripType: string;
    status: string;
    bookingAction: string;
    source: string;
    price: string | number;
    createdBy: string;
    issuedOn: string;
    saleStatus?: string;
    agency?: string;
  }

  const [agencyNameMap, setAgencyNameMap] = useState<Record<string, string>>(
    {}
  );
  const router = useRouter();
  const dispatch = useDispatch();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("All");
  const [activeSaleTab, setActiveSaleTab] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [bookingCounts, setBookingCounts] = useState({
    totalBookings: 0,
  });
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);

  const bookingResult = useSelector(
    (state: RootState) => state.bookingConfirmation.bookingResult
  );
  const travelerData = useSelector(
    (state: RootState) => state.bookingConfirmation.travelerData
  );
  const fullTicket = useSelector(
    (state: RootState) => state.bookingConfirmation.fullTicket
  );
  const passengerCounts = useSelector(
    (state: RootState) => state.bookingConfirmation.passengerCounts
  );
  const bookingType = useSelector(
    (state: RootState) => state.ticketSearchForm.value.bookingType
  );
  const itinerary = useSelector(
    (state: RootState) => state.ticketSearchForm.value.itinerary
  );

  // Use IntersectionObserver via useInView hook for infinite scrolling
  const { ref, inView } = useInView({
    threshold: 0.5, // Trigger when 50% of the element is visible
    triggerOnce: false,
    rootMargin: "0px 0px 0px 0px", // No extra margin
    initialInView: false,
  });

  // Ref to track if we're currently loading more bookings
  const isLoadingMoreRef = useRef(false);

  const [filters, setFilters] = useState({
    status: "All",
    source: "All",
    issuedOn: "All",
    flightDate: "All Time",
    tripType: "All",
    carrier: "All",
    agency: "All",
    agent: "All",
    paymentMethod: "All",
  });

  const buildQuery = (f: typeof filters) => ({
    ...(f.status !== "All" && { status: f.status }),
    ...(f.source !== "All" && { source: f.source }),
    ...(f.flightDate !== "All Time" && { flightDate: f.flightDate }),
    ...(f.issuedOn !== "All" && { issuedOn: f.issuedOn }),
    ...(f.tripType !== "All" && { tripType: f.tripType }),
    ...(f.carrier !== "All" && { carrier: f.carrier }),
    ...(f.agency !== "All" && { agency: f.agency }),
    ...(f.agent !== "All" && { agent: f.agent }),
    ...(f.paymentMethod !== "All" && { paymentMethod: f.paymentMethod }),
  });

  // Function to load more bookings when scrolling
  const loadMoreBookings = async () => {
    // Don't load more if already loading or no more data to load
    if (isLoadingMoreRef.current || loadingMore || !hasMore) {
      return;
    }

    // Set loading flag in ref to prevent multiple simultaneous calls
    isLoadingMoreRef.current = true;
    setLoadingMore(true);

    // Add a small delay to ensure the loading indicator is visible
    await new Promise((resolve) => setTimeout(resolve, 500));

    try {
      // Fetch more bookings using the cursor with a limit of 10
      const data = await getAllGlobalBookings(
        nextCursor,
        10,
        buildQuery(filters)
      );

      if (data && data.success && data.results) {
        // Update total count if available
        if (data.results.bookingsTotal) {
          const totalCount = data.results.bookingsTotal;
          setBookingCounts({ totalBookings: totalCount });
        }

        // Map the new bookings to UI format
        const newBookings = data.results.bookings || [];

        // If no new bookings were returned, we've reached the end
        if (newBookings.length === 0) {
          setHasMore(false);
          return;
        }

        // Process each booking to add to the list
        const newMappedBookings = newBookings.map((b: any) => {
          const isDepartureRoundTrip =
            b.ticket.departureTime === b.meta?.departure?.departureTime;
          const isArrivalRoundTrip =
            b.ticket.departureTime === b.meta?.return?.departureTime;
          const price = calculatePrice(b);

          return {
            reference: b.requestId || b.id || "",
            date: b.ticket?.flightDate
              ? new Date(b.ticket.flightDate).toLocaleDateString()
              : b.flightDate
              ? new Date(b.flightDate).toLocaleDateString()
              : "-",
            route: (() => {
              // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights
              if (
                isReturnFlight &&
                b.meta?.return?.departureAirport &&
                b.meta?.return?.arrivalAirport
              ) {
                return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
              }

              // For departure flights (default)
              if (
                b.meta?.departure?.departureAirport &&
                b.meta?.departure?.arrivalAirport
              ) {
                return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
              }

              // Fallback
              if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
              }

              return "-";
            })(),
            airline: (() => {
              // Use the same logic as route to determine if this is a return flight
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights, use return flight's carrier
              if (isReturnFlight && b.meta?.return?.carrier) {
                return b.meta.return.carrier;
              }

              // For departure flights or fallback
              return b.meta?.departure?.carrier || b.meta?.carrier || "-";
            })(),
            passenger:
              b.travelers &&
              Array.isArray(b.travelers) &&
              b.travelers.length > 0
                ? (() => {
                    const tr = b.travelers[0].traveler || b.travelers[0];
                    return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                  })()
                : "-",
            travelers: b.travelers || [],
            // Show the agent who created the booking in the Seller Agency column
            agent: b.meta?.buyerAgentName || "-",
            // Show the agent who created the booking in the Seller Agency column
            agency: b.meta?.buyerAgencyName || "-",
            createdBy: b.meta?.sellerAgencyName || b.meta?.sellerName || "-",
            tripType: b.tripType || "-",
            status: b.status || "-",
            bookingAction: b.initialHoldType || b.type || "-",
            source: normalizeSourceValue(b.source) || "-",
            price:
              price ||
              (b.totalAmount && b.totalAmount !== "0"
                ? b.totalAmount
                : b.bookedSeats && b.bookedSeats.length > 0
                ? b.bookedSeats[0].totalPrice
                : "-"),
            issuedOn: b.createdAt
              ? new Date(b.createdAt).toLocaleDateString()
              : "-",
            saleStatus: determineSaleStatus(b.status),
          };
        });

        // Update state with new bookings
        setBookings((prevBookings) => {
          // Check if we already have this booking to prevent duplicates
          const existingIds = new Set(prevBookings.map((b) => b.reference));
          const uniqueNewBookings = newMappedBookings.filter(
            (b) => !existingIds.has(b.reference)
          );

          if (uniqueNewBookings.length === 0) {
            // No new unique bookings, we've reached the end
            setHasMore(false);
            return prevBookings;
          }

          return [...prevBookings, ...uniqueNewBookings];
        });

        // Update cursor for next page
        const newCursor = data.results.nextCursor || null;
        setNextCursor(newCursor);

        // If we received fewer items than requested, we've reached the end
        if (newBookings.length < 10) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error loading more bookings:", error);
      setHasMore(false);
    } finally {
      // Reset loading states
      setLoadingMore(false);
      isLoadingMoreRef.current = false;
    }
  };

  // Track the last time we loaded more bookings
  const lastLoadTimeRef = useRef<number>(0);

  // Effect to load more bookings when user scrolls to the bottom
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (inView && !loading && !loadingMore && hasMore && nextCursor) {
      // Check if enough time has passed since the last load (at least 1 second)
      const now = Date.now();
      const timeSinceLastLoad = now - lastLoadTimeRef.current;

      if (timeSinceLastLoad < 1000) {
        return; // Don't load if less than 1 second has passed
      }

      // Add a small delay to prevent rapid loading
      timeoutId = setTimeout(() => {
        // Check again before loading to prevent race conditions
        if (!loadingMore && hasMore && nextCursor) {
          lastLoadTimeRef.current = Date.now(); // Update last load time
          loadMoreBookings();
        }
      }, 300);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [inView, loading, loadingMore, hasMore, nextCursor]);

  // Fetch all agents and build a map of agentId -> agencyName
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        const { fetchAllAgents } = await import("@/lib/data/agencyData");
        const agents = await fetchAllAgents();
        // If agents is an array of agent objects with id and agencyName
        if (Array.isArray(agents)) {
          const map: Record<string, string> = {};
          agents.forEach((agent: any) => {
            if (agent.id && agent.agencyName) {
              map[agent.id] = agent.agencyName;
            }
          });
          setAgencyNameMap(map);
        }
      } catch (e) {
        // Fallback: do nothing
      }
    };
    fetchAgents();
  }, []);

  // Fetch initial bookings data from API
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        setBookings([]);
        setNextCursor(null);
        setHasMore(true);

        const data = await getAllGlobalBookings(null, 20, buildQuery(filters));

        if (data && data.success && data.results) {
          // Immediately update the total count
          if (data.results.bookingsTotal) {
            const totalCount = data.results.bookingsTotal;
            setBookingCounts({ totalBookings: totalCount });
          }

          const bookingsData = data.results.bookings || [];

          // Map backend booking data to UI structure
          const mappedBookings = bookingsData.map((b: any) => {
            // Get the agent who created the booking from meta data
            // First try to get from meta.agentName, then meta.bookedByAgentName, then user info, then fallback to ID
            const agentName =
              b.meta?.agentName ||
              b.meta?.bookedByAgentName ||
              (b.user
                ? `${b.user.firstName || ""} ${b.user.lastName || ""}`.trim()
                : "") ||
              b.agencyAgentId ||
              "-";

            // Get the agency name from meta.agencyName, then user.agencyName, then agencyNameMap
            const agencyName =
              b.meta?.agencyName ||
              b.user?.agencyName ||
              "" ||
              (b.agencyAgentId ? agencyNameMap[b.agencyAgentId] : "") ||
              "-";

            const isDepartureRoundTrip =
              b.ticket.departureTime === b.meta?.departure?.departureTime;
            const isArrivalRoundTrip =
              b.ticket.departureTime === b.meta?.return?.departureTime;
            const price = calculatePrice(b);

            return {
              reference: b.requestId || b.id || "",
              date: b.ticket?.flightDate
                ? new Date(b.ticket.flightDate).toLocaleDateString()
                : b.flightDate
                ? new Date(b.flightDate).toLocaleDateString()
                : "-",
              route: (() => {
                // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
                const isReturnFlight =
                  b.meta?.return?.departureTime &&
                  b.ticket?.departureTime === b.meta.return.departureTime;

                // For return flights
                if (
                  isReturnFlight &&
                  b.meta?.return?.departureAirport &&
                  b.meta?.return?.arrivalAirport
                ) {
                  return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
                }

                // For departure flights (default)
                if (
                  b.meta?.departure?.departureAirport &&
                  b.meta?.departure?.arrivalAirport
                ) {
                  return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
                }

                // Fallback
                if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                  return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
                }

                return "-";
              })(),
              airline: (() => {
                // Use the same logic as route to determine if this is a return flight
                const isReturnFlight =
                  b.meta?.return?.departureTime &&
                  b.ticket?.departureTime === b.meta.return.departureTime;

                // For return flights, use return flight's carrier
                if (isReturnFlight && b.meta?.return?.carrier) {
                  return b.meta.return.carrier;
                }

                // For departure flights or fallback
                return b.meta?.departure?.carrier || b.meta?.carrier || "-";
              })(),
              passenger:
                b.travelers &&
                Array.isArray(b.travelers) &&
                b.travelers.length > 0
                  ? ((): string => {
                      const tr = b.travelers[0].traveler || b.travelers[0];
                      return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                    })()
                  : "-",
              // agent: agentName,
              // agency: agencyName,
              travelers: b.travelers || [],
              tripType: b.tripType || "-",
              status: b.status || "-",
              bookingAction: b.initialHoldType || b.type || "-",
              source: normalizeSourceValue(b.source) || "-",
              price:
                price ||
                (b.totalAmount && b.totalAmount !== "0"
                  ? b.totalAmount
                  : b.bookedSeats && b.bookedSeats.length > 0
                  ? b.bookedSeats[0].totalPrice
                  : "-"),
              agent:
                b.source === "INTERNAL"
                  ? b.agent !== "Unknown Agent"
                    ? b.agent
                    : b.meta?.buyerAgentName !== "Unknown Agent"
                    ? b.meta?.buyerAgentName
                    : b.meta?.agentName !== "Unknown Agent"
                    ? b.meta?.agentName
                    : b.bookedByAgentName !== "Unknown Creator"
                    ? b.bookedByAgentName
                    : "-"
                  : b.agent !== "Unknown Agent"
                  ? b.agent
                  : b.meta?.buyerAgentName !== "Unknown Agent"
                  ? b.meta?.buyerAgentName
                  : b.meta?.agentName !== "Unknown Agent"
                  ? b.meta?.agentName
                  : b.bookedByAgentName !== "Unknown Creator"
                  ? b.bookedByAgentName
                  : "-",
              agency:
                b.source === "INTERNAL"
                  ? b.meta?.ownerName || b.meta?.departureSeller?.agencyName
                  : b.meta?.buyerAgencyName ||
                    b.meta?.buyerAgencyId ||
                    b?.buyerAgencyId ||
                    b?.ticket?.ownerId ||
                    b.meta?.ownerName ||
                    "-",
              createdBy:
                b.meta?.sellerAgencyName ||
                b.meta?.sellerName ||
                b.meta?.ownerName ||
                (isDepartureRoundTrip
                  ? b.meta?.departureSeller?.agencyName
                  : b.meta?.returnSeller?.agencyName) ||
                "-",
              paymentMethod:
                b.source === "THIRD_PARTY"
                  ? "Airvilla Wallet"
                  : normalizePaymentMethod(b.payment?.paymentMethod) || "-",
              issuedOn: b.createdAt
                ? new Date(b.createdAt).toLocaleDateString()
                : "-",
              saleStatus: determineSaleStatus(b.status),
            };
          });

          setBookings(mappedBookings);

          // Update cursor for next page
          setNextCursor(data.results.nextCursor || null);

          // Check if there are more bookings to load
          setHasMore(!!data.results.nextCursor);
        } else {
          setError("Failed to fetch booking data. Please try again later.");
        }

        setLoading(false);
      } catch (err: any) {
        setError(
          err.message || "Failed to fetch bookings. Please try again later."
        );
        setLoading(false);
      }
    };
    fetchBookings();
  }, [filters]);

  // Add useEffect to reset the opposite tab when one tab changes
  useEffect(() => {
    // If activeTab is not "All", reset activeSaleTab to "All"
    if (activeTab !== "All") {
      setActiveSaleTab("All");
    }
  }, [activeTab]);

  // Similarly, monitor activeSaleTab changes
  useEffect(() => {
    // If activeSaleTab is not "All", reset activeTab to "All"
    if (activeSaleTab !== "All") {
      setActiveTab("All");
    }
  }, [activeSaleTab]);

  // Filter bookings based on active tab and search
  const filteredBookings = bookings.filter((booking) => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        booking.reference.toLowerCase().includes(query) ||
        booking.passenger.toLowerCase().includes(query) ||
        booking.airline.toLowerCase().includes(query) ||
        booking.route.toLowerCase().includes(query)
      );
    }

    // Get the trip status for the current booking
    const validBookingAction = validateBookingStatus(
      booking.bookingAction,
      booking.source
    );
    const validSaleStatus = validateSaleStatus(
      validBookingAction,
      booking.saleStatus
    );
    const calculatedTripStatus = determineTripStatus(
      validBookingAction,
      booking.date,
      booking.bookingAction,
      validSaleStatus,
      booking.source
    );

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

    // Parse the flight date (MM/DD/YYYY)
    const [month, day, year] = booking.date.split("/").map(Number);
    const flightDate = new Date(year, month - 1, day);
    flightDate.setHours(0, 0, 0, 0);

    // Calculate the difference in days
    const diffTime = flightDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Apply tab filter based on activeTab
    if (activeTab !== "All") {
      // Special handling for Rejected status
      if (activeTab === TRIP_STATUS.NOT_VALID) {
        return (
          booking.status === BOOKING_STATUS.BOOKING_REJECTED ||
          booking.status === BOOKING_STATUS.TIMED_OUT ||
          booking.status === BOOKING_STATUS.CANCELLED_BY_USER ||
          booking.status === BOOKING_STATUS.CANCELLED_BY_SYSTEM
        );
      }

      // Special handling for Pending status
      if (activeTab === TRIP_STATUS.PENDING) {
        return (
          booking.status === BOOKING_STATUS.PENDING_APPROVAL ||
          booking.status === BOOKING_STATUS.QUICK_HOLD
        );
      }

      // Special handling for Completed status
      if (activeTab === TRIP_STATUS.COMPLETED) {
        return (
          booking.status === BOOKING_STATUS.BOOKING_CONFIRMED && diffDays < 0
        );
      }

      // Special handling for Upcoming status
      if (activeTab === TRIP_STATUS.UPCOMING) {
        return (
          booking.status === BOOKING_STATUS.BOOKING_CONFIRMED && diffDays > 0
        );
      }

      // Special handling for In Progress status
      if (activeTab === TRIP_STATUS.IN_PROGRESS) {
        return (
          booking.status === BOOKING_STATUS.BOOKING_CONFIRMED && diffDays === 0
        );
      }

      // For other statuses, check the calculated trip status
      if (calculatedTripStatus === activeTab) {
        return true;
      }

      return false;
    }

    // If no tab is selected, apply sale status filter if active
    if (activeSaleTab !== "All") {
      return booking.saleStatus === activeSaleTab;
    }

    // If no filters are active, show all bookings
    return true;
  });

  // Handle tab click
  const handleTabClick = (tabName: string) => {
    setActiveTab(tabName);
  };

  // Handle sale status tab click
  const handleSaleTabClick = (tabName: string) => {
    setActiveSaleTab(tabName);
  };

  // Handler for exporting bookings to CSV
  const handleExportBookings = () => {
    try {
      // Prepare bookings with trip status for export
      const bookingsWithTripStatus = filteredBookings.map((booking) => {
        // Validate booking status based on source
        const validBookingAction = validateBookingStatus(
          booking.status,
          booking.source
        );

        // Validate sale status based on booking action
        const validSaleStatus = validateSaleStatus(
          validBookingAction,
          booking.saleStatus
        );

        // Calculate trip status
        const calculatedTripStatus = determineTripStatus(
          validBookingAction,
          booking.date,
          booking.status,
          validSaleStatus,
          booking.source
        );

        // Return booking with trip status
        return {
          ...booking,
          tripStatus: calculatedTripStatus,
        };
      });

      // Format the bookings data for export
      const formattedData = formatBookingsForExport(bookingsWithTripStatus);

      // Generate a filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split("T")[0]; // YYYY-MM-DD format
      const filename = `airvilla-bookings-${dateStr}`;

      // Export the data to CSV
      exportToCSV(formattedData, filename);
    } catch (error) {
      console.error("Error exporting bookings:", error);
      // You could add a toast notification here
    }
  };

  // Handler for editing booking
  const handleEditBooking = async (bookingId: string) => {
    try {
      // Show loading state
      setLoading(true);

      if (!bookingId) {
        console.error("No bookingId provided to getBookingById");
        setLoading(false);
        return;
      }

      // Fetch the full booking data from API
      const bookingData = await getBookingById(bookingId);

      if (bookingData) {
        // Store the booking data in Redux
        dispatch(
          setBookingConfirmationData({
            bookingResult: bookingData,
            travelerData: travelerData,
            ticket: bookingData,
            fullTicket: bookingData,
            passengerCounts: passengerCounts,
          })
        );

        // Navigate to the manage booking page
        router.push(
          `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingId}&entryPoint=global-bookings`
        );
      } else {
        console.error("Failed to fetch booking data");
      }
    } catch (error) {
      console.error("Error fetching booking data:", error);
    } finally {
      // Hide loading state
      setLoading(false);
    }
  };

  return (
    <div className="dark:text-white">
      <div className="min-w-screen-2xl max-w-[1660px] mx-auto">
        <DashboardHeader />
        <FilterCard filters={filters} setFilters={setFilters} />
        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg">
          <div className="bg-white dark:bg-gray-800 py-4 px-6 flex items-center justify-between border-b border-gray-300 dark:border-gray-700">
            <div className="flex items-center space-x-6">
              <div className="flex items-center">
                <span className="text-2xl font-bold text-red-500 mr-2">
                  {activeTab === "All" || activeSaleTab === "All"
                    ? filteredBookings.length
                    : bookingCounts.totalBookings}
                </span>
                <span className="text-lg font-semibold">Total Bookings</span>
              </div>
              <div className="h-8 w-px bg-gray-300 dark:bg-gray-700"></div>
              {/* Tab Navigation */}
              <div className="flex items-center space-x-2 text-sm">
                <TabItem
                  label="All"
                  isActive={activeTab === "All"}
                  onClick={() => handleTabClick("All")}
                />
                <TabItem
                  label="Completed"
                  isActive={activeTab === TRIP_STATUS.COMPLETED}
                  onClick={() => handleTabClick(TRIP_STATUS.COMPLETED)}
                />
                <TabItem
                  label="In Progress"
                  isActive={activeTab === TRIP_STATUS.IN_PROGRESS}
                  onClick={() => handleTabClick(TRIP_STATUS.IN_PROGRESS)}
                />
                <TabItem
                  label="Upcoming"
                  isActive={activeTab === TRIP_STATUS.UPCOMING}
                  onClick={() => handleTabClick(TRIP_STATUS.UPCOMING)}
                />
                <TabItem
                  label="Pending"
                  isActive={activeTab === TRIP_STATUS.PENDING}
                  onClick={() => handleTabClick(TRIP_STATUS.PENDING)}
                />
                <TabItem
                  label="Not Valid"
                  isActive={activeTab === TRIP_STATUS.NOT_VALID}
                  onClick={() => handleTabClick(TRIP_STATUS.NOT_VALID)}
                />
              </div>
              <div className="h-8 w-px bg-gray-300 dark:bg-gray-700 ml-2"></div>
              {/* Sale Status Navigation */}
              <div className="flex items-center space-x-2 text-sm">
                <span className="dark:text-gray-400 mr-1">Sale:</span>
                <TabItem
                  label="All"
                  isActive={activeSaleTab === "All"}
                  onClick={() => handleSaleTabClick("All")}
                />
                <TabItem
                  label="Completed"
                  isActive={activeSaleTab === SALE_STATUS.COMPLETED}
                  onClick={() => handleSaleTabClick(SALE_STATUS.COMPLETED)}
                />
                <TabItem
                  label="Refunded"
                  isActive={activeSaleTab === SALE_STATUS.REFUNDED}
                  onClick={() => handleSaleTabClick(SALE_STATUS.REFUNDED)}
                />
              </div>
            </div>
            <SearchAndActions
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              onExport={handleExportBookings}
            />
          </div>
          {loading && !hasMore ? (
            <div className="py-3 w-full">
              <div className="flex justify-center">
                <ListLoading />
              </div>
            </div>
          ) : (
            <BookingsTable
              bookings={filteredBookings}
              agencyNameMap={agencyNameMap}
              onEditBooking={handleEditBooking}
              hasMore={hasMore}
              loadingMore={loadingMore}
              observerRef={ref}
              totalBookings={bookingCounts.totalBookings}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default function GlobalBookings() {
  // check user's access
  const { loading } = useMasterUserAuth();

  if (loading) {
    return <ProgressLoading />;
  }
  return (
    <div className="pt-8 w-full max-w-full mx-auto">
      <GlobalBookingsDashboard />
    </div>
  );
}
