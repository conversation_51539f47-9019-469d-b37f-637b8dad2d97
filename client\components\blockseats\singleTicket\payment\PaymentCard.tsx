"use client";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { <PERSON><PERSON><PERSON><PERSON>, CircleCheckBig, CreditCard, Wallet } from "lucide-react";
import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector } from "@/redux/hooks";
import { getOwnerCreditBalance } from "@/lib/data/userProfileData";
import { getFormatTime } from "@/utils/functions/functions";
import { RootState } from "@/redux/store";

// Define traveler interface
interface Traveler {
  title: string;
  gender: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  issuingCountry: string;
  passportExpiry: string;
}

// Define agent operations interface
interface AgentOperations {
  bookingReference: string;
  ticketId: string;
  issuingBookingSource: string;
  agent: {
    name: string | null;
    role: string | null;
    agency: string | null;
  };
  formFiller?: {
    id: string;
    name: string | null;
    email: string | null;
    role: string | null;
    agency: string | null;
  };
}

export default function PaymentCard({
  quickHold,
  setQuickHold,
  submitBooking,
  setSubmitBooking,
  bookingType,
  fare,
  agentOperations,
}: {
  quickHold: boolean;
  setQuickHold: (value: boolean) => void;
  submitBooking: boolean;
  setSubmitBooking: (value: boolean) => void;
  bookingType: "INTERNAL" | "THIRD_PARTY" | null;
  fare: { total: number; currency: string };
  agentOperations?: AgentOperations;
}) {
  // Only show the Agent Operations section if we have valid data
  const showAgentOperations =
    agentOperations &&
    (agentOperations.bookingReference ||
      agentOperations.agent?.name ||
      agentOperations.agent?.agency);

  // Use the agentOperations prop with fallback to empty values
  const safeAgentOperations = showAgentOperations ? agentOperations : null;
  const isBookingActionSelected = quickHold || submitBooking;
  const user: any = useAppSelector(selectUser);
  const [availableBalance, setAvailableBalance] = useState(
    typeof user?.creditBalance === "number"
      ? user.creditBalance
      : Number(user?.creditBalance) || 0
  );

  // Fetch owner balance for third-party booking if user is team/agency member
  useEffect(() => {
    const fetchBalance = async () => {
      if (bookingType === "THIRD_PARTY" && user) {
        const isTeamMember =
          user.role === "master" && user.roleType !== "master_owner";
        const isAgencyAgent =
          user.role === "agency" && user.roleType !== "agency_owner";
        if (isTeamMember || isAgencyAgent) {
          try {
            const balanceData = await getOwnerCreditBalance();
            if (balanceData.success) {
              const balanceValue = parseFloat(
                balanceData?.creditBalance || "0.00"
              );
              setAvailableBalance(isNaN(balanceValue) ? 0 : balanceValue);
            } else {
              setAvailableBalance(0);
            }
          } catch (err) {
            setAvailableBalance(0);
          }
        } else {
          // fallback to user's own balance
          const userBalance = parseFloat(user.creditBalance || "0");
          setAvailableBalance(isNaN(userBalance) ? 0 : userBalance);
        }
      } else if (user) {
        // Not third-party: always use user's own balance
        const userBalance = parseFloat(user.creditBalance || "0");
        setAvailableBalance(isNaN(userBalance) ? 0 : userBalance);
      }
    };
    fetchBalance();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookingType, user]);

  // Get URL parameters
  const searchParams = useSearchParams();

  // Flight details from URL
  const [flightDetails, setFlightDetails] = useState({
    itinerary: "",
    travelClass: "",
    departureCode: "",
    departureCity: "",
    departureCountry: "",
    departureAirport: "",
    arrivalCode: "",
    arrivalCity: "",
    arrivalCountry: "",
    arrivalAirport: "",
    departureCarrier: "",
    departureFlightNumber: "",
    returnCarrier: "",
    returnFlightNumber: "",
    flightNumber: "", // Deprecated
    departureFlightDate: "",
    arrivalFlightDate: "",
    departureTime: "",
    arrivalTime: "",
    duration: "",
    stops: 0,
    adults: 0,
    children: 0,
    infants: 0,
    travelers_count: 0,
    agentOperations: null as AgentOperations | null, // Fixed type mismatch
    price: {
      departureAdultPrice: 0,
      departureChildPrice: 0,
      departureInfantPrice: 0,
      departureTaxPrice: 0,
      departureCurrency: "",

      returnAdultPrice: 0,
      returnChildPrice: 0,
      returnInfantPrice: 0,
      returnTaxPrice: 0,
      returnCurrency: "",
    },
  });

  // Calculate payment amount based on actual passenger selections
  const isRoundTrip = flightDetails.itinerary === "round trip";

  // Get passenger counts with defaults
  const passengerCounts2 = {
    adults: flightDetails.adults || 0,
    children: flightDetails.children || 0,
    infants: flightDetails.infants || 0,
    total: flightDetails.travelers_count || 0,
  };

  // Calculate base fare total (should match what's shown in the UI)
  const calculateBaseFare = () => {
    if (!flightDetails.price) return 0;

    const { price } = flightDetails;
    const adultFare =
      passengerCounts2.adults * (price.departureAdultPrice || 0);
    const childFare =
      passengerCounts2.children * (price.departureChildPrice || 0);
    const infantFare =
      passengerCounts2.infants * (price.departureInfantPrice || 0);
    const subtotal = adultFare + childFare + infantFare;
    // Calculate tax as a percentage of the subtotal (5% for outbound)
    const taxRate = price.departureTaxPrice || 0; // 5% tax rate for outbound
    const tax = (subtotal * taxRate) / 100;

    return adultFare + childFare + infantFare + tax;
  };

  // Calculate return fare total if round trip
  const calculateReturnFare = () => {
    if (!isRoundTrip || !flightDetails.price) return 0;

    const { price } = flightDetails;
    const adultFare = passengerCounts2.adults * (price.returnAdultPrice || 0);
    const childFare = passengerCounts2.children * (price.returnChildPrice || 0);
    const infantFare =
      passengerCounts2.infants * (price.returnInfantPrice || 0);
    const subtotal = adultFare + childFare + infantFare;
    // Calculate tax as a percentage of the subtotal (5.2% for return)
    const taxRate = price.returnTaxPrice || 0; // 5.2% tax rate for return
    const tax = (subtotal * taxRate) / 100;

    return adultFare + childFare + infantFare + tax;
  };

  // Calculate totals
  const baseFare = calculateBaseFare();
  const returnFare = calculateReturnFare();

  // Final payment amount
  const paymentAmount = isRoundTrip ? baseFare + returnFare : baseFare;

  const currency =
    flightDetails.price?.departureCurrency || fare?.currency || "JOD";

  // Traveler details from URL
  const [travelers, setTravelers] = useState<Traveler[]>([]);

  useEffect(() => {
    // Extract flight details from URL parameters
    let agentOperationsJson: AgentOperations | null = null;

    let priceJson = {
      departureAdultPrice: 0,
      departureChildPrice: 0,
      departureInfantPrice: 0,
      departureTaxPrice: 0,
      departureCurrency: "",
      returnAdultPrice: 0,
      returnChildPrice: 0,
      returnInfantPrice: 0,
      returnTaxPrice: 0,
      returnCurrency: "",
    };

    // First check if agentOperations prop is provided
    if (agentOperations) {
      agentOperationsJson = agentOperations;
    } else {
      // If not, try to get from URL parameters
      try {
        const agentOperationsStr = searchParams.get("agentOperations");

        if (agentOperationsStr) {
          // First try direct parsing
          try {
            agentOperationsJson = JSON.parse(agentOperationsStr);
          } catch (parseError) {
            // Try decoding first
            try {
              const decodedStr = decodeURIComponent(agentOperationsStr);
              agentOperationsJson = JSON.parse(decodedStr);
            } catch (decodeError) {
              console.error(
                "Error parsing decoded agentOperations:",
                decodeError
              );
            }
          }
        } else {
          // Get user data from Redux
          const userData = user || {};
          // Create agent operations object from user data and URL parameters
          agentOperationsJson = {
            bookingReference:
              searchParams.get("bookRef") ||
              searchParams.get("departureId") ||
              "BOOK-" + new Date().getTime(),
            ticketId:
              searchParams.get("tktId") ||
              searchParams.get("departureId") ||
              "TKT-" + new Date().getTime(),
            issuingBookingSource: searchParams.get("source") || "Internal",
            agent: {
              name: userData.name || searchParams.get("agentName") || "",
              role: userData.role || searchParams.get("agentRole") || "",
              agency: userData.agency || searchParams.get("agentAgency") || "",
            },
            formFiller: {
              id: userData.id || searchParams.get("fillerID") || "",
              name: userData.name || searchParams.get("fillerName") || "",
              email: userData.email || searchParams.get("fillerEmail") || "",
              role: userData.role || searchParams.get("fillerRole") || "",
              agency: userData.agency || searchParams.get("fillerAgency") || "",
            },
          };
        }
      } catch (error) {
        agentOperationsJson = null;
        console.error("Error extracting agent operations:", error);
      }
    }

    try {
      const priceStr = searchParams.get("price");
      if (priceStr) {
        priceJson = JSON.parse(priceStr);
      }
    } catch (error) {
      console.error("Error parsing price JSON:", error);
    }

    const flightData = {
      itinerary: searchParams.get("itinerary") || "",
      travelClass: searchParams.get("travelClass") || "",
      departureCode: searchParams.get("departureCode") || "",
      departureCity: searchParams.get("departureCity") || "",
      departureCountry: searchParams.get("departureCountry") || "",
      departureAirport: searchParams.get("departureAirport") || "",
      arrivalCode: searchParams.get("arrivalCode") || "",
      arrivalCity: searchParams.get("arrivalCity") || "",
      arrivalCountry: searchParams.get("arrivalCountry") || "",
      arrivalAirport: searchParams.get("arrivalAirport") || "",
      departureCarrier: searchParams.get("departureCarrier") || "",
      departureFlightNumber: searchParams.get("departureFlightNumber") || "",
      returnCarrier: searchParams.get("returnCarrier") || "",
      returnFlightNumber: searchParams.get("returnFlightNumber") || "",
      flightNumber: searchParams.get("flightNumber") || "", // Added flightNumber property
      departureTime: searchParams.get("departureTime") || "",
      arrivalTime: searchParams.get("arrivalTime") || "",
      stops: parseInt(searchParams.get("stops") || "0"),
      adults: parseInt(searchParams.get("adults") || "0"),
      children: parseInt(searchParams.get("children") || "0"),
      infants: parseInt(searchParams.get("infants") || "0"),
      travelers_count: parseInt(searchParams.get("travelers_count") || "0"),
      agentOperations: agentOperationsJson as AgentOperations | null, // Fixed type mismatch
      price: priceJson,
      departureFlightDate: searchParams.get("flightDate") || "",
      arrivalFlightDate: searchParams.get("returnDate") || "",
      duration: searchParams.get("duration") || "",
    };

    setFlightDetails(flightData);

    // Extract traveler details from URL parameters
    const travelersCount = parseInt(searchParams.get("travelers_count") || "0");
    const extractedTravelers = [];

    for (let i = 0; i < travelersCount; i++) {
      const traveler = {
        title: searchParams.get(`traveler_${i}_title`) || "",
        gender: searchParams.get(`traveler_${i}_gender`) || "",
        firstName: searchParams.get(`traveler_${i}_firstName`) || "",
        lastName: searchParams.get(`traveler_${i}_lastName`) || "",
        email: searchParams.get(`traveler_${i}_email`) || "",
        phoneNumber: searchParams.get(`traveler_${i}_phoneNumber`) || "",
        dateOfBirth: searchParams.get(`traveler_${i}_dateOfBirth`) || "",
        nationality: searchParams.get(`traveler_${i}_nationality`) || "",
        passportNumber: searchParams.get(`traveler_${i}_passportNumber`) || "",
        issuingCountry:
          searchParams.get(`traveler_${i}_issuingCountry`) || "",
        passportExpiry: searchParams.get(`traveler_${i}_passportExpiry`) || "",
      };
      extractedTravelers.push(traveler);
    }
    setTravelers(extractedTravelers);
  }, [searchParams, agentOperations, user]);

  // Format date for display
  const formatDate = (dateString: string): string => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "";
    }
  };

  // Format traveler count with singular/plural form
  const formatTravelerCount = (count: number, type: string): string => {
    if (!count) return "";
    try {
      const singular = type.endsWith("s") ? type.slice(0, -1) : type;
      return `${count} ${count === 1 ? singular : type}`;
    } catch (error) {
      console.error("Error formatting traveler count:", error);
      return `${count} ${type}`;
    }
  };

  // // Show booking result if present
  // if (bookingResult) {
  //   return (
  //     <div className="mx-auto rounded-lg shadow-xl p-8 bg-white dark:bg-gray-700">
  //       <h2 className="text-2xl font-bold mb-4 text-gray-700 dark:text-white">
  //         Booking Result
  //       </h2>
  //       <pre className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 text-sm text-gray-800 dark:text-gray-200 overflow-x-auto">
  //         {JSON.stringify(bookingResult, null, 2)}
  //       </pre>
  //     </div>
  //   );
  // }

  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );

  // Now you can access the data
  const { bookingResult, travelerData, ticket, fullTicket, passengerCounts } =
    bookingConfirmation;

  const flightClass = bookingConfirmation?.fullTicket?.flightClasses?.[0];

  const carryOnAllowed = flightClass?.carryOnAllowed || 0;
  const carryOnWeight = flightClass?.carryOnWeight || 0;
  const checkedAllowed = flightClass?.checkedAllowed || 0;
  const checkedWeight = flightClass?.checkedWeight || 0;
  // Format baggage display (if needed for future use)
  const baggageDisplay = [];
  if (checkedAllowed > 0) {
    baggageDisplay.push(`${checkedAllowed} x ${checkedWeight} kg Checked`);
  }
  if (carryOnAllowed > 0) {
    baggageDisplay.push(`${carryOnAllowed} x ${carryOnWeight} kg Carry-on`);
  }

  return (
    <div className="mx-auto rounded-lg shadow-xl">
      {/* Detailed Booking Summary */}
      <section className="bg-gray-100 dark:bg-gray-700 rounded-xl border-0 shadow-lg mb-6">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="font-bold leading-8 text-2xl tracking-tight text-gray-700 dark:text-white ">
            Booking Summary
          </h3>
        </div>
        <div className="p-6 pt-0">
          <div className="space-y-6">
            <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
              <div className="flex items-center justify-between mb-4">
                <h3 className="dark:text-white text-gray-700 font-semibold bg-red-500/20 px-3 py-1 rounded-lg">
                  OUTBOUND
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {formatDate(flightDetails.departureFlightDate)}
                </p>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center">
                    <span className="text-xl font-semibold text-gray-700 dark:text-white">
                      {flightDetails.departureCode}
                    </span>
                    <span className="mx-3 text-gray-400">→</span>
                    <span className="text-xl font-semibold text-gray-700 dark:text-white">
                      {flightDetails.arrivalCode}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {flightDetails.stops === 0
                      ? "Direct Flight"
                      : `${flightDetails.stops} Stops`}{" "}
                    • {flightDetails.duration}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold text-gray-700 dark:text-white">
                    {flightDetails.departureCarrier} (
                    {flightDetails.departureFlightNumber})
                  </p>
                </div>
              </div>
              <div className="pt-4 mt-2">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <span>
                    Departure: {getFormatTime(flightDetails.departureTime)}
                  </span>
                  <span className="mx-2">•</span>
                  <span>
                    Arrival: {getFormatTime(flightDetails.arrivalTime)}
                  </span>
                </div>
              </div>
            </div>
            {flightDetails.itinerary === "round trip" && (
              <>
                <div className="flex items-center gap-2">
                  <div className="h-px flex-grow bg-gray-400/50 dark:bg-gray-500"></div>
                  <div className="text-gray-500 dark:text-white bg-gray-300 dark:bg-gray-600 rounded-full p-2 h-8 w-8 flex items-center justify-center">
                    <span>↕</span>
                  </div>
                  <div className="h-px flex-grow bg-gray-400/50 dark:bg-gray-500"></div>
                </div>
                <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="dark:text-white text-gray-700 font-semibold bg-blue-500/80 dark:bg-blue-500/20 px-3 py-1 rounded-lg">
                      RETURN
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {formatDate(bookingResult?.returnTicket?.flightDate ?? 
                        ticket?.returnTicket?.flightDate
                      )}
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center">
                        <span className="text-xl font-semibold text-gray-700 dark:text-white">
                          {bookingResult?.returnTicket?.departure?.airportCode ??
                            ticket?.returnTicket?.departure?.airportCode}
                        </span>
                        <span className="mx-3 text-gray-400">→</span>
                        <span className="text-xl font-semibold text-gray-700 dark:text-white">
                          {bookingResult?.returnTicket?.arrival?.airportCode ??
                            ticket?.returnTicket?.arrival?.airportCode}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {bookingResult?.returnTicket?.stops === 0
                          ? "Direct Flight"
                          : `${bookingResult?.returnTicket?.stops} Stops`}{" "}
                        • {bookingResult?.returnTicket?.duration}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-semibold text-gray-700 dark:text-white">
                        {bookingResult?.returnTicket?.segments[0]?.carrier ??
                          ticket?.returnTicket?.segments[0]?.carrier} (
                        {bookingResult?.returnTicket?.segments[0]?.flightNumber ??
                          ticket?.returnTicket?.segments[0]?.flightNumber})
                      </p>
                    </div>
                  </div>
                  <div className="pt-4 mt-2">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <span>
                        Departure: {getFormatTime(
                          bookingResult?.returnTicket?.segments[0]?.departureTime ??
                            ticket?.returnTicket?.segments[0]?.departureTime
                        )}
                      </span>
                      <span className="mx-2">•</span>
                      <span>
                        Arrival: {getFormatTime(
                          bookingResult?.returnTicket?.segments[0]?.arrivalTime ??
                            ticket?.returnTicket?.segments[0]?.arrivalTime
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
            <div className="pt-4">
              <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
                <h3 className="font-medium text-gray-700 dark:text-white">
                  Passenger & Baggage
                </h3>
                <div className="h-px bg-gray-300 dark:bg-gray-500 w-full my-3"></div>
                <section className="flex justify-between items-center">
                  <p className="font-semibold text-gray-700 dark:text-white">
                    {flightDetails.adults +
                      flightDetails.children +
                      flightDetails.infants}{" "}
                    {flightDetails.adults +
                      flightDetails.children +
                      flightDetails.infants >
                    1
                      ? "Travelers"
                      : "Traveler"}{" "}
                    • {formatTravelerCount(flightDetails.adults, "Adults")}
                    {flightDetails.children
                      ? " • " +
                        formatTravelerCount(flightDetails.children, "Children")
                      : ""}
                    {flightDetails.infants
                      ? " • " +
                        formatTravelerCount(flightDetails.infants, "Infants")
                      : ""}
                  </p>
                  <section className="flex justify-between items-center gap-2">
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {flightDetails.travelClass}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {baggageDisplay.length > 0 ? (
                        baggageDisplay.map((item, index) => (
                          <span key={index}>
                            {" • "}
                            {item}
                          </span>
                        ))
                      ) : (
                        <span>• No baggage included</span>
                      )}
                    </p>
                  </section>
                </section>
              </div>
            </div>

            {/* Traveler Information */}
            {/* <section className="mt-4 pt-4 border-t border-gray-400/50 dark:border-gray-500">
              <h3 className="font-semibold text-gray-700 dark:text-white mb-2">
                Traveler Information
              </h3>
              {travelers.map((traveler, index) => (
                <div
                  key={index}
                  className="mb-3 p-3 bg-gray-50 dark:bg-gray-600 rounded-lg"
                >
                  <p className="font-medium text-gray-700 dark:text-white">
                    {traveler.title} {traveler.firstName} {traveler.lastName}
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2 text-sm text-gray-600 dark:text-gray-300">
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Email:
                      </span>{" "}
                      {traveler.email}
                    </p>
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Phone:
                      </span>{" "}
                      {traveler.phoneNumber}
                    </p>
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Date of Birth:
                      </span>{" "}
                      {formatDate(traveler.dateOfBirth)}
                    </p>
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Nationality:
                      </span>{" "}
                      {traveler.nationality}
                    </p>
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Passport:
                      </span>
                      {traveler.passportNumber}
                    </p>
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Issued by:
                      </span>{" "}
                      {traveler.passportIssuingCountry}
                    </p>
                    <p>
                      <span className="dark:text-white font-semibold w-16">
                        Expires:
                      </span>{" "}
                      {formatDate(traveler.passportExpiry)}
                    </p>
                  </div>
                </div>
              ))}
            </section> */}
          </div>
        </div>
      </section>

      {/* Agent Operations */}
      {bookingType === "INTERNAL" && showAgentOperations && (
        <section className="bg-gray-100 dark:bg-gray-700 rounded-xl border-0 shadow-lg mb-6">
          <div className="flex flex-col space-y-1.5 p-6">
            <h3 className="font-bold leading-8 text-2xl tracking-tight text-gray-700 dark:text-white">
              User Operations
            </h3>
          </div>
          <div className="p-6 pt-0">
            <div className="space-y-4">
              <div className="grid gap-4">
                <section className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Request ID */}
                  <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">
                      Request ID
                    </p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-white">
                      Pending
                    </p>
                  </div>
                  {/* Agent */}
                  <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
                    <p className="text-sm text-gray-600 dark:text-gray-300  mb-1">
                      Employee Name
                    </p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-white">
                      {safeAgentOperations?.agent?.name || "Pending"}
                    </p>
                  </div>
                </section>
                <section className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Booking Source */}
                  <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
                    <p className="text-sm text-gray-600 dark:text-gray-300  mb-1">
                      Booking Source
                    </p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-white">
                      {bookingType === "INTERNAL" ? "Internal" : "Third-Party"}
                    </p>
                  </div>
                  {/* Agency */}
                  <div className="p-4 rounded-lg bg-white dark:bg-gray-600">
                    <p className="text-sm text-gray-600 dark:text-gray-300  mb-1">
                      Seller Agency
                    </p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-white">
                      {safeAgentOperations?.agent?.agency || "Not available"}
                    </p>
                  </div>
                </section>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Select Booking Action */}
      <section className="bg-gray-100 dark:bg-gray-700 rounded-xl border-0 shadow-lg mb-6">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="font-bold leading-8 text-2xl tracking-tight text-gray-700 dark:text-white">
            {bookingType === "INTERNAL"
              ? "Select Booking Action"
              : "Select Payment Method"}
          </h3>
        </div>
        <div className="p-6 pt-0">
          <div className="space-y-4">
            <button
              className={`bg-gray-50 dark:bg-gray-600 w-full p-4 rounded-lg transition-all duration-300 border-none ${
                quickHold ? "ring-2 ring-blue-500" : ""
              }`}
              onClick={() => {
                if (!quickHold) {
                  setQuickHold(true);
                  setSubmitBooking(false);
                }
              }}
            >
              <div className="flex flex-col">
                <section className="flex items-center">
                  <div className="h-12 w-12 bg-blue-500/10 rounded-full flex items-center justify-center mr-4">
                    {bookingType === "INTERNAL" ? (
                      <CircleAlert className="h-6 w-6 text-blue-500" />
                    ) : (
                      <CreditCard className="h-6 w-6 text-blue-500" />
                    )}
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-700 dark:text-white">
                      {bookingType === "INTERNAL"
                        ? "Quick Hold"
                        : "Credit or Debit Card"}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {bookingType === "INTERNAL"
                        ? "Temporarily block seat for 1 hour"
                        : "Secure payment with credit card"}
                    </p>
                  </div>
                </section>
                {/* toggle quick hold button */}
                {quickHold && (
                  <section>
                    <div className="h-px w-full bg-gray-300 dark:bg-gray-500 my-3"></div>
                    <div className="bg-gray-400/10 rounded-xl p-4">
                      <p className="text-sm text-gray-700 dark:text-white font-medium">
                        <span className="block font-semibold mb-1">
                          {bookingType === "INTERNAL"
                            ? "Quick Hold Details"
                            : "Coming Soon!"}
                        </span>
                        {bookingType === "INTERNAL"
                          ? `By using quick hold, your seat will be held for 1 hour
                        without payment. You must complete payment within this
                        time or your reservation will be canceled automatically.`
                          : `Credit card payments are on the way! For now, you can use your wallet balance to complete your booking. Thanks for your patience!`}
                      </p>
                    </div>
                  </section>
                )}
              </div>
            </button>
            <button
              className={`bg-gray-50 dark:bg-gray-600 w-full p-4 rounded-lg transition-all duration-300 border-none ${
                submitBooking ? "ring-2 ring-red-500" : ""
              }`}
              onClick={() => {
                if (!submitBooking) {
                  setSubmitBooking(true);
                  setQuickHold(false);
                }
              }}
            >
              <div className="flex flex-col">
                <section className="flex items-center">
                  <div className="h-12 w-12 bg-red-500/10 rounded-full flex items-center justify-center mr-4">
                    {bookingType === "INTERNAL" ? (
                      <CreditCard className="h-6 w-6 text-red-500" />
                    ) : (
                      <Wallet className="h-6 w-6 text-red-500" />
                    )}
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-700 dark:text-white">
                      {bookingType === "INTERNAL"
                        ? "Reserve Seat"
                        : "Wallet Balance"}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {bookingType === "INTERNAL"
                        ? "Secure seat and complete payment"
                        : "Pay using your wallet balance"}
                    </p>
                  </div>
                </section>
                {/* toggle submit booking button */}
                {bookingType === "INTERNAL" && submitBooking && (
                  <section>
                    <div className="h-px w-full bg-gray-300 dark:bg-gray-500 my-3"></div>
                    <div className="bg-gray-400/10 rounded-xl p-4">
                      <p className="text-sm text-gray-700 dark:text-white font-medium text-center">
                        <span className="block font-semibold mb-1">
                          Submission Details
                        </span>
                        By submitting this booking, payment information will be
                        required. The booking will be finalized after Booking
                        Source management approval.
                      </p>
                    </div>
                  </section>
                )}
                {bookingType === "THIRD_PARTY" && submitBooking && (
                  <div className="mt-4 pt-4 border-t border-gray-300 dark:border-gray-500">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm dark:text-gray-300 text-gray-700">
                          Available Balance
                        </span>
                        <span className="text-2xl font-semibold dark:text-white text-gray-700">
                          {availableBalance.toFixed(2)} {currency}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm dark:text-gray-300 text-gray-700">
                          Payment Amount
                        </span>
                        <span className="text-lg font-semibold dark:text-white text-gray-700">
                          {paymentAmount.toFixed(2)} {currency}
                        </span>
                      </div>
                      <div
                        // onClick={onTopUp}
                        className="mt-3 w-full py-2 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200 flex items-center justify-center"
                      >
                        Top Up Balance
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </button>
          </div>
        </div>
      </section>
      {/* Banner (for third-party booking) */}
      {bookingType === "THIRD_PARTY" && (
        <section className="border-0 text-sm shadow-lg bg-gray-100 dark:bg-gray-700 rounded-lg p-4 flex flex-col md:flex-row items-start justify-start gap-3">
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Your booking and payment information is encrypted and secure. We use
            industry-standard security measures to protect your data.
          </p>
        </section>
      )}
    </div>
  );
}
