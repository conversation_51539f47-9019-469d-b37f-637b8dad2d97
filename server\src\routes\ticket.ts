import { Router } from "express";
const router = Router();

import userAuth from "../middlewares/userAuth";
import { checkTeamMemberAccess } from "../middlewares/teamAuth";
import { checkTicketAccess } from "../middlewares/ticketAccess";
import {
  getAllTickets,
  getSingleTicket,
  searchTickets,
} from "../controllers/getTicketController";
import {
  createTicket,
  updateTicket,
} from "../controllers/userCreateTicketController";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

// Middleware to check either user or team member auth
const authMiddleware = [userAuth, checkTeamMemberAccess];

// router.use(enterpriseApiLimiter);

// filter and search
/**
 * @openapi
 * /ticket/search:
 *   post:
 *     tags:
 *       - Ticket
 *     summary: Search for tickets
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: List of tickets
 */
router.post("/search", searchTickets);
// router.post("/search", userAuth, searchTickets);

// get a ticket/tickets
/**
 * @openapi
 * /ticket:
 *   get:
 *     tags:
 *       - Ticket
 *     summary: Get all tickets
 *     responses:
 *       200:
 *         description: List of all tickets
 *
 * /ticket/{ticketId}:
 *   get:
 *     tags:
 *       - Ticket
 *     summary: Get single ticket
 *     parameters:
 *       - in: path
 *         name: ticketId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Ticket details
 */
router.get("/", getAllTickets);
router.get("/:ticketId", getSingleTicket);
// router.get("/", userAuth, getAllTickets);
// router.get("/:ticketId", userAuth, getSingleTicket);

// create and update a ticket
// router.post("/new", createTicket);
/**
 * @openapi
 * /ticket/{ticketId}:
 *   put:
 *     tags:
 *       - Ticket
 *     summary: Update ticket
 *     parameters:
 *       - in: path
 *         name: ticketId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Ticket updated
 *
 * /ticket/new:
 *   post:
 *     tags:
 *       - Ticket
 *     summary: Create new ticket
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Ticket created
 */
// Apply userAuth middleware to all routes that need authentication
router.put("/:ticketId", userAuth, updateTicket);
router.post("/new", userAuth, createTicket);

export default router;
