import { ROLES } from "@/utils/constants/agencyConstants";
import { SearchAndFiltersProps } from "@/utils/types/agencyTypes";
import { Check, Plus, Search, SlidersHorizontal } from "lucide-react";
import { useEffect, useRef, useState } from "react";

// Component for the search and filter section
export const SearchAndFilters = ({
  selectedRoles,
  onRoleToggle,
  onAddUser,
  searchQuery,
  setSearchQuery,
}: SearchAndFiltersProps) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const filterRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target as Node)
      ) {
        setIsFilterOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className="flex flex-col md:flex-row md:space-x-4 gap-y-4 md:gap-y-0 space-y-4 md:space-y-0">
      {/* Search Input */}
      <div className="relative w-full sm:w-auto flex items-center">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        </div>
        <input
          type="text"
          value={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search by name or email..."
          className="block w-64 pl-10 pr-4 py-2 bg-gray-200 dark:bg-gray-600 rounded-lg border-0 focus:ring-2 focus:ring-red-500 focus:border-transparent text-gray-600 dark:text-gray-200 placeholder-gray-400"
        />
      </div>

      {/* Filters and Add Agent Button */}
      <div
        className="relative flex flex-col-reverse gap-2 xl:flex-row xl:gap-4"
        ref={filterRef}
      >
        {/* Filters Button */}
        <button
          className="flex items-center px-4 py-2 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400/70 dark:hover:bg-gray-600/70 rounded-lg transition-colors duration-150 text-gray-600 dark:text-gray-200"
          onClick={() => setIsFilterOpen(!isFilterOpen)}
        >
          <SlidersHorizontal size={18} className="mr-2" />
          Filters
          {selectedRoles.length >= 0 && (
            <span className="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {selectedRoles.length}
            </span>
          )}
        </button>

        {/* Dropdown for Filters */}
        {isFilterOpen && (
          <div className="absolute left-0 mt-12 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-50 border border-gray-300 dark:border-gray-600/50">
            <div className="p-0 divide-y divide-gray-200 dark:divide-gray-700">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 px-3 py-2 border border-gray-300 dark:border-gray-500 rounded-t-lg">
                Filter by access
              </div>
              {ROLES.map((role: string) => (
                <button
                  key={role as string}
                  onClick={() => onRoleToggle(role)}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center justify-between group capitalize"
                >
                  <span
                    className={
                      selectedRoles.includes(role)
                        ? "dark:text-white"
                        : "dark:text-gray-300"
                    }
                  >
                    {role}
                  </span>
                  {selectedRoles.includes(role) && (
                    <Check size={16} className="text-red-500" />
                  )}
                </button>
              ))}
              {selectedRoles.length > 0 && (
                <button
                  onClick={() => onRoleToggle(null)}
                  className="w-full px-3 py-2 mt-2 text-sm text-red-500 hover:text-red-600 dark:hover:text-red-400 border-t border-gray-600"
                >
                  Reset Filterss
                </button>
              )}
            </div>
          </div>
        )}

        {/* Add Agent Button */}
        <button
          onClick={onAddUser}
          className="flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 rounded-lg transition-colors duration-150 text-white font-medium"
        >
          <Plus className="w-5 h-5 mr-2" />
          Add Employee
        </button>
      </div>
    </div>
  );
};
