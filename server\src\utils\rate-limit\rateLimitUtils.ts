import crypto from "crypto";
import { AuthRequest } from "../definitions";
import { RateLimitConfig, RateLimitTier } from "../types/rate-limit";
import { PrismaRateLimitStore } from "./postgres-store";
import { prisma } from "../../prisma";
const FIFTEEN_MINUTES = 15 * 60 * 1000;
const isTestMode = true;

/**
 * Get a unique client identifier for rate limiting. This identifier takes into
 * account the client's IP address, user agent, and user ID (if the request is
 * authenticated). The identifier is a SHA-256 hash of a JSON object containing
 * these values. The user agent is truncated to the first 100 characters to limit
 * variability.
 *
 * @param req - The Express request object.
 * @returns A string client identifier.
 */
export const getClientIdentifier = (req: AuthRequest): string => {
  // Check if we already computed the identifier for this request
  if ((req as any)._rateLimit?.identifier) {
    return (req as any)._rateLimit.identifier;
  }
  const identifier: {
    ip: string | undefined;
    userAgent: string;
    userId?: string;
  } = {
    ip: req.ip || "",
    userAgent: (req.headers["user-agent"] || "").slice(0, 100).trim(), // Limit variability
    userId: req.user?.id || "",
  };

  // Sort keys to ensure consistent object structure
  const sortedIdentifier = Object.keys(identifier)
    .sort()
    .reduce<typeof identifier>(
      (obj, key) => {
        if (key in identifier) {
          obj[key as keyof typeof identifier] =
            identifier[key as keyof typeof identifier] || "";
        }
        return obj;
      },
      { ip: undefined, userAgent: "", userId: "" }
    );

  const hash = crypto
    .createHash("sha256")
    .update(JSON.stringify(sortedIdentifier))
    .digest("hex");

  // Store the computed identifier on the request object
  (req as any)._rateLimit = {
    identifier: hash,
  };

  return hash;
};

/**
 * Returns the rate limit configuration for a given tier
 * @param {RateLimitTier} tier Rate limit tier
 * @returns {RateLimitConfig} The rate limit configuration
 */
export const getRateLimitConfig = (tier: RateLimitTier): RateLimitConfig => {
  const rateLimitStore = new PrismaRateLimitStore(prisma, FIFTEEN_MINUTES);
  const limits = {
    free: {
      windowMs: FIFTEEN_MINUTES,
      max: isTestMode ? 500 : 500,
      burstInterval: 1000,
      burstMax: 10,
      store: rateLimitStore,
    },
    premium: {
      windowMs: FIFTEEN_MINUTES,
      max: isTestMode ? 10000 : 10000,
      burstInterval: 1000,
      burstMax: 100,
      store: rateLimitStore,
    },
    enterprise: {
      windowMs: FIFTEEN_MINUTES,
      max: isTestMode ? 30000 : 30000,
      burstInterval: 1000,
      burstMax: 100,
      store: rateLimitStore,
    },
  };

  return limits[tier];
};
