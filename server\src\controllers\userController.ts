import { Response } from "express";
import { AuthRequest } from "../utils/definitions";
import checkUserAuth from "../utils/authorization/checkUserAuth";
import { prisma } from "../prisma";

import { AccountStatus, User } from "@prisma/client";
import { capitalize } from "../utils/functions";
import { userUpdateValidation } from "../utils/validators/userValidation";
import jwt from "jsonwebtoken";
import <PERSON><PERSON> from "joi";
import bcrypt from "bcrypt";
import { clearSessionExpiration } from "../utils/schedule/trackSessionExpiration";
import {
  isTestMode,
  REACTIVATION_PERIOD,
} from "../utils/constants/timeVariables";
import { getIO } from "../socket";
import { handleAgencyDeactivation } from "../utils/triggers/handleAgencyDeactivation";
import { validateUsers } from "../utils/validators/teamValidation";
import { updateAgencyTicketsToHold } from "../utils/triggers/handleAgencyTickets";
import { emailDeactivated } from "../utils/email/emailDeactivated";
import passwordUpdateSuccess from "../utils/email/passwordUpdateSuccess";
import emailUpdateSuccess from "../utils/email/emailUpdateSuccess";
import { emailVerifyNewEmail } from "../utils/email/emailVerifyNewEmail";

const userSelector: any = {
  address: true,
};

/**
 * Retrieves user information for the authenticated user.
 *
 * @param req - The request object containing the user's authorization token.
 * @param res - The response object to send the result.
 * @returns A JSON response with the success status and the retrieved user information.
 * The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: User,
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const getUserInfo = async (req: AuthRequest, res: Response) => {
  try {
    // Check if this is a team member or regular user
    const account = await checkUserAuth(req, res, req.accountType as any);

    // If checkUserAuth already sent a response, don't continue
    if (res.headersSent) {
      return;
    }

    // If account is not found but we have userId, try to get it directly from the database
    // This helps with timing issues after account reactivation
    if (!account && req.userId && req.accountType === "agencyOwner") {
      const directAccountCheck = await prisma.user.findUnique({
        where: { id: req.userId },
      });

      if (
        directAccountCheck &&
        directAccountCheck.accountStatus === "accepted"
      ) {
        // Use this account instead
        let responseData = await prisma.user.findUnique({
          where: { id: directAccountCheck.id },
          include: userSelector,
        });

        await prisma.user.update({
          where: { id: directAccountCheck.id },
          data: {
            lastLogin: new Date(),
          },
        });

        return res.status(200).json({ success: true, results: responseData });
      }
    }

    if (!account) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    let responseData;

    if (req.accountType === "masterUser") {
      // For team members, return the team member info
      const teamMemberInfo = await prisma.teamMember.findUnique({
        where: { id: account.id },
      });
      responseData = teamMemberInfo;
    } else if (req.accountType === "agencyUser") {
      // For agency agents, first try to get info from AgencyAgent
      let agencyAgentInfo = await prisma.agencyAgent.findUnique({
        where: { id: account.id },
      });

      // If not found in AgencyAgent, try User table since our auth middleware allows this
      if (!agencyAgentInfo) {
        const userInfo = await prisma.user.findUnique({
          where: { id: account.id },
          include: userSelector,
        });

        // Only set as responseData if the user has role 'agency'
        if (userInfo && userInfo.role === "agency") {
          responseData = userInfo;

          // Update last login
          await prisma.user.update({
            where: { id: account.id },
            data: {
              lastLogin: new Date(),
            },
          });
        }
      } else {
        responseData = agencyAgentInfo;
      }
    } else {
      // For regular users, include address and update last login
      const userInfo = await prisma.user.findUnique({
        where: { id: account.id },
        include: userSelector,
      });

      await prisma.user.update({
        where: { id: account.id },
        data: {
          lastLogin: new Date(),
        },
      });
      responseData = userInfo;
    }

    if (!responseData) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    return res.status(200).json({ success: true, results: responseData });
  } catch (error) {
    const err = error as Error;
    console.error("Get User Info error", {
      message: err.message,
      stack: err.stack,
    });

    if (!res.headersSent) {
      return res.status(500).json({
        success: false,
        message: "Failed to get user info. Please try again later.",
      });
    }
  }
};

/**
 * Updates user information.
 *
 * @param req - The request object containing the user's authorization token and the updated user information.
 * @param res - The response object to send the result.
 * @returns A JSON response with the success status and the updated user information.
 * The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: User,
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const updateUserInfo = async (req: AuthRequest, res: Response) => {
  // Define the allowed keys for user update
  const input = req.body;

  try {
    // validate the first and last name
    const validatedData = validateUsers({
      firstName: input.firstName,
      lastName: input.lastName,
    });

    if (input?.phoneNumber[0] !== "+")
      input.phoneNumber = "+" + input.phoneNumber.toString();
    // Authorize the user
    const user = await checkUserAuth(req, res, req.accountType as any);

    // Find the user info
    const userInfo = await prisma.user.findUnique({
      where: { id: user?.id },
      include: userSelector,
    });

    // If there is no user info
    if (!userInfo) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    // Validate the inputs
    const { error } = userUpdateValidation.validate(input, {
      abortEarly: false,
    });

    // Return a list of errors if validation fails
    if (error) {
      const errorDetails = error.details.reduce(
        (acc, detail) => {
          acc[detail.path.join(".")] = detail.message;
          return acc;
        },
        {} as Record<string, string>
      );

      return res
        .status(400)
        .json({ success: false, validationError: errorDetails });
    }
    let validationError = { ...validatedData.validationErrors };

    // Return if there are any validation errors
    if (Object.keys(validationError).length > 0) {
      return res.status(400).json({
        success: false,
        validationError,
      });
    }
    // Remove addressId from input before updating
    const { addressId, ...updateData } = input;

    // Update the user info
    const updatedUserInfo = await prisma.user.update({
      where: { id: user?.id },
      data: {
        // Update user fields
        refId: input.refId?.trim().toLowerCase(),
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        username: input.username?.trim().toLowerCase(),
        agencyName: input.agencyName ? capitalize(input.agencyName) : null,
        email: input.email?.trim().toLowerCase(),
        phoneNumber: input.phoneNumber?.trim(),
        nationality: input.nationality?.trim().toLowerCase(),
        dateOfBirth: input.dateOfBirth?.trim(),
        gender: input.gender?.trim().toLowerCase(),
        logo: input.logo?.trim(),
        role: input.role?.trim().toLowerCase(),
        roleType: input.roleType?.trim().toLowerCase(),
        iataNo: input.iataNo?.trim(),
        commercialOperationNo: input.commercialOperationNo?.trim(),
        website: input.website?.toLowerCase().trim(),
        deactivationDate: input.deactivationDate || null,
        subscriptionStatus: input.subscriptionStatus,
        lastLogin: input.lastLogin || null,
        address: {
          update: {
            // Update address fields
            country: capitalize(input.address?.country),
            city: capitalize(input.address?.city),
            street: input.address?.street?.trim(),
          },
        },
      },
      include: userSelector,
    });

    // Return the updated user info
    return res.status(200).json({ success: true, results: updatedUserInfo });
  } catch (error) {
    const err = error as Error;
    console.error("Update User Info error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update user info. Please try again later.",
    });
  }
};

/**
 * Soft deletes a single user by deactivating their account.
 *
 * @remarks
 * Only master users can access this endpoint.
 * The user's account status is changed to deactivated and a deactivation date is set.
 * Users can reactivate within 30 days by logging in.
 * After 30 days, admin assistance is required.
 * After 180 days, the account is permanently deleted.
 *
 * @param req - The authenticated request object.
 * @param res - The response object.
 * @returns The response with the deactivated user or an error message.
 */
export const softDeleteSingleUser = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  try {
    // Authorize the user
    const user = await checkUserAuth(req, res, req.accountType as any);
    if (!user) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    // Check if user is a master account
    if (user.role === "master") {
      return res.status(403).json({
        success: false,
        message: "Master accounts cannot be deactivated",
      });
    }

    // Check if the user exists
    const userInfo = await prisma.user.findUnique({
      where: { id: user.id },
    });

    if (!userInfo) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if the user is already deactivated
    if (userInfo.accountStatus === AccountStatus.deactivated) {
      return res.status(400).json({
        success: false,
        message: "User is already deactivated",
      });
    }

    const email = userInfo.email;

    // Update tickets to HOLD
    await updateAgencyTicketsToHold(user.id, AccountStatus.deactivated);

    // Deactivate the user
    const deactivatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        accountStatus: AccountStatus.deactivated,
        deactivationDate: new Date(),
      },
    });

    if (req.user) {
      await emailDeactivated(email, deactivatedUser);
    }

    const io = getIO();
    if (deactivatedUser?.accountStatus === AccountStatus.deactivated) {
      await handleAgencyDeactivation(deactivatedUser.id);
      io.to(deactivatedUser.id).emit("userRequestResponse", {
        accountStatus: deactivatedUser.accountStatus,
        forceLogout: true,
      });
      io.to(user.id).emit("accountStatusChanged", {
        accountStatus: deactivatedUser.accountStatus,
        forceLogout: true,
      });
      io.to(user.id).emit("sessionExpiration", {
        accountStatus: deactivatedUser.accountStatus,
        forceLogout: true,
      });
      clearSessionExpiration(user.id as string);
      // Clear the token cookie
      res.clearCookie("token", {
        httpOnly: true, // Ensure the cookie is only accessible by the web server
        sameSite: "none", // Allow the cookie to be sent in cross-site requests
        secure: true, // Ensure the cookie is sent over HTTPS
      });
    }

    io.to(deactivatedUser.id).emit("userRequestResponse", {
      accountStatus: deactivatedUser.accountStatus,
      forceLogout: true,
    });
    io.to(user.id).emit("accountStatusChanged", {
      accountStatus: deactivatedUser.accountStatus,
      forceLogout: true,
    });
    io.to(user.id).emit("sessionExpiration", {
      accountStatus: deactivatedUser.accountStatus,
      forceLogout: true,
    });
    clearSessionExpiration(user.id as string);
    // Clear the token cookie
    res.clearCookie("token", {
      httpOnly: true, // Ensure the cookie is only accessible by the web server
      sameSite: "none", // Allow the cookie to be sent in cross-site requests
      secure: true, // Ensure the cookie is sent over HTTPS
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: `User has been deactivated. They can reactivate their account within ${isTestMode ? REACTIVATION_PERIOD : REACTIVATION_PERIOD} 
      ${isTestMode ? "minutes" : "days"} by logging in. After that, admin assistance will be required.`,
      results: deactivatedUser,
      forceLogout: true,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Delete User: Soft delete user error", {
      message: err.message,
      stack: err.stack,
    });

    // Return 500 error if something went wrong
    return res.status(500).json({
      success: false,
      message: "Failed to soft delete user. Please try again later.",
    });
  }
};

export const updateUserEmail = async (req: AuthRequest, res: Response) => {
  const { newEmail } = req.body;
  try {
    // Authorize the user
    const user = await checkUserAuth(req, res, req.accountType as any);

    // Validate the inputs
    const schema = Joi.object({
      newEmail: Joi.string().email().required().messages({
        "string.base": "Email must be a string",
        "string.empty": "Email cannot be an empty field",
        "string.email": "Email must be a valid email",
        "any.required": "Please enter your email",
      }),
    });
    const { error } = schema.validate({ newEmail });
    if (error) {
      return res
        .status(400)
        .json({ success: false, validationError: error.message });
    }

    // check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: newEmail?.trim().toLowerCase() },
    });

    if (existingUser) {
      return res
        .status(400)
        .json({ success: false, validationError: "Email already exists" });
    }

    // send email update request
    if (user) {
      sendEmailVerificationToken(user, newEmail);
    }

    return res.status(200).json({
      success: true,
      message: "Update email request sent successfully",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Update User Info error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update user info. Please try again later.",
    });
  }
};

/**
 * Verifies the updated email for a user.
 *
 * @param {AuthRequest} req - The request object containing the user's authorization token and the verification token.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A Promise that resolves when the request is handled.
 */
export const verifyUpdatedEmail = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { token } = req.params;

  try {
    const user = await checkUserAuth(req, res, req.accountType as any);
    if (!user) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    if (!token) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    const secretKey = process.env.SECRET_KEY;
    if (!secretKey) {
      return res
        .status(500)
        .json({ success: false, message: "Secret key not configured" });
    }

    const decoded: any = jwt.verify(token, secretKey);

    if (user.email !== decoded.email) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    // check if newEmail already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: decoded.newEmail?.trim().toLowerCase() },
    });

    if (existingUser) {
      return res
        .status(400)
        .json({ success: false, message: "Email already exists" });
    }

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        email: decoded.newEmail,
        verified: true,
      },
      include: userSelector,
    });

    await emailUpdateSuccess(user.email, {
      firstName: user.firstName,
      lastName: user.lastName,
      newEmail: decoded.newEmail,
    });

    return res.status(200).json({ success: true, results: updatedUser });
  } catch (error) {
    const err = error as Error;
    console.error("Update User Info error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update user info. Please try again later.",
    });
  }
};

// email verification token
function sendEmailVerificationToken(user: any, newEmail: string) {
  // create a token to send the email verification
  const secretKey = process.env.SECRET_KEY;
  let token;
  if (secretKey) {
    token = jwt.sign({ email: user.email, newEmail }, secretKey, {
      expiresIn: "10m",
    });
  }

  // user fullname
  const fullName = user.firstName + " " + user.lastName;

  const link = "/account-hub/verify/";
  // send email with the token
  if (token)
    emailVerifyNewEmail(token, link, {
      fullName: fullName,
      toEmail: newEmail,
    });
}

// update password
export const updateUserPassword = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { currentPassword, newPassword, confirmNewPassword } = req.body;

  try {
    const user = await checkUserAuth(req, res, req.accountType as any);
    if (!user) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    // validate new password
    const schema = Joi.object({
      newPassword: Joi.string()
        .empty()
        .pattern(new RegExp("(?=.*[a-z])")) // at least one lowercase letter
        .pattern(new RegExp("(?=.*[A-Z])")) // at least one uppercase letter
        .pattern(new RegExp("(?=.*[0-9])")) // at least one digit
        .pattern(new RegExp("(?=.*[!@#$%^&*])")) // at least one special character
        .min(8) // minimum length 8
        .max(30) // maximum length 30
        .required()
        .messages({
          "string.empty": "New Password cannot be empty",
          "string.pattern.base":
            "New Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character",
          "string.min":
            "New Please make sure your password is at least 8 characters long",
          "string.max": "New Password must be at most 30 characters long",
          "any.required": "Please enter your password",
        }),
      confirmNewPassword: Joi.string()
        .valid(Joi.ref("newPassword"))
        .required()
        .custom(() => {})
        .messages({
          "any.only": "Password and Confirm Password must match",
          "string.empty": "Confirm Password cannot be empty",
          "any.required": "Please enter your confirm password",
        }),
    });

    const { error } = schema.validate({
      newPassword,
      confirmNewPassword,
    });
    if (error) {
      const validationErrors = error.details.reduce((acc: any, curr: any) => {
        acc[curr.path[0]] = curr.message;
        return acc;
      }, {});

      return res
        .status(400)
        .json({ success: false, validationError: validationErrors });
    }

    // check if old password is correct
    const isPasswordMatch = await bcrypt.compare(
      currentPassword,
      user.hashedPassword
    );
    if (!isPasswordMatch) {
      return res.status(400).json({
        success: false,
        validationError: { currentPassword: "Current password is incorrect" },
      });
    }

    // check if new password matches confirm new password
    if (currentPassword === newPassword) {
      return res.status(400).json({
        success: false,
        validationError: {
          newPassword: "Old password cannot be the same as new password",
        },
      });
    }

    // hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        hashedPassword: hashedPassword,
      },
      include: userSelector,
    });

    await passwordUpdateSuccess(updatedUser.email, updatedUser);

    // if all correct, return success
    return res.status(200).json({
      success: true,
      results: updatedUser,
      message: "Password changed successfully",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Update User Info error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to update user password. Please try again later.",
    });
  }
};

/**
 * Get the credit balance of the owner associated with the current user
 * For team members, returns the master owner's balance
 * For agency agents, returns the agency owner's balance
 * For regular users, returns their own balance
 */
export const getOwnerCreditBalance = async (
  req: AuthRequest,
  res: Response
) => {
  try {
    const userId = req.userId;
    const accountType = req.accountType;

    // Default to the user's own balance
    let ownerBalance = 0;

    if (accountType === "masterUser") {
      // For team members, get the master owner's balance
      const teamMember = await prisma.teamMember.findUnique({
        where: { id: userId },
        include: {
          createdBy: {
            select: { id: true }
          },
          createdByTeamMember: {
            include: {
              createdBy: {
                select: { id: true }
              }
            }
          }
        }
      });
      let creatorId: string | undefined;
      if (teamMember?.createdBy) {
        creatorId = teamMember.createdBy.id;
      }    // If created by another team member, get that member's creator
      else if (teamMember?.createdByTeamMember?.createdBy) {
        creatorId = teamMember.createdByTeamMember.createdBy.id;
      }
      if (creatorId) {
        const masterOwner = await prisma.user.findFirst({
          where: {
            id: creatorId,
            role: "master",
            accountStatus: "accepted",
          },
          select: { creditBalance: true },
        });

        if (masterOwner) {
          ownerBalance = parseFloat(masterOwner.creditBalance.toString());
        }
      }
    } else if (accountType === "agencyUser") {
      // For agency agents, get the agency owner's balance
      const agencyAgent = await prisma.agencyAgent.findUnique({
        where: { id: userId },
        select: { agencyId: true },
      });

      if (agencyAgent?.agencyId) {
        const agencyOwner = await prisma.user.findFirst({
          where: {
            id: agencyAgent.agencyId,
            role: "agency",
            accountStatus: "accepted",
          },
          select: { creditBalance: true },
        });

        if (agencyOwner) {
          ownerBalance = parseFloat(agencyOwner.creditBalance.toString());
        }
      }
    } else {
      // For regular users, get their own balance
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { creditBalance: true },
      });

      if (user) {
        ownerBalance = parseFloat(user.creditBalance.toString());
      }
    }

    // Ensure we're working with a number before formatting
    const formattedBalance = isNaN(ownerBalance)
      ? "0.00"
      : ownerBalance.toFixed(2);
    return res.status(200).json({
      success: true,
      creditBalance: formattedBalance,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Get owner credit balance error:", {
      message: err.message,
      stack: err.stack,
    });

    return res.status(500).json({
      success: false,
      message: "Failed to fetch owner credit balance",
    });
  }
};
