import { Response } from "express";
import { AuthRequest } from "../definitions";
import { prisma } from "../../prisma";
import { User, TeamMember, AgencyAgent } from "@prisma/client";
import { getJwtType } from "./jwtTypeUtils";

/////////////
// this function checks and authorizes if the account exists
////////////

// Function overloads to provide type safety
async function checkUserAuth(
  req: AuthRequest,
  res: Response,
  type: "masterOwner"
): Promise<User | null>;
async function checkUserAuth(
  req: AuthRequest,
  res: Response,
  type: "agencyOwner"
): Promise<User | null>;
async function checkUserAuth(
  req: AuthRequest,
  res: Response,
  type: "masterUser"
): Promise<TeamMember | null>;
async function checkUserAuth(
  req: AuthRequest,
  res: Response,
  type: "agencyUser"
): Promise<User | AgencyAgent | null>;
async function checkUserAuth(
  req: AuthRequest,
  res: Response,
  type: "affiliate"
): Promise<User | null>;

// Implementation
async function checkUserAuth(
  req: AuthRequest,
  res: Response,
  type?:
    | "masterOwner"
    | "agencyOwner"
    | "masterUser"
    | "agencyUser"
    | "affiliate"
): Promise<User | TeamMember | AgencyAgent | null> {
  // Don't send response if headers are already sent
  if (res.headersSent) {
    return null;
  }

  if (!req.userId || !req.accountType) {
    res.status(401).json({ message: "Please login" });
    return null;
  }

  // Map teamMember from JWT to masterUser for authorization
  const effectiveAccountType =
    (req.accountType as string) === "teamMember"
      ? "masterUser"
      : req.accountType;

  // If type is specified, ensure it matches the effective account type
  if (type && type !== effectiveAccountType) {
    res.status(401).json({ message: "Unauthorized access" });
    return null;
  }

  let account: User | TeamMember | AgencyAgent | null = null;

  // First, try to use the user object that was already set by the middleware
  // This ensures we have the most up-to-date user status, especially after reactivation
  if (
    (req.accountType as string) === "masterOwner" ||
    (req.accountType as string) === "agencyOwner" ||
    (req.accountType as string) === "affiliate"
  ) {
    account = req.user;
  } else if (
    (req.accountType as string) === "masterUser" ||
    (req.accountType as string) === "teamMember"
  ) {
    account = req.teamMember || null;
  } else if ((req.accountType as string) === "agencyUser") {
    account = req.agencyAgent || null;
    if (!account) {
      // First try to find as AgencyAgent
      account = (await prisma.agencyAgent.findUnique({
        where: { id: req.userId },
      })) as AgencyAgent | null;

      // If not found as AgencyAgent, try finding in User table
      if (!account) {
        const user = (await prisma.user.findUnique({
          where: { id: req.userId },
        })) as User | null;

        // Check if the user has role 'agency'
        if (user && user.role === "agency") {
          const accountType =
            user.roleType === "agency_owner" ? "agencyOwner" : "agencyUser";
          account = accountType === "agencyOwner" ? user : account;
        }
      }
    }
  } else {
    // Fallback to database query if the account wasn't set in the request
    if (
      (req.accountType as string) === "masterUser" ||
      (req.accountType as string) === "teamMember"
    ) {
      account = (await prisma.teamMember.findUnique({
        where: { id: req.userId },
      })) as TeamMember | null;
    } else {
      account = (await prisma.user.findUnique({
        where: { id: req.userId },
      })) as User | null;
    }
  }

  // Define mapping of logical types to account types
  const typeToAccountTypes: Record<string, string[]> = {
    agencyAgent: ["agencyUser"],
    teamMember: ["masterUser"],
    user: ["masterOwner", "agencyOwner", "affiliate"],
  };

  // Validate that the resolved jwtType matches the session's accountType (allow mapped types)
  const jwtType = getJwtType({
    user: account && "id" in account ? (account as User) : undefined,
    teamMember:
      account && "id" in account ? (account as TeamMember) : undefined,
    agent: account && "refId" in account ? (account as AgencyAgent) : undefined,
  });
  // Check if the jwtType corresponds to the accountType or if accountType is in the allowed mapping
  const validTypes = Object.entries(typeToAccountTypes).find(
    ([_logical, arr]) => arr.includes(jwtType)
  );
  const allowedTypes = validTypes ? validTypes[1] : [jwtType];
  account = (allowedTypes as string[]).includes(req.accountType)
    ? account
    : null;

  // Don't send response, just return null and let the caller handle it
  if (!account) {
    res.status(401).json({ message: "Account not found" });
    return null;
  }

  return account;
}

export default checkUserAuth;
