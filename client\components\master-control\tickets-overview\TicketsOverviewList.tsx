import avatarPlaceholder from "@/public/images/placeholders/profile-placeholder.jpg";
import React from "react";
import Image from "next/image";
import { getFormatDateTable, getFormatTime } from "@/utils/functions/functions";
import Link from "next/link";
import { MasterTicketResultType } from "@/utils/definitions/masterDefinitions";
import { Edit } from "lucide-react";

const getStatusStyle = (status: string) => {
  const baseStyle =
    "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium";
  switch (status.toLowerCase()) {
    case "updated":
      return `${baseStyle} bg-yellow-100 text-yellow-800`;
    case "unavailable":
      return `${baseStyle} bg-red-100 text-red-800`;
    case "available":
      return `${baseStyle} bg-green-100 text-green-800`;
    case "pending":
      return `${baseStyle} bg-blue-100 text-blue-800`;
    case "rejected":
      return `${baseStyle} bg-yellow-100 text-yellow-800`;
    case "hold":
      return `${baseStyle} bg-orange-100 text-orange-800`;
    case "blocked":
      return `${baseStyle} bg-gray-100 text-gray-800`;
    default:
      return `${baseStyle} bg-gray-100 text-gray-800`;
  }
};

const getStatusDot = (status: string) => {
  switch (status.toLowerCase()) {
    case "updated":
      return "bg-yellow-400";
    case "unavailable":
      return "bg-red-400";
    case "available":
      return "bg-green-400";
    case "pending":
      return "bg-blue-400";
    case "rejected":
      return "bg-[#d1b000]";
    case "hold":
      return "bg-orange-500";
    case "blocked":
      return "bg-gray-400";
    default:
      return "bg-gray-400";
  }
};
export default function TicketsOverviewList({
  ticket,
}: {
  ticket: MasterTicketResultType;
}) {
  const agent =
    ticket?.createdBy !== "undefined undefined"
      ? `${ticket?.createdBy}`
      : `${ticket?.owner?.firstName} ${ticket?.owner?.lastName}`;
  return (
    <>
      {/* RefId */}
      <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap ">
        <div className="font-medium text-blue-400">#{ticket.refId}</div>
      </td>

      {/* Agency */}
      <td className="px-2 py-3 whitespace-nowrap">
        <div className="font-medium text-gray-800 dark:text-gray-100 overflow-hidden whitespace-nowrap text-ellipsis">
          {ticket?.owner?.agencyName && ticket?.owner?.agencyName?.length > 20
            ? ticket?.owner?.agencyName?.slice(0, 20) + "..."
            : ticket?.owner?.agencyName}
        </div>
      </td>

      {/* User Name */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">{agent}</div>
      </td>

      {/* Status */}
      <td className="table-list-field">
        <span className={`capitalize ${getStatusStyle(ticket.ticketStatus)}`}>
          <span
            className={`inline-block w-1.5 h-1.5 rounded-full mr-1.5 ${getStatusDot(
              ticket.ticketStatus
            )}`}
          ></span>
          {ticket.ticketStatus}
        </span>
      </td>

      {/* Flight Date */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {getFormatDateTable(ticket.flightDate)}
        </div>
      </td>

      {/* Departure */}

      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {ticket?.departure?.airportCode}
        </div>
      </td>

      {/* Arrival */}

      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {ticket?.arrival?.airportCode}
        </div>
      </td>

      {/* Seats */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          {ticket?.remainingSeats}
        </div>
      </td>

      {/* Issued On */}
      <td className="table-list-field">
        <div className="font-medium text-gray-800 dark:text-gray-100">
          <div>
            {getFormatDateTable(ticket.createdAt as string)}
            {" | "}
            {getFormatTime(ticket.createdAt as string)}
          </div>
        </div>
      </td>

      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
        <div className="flex space-x-2">
          {/* Menu button */}
          <Link
            href={`/master-control/tickets-overview/${ticket.refId}`}
            className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300"
          >
            <Edit size={18} />
          </Link>
        </div>
      </td>
    </>
  );
}
