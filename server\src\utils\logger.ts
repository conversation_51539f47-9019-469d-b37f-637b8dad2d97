/**
 * @module logger
 * Centralized Winston logger instance for robust, production-grade logging.
 * - Uses JSON logs in production for aggregation/monitoring
 * - Uses colorized, human-readable logs in development for debugging
 * - Includes timestamps, error stack traces, and string interpolation
 * - DRY: Single shared instance for all modules
 * - Ready for log aggregation and developer-friendly output
 */

import {
  createLogger,
  format,
  transports,
  Logger,
  transport,
  addColors,
} from "winston";
import { sanitizeLogObject } from "./logger.sanitizer";
// @ts-ignore
import Loggly from "winston-loggly-bulk";

// Determine environment
const isProduction = process.env.NODE_ENV === "production";
const useJsonFormat = process.env.LOG_FORMAT === "json" || isProduction;

// Base formats for all environments
const baseFormat = format.combine(
  format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
  format.errors({ stack: true }),
  format.splat(),
  format((info) => {
    // Sanitize meta objects
    if (info && typeof info === "object") {
      Object.assign(info, sanitizeLogObject(info));
    }
    return info;
  })()
);

// Console transport: JSON in prod, colorized/simple in dev
const consoleTransport = new transports.Console({
  format: useJsonFormat
    ? format.combine(baseFormat, format.json())
    : format.combine(baseFormat, format.colorize(), format.simple()),
});

// File transport: always logs in JSON format for persistence
const fileTransport = new transports.File({
  filename: "app.log",
  format: format.combine(baseFormat, format.json()),
  level: "warn",
  options: { flags: "a" }, // async append
});

/**
 * Winston logger instance
 * - Use for all application logging (info, warn, error, debug, etc.)
 * - Example: logger.info("message", { meta })
 */
const transportsArr: transport[] = [consoleTransport, fileTransport];

// Optionally add Loggly transport if env vars are set
if (process.env.LOGGLY_TOKEN && process.env.LOGGLY_SUBDOMAIN) {
  transportsArr.push(
    new Loggly({
      token: process.env.LOGGLY_TOKEN,
      subdomain: process.env.LOGGLY_SUBDOMAIN,
      tags: [process.env.LOGGLY_TAG || "NodeJS"],
      json: true,
      level: "warn",
    })
  );
}

const logger: Logger = createLogger({
  level: process.env.LOG_LEVEL || "warn",
  exitOnError: false, // Do not exit on handled exceptions
  format: format.combine(
    format.timestamp(),
    format.json(),
    format.errors({ stack: true })
  ),
  defaultMeta: { service: "api-service" },
  transports: [
    ...transportsArr,
    new transports.File({
      filename: "logs/error.log",
      level: "error",
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    new transports.File({
      filename: "logs/combined.log",
      maxsize: 5242880,
      maxFiles: 5,
    }),
  ],
});
addColors({
  error: "red",
  warn: "yellow",
  info: "green",
  http: "magenta",
  debug: "white",
});

// Add error listeners to file transport
fileTransport.on("error", (err) => {
  // Fallback: log to console if file transport fails
  // eslint-disable-next-line no-console
  console.error("[LOGGER] File transport error:", err);
});

// Register process event handlers for full observability
process.on("unhandledRejection", (reason) => {
  logger.error("Unhandled Rejection", {
    reason,
    reasonString: reason ? reason.toString() : "undefined",
    stack: reason instanceof Error ? reason.stack : "no stack",
  });
  // Also print to console for diagnostics
  // eslint-disable-next-line no-console
  console.error(
    "Unhandled Rejection:",
    reason,
    reason instanceof Error ? reason.stack : undefined
  );
});
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception", {
    error,
    errorString: error ? error.toString() : "undefined",
    stack: error instanceof Error ? error.stack : "no stack",
  });
  // Also print to console for diagnostics
  // eslint-disable-next-line no-console
  console.error(
    "Uncaught Exception:",
    error,
    error instanceof Error ? error.stack : undefined
  );
});

export default logger;
