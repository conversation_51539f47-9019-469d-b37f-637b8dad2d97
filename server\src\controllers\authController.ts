import { prisma } from "../prisma";
import { getJwtType } from "../utils/authorization/jwtTypeUtils";
import { Request, Response } from "express";
import {
  loginValidation,
  signupValidation,
} from "../utils/validators/authValidator";
import { capitalize, getRoleType, trimPhoneNumber } from "../utils/functions";
import bcrypt from "bcrypt";
import jwt, { TokenExpiredError } from "jsonwebtoken";
import { AuthRequest } from "../utils/definitions";
import checkUserAuth from "../utils/authorization/checkUserAuth";
import { AccountStatus, RoleType, User } from "@prisma/client";
// import { AccountStatus, RoleType, User } from "../types/prismaEnums";
import Joi from "joi";
import {
  clearSessionExpiration,
  trackSessionExpiration,
} from "../utils/schedule/trackSessionExpiration";
import generateUserRefId from "../utils/generateUserRefId";
import {
  calculateDeactivationDays,
  DELETION_PERIOD,
  isTestMode,
  REACTIVATION_PERIOD,
} from "../utils/constants/timeVariables";
import { handleAgencyAcceptation } from "../utils/triggers/handleAgencyAcceptation";
import { validateUsers } from "../utils/validators/teamValidation";
import { emailResetPassword } from "../utils/email/emailResetPassword";
import { emailVerifyRequest } from "../utils/email/emailVerifyRequest";
import passwordUpdateSuccess from "../utils/email/passwordUpdateSuccess";
import notificationService from "../utils/services/notification.service";

/**
 * Signup endpoint for creating a new user.
 * @param req - The request object containing user data.
 * @param res - The response object to send the response.
 * @returns The response object with the result of the signup process.
 */
export const signup = async (req: Request, res: Response) => {
  // Extract the user data from the request body
  const {
    firstName,
    lastName,
    username,
    email,
    password,
    confirmPassword,
    phoneNumber,
    nationality,
    dateOfBirth,
    gender,
    country,
    city,
    street,
    role,
    userRoles,
    agencyName,
    iataNo,
    commercialOperationNo,
    website,
  } = req.body;

  try {
    // validate the first and last name
    const validatedData = validateUsers({
      firstName,
      lastName,
    });

    // if (Object.keys(validatedData.validationErrors).length > 0) {
    //   return res.status(400).json({
    //     success: false,
    //     validationErrors: validatedData.validationErrors,
    //   });
    // }

    // Add a '+' before the phone number
    if (phoneNumber[0] !== "+")
      req.body.phoneNumber = "+" + phoneNumber.toString();

    // Validate the inputs
    const { error } = signupValidation.validate(req.body, {
      abortEarly: false,
    });

    let validationErrors = { ...validatedData.validationErrors };

    if (error) {
      error.details.forEach((detail) => {
        if (detail.context && detail.context.key) {
          validationErrors[detail.context.key] = detail.message;
        }
      });
    }

    // Return if there are any validation errors
    if (Object.keys(validationErrors).length > 0) {
      return res.status(400).json({
        success: false,
        validationErrors,
      });
    }

    // Check if email already exists
    const emailExist = await prisma.user.findUnique({
      where: { email },
    });
    if (emailExist) {
      return res.status(400).json({
        success: false,
        message: "email already exist",
        validationErrors: { email: "email already exist" },
      });
    }

    // Check if username already exists
    const usernameExist = await prisma.user.findUnique({ where: { username } });
    if (usernameExist) {
      return res.status(400).json({
        success: false,
        message: "username already exist",
        validationErrors: { username: "username already exist" },
      });
    }

    // Check if agency name already exists
    const agencyNameExist = await prisma.user.findUnique({ where: { agencyName } });
    if (agencyNameExist) {
      return res.status(400).json({
        success: false,
        message: "Agency name already in use",
        validationErrors: { agencyName: "Agency name already in use" },
      });
    }

    // Confirm password matches confirmPassword
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "Password doesn't match confirm password",
        validationErrors: {
          confirmPassword: "Password doesn't match confirm password",
        },
      });
    }

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Generate a unique refId (you can use any logic to generate this)
    const refId = await generateUserRefId();

    // Return an error if the reference ID is not available
    if (!refId) {
      return res.status(400).json({
        success: false,
        message: "Couldn't generate a reference ID",
      });
    }

    // Determine the roleType based on the role and userRoles
    const roleType = getRoleType(role);

    // Save user signup in the database
    const user = await prisma.user.create({
      data: {
        refId: refId,
        firstName: capitalize(validatedData.firstName),
        lastName: capitalize(validatedData.lastName),
        username: username?.trim().toLowerCase(),
        email: email?.trim().toLowerCase(),
        hashedPassword,
        phoneNumber: trimPhoneNumber(phoneNumber),
        nationality: nationality?.trim().toLowerCase(),
        dateOfBirth: dateOfBirth?.trim(),
        gender: gender?.trim().toLowerCase(),
        role: role.trim().toLowerCase(),
        roleType: roleType as RoleType,
        agencyName: agencyName ? capitalize(agencyName) : null,
        iataNo: iataNo?.trim(),
        commercialOperationNo: commercialOperationNo?.trim(),
        website: website?.toLowerCase().trim(),
        address: {
          create: {
            country: capitalize(country),
            city: capitalize(city),
            street: street?.trim(),
          },
        },
      },
    });

    // Convert null to undefined for type compatibility
    if (user.refId === null) user.refId = undefined as any;
    if (user.agencyName === null) user.agencyName = undefined as any;
    if (user.dateOfBirth === null) user.dateOfBirth = undefined as any;

    // Send email verification token
    sendEmailValidationToken(user as any);

    let jwtType = getJwtType({ user: user as any });
    createCookieToken(user as any, jwtType as any, res);

    // Send just some user info
    const userInfo = {
      refId: refId,
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      agencyName: user.agencyName,
      role: user.role as any,
      accountStatus: user.accountStatus,
      verified: user.verified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    // Return success
    return res.status(200).json({
      success: true,
      message: "signup successfully",
      results: userInfo,
    });
  } catch (error) {
    const err = error as Error;
    // Log the error and return a 500 response
    console.error("Signup error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to signup. Please try again later.",
    });
  }
};

/**
 * Handles the login request.
 * @param req - The request object containing the user's email and password.
 * @param res - The response object to send the response.
 * @returns The response object with the result of the login process.
 */
export const login = async (req: Request, res: Response) => {
  // Destructure the request body
  const { email, password } = req.body;

  try {
    // Validate the inputs
    const { error } = loginValidation.validate(req.body, {
      abortEarly: false,
    });

    // Return list of errors if validation fails
    if (error) {
      const errorDetails = error.details.reduce(
        (acc, detail) => {
          if (detail.context && detail.context.key) {
            acc[detail.context.key] = detail.message;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      return res
        .status(400)
        .json({ success: false, validationErrors: errorDetails });
    }

    // Trim and clean the input email
    const baseEmail = email.trim().toLowerCase();
    // Check all account types for disabled status with the given email
    const disabledUser = await prisma.user.findFirst({
      where: {
        email: { startsWith: `${baseEmail}_` },
        accountStatus: AccountStatus.disabled,
      },
    });

    if (disabledUser) {
      return res.status(403).json({
        success: false,
        message:
          "Your access has been disabled. Please contact Airvilla Support for assistance in restoring your access.",
      });
    }

    // Verify if the email exists
    let user = await prisma.user.findUnique({
      where: { email: baseEmail },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        // userRoles: true,
        roleType: true,
        agencyName: true,
        logo: true,
        verified: true,
        createdAt: true,
        updatedAt: true,
        email: true,
        hashedPassword: true,
        accountStatus: true,
        deactivationDate: true,
      },
    });

    const agent = await prisma.agencyAgent.findUnique({
      where: { email: baseEmail },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        roleType: true,
        department: true,
        dateOfBirth: true,
        status: true,
        accountStatus: true,
        agencyId: true,
        agencyName: true,
        hashedPassword: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const teamMember = await prisma.teamMember.findUnique({
      where: { email: baseEmail },
    });

    // Return error if email is invalid
    let account = user || teamMember || agent;
    if (!account) {
      return res.status(400).json({
        success: false,
        message: "Incorrect login credentials.",
        validationErrors: { email: "Incorrect login credentials." },
      });
    }

    // Check if user account is rejected or suspended
    if (
      user &&
      (user.accountStatus === "suspended" || user.accountStatus === "rejected")
    ) {
      return res.status(403).json({
        success: false,
        message: `Your access has been ${user.accountStatus}. Please contact Airvilla Support for assistance${user.accountStatus === "rejected" ? "." : " in restoring your access."}`,
      });
    }

    // Check if team member account is inactive or rejected
    if (
      teamMember &&
      (teamMember.status === "inactive" ||
        teamMember.accountStatus === "rejected")
    ) {
      return res.status(403).json({
        success: false,
        message: `Your access has been ${teamMember.accountStatus === "rejected" ? teamMember.accountStatus : "disabled"}. Please contact Airvilla Support for assistance.`,
      });
    }

    // Check if agency agent account is inactive or rejected
    if (
      agent &&
      (agent.status === "inactive" || agent.accountStatus === "rejected")
    ) {
      return res.status(403).json({
        success: false,
        message: `Your access has been disabled. Please contact Airvilla Support for assistance.`,
      });
    }

    // Compare the password with the hashed password in the database
    // Check if the account has a password set
    let hashedPassword: string | null = null;
    if (account === user) {
      if (!user.hashedPassword) {
        return res.status(400).json({
          success: false,
          message:
            "Account does not have a password set. Please use another login method.",
          validationErrors: { email: "Invalid email or password" },
        });
      }
      hashedPassword = user.hashedPassword;
    } else if (account === teamMember) {
      if (!teamMember.password) {
        return res.status(400).json({
          success: false,
          message:
            "Account does not have a password set. Please use another login method.",
          validationErrors: { email: "Invalid email or password" },
        });
      }
      hashedPassword = teamMember.password;
    } else if (account === agent) {
      if (!agent.hashedPassword) {
        return res.status(400).json({
          success: false,
          message:
            "Account does not have a password set. Please use another login method.",
          validationErrors: { email: "Invalid email or password" },
        });
      }
      hashedPassword = agent.hashedPassword;
    }

    // Compare the password with the hashed password
    const comparePassword = await bcrypt.compare(password, hashedPassword!);

    // const comparePassword = await bcrypt.compare(
    //   password,
    //   account === user
    //     ? user.hashedPassword
    //     : account === teamMember
    //       ? teamMember.password
    //       : agent!.hashedPassword
    // );

    // Return error if password is invalid
    if (!comparePassword) {
      return res.status(400).json({
        success: false,
        message: "The password you entered is incorrect",
        validationErrors: { password: "The password you entered is incorrect" },
      });
    }

    // Check if the account is deactivated and handle reactivation windows
    if (
      user &&
      user.accountStatus === AccountStatus.deactivated &&
      user.deactivationDate
    ) {
      // Calculate the number of days since deactivation
      const daysDeactivated = calculateDeactivationDays(user.deactivationDate);
      // Within reactivation period - allow self-service reactivation
      if (daysDeactivated <= REACTIVATION_PERIOD) {
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: {
            accountStatus: AccountStatus.accepted,
            deactivationDate: null,
          },
        });
        handleAgencyAcceptation(updatedUser.id);
        user = updatedUser;

        // Ensure we're using the correct account type for the reactivated user
        // account = user;
        // Force the account type to be 'user' for reactivated accounts
        // const accountType = "user";

        // Determine JWT type using shared utility
        let teamMember = await prisma.teamMember.findUnique({
          where: { email: user.email },
        });
        let agent = await prisma.agencyAgent.findUnique({
          where: { email: user.email },
        });
        let jwtType = getJwtType({
          user: user as any,
          teamMember: teamMember as any,
          agent: agent as any,
        });

        createCookieToken(user as any, jwtType as any, res);
      } else if (daysDeactivated <= DELETION_PERIOD) {
        // Between reactivation and deletion period - require admin assistance
        const errorMessage = `Your account has been deactivated for ${daysDeactivated} ${isTestMode ? "minutes" : "days"}. Contact Airvilla Support to restore access.`;
        return res.status(400).json({
          success: false,
          validationErrors: { email: errorMessage },
        });
      } else {
        // Past deletion period - account should be deleted
        const errorMessage =
          "Your account has been permanently deleted due to prolonged inactivity.";
        return res
          .status(400)
          .json({ success: false, validationErrors: { email: errorMessage } });
      }
    }

    // Update lastLogin only if user is verified and accepted
    if (user && user.verified && user.accountStatus === "accepted") {
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      });
    }

    // Update lastLogin for team member
    if (teamMember) {
      await prisma.teamMember.update({
        where: { id: teamMember.id },
        data: { lastLogin: new Date() },
      });
    }

    // Update lastLogin for agency agent
    if (agent) {
      await prisma.agencyAgent.update({
        where: { id: agent.id },
        data: { lastLogin: new Date() },
      });
    }

    // DRY: Use shared utility for JWT type detection
    let jwtType = getJwtType({
      user: user as any,
      teamMember: teamMember as any,
      agent: agent as any,
    });
    createCookieToken(account as any, jwtType as any, res);

    // Send just some user info
    const userInfo = user
      ? {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          username: user.username,
          agencyName: user.agencyName,
          role: user.role as any,
          // userRoles: user.userRoles,
          roleType: user.roleType,
          logo: user.logo,
          accountStatus: user.accountStatus,
          verified: user.verified,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        }
      : teamMember
        ? {
            id: teamMember.id,
            firstName: teamMember.firstName,
            lastName: teamMember.lastName,
            email: teamMember.email,
            role: teamMember.role,
            department: teamMember.department,
            status: teamMember.status,
            agencyName: teamMember.agencyName,
            teamId: teamMember.teamId,
            lastLogin: teamMember.lastLogin,
            createdAt: teamMember.createdAt,
            updatedAt: teamMember.updatedAt,
            verified: teamMember.verified,
          }
        : agent
          ? {
              id: agent.id,
              firstName: agent.firstName,
              lastName: agent.lastName,
              email: agent.email,
              role: agent.role,
              roleType: agent.roleType,
              department: agent.department,
              dateOfBirth: agent.dateOfBirth,
              status: agent.status,
              agencyName: agent.agencyName,
              agencyId: agent.agencyId,
              createdAt: agent.createdAt,
              updatedAt: agent.updatedAt,
              verified: "verified" in agent ? agent.verified : undefined,
            }
          : (() => {
              return res.status(400).json({
                success: false,
                message: "Agent not found",
              });
            })();

    // Return success
    return res.status(200).json({
      success: true,
      message: "login successfully",
      results: userInfo,
    });
  } catch (error) {
    const err = error as Error;

    // Log the error and return a 500 response
    console.error("Login error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to login. Please try again later.",
    });
  }
};

/**
 * Logs out the user by clearing the token cookie.
 *
 * @param req - The request object containing the user's token.
 * @param res - The response object to send the result.
 * @returns A JSON response with the success status and a message.
 */
export const logout = async (req: AuthRequest, res: Response) => {
  try {
    // Remove the token and expiration from the database
    // const user = await prisma.user.update({
    //   where: { id: req.userId },
    //   data: {
    //     tokenExpiresAt: "",
    //   },
    // });

    // Cancel the session expiration timer
    clearSessionExpiration(req.userId as string);

    // if (user.tokenExpiresAt === "") {
    // Clear the token cookie
    res.clearCookie("token", {
      httpOnly: true, // Ensure the cookie is only accessible by the web server
      sameSite: "none", // Allow the cookie to be sent in cross-site requests
      secure: true, // Ensure the cookie is sent over HTTPS
    });
    // }

    // Return success response
    return res
      .status(200)
      .json({ success: true, message: "Logout successful" });
  } catch (error) {
    const err = error as Error;
    console.error("Logout error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to logout. Please try again later.",
    });
  }
};

/**
 * Handles the email verification request.
 *
 * @param req - The request object containing the verification token.
 * @param res - The response object to send the result.
 * @returns A JSON response with the success status and user information if verified successfully, or an error message.
 */
export const emailVerify = async (req: Request, res: Response) => {
  // Get the verification token from the request parameters
  const { token } = req.params;

  try {
    // Check if the token is provided
    if (!token) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    // Decode the access token
    const secretKey = process.env.SECRET_KEY;

    if (!secretKey) {
      throw new Error("Secret key not configured");
    }

    try {
      const decoded = jwt.verify(token, secretKey);

      // Check if the user already exists and is signed up
      let user;
      if (typeof decoded === "object") {
        user = await prisma.user.findUnique({
          where: { email: decoded.email },
        });
      }

      // If the email is not found
      if (!user) {
        return res
          .status(400)
          .json({ success: false, message: "Email not found" });
      }

      // If user is already verified
      if (user.verified) {
        return res.status(200).json({
          success: true,
          message: "Email already verified",
          results: user,
        });
      }

      // Make the user verified
      const verifiedUser = await prisma.user.update({
        where: { email: user.email },
        data: { verified: true, lastLogin: null },
      });

      // Convert null to undefined for type compatibility
      if (verifiedUser.refId === null) verifiedUser.refId = undefined as any;
      if (verifiedUser.agencyName === null)
        verifiedUser.agencyName = undefined as any;
      if (verifiedUser.dateOfBirth === null)
        verifiedUser.dateOfBirth = undefined as any;

      // Determine JWT type using shared utility
      let teamMember = await prisma.teamMember.findUnique({
        where: { email: user.email },
      });
      let agent = await prisma.agencyAgent.findUnique({
        where: { email: user.email },
      });
      let jwtType = getJwtType({
        user: verifiedUser as any,
        teamMember: teamMember as any,
        agent: agent as any,
      });
      createCookieToken(verifiedUser as any, jwtType as any, res);

      // Prepare user information to be sent in the response
      const userInfo = {
        id: verifiedUser.id,
        email: verifiedUser.email,
        username: verifiedUser.username,
        firstName: verifiedUser.firstName,
        lastName: verifiedUser.lastName,
        agencyName: verifiedUser.agencyName,
        role: verifiedUser.role as any,
        logo: verifiedUser.logo,
        accountStatus: verifiedUser.accountStatus,
        verified: verifiedUser.verified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      // If the user's account status is "pending" (Awaiting Approval), notify Master users
      if (user.accountStatus === AccountStatus.pending) {
        // Send notification to Master users about the new user signup
        const masterUser = await prisma.user.findFirst({
          where: {
            roleType: "master_owner",
          },
        });
        // 3. Emit notification
        await notificationService.createNotification({
          userId: masterUser?.id as string,
          type: "NEW_USER_SIGNUP",
          title: "New User Registration",
          message: `A new user has registered and is awaiting approval.`,
          relatedId: user.id,
          link: `/master/users/${user.id}`,
          priority: 2, // Medium priority
        });
      }

      return res.status(200).json({
        success: true,
        message: "Email verified successfully",
        results: userInfo,
      });
    } catch (tokenError: any) {
      if (tokenError.name === "TokenExpiredError") {
        return res.status(401).json({
          success: false,
          message:
            "Verification link has expired. Please request a new verification email.",
          code: "TOKEN_EXPIRED",
        });
      }
      throw tokenError;
    }
  } catch (error: any) {
    console.error("Email Verify error", error);
    return res.status(500).json({
      success: false,
      message: "Failed to verify email. Please try again later.",
    });
  }
};

/**
 * Sends an email verification to the user.
 *
 * @param req - The request object containing the user authentication information.
 * @param res - The response object to send the result.
 * @returns A JSON response with the success status and a message.
 */
export const sendEmailVerification = async (
  req: AuthRequest,
  res: Response
) => {
  try {
    // Authorize the user
    const user = await checkUserAuth(req, res, req.accountType as any);

    // Check if user is already verified
    if (user && "verified" in user && user.verified) {
      return res
        .status(400)
        .json({ success: false, message: "user is already verified" });
    }

    // If not verified, send the email verification
    if (user) {
      sendEmailValidationToken(user as any);
    }

    // Return a success response
    return res.status(200).json({
      success: true,
      message: "Check your email to verify your account",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Send email verification error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to send the email verification. Please try again later.",
    });
  }
};

/**
 * Send reset password request email.
 *
 * @param req - The request object containing the user's email in the request body.
 * @param res - The response object.
 * @returns The response object.
 */
export const sendResetPassword = async (req: Request, res: Response) => {
  // Extract email from request body
  const { email } = req.body;

  try {
    // Find user with the given email
    let user = await prisma.user.findUnique({
      where: { email },
    });

    // If user not found, return error response
    if (!user) {
      return res
        .status(400)
        .json({ success: false, validationErrors: "Email not found" });
    }

    // Send reset password email to the user
    sendResetPassEmail(user as any);

    // Return success response
    return res.status(200).json({
      success: true,
      message: "Check your email to reset your password",
    });
  } catch (error) {
    // Log and return error response
    const err = error as Error;
    console.error("Send email verification error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message:
        "Failed to send the reset email request. Please try again later.",
    });
  }
};

/**
 * Reset password endpoint.
 *
 * @param req - The request object.
 * @param res - The response object.
 * @returns The response object.
 */
export const resetPassword = async (req: Request, res: Response) => {
  // Extract token and password from request parameters and body
  const { token } = req.params;
  const { password, confirmPassword } = req.body;

  try {
    // Verify token
    const secretKey = process.env.SECRET_KEY;
    let decoded;
    if (secretKey) {
      decoded = jwt.verify(token, secretKey);
    }

    if (!decoded) {
      return res.status(400).json({ success: false, message: "Invalid token" });
    }

    // Find user by email
    let user = await prisma.user.findUnique({
      where: {
        email: (decoded as { email: string }).email,
      },
    });
    if (!user) {
      return res
        .status(400)
        .json({ success: false, message: "Email not found" });
    }

    // Validate new password
    const schema = Joi.object({
      password: Joi.string()
        .empty()
        .pattern(new RegExp("(?=.*[a-z])")) // at least one lowercase letter
        .pattern(new RegExp("(?=.*[A-Z])")) // at least one uppercase letter
        .pattern(new RegExp("(?=.*[0-9])")) // at least one digit
        .pattern(new RegExp("(?=.*[!@#$%^&*])")) // at least one special character
        .min(8) // minimum length 8
        .max(30) // maximum length 30
        .required()
        .messages({
          "string.empty": "Password cannot be empty",
          "string.pattern.base":
            "New Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character",
          "string.min":
            "Please make sure your password is at least 8 characters long",
          "string.max": "Password must be at most 30 characters long",
          "any.required": "Please enter your password",
        }),
      confirmPassword: Joi.string()
        .valid(Joi.ref("password"))
        .required()
        .custom(() => {})
        .messages({
          "any.only": "Password and Confirm Password must match",
          "string.empty": "Confirm Password cannot be empty",
          "any.required": "Please enter your confirm password",
        }),
    });

    // Validate password and confirmPassword
    const { error } = schema.validate({
      password,
      confirmPassword,
    });
    if (error) {
      // Map validation errors to validationErrors object
      const validationErrors = error.details.reduce((acc: any, curr: any) => {
        acc[curr.path[0]] = curr.message;
        return acc;
      }, {});

      return res
        .status(400)
        .json({ success: false, validationErrors: validationErrors });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update user's password
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { hashedPassword: hashedPassword },
    });

    // Convert null to undefined for type compatibility
    if (updatedUser.refId === null) updatedUser.refId = undefined as any;
    if (updatedUser.agencyName === null)
      updatedUser.agencyName = undefined as any;
    if (updatedUser.dateOfBirth === null)
      updatedUser.dateOfBirth = undefined as any;

    // Send password update success email
    await passwordUpdateSuccess(updatedUser.email, updatedUser as any);

    // Return success response
    return res.status(200).json({
      success: true,
      message: "Password updated successfully",
    });
  } catch (error) {
    if (error instanceof TokenExpiredError) {
      return res.status(400).json({
        success: false,
        message:
          "The reset password link has expired. Please request a new one.",
      });
    }

    const err = error as Error;
    console.error("Reset password error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to reset password. Please try again later.",
    });
  }
};

// ###################################
// ########### functions #############
// ###################################

// create token and save it in http-only cookie
async function createCookieToken(
  user: { id: string; role?: string },
  type:
    | "user"
    | "teamMember"
    | "agencyAgent"
    | "masterOwner"
    | "agencyOwner"
    | "masterUser"
    | "agencyUser"
    | "affiliate",
  res: Response
) {
  // generate access token using JWT
  const secretKey = process.env.SECRET_KEY;
  // const token = jwt.sign(
  //   {
  //     id: user.id,
  //     type,
  //   },
  //   process.env.JWT_SECRET!,
  //   { expiresIn: "24h" }
  // );

  // // Set cookie options
  // const cookieOptions = {
  //   expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
  let token;
  if (secretKey) {
    let tokenType = type as string;
    if (type === "teamMember") {
      tokenType = "masterUser";
    } else if (type === "user") {
      if (user.role === "agency" && (user as any).roleType === "agency_owner") {
        tokenType = "agencyOwner";
      } else if (user.role === "master") {
        tokenType = "masterOwner";
      } else {
        tokenType = "affiliate"; // Defaulting user to affiliate, adjust if needed
      }
    } else if (type === "agencyAgent") {
      tokenType = "agencyUser";
    }
    token = jwt.sign({ id: user.id, type: tokenType }, secretKey, {
      expiresIn: "1h",
    });
  }

  const expiredAt = 1000 * 60 * 60; // session expire after one hour

  // save the token in cookie using http-only
  // expires after one hour
  if (token) {
    res.cookie("token", token, {
      httpOnly: true,
      secure: true, // Ensure cookies are only sent over HTTPS
      sameSite: "none",
      maxAge: expiredAt,
    });
    //   secure: process.env.NODE_ENV === "production",
    // };

    // // Save token in cookie
    // res.cookie("token", token, cookieOptions);

    // if all success track the expiration time of the session
    trackSessionExpiration(user.id, expiredAt);
  }
  // Track session expiration
  // trackSessionExpiration(user.id, 24 * 60 * 60 * 1000);
}

// email verification token
function sendEmailValidationToken(user: User) {
  // create a token to send the email verification
  const secretKey = process.env.SECRET_KEY;
  let token;
  if (secretKey) {
    token = jwt.sign({ email: user.email }, secretKey, { expiresIn: "10m" });
  }

  // user full name
  const fullName = user.firstName + " " + user.lastName;

  const link = "/signup-process/verification/";
  // send email with the token
  if (token) emailVerifyRequest(token, link, { toEmail: user.email, fullName });
}

// send reset email token
function sendResetPassEmail(user: User) {
  // create a token to send the email verification
  const secretKey = process.env.SECRET_KEY;
  let token;
  if (secretKey) {
    token = jwt.sign({ email: user.email }, secretKey, { expiresIn: "10m" });
  }

  // user full name
  const fullName = user.firstName + " " + user.lastName;

  // send email with the token
  if (token) emailResetPassword({ toEmail: user.email, token, fullName });
}
