import {
  TeamM<PERSON>berRole,
  Department,
  RoleType,
  Role,
  AgentRole,
} from "@prisma/client";
import { InputSanitizer } from "../sanitizers/inputSanitizer";

interface TeamMemberInput {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: Role;
  department: Department;
  teamId: string;
  roleType?: RoleType | Role;
  subRole?: TeamMemberRole;
  // accountStatus?: TeamMemberStatus;
}

interface TeamMemberValidationError {
  field: string;
  message: string;
}

interface SanitizedTeamMember {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: Role;
  subRole?: TeamMemberRole;
  department: Department;
  teamId: string;
  roleType?: RoleType;
  // accountStatus?: TeamMemberStatus;
  errors: TeamMemberValidationError[];
}

interface AgentMemberInput {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: Role;
  department: Department;
  subRole?: AgentRole;
}

interface SanitizedAgentMember {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  role: Role;
  subRole?: AgentRole;
  department: Department;
  roleType?: RoleType;
  errors: TeamMemberValidationError[];
}

interface UserInput {
  firstName: string;
  lastName: string;
}

interface SanitizedUser {
  firstName: string;
  lastName: string;
  errors: TeamMemberValidationError[];
}

// const NAME_PATTERN =
//   /^[A-Za-z]+(?:[''-]?[A-Za-z]+)*(?:\s[A-Za-z]+(?:[''-]?[A-Za-z]+)*)*$/;
const NAME_PATTERN =
  /^[A-Za-z]+(?:[''-][A-Za-z]+)*(?:\s[A-Za-z]+(?:[''-][A-Za-z]+)*)*$/;

function decodeHtmlEntities(str: string): string {
  return str.replace(/&([^;]+);/g, (entity, entityCode) => {
    const entities: { [key: string]: string } = {
      apos: "'",
      "#x27": "'",
      "#39": "'",
      nbsp: " ",
      "#x2D": "-",
      "#45": "-",
      minus: "-",
      hyphen: "-",
    };
    return entities[entityCode] || entity;
  });
}

// Add a function to check for leading/trailing spaces
function hasLeadingOrTrailingSpaces(str: string): boolean {
  return str.trim() !== str;
}

export const roleToRoleTypeMapping: { [key: string]: RoleType } = {
  // moderator: "master_moderator",
  // master: "master_admin",
  // accountant: "master_accountant",
  // admin: "master_admin",
  moderator: RoleType.master_moderator,
  master: RoleType.master_admin,
  accountant: RoleType.master_accountant,
  admin: RoleType.master_admin,
};

export function validateTeamMember(
  data: TeamMemberInput,
  currentUserRole?: TeamMemberRole | Role
): SanitizedTeamMember {
  const errors: TeamMemberValidationError[] = [];
  const sanitized: Partial<SanitizedTeamMember> = {};

  // Email validation with domain restriction
  const emailResult = InputSanitizer.sanitizeTeamMemberEmail(data.email);
  if (!emailResult.isValid) {
    errors.push({
      field: "email",
      message: emailResult.error || "Invalid email",
    });
  }
  sanitized.email = emailResult.value;

  // Role-based validation
  if (currentUserRole) {
    // Check if trying to modify an admin account
    if (data.subRole === TeamMemberRole.admin) {
      if (
        currentUserRole !== Role.master &&
        // currentUserRole !== SubRole.ADMIN
        currentUserRole !== TeamMemberRole.admin
      ) {
        errors.push({
          field: "role",
          message: "Only Admin users can modify Admin accounts",
        });
      }
    }

    // Moderator restrictions
    // if (currentUserRole === SubRole.MODERATOR) {
    if (currentUserRole === TeamMemberRole.moderator) {
      if (data.subRole === TeamMemberRole.admin) {
        errors.push({
          field: "role",
          message: "Moderators cannot modify Admin accounts",
        });
      }

      if (
        data.subRole !== TeamMemberRole.moderator &&
        data.subRole !== TeamMemberRole.accountant
      ) {
        errors.push({
          field: "role",
          message:
            "Moderators can only manage Moderator and Accountant accounts",
        });
      }
    }

    // Accountant restrictions
    // if (currentUserRole === SubRole.ACCOUNTANT) {
    if (currentUserRole === TeamMemberRole.accountant) {
      errors.push({
        field: "role",
        message: "Accountants cannot modify any user accounts",
      });
    }
  }

  // Sanitize and validate first name
  const firstNameResult = InputSanitizer.sanitizeString(data.firstName);
  sanitized.firstName = firstNameResult.value?.trim();

  // Decode HTML entities before validation
  const decodedFirstName = decodeHtmlEntities(sanitized.firstName);

  if (!firstNameResult.isValid || !sanitized.firstName || !decodedFirstName) {
    errors.push({
      field: "firstName",
      message: firstNameResult.error || "First name is required",
    });
  } else if (decodedFirstName.length < 3) {
    errors.push({
      field: "firstName",
      message: "First name must be at least 3 characters long",
    });
  } else if (decodedFirstName.length > 30) {
    errors.push({
      field: "firstName",
      message: "First name must be at most 30 characters long",
    });
  } else if (!NAME_PATTERN.test(decodedFirstName)) {
    errors.push({
      field: "firstName",
      message:
        "First name can only contain letters, apostrophes, hyphens, and spaces",
    });
  } else if (/(.)\1{4,}/.test(decodedFirstName)) {
    errors.push({
      field: "firstName",
      message: "First name contains excessive character repetition",
    });
  } else if (hasLeadingOrTrailingSpaces(data.firstName)) {
    errors.push({
      field: "firstName",
      message: "First name cannot have leading or trailing spaces",
    });
  }

  // Sanitize and validate last name
  const lastNameResult = InputSanitizer.sanitizeString(data.lastName);
  sanitized.lastName = lastNameResult.value?.trim();
  const decodedLastName = decodeHtmlEntities(sanitized.lastName);

  if (!lastNameResult.isValid || !sanitized.lastName || !decodedLastName) {
    errors.push({
      field: "lastName",
      message: lastNameResult.error || "Last name is required",
    });
  } else if (decodedLastName.length < 3) {
    errors.push({
      field: "lastName",
      message: "Last name must be at least 3 characters long",
    });
  } else if (decodedLastName.length > 30) {
    errors.push({
      field: "lastName",
      message: "Last name must be at most 30 characters long",
    });
  } else if (!NAME_PATTERN.test(decodedLastName)) {
    errors.push({
      field: "lastName",
      message:
        "Last name can only contain letters, apostrophes, hyphens, and spaces",
    });
  } else if (/(.)\1{4,}/.test(decodedLastName)) {
    errors.push({
      field: "lastName",
      message: "Last name contains excessive character repetition",
    });
  } else if (hasLeadingOrTrailingSpaces(data.lastName)) {
    errors.push({
      field: "lastName",
      message: "Last name cannot have leading or trailing spaces",
    });
  }

  // Sanitize and validate password if provided
  if (data.password !== undefined) {
    const passwordResult = InputSanitizer.sanitizePassword(data.password);
    sanitized.password = passwordResult.value;
    if (!passwordResult.isValid) {
      errors.push({
        field: "password",
        message:
          passwordResult.error || "The password you entered is incorrect",
      });
    }
  }

  // Sanitize and validate role
  const roleResult = InputSanitizer.sanitizeEnum(data.role, Role, "Role");
  sanitized.role = roleResult.value as Role;
  if (!roleResult.isValid) {
    errors.push({
      field: "role",
      message: roleResult.error || "Invalid role",
    });
  }

  // Sanitize and validate subRole
  const subRoleResult = InputSanitizer.sanitizeEnum(
    data.subRole,
    TeamMemberRole,
    "TeamMemberRole"
  );
  sanitized.subRole = subRoleResult.value as TeamMemberRole;
  if (!subRoleResult.isValid) {
    errors.push({
      field: "subRole",
      message: subRoleResult.error || "Invalid sub role",
    });
  }

  // Derive roleType from role
  sanitized.roleType = roleToRoleTypeMapping[sanitized.role];
  if (!sanitized.roleType) {
    errors.push({
      field: "roleType",
      message: "Invalid role type mapping for the provided role",
    });
  }

  // Sanitize and validate department
  const departmentResult = InputSanitizer.sanitizeEnum(
    data.department,
    Department,
    "Department"
  );
  sanitized.department = departmentResult.value as Department;
  if (!departmentResult.isValid) {
    errors.push({
      field: "department",
      message: departmentResult.error || "Invalid department",
    });
  }

  // Sanitize and validate teamId
  const teamIdResult = InputSanitizer.sanitizeString(data.teamId);
  sanitized.teamId = teamIdResult.value;
  if (!teamIdResult.isValid) {
    errors.push({
      field: "teamId",
      message: teamIdResult.error || "Team ID is required",
    });
  }

  return {
    ...(sanitized as SanitizedTeamMember),
    errors,
  };
}

export function validateAgentMember(
  data: AgentMemberInput,
  currentUserRole?: Role
): SanitizedAgentMember {
  const errors: TeamMemberValidationError[] = [];
  const sanitized: Partial<SanitizedAgentMember> = {};

  // Email validation with domain restriction
  // const emailResult = InputSanitizer.sanitizeTeamMemberEmail(data.email);
  const emailResult = InputSanitizer.sanitizeEmail(data.email);
  if (!emailResult.isValid) {
    errors.push({
      field: "email",
      message: emailResult.error || "Invalid email",
    });
  }
  sanitized.email = emailResult.value;

  // Role-based validation
  if (currentUserRole) {
    if (
      data.subRole === AgentRole.admin &&
      currentUserRole !== Role.master &&
      data.role !== Role.agency
    ) {
      errors.push({
        field: "role",
        message: "Only agency users can assign admin role to agents",
      });
    }
  }

  // Set role to AGENCY always
  sanitized.role = Role.agency;

  // Set subRole directly from input
  sanitized.subRole = data.subRole;

  // Validate subRole
  if (!Object.values(AgentRole).includes(data.subRole as AgentRole)) {
    errors.push({
      field: "subRole",
      message: "Invalid agent role",
    });
  }

  // Derive roleType from role and subRole
  switch (data.subRole) {
    case AgentRole.admin:
      sanitized.roleType = RoleType.agency_admin;
      break;
    case AgentRole.accountant:
      sanitized.roleType = RoleType.agency_accountant;
      break;
    case AgentRole.operation:
      sanitized.roleType = RoleType.agency_operation;
      break;
    case AgentRole.sales:
      sanitized.roleType = RoleType.agency_sales;
      break;
    default:
      errors.push({
        field: "roleType",
        message: "Invalid role type mapping for the provided role",
      });
  }

  // Sanitize and validate first name
  const firstNameResult = InputSanitizer.sanitizeString(data.firstName);
  sanitized.firstName = firstNameResult.value?.trim();

  // Decode HTML entities before validation
  const decodedFirstName = decodeHtmlEntities(sanitized.firstName);

  if (!firstNameResult.isValid || !sanitized.firstName || !decodedFirstName) {
    errors.push({
      field: "firstName",
      message: firstNameResult.error || "First name is required",
    });
  } else if (decodedFirstName.length < 3) {
    errors.push({
      field: "firstName",
      message: "First name must be at least 3 characters long",
    });
  } else if (decodedFirstName.length > 30) {
    errors.push({
      field: "firstName",
      message: "First name must be at most 30 characters long",
    });
  } else if (!NAME_PATTERN.test(decodedFirstName)) {
    errors.push({
      field: "firstName",
      message:
        "First name can only contain letters, apostrophes, hyphens, and spaces",
    });
  } else if (/(.)\1{4,}/.test(decodedFirstName)) {
    errors.push({
      field: "firstName",
      message: "First name contains excessive character repetition",
    });
  } else if (hasLeadingOrTrailingSpaces(data.firstName)) {
    errors.push({
      field: "firstName",
      message: "First name cannot have leading or trailing spaces",
    });
  }

  // Sanitize and validate last name
  const lastNameResult = InputSanitizer.sanitizeString(data.lastName);
  sanitized.lastName = lastNameResult.value?.trim();

  // Decode HTML entities before validation
  const decodedLastName = decodeHtmlEntities(sanitized.lastName);

  if (!lastNameResult.isValid || !sanitized.lastName || !decodedLastName) {
    errors.push({
      field: "lastName",
      message: lastNameResult.error || "Last name is required",
    });
  } else if (decodedLastName.length < 3) {
    errors.push({
      field: "lastName",
      message: "Last name must be at least 3 characters long",
    });
  } else if (decodedLastName.length > 30) {
    errors.push({
      field: "lastName",
      message: "Last name must be at most 30 characters long",
    });
  } else if (!NAME_PATTERN.test(decodedLastName)) {
    errors.push({
      field: "lastName",
      message:
        "Last name can only contain letters, apostrophes, hyphens, and spaces",
    });
  } else if (/(.)\1{4,}/.test(decodedLastName)) {
    errors.push({
      field: "lastName",
      message: "Last name contains excessive character repetition",
    });
  } else if (hasLeadingOrTrailingSpaces(data.lastName)) {
    errors.push({
      field: "lastName",
      message: "Last name cannot have leading or trailing spaces",
    });
  }

  // Sanitize and validate password if provided
  if (data.password !== undefined) {
    const passwordResult = InputSanitizer.sanitizePassword(data.password);
    sanitized.password = passwordResult.value;
    if (!passwordResult.isValid) {
      errors.push({
        field: "password",
        message:
          passwordResult.error || "The password you entered is incorrect",
      });
    }
  }

  // Sanitize and validate department
  const departmentResult = InputSanitizer.sanitizeEnum(
    data.department,
    Department,
    "Department"
  );
  sanitized.department = departmentResult.value as Department;
  if (!departmentResult.isValid) {
    errors.push({
      field: "department",
      message: departmentResult.error || "Invalid department",
    });
  }

  return {
    ...(sanitized as SanitizedAgentMember),
    errors,
  };
}

export function validateUser(data: UserInput): SanitizedUser {
  const errors: TeamMemberValidationError[] = [];
  const sanitized: Partial<SanitizedUser> = {};

  // Sanitize and validate first name
  const firstNameResult = InputSanitizer.sanitizeString(data.firstName);
  sanitized.firstName = firstNameResult.value?.trim();

  // Decode HTML entities before validation
  const decodedFirstName = decodeHtmlEntities(sanitized.firstName);

  if (!firstNameResult.isValid || !sanitized.firstName || !decodedFirstName) {
    errors.push({
      field: "firstName",
      message: firstNameResult.error || "First name is required",
    });
  } else if (decodedFirstName.length < 3) {
    errors.push({
      field: "firstName",
      message: "First name must be at least 3 characters long",
    });
  } else if (decodedFirstName.length > 30) {
    errors.push({
      field: "firstName",
      message: "First name must be at most 30 characters long",
    });
  } else if (/(.)\1{4,}/.test(decodedFirstName)) {
    errors.push({
      field: "firstName",
      message: "First name contains excessive character repetition",
    });
  } else if (hasLeadingOrTrailingSpaces(data.firstName)) {
    errors.push({
      field: "firstName",
      message: "First name cannot have leading or trailing spaces",
    });
  } else if (!NAME_PATTERN.test(decodedFirstName)) {
    errors.push({
      field: "firstName",
      message:
        "First name can only contain letters, apostrophes, hyphens, and spaces3",
    });
  }
  // Sanitize and validate last name
  const lastNameResult = InputSanitizer.sanitizeString(data.lastName);
  sanitized.lastName = lastNameResult.value?.trim();
  const decodedLastName = decodeHtmlEntities(sanitized.lastName);

  if (!lastNameResult.isValid || !sanitized.lastName || !decodedLastName) {
    errors.push({
      field: "lastName",
      message: lastNameResult.error || "Last name is required",
    });
  } else if (decodedLastName.length < 3) {
    errors.push({
      field: "lastName",
      message: "Last name must be at least 3 characters long",
    });
  } else if (decodedLastName.length > 30) {
    errors.push({
      field: "lastName",
      message: "Last name must be at most 30 characters long",
    });
  } else if (!NAME_PATTERN.test(decodedLastName)) {
    errors.push({
      field: "lastName",
      message:
        "Last name can only contain letters, apostrophes, hyphens, and spaces",
    });
  } else if (/(.)\1{4,}/.test(decodedLastName)) {
    errors.push({
      field: "lastName",
      message: "Last name contains excessive character repetition",
    });
  } else if (hasLeadingOrTrailingSpaces(data.lastName)) {
    errors.push({
      field: "lastName",
      message: "Last name cannot have leading or trailing spaces",
    });
  }

  return {
    ...(sanitized as SanitizedUser),
    errors,
  };
}
