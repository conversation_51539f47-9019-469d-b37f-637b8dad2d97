import { Router } from "express";
import { requirePermission } from "../middlewares/authMiddleware";
import { PERMISSIONS } from "../utils/types/auth";
import {
  getTeamId,
  createTeamMember,
  updateTeamMember,
  getTeamMembers,
  searchTeamMembers,
  removeTeamMember,
  resendInvitation,
} from "../controllers/teamController";

const router = Router();

// Team management routes with permission checks
router.get("/id", requirePermission(PERMISSIONS.VIEW_TEAM.name), getTeamId);
router.post(
  "/member",
  requirePermission(PERMISSIONS.MANAGE_TEAM.name),
  createTeamMember
);
router.put(
  "/member/:id",
  requirePermission(PERMISSIONS.MANAGE_TEAM.name),
  updateTeamMember
);
router.get(
  "/members",
  requirePermission(PERMISSIONS.VIEW_TEAM.name),
  getTeamMembers
);
router.get(
  "/search",
  requirePermission(PERMISSIONS.VIEW_TEAM.name),
  searchTeamMembers
);
router.delete(
  "/member/:id",
  requirePermission(PERMISSIONS.MANAGE_TEAM.name),
  removeTeamMember
);
router.post(
  "/resend-invitation",
  requirePermission(PERMISSIONS.MANAGE_TEAM.name),
  resendInvitation
);

export default router;
