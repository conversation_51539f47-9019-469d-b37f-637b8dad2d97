// // TICKET
// model FlightTicket {
//   id                String                 @id @default(cuid())
//   refId             String                 @unique
//   ticketStatus      String                 @default("pending")
//   previousStatus    String? // To store the status before being set to HOLD
//   description       String?
//   seats             Int
//   remainingSeats    Int
//   departure         FlightLocation?        @relation("TicketDepartures", fields: [departureId], references: [id])
//   departureId       String?                @unique // O - O
//   arrival           FlightLocation?        @relation("TicketArrivals", fields: [arrivalId], references: [id])
//   arrivalId         String?                @unique // O - O
//   flightDate        String
//   departureTime     String?
//   arrivalTime       String?
//   duration          String?
//   stops             Int
//   owner             User                   @relation(fields: [ownerId], references: [id], onUpdate: Restrict, onDelete: Restrict)
//   ownerId           String // O - M agency who created the ticket
//   agencyAgent       AgencyAgent?           @relation(fields: [agencyAgentId], references: [id], onUpdate: Restrict)
//   agencyAgentId     String? // O - M agent who created this ticket 
//   updated           Boolean                @default(false) // only when the ticket is validate and updated
//   bookedSeats       BookedFlightSeat[]
//   flightClasses     FlightClass[]
//   segments          FlightSegment[]
//   purchasedSeats    PurchasedFlightTicket?
//   ticketHistoryLogs TicketHistoryLog[]     @relation("TicketHistoryLogs")
//   notifications     Notification[]         @relation("TicketNotifications")

//   createdBy    String? // ID of the user/team member who created the ticket
//   ticketAccess TicketAccess[]

//   archived   Boolean   @default(false)
//   archivedAt DateTime?
//   createdAt  DateTime  @default(now())
//   updatedAt  DateTime  @updatedAt
//   Booking    Booking[]

//   @@index([ownerId], name: "flight_ticket_owner_idx")
//   @@index([agencyAgentId], name: "flight_ticket_agency_agent_idx")
//   @@index([flightDate], name: "ticket_flight_date_idx")
//   @@index([ticketStatus, flightDate, seats], name: "ticket_availability_idx")
//   @@map("flightTickets")
// }

// model TicketHistoryLog {
//   id            String       @id @default(cuid())
//   ticket        FlightTicket @relation("TicketHistoryLogs", fields: [ticketId], references: [id], onDelete: Restrict)
//   ticketId      String
//   changeType    String
//   changeDetails String?
//   oldValue      Json? // Stores old value (before update)
//   newValue      Json? // Stores new value (after update)
//   agency        User?        @relation(fields: [agencyId], references: [id])
//   agencyId      String?
//   agencyAgent   AgencyAgent? @relation(fields: [agencyAgentId], references: [id])
//   agencyAgentId String?

//   changedAt DateTime @default(now())

//   @@index([ticketId], name: "ticket_history_log_ticket_idx")
//   @@index([agencyId], name: "ticket_history_log_agency_idx")
//   @@index([agencyAgentId], name: "ticket_history_log_agency_agent_idx")
//   @@map("ticketHistoryLogs")
// }

// // flight seat class 
// model FlightClass {
//   id             String @id @default(cuid())
//   type           String
//   carryOnAllowed Int // Number of carry-on bags allowed
//   carryOnWeight  Float // Maximum weight for carry-on bags in kg
//   checkedAllowed Int // Number of checked bags allowed
//   checkedWeight  Float // Maximum weight for checked bags in kg
//   checkedFee     Float // Fee for the first checked bag
//   additionalFee  Float // Fee for additional checked bags

//   extraOffers    FlightExtraOffer[] // O - M
//   flightTicket   FlightTicket       @relation(fields: [flightTicketId], references: [id], onDelete: Restrict)
//   flightTicketId String
//   price          FlightPrice?

//   @@index([flightTicketId], name: "flight_class_ticket_idx")
//   @@map("flightClasses")
// }

// // extra offers for each class 
// model FlightExtraOffer {
//   id            String      @id @default(cuid())
//   flightClass   FlightClass @relation(fields: [flightClassId], references: [id], onDelete: Restrict) // O - M 
//   flightClassId String
//   name          String?
//   available     String?

//   @@index([flightClassId], name: "flight_extra_offer_class_idx")
//   @@map("flightExtraOffer")
// }

// // PRICE 
// model FlightPrice {
//   id            String      @id @default(cuid())
//   adult         Float
//   child         Float
//   infant        Float
//   tax           Float?
//   currency      String      @default("JOD")
//   flightClass   FlightClass @relation(fields: [flightClassId], references: [id], onDelete: Restrict) // O - O
//   flightClassId String      @unique

//   @@map("flightPrices")
// }

// // fight segment [ticket has one stop = 2 segments]
// model FlightSegment {
//   id             String         @id @default(cuid())
//   flightTicket   FlightTicket   @relation(fields: [flightTicketId], references: [id], onDelete: Restrict)
//   flightTicketId String
//   flightNumber   String
//   carrier        String
//   departure      FlightLocation @relation("SegmentDepartures", fields: [departureId], references: [id], onDelete: Restrict)
//   departureId    String         @unique // O - O
//   arrival        FlightLocation @relation("SegmentArrivals", fields: [arrivalId], references: [id])
//   arrivalId      String         @unique // O - O
//   departureTime  String
//   arrivalTime    String
//   duration       String

//   @@index([flightTicketId], name: "flight_segment_ticket_idx")
//   @@index([departureTime, arrivalTime], name: "segment_time_idx")
//   @@map("flightSegments")
// }

// // flight location
// model FlightLocation {
//   id                String          @id @default(cuid())
//   airportCode       String
//   country           String
//   city              String
//   airport           String
//   ticketDepartures  FlightTicket[]  @relation("TicketDepartures") // O - M 
//   ticketArrivals    FlightTicket[]  @relation("TicketArrivals") // O - M 
//   segmentDepartures FlightSegment[] @relation("SegmentDepartures") // O - M 
//   segmentArrivals   FlightSegment[] @relation("SegmentArrivals") // O - M 

//   @@index([airportCode], name: "location_airport_code_idx")
//   @@map("flightLocations")
// }

// model TicketAccess {
//   id           String       @id @default(cuid())
//   ticket       FlightTicket @relation(fields: [ticketId], references: [id])
//   ticketId     String
//   teamMemberId String
//   accessLevel  AccessLevel // e.g., 'READ', 'EDIT'
//   createdBy    User         @relation(fields: [createdById], references: [id])
//   createdById  String
//   createdAt    DateTime     @default(now())
//   updatedAt    DateTime     @updatedAt

//   @@index([ticketId, teamMemberId], name: "ticket_access_composite_idx")
//   @@index([createdById])
// }

// enum AccessLevel {
//   READ
//   WRITE
//   ADMIN
// }

// model Booking {
//   id            String       @id @default(cuid())
//   ticketId      String
//   ticket        FlightTicket @relation(fields: [ticketId], references: [id])
//   userId        String
//   user          User         @relation(fields: [userId], references: [id])
//   agencyAgentId String?
//   agencyAgent   AgencyAgent? @relation(fields: [agencyAgentId], references: [id])
//   teamMemberId  String?
//   TeamMember    TeamMember?  @relation(fields: [teamMemberId], references: [id])

//   sellerAgencyId String? // Agency who owns the ticket
//   buyerAgencyId  String? // Agency making the booking

//   // Core booking data
//   source          BookingSource // ← Key discriminator (INTERNAL/THIRD_PARTY)
//   type            BookingType // ← QUICK_HOLD/SUBMIT_BOOKING (for internal)
//   status          BookingStatus // ← Works for both flows
//   requestId       String        @unique
//   eTickets        ETicket[]     @relation("BookingETickets")
//   totalSeats      Int           @default(1)
//   totalAmount     Decimal       @default(0) @db.Decimal(10, 2)
//   initialHoldType BookingType   @default(QUICK_HOLD)

//   // Timing data
//   timerDuration  Int // In minutes (60 or 15)
//   timerStartedAt DateTime  @default(now())
//   expiresAt      DateTime
//   timedOutAt     DateTime?

//   // Reason fields
//   cancellationReason String?
//   statusReason       String?

//   // Transaction data
//   transactionId      String?   @unique
//   transactionDate    DateTime?
//   paymentCompletedAt DateTime?
//   payment            Payment?

//   // Extensibility
//   meta          Json?
//   notifications Notification[]     @relation("BookingNotifications")
//   travelers     BookingTraveler[]
//   bookedSeats   BookedFlightSeat[]

//   webPushSubscriptions WebPushSubscription[] // Web push subscriptions relation (one-to-many)
//   // History
//   bookingHistoryLogs   BookingHistoryLog[]
//   Receipt              Receipt[]

//   createdAt DateTime? @default(now())
//   updatedAt DateTime? @updatedAt

//   // Indexes
//   @@index([ticketId], name: "booking_ticket_idx")
//   @@index([userId], name: "booking_user_idx")
//   @@index([requestId], name: "booking_request_id_idx")
//   @@index([agencyAgentId], name: "booking_agency_agent_idx")
//   @@index([teamMemberId], name: "booking_team_member_idx")
//   @@index([userId, status], name: "booking_user_status_idx")
//   @@index([status, expiresAt], name: "booking_status_expiry_idx")
//   @@index([sellerAgencyId], name: "booking_seller_agency_idx")
//   @@index([buyerAgencyId], name: "booking_buyer_agency_idx")
//   @@index([sellerAgencyId, status], name: "booking_seller_status_idx")
//   @@index([buyerAgencyId, status], name: "booking_buyer_status_idx")
//   @@index([status], name: "booking_status_idx")
//   @@index([createdAt], name: "booking_created_at_idx")
//   @@map("bookings")
// }

// enum BookingType {
//   QUICK_HOLD
//   SUBMIT_BOOKING
// }

// enum BookingSource {
//   INTERNAL
//   THIRD_PARTY
// }

// enum BookingStatus {
//   QUICK_HOLD // Initial 1hr hold
//   TIMED_OUT // Hold expired before action
//   PENDING_APPROVAL // After payment completed, waiting for admin
//   BOOKING_CONFIRMED // Approved by admin
//   BOOKING_REJECTED // Rejected by admin
//   CANCELLED_BY_USER // Cancelled by user
//   CANCELLED_BY_SYSTEM // e.g., if ticket becomes unavailable
// }

// model Payment {
//   id            String  @id @default(cuid())
//   booking       Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade)
//   bookingId     String  @unique
//   amount        Decimal @db.Decimal(10, 2)
//   paymentMethod String? // e.g. Credit Card, Bank Transfer
//   paymentMeta   Json? // For extensibility (e.g. transaction refs, gateway data)
//   paidByUserId  String?
//   paidByUser    User?   @relation(fields: [paidByUserId], references: [id], onDelete: SetNull)

//   currency         String        @default("JOD") @db.VarChar(3)
//   paymentStatus    PaymentStatus @default(PENDING)
//   paymentReference String?
//   idempotencyKey   String?       @unique // To prevent duplicate payments
//   // Add refund tracking
//   refundedAmount   Decimal?      @default(0) @db.Decimal(10, 2)
//   refundReason     String?
//   retryCount       Int           @default(0)
//   lastAttempt      DateTime?
//   errorMessage     String?
//   createdAt        DateTime      @default(now())
//   updatedAt        DateTime      @updatedAt

//   @@index([bookingId])
//   @@index([paidByUserId])
//   @@map("payments")
// }

// enum PaymentStatus {
//   PENDING
//   COMPLETED
//   FAILED
//   REFUNDED
//   PARTIALLY_REFUNDED
// }

// model BookingHistoryLog {
//   id         String         @id @default(cuid())
//   booking    Booking        @relation(fields: [bookingId], references: [id], onDelete: Restrict)
//   bookingId  String
//   changeType String
//   oldStatus  BookingStatus?
//   newStatus  BookingStatus?
//   actorType  ActorType
//   actorId    String? // ID of user who made the change
//   reason     String? // Especially for rejections
//   createdAt  DateTime       @default(now())

//   @@index([bookingId])
//   @@map("bookingHistoryLogs")
// }

// enum ActorType {
//   AGENT
//   ADMIN
//   SYSTEM
//   CUSTOMER
// }

// model Traveler {
//   id             String                @id @default(cuid())
//   title          CustomerInfoTitle?
//   firstName      String?
//   lastName       String?
//   nationality    String?
//   dateOfBirth    DateTime?
//   gender         String?
//   documentType   CustomerDocumentType?
//   documentNumber String?
//   issuingCountry String?
//   expirationDate DateTime?
//   contactEmail   String?
//   contactPhone   String?
//   primaryContact Boolean?              @default(false)
//   // Relations
//   bookings       BookingTraveler[]
//   bookedSeats    BookedFlightSeat[]
//   createdAt      DateTime              @default(now())
//   updatedAt      DateTime              @updatedAt

//   @@index([documentNumber, documentType]) // For passport/ID lookups
//   @@map("travelers")
// }

// model BookingTraveler {
//   id         String              @id @default(cuid())
//   bookingId  String
//   travelerId String
//   infoStatus TravelerInfoStatus? @default(PLACEHOLDER)

//   booking  Booking  @relation(fields: [bookingId], references: [id])
//   traveler Traveler @relation(fields: [travelerId], references: [id])

//   @@unique([bookingId, travelerId])
//   @@index([travelerId])
// }

// enum TravelerInfoStatus {
//   PLACEHOLDER
//   COMPLETED
// }

// model ETicket {
//   id             String        @id @default(cuid())
//   bookingId      String
//   Booking        Booking       @relation("BookingETickets", fields: [bookingId], references: [id])
//   eTicketNumber  String        @unique
//   fileUrl        String? // Path or URL to e-ticket file (PDF, etc.)
//   meta           Json? // Additional e-ticket metadata (QR, PDF, etc.)
//   issuedAt       DateTime      @default(now())
//   issuedBy       String? // Agent/user who issued the ticket
//   ticketType     TicketType
//   status         ETicketStatus @default(ISSUED)
//   voidedAt       DateTime?
//   voidReason     String?
//   reissuedFromId String? // For reissues
//   reissuedFrom   ETicket?      @relation("ReissueChain", fields: [reissuedFromId], references: [id], onDelete: Restrict, onUpdate: Restrict)
//   reissues       ETicket[]     @relation("ReissueChain")
//   receipt        Receipt?
//   createdAt      DateTime      @default(now())
//   updatedAt      DateTime      @updatedAt

//   @@index([reissuedFromId])
//   @@index([bookingId])
// }

// enum TicketType {
//   INTERNAL
//   THIRD_PARTY
// }

// enum ETicketStatus {
//   ISSUED
//   VOIDED
//   REISSUED
//   CANCELLED
// }

// model Receipt {
//   id            String        @id @default(cuid())
//   bookingId     String
//   Booking       Booking       @relation(fields: [bookingId], references: [id])
//   eTicketId     String?       @unique // Optional: link to ETicket if needed
//   ETicket       ETicket?      @relation(fields: [eTicketId], references: [id])
//   receiptNumber String        @unique
//   fileUrl       String? // Path or URL to PDF/receipt
//   meta          Json? // Additional receipt metadata (PDF, QR, etc.)
//   issuedAt      DateTime      @default(now())
//   issuedBy      String? // Agent/user who issued the receipt
//   receiptType   ReceiptType
//   status        ReceiptStatus @default(ISSUED)
//   voidedAt      DateTime?
//   voidReason    String?
//   createdAt     DateTime      @default(now())
//   updatedAt     DateTime      @updatedAt

//   @@index([bookingId])
//   @@index([eTicketId])
// }

// enum ReceiptType {
//   INTERNAL
//   THIRD_PARTY
// }

// enum ReceiptStatus {
//   ISSUED
//   VOIDED
//   CANCELLED
// }
