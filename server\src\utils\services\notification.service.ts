import {
  Notification as PrismaNotification,
  WebPushSubscription,
  Prisma,
} from "@prisma/client";
import webPush from "web-push";
import { sendRealTimeNotification } from "../../socket";
import { prisma } from "../../prisma";

export interface CreateNotificationDto {
  userId: string;
  type: string;
  title: string;
  message: string;
  relatedId?: string;
  link?: string;
  priority?: number;
}
interface PushNotification {
  title: string;
  message: string;
  link?: string;
}

export interface UpdateNotificationDto {
  read?: boolean;
  priority?: number;
}

export interface NotificationResponseDto {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  relatedId?: string;
  link?: string;
  priority: number;
}

class NotificationService {
  async createNotification(
    data: CreateNotificationDto
  ): Promise<NotificationResponseDto> {
    // return prisma.notification.create({
    //   data,
    // });
    try {
      const notification = await prisma.notification.create({
        data,
      });

      // Send real-time notification immediately after creation
      await sendRealTimeNotification(data.userId, notification as any);

      // Also try to send push notification if configured
      try {
        await this.sendPushNotifications(data.userId, {
          title: data.title,
          message: data.message,
          link: data.link,
        });
      } catch (error) {
        console.error("Failed to send push notification:", error);
      }

      return notification as unknown as NotificationResponseDto;
    } catch (error) {
      console.error("Error creating notification:", error);
      throw error;
    }
  }

  async getNotificationById(
    id: string
  ): Promise<NotificationResponseDto | null> {
    return prisma.notification.findUnique({
      where: { id },
    }) as unknown as NotificationResponseDto | null;
  }

  async getUserNotifications(
    userId: string
  ): Promise<NotificationResponseDto[]> {
    return prisma.notification.findMany({
      where: { userId },
      orderBy: { createdAt: "desc" },
    }) as unknown as NotificationResponseDto[];
  }

  async markAsRead(id: string): Promise<NotificationResponseDto> {
    return prisma.notification.update({
      where: { id },
      data: { read: true },
    }) as unknown as NotificationResponseDto;
  }

  async deleteNotification(id: string): Promise<{ count: number }> {
    // Use deleteMany so it never throws if the record is missing
    const result = await prisma.notification.deleteMany({ where: { id } });
    return { count: result.count };
  }

  async markAllAsRead(userId: string): Promise<NotificationResponseDto[]> {
    // First update all notifications
    await prisma.notification.updateMany({
      where: { userId },
      data: { read: true },
    });

    // Then return the updated notifications
    return prisma.notification.findMany({
      where: { userId },
    }) as unknown as NotificationResponseDto[];
  }

  // Get unread count
  async getUnreadCount(userId: string): Promise<number> {
    return prisma.notification.count({
      where: { userId, read: false },
    });
  }

  // Add push subscription management
  async storePushSubscription(
    userId: string,
    subscription: WebPushSubscription
  ): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: {
        webPushSubscriptions: {
          create: {
            endpoint: JSON.stringify(subscription),
          },
        },
      },
    });
  }

  private async sendPushNotifications(
    userId: string,
    notification: PushNotification
  ) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { webPushSubscriptions: true },
    });

    user?.webPushSubscriptions.forEach((sub: any) => {
      webPush
        .sendNotification(
          JSON.parse(sub.endpoint),
          JSON.stringify({
            title: notification.title,
            body: notification.message,
            icon: "/logo.png",
            data: { url: notification.link },
          })
        )
        .catch(async (error) => {
          if (error.statusCode === 410) {
            // Remove invalid subscriptions
            await prisma.webPushSubscription.delete({
              where: { id: sub.id },
            });
          }
        });
    });
  }

  // Add a method to remove subscription
  async removePushSubscription(
    userId: string,
    endpoint: string
  ): Promise<void> {
    await prisma.webPushSubscription.deleteMany({
      where: {
        userId,
        endpoint: JSON.stringify({ endpoint }),
      },
    });
  }
}

// Initialize web-push with VAPID keys
webPush.setVapidDetails(
  "mailto:" + process.env.VAPID_EMAIL!,
  process.env.VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
);

export default new NotificationService();
