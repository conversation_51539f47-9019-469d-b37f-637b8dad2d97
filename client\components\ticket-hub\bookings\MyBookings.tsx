"use client";
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { createPortal } from "react-dom";
import {
  getBookingAgentNames,
  getAllBookings,
  getBookingById,
} from "@/lib/data/bookingData";
import FilterDropdown from "@/components/common/FilterDropdown";
import { Search, Edit, Calendar, Clock, User, Users, X } from "lucide-react";
import { Booking, Passenger } from "./utils/definitions/myBookingDefinitions";
import {
  calculatePrice,
  getFormatDateTable,
} from "@/utils/functions/functions";
import { useAppSelector } from "@/redux/hooks";
import { selectItinerary } from "@/redux/features/BookingConfirmationSlice";
import { selectUser } from "@/redux/features/AuthSlice";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import { useInView } from "react-intersection-observer";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { setBookingConfirmationData } from "@/redux/features/BookingConfirmationSlice";
import ProgressLoading from "@/components/utils/ProgressLoading";
import { isAdminOrAccountant } from "../../../utils/permissions/roleChecks";
import useAffiliateUserAuth from "@/components/hooks/useAffiliateUserAuth";
import { useDebounce } from "@/hooks/useDebounce";

interface Filters {
  bookingStatus: string;
  bookingAgent: string;
  bookingSource: string;
  travelDateRange:
    | "All Time"
    | "Next 90 Days"
    | "Next 30 Days"
    | "Next 7 Days"
    | "Today"
    | undefined;
}

interface PassengerDisplayProps {
  passengers?: Array<{
    id?: string | null;
    name?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    passengerType?: string | null;
    title?: string | null;
    [key: string]: any;
  } | null> | null;
  passengerString?: string;
}

// Passenger display component with tooltip for multiple passengers
const TooltipContent = React.forwardRef<
  HTMLDivElement,
  {
    show: boolean;
    passengers: any[];
    onMouseEnter: () => void;
    onMouseLeave: () => void;
    getPassengerName: (p: any) => string;
    style?: React.CSSProperties;
  }
>(
  (
    { show, passengers, onMouseEnter, onMouseLeave, getPassengerName, style },
    ref
  ) => {
    if (!show) return null;

    return createPortal(
      <div
        ref={ref}
        className="fixed z-[9999] w-[200px] p-3 text-sm text-gray-700 bg-white border border-gray-200 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200"
        style={{
          maxHeight: "300px",
          overflowY: "auto",
          ...style,
        }}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div className="space-y-2">
          <h4 className="font-semibold text-gray-900 dark:text-white">
            Passengers ({passengers.length})
          </h4>
          <ul className="space-y-2">
            {passengers.map((passenger, index) => (
              <li
                key={`${passenger?.id || index}-${passenger?.firstName}-${
                  passenger?.lastName
                }`}
                className="flex items-start py-1"
              >
                <span className="inline-flex items-center justify-center w-6 h-6 mr-2 text-xs font-medium text-blue-600 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-100">
                  {index + 1}
                </span>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {getPassengerName(passenger)}
                  </p>
                  {passenger?.passengerType && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {passenger.passengerType}
                    </p>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>,
      document.body
    );
  }
);

TooltipContent.displayName = "TooltipContent";

export const PassengerDisplay: React.FC<PassengerDisplayProps> = ({
  passengers,
  passengerString,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [isHoveringTooltip, setIsHoveringTooltip] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  // Update tooltip position based on trigger element
  const updateTooltipPosition = useCallback(() => {
    if (triggerRef.current && showTooltip) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 4, // 4px offset from the trigger
        left: rect.left + window.scrollX + rect.width / 2, // Center horizontally
      });
    }
  }, [showTooltip]);

  // Handle click outside to close tooltip
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        !triggerRef.current?.contains(event.target as Node) &&
        !tooltipRef.current?.contains(event.target as Node) &&
        !isHoveringTooltip
      ) {
        setShowTooltip(false);
      }
    };

    if (showTooltip) {
      updateTooltipPosition();
      document.addEventListener("mousedown", handleClickOutside);
      window.addEventListener("resize", updateTooltipPosition);
      window.addEventListener("scroll", updateTooltipPosition, true);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", updateTooltipPosition);
      window.removeEventListener("scroll", updateTooltipPosition, true);
    };
  }, [showTooltip, isHoveringTooltip, updateTooltipPosition]);

  // Handle case when we only have a passenger string
  if (passengerString) {
    return (
      <div className="flex items-center">
        <span className="truncate max-w-[150px]">{passengerString}</span>
      </div>
    );
  }

  // Handle case when we have no data at all
  if (
    !passengers ||
    passengers.length === 0 ||
    !passengers.some((p) => p !== null)
  ) {
    return (
      <div className="flex items-center text-gray-400">
        <span>No passenger data</span>
      </div>
    );
  }

  // Filter out null values and get valid passengers
  const validPassengers = passengers.filter(
    (p): p is NonNullable<typeof p> => p !== null
  );
  const firstPassenger = validPassengers[0];
  const hasMultiple = validPassengers.length > 1;

  const passengerName =
    firstPassenger?.name ||
    `${firstPassenger?.title || ""} ${firstPassenger?.firstName || ""} ${
      firstPassenger?.lastName || ""
    }`.trim() ||
    "Passenger";

  const getPassengerName = (passenger: NonNullable<typeof firstPassenger>) => {
    return (
      passenger?.name ||
      `${passenger?.title || ""} ${passenger?.firstName || ""} ${
        passenger?.lastName || ""
      }`.trim() ||
      "Passenger"
    );
  };

  return (
    <div className="relative inline-block">
      <div className="flex items-center p-1 -m-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
        <button
          ref={triggerRef}
          type="button"
          className="truncate max-w-[150px] text-left cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-colors focus:outline-none"
          onClick={(e) => {
            e.stopPropagation();
            setShowTooltip(!showTooltip);
          }}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => {
            if (!isHoveringTooltip) {
              const timer = setTimeout(() => {
                setShowTooltip(false);
              }, 200);
              return () => clearTimeout(timer);
            }
          }}
        >
          {passengerName}
          {hasMultiple ? ` + ${passengers.length - 1} More` : ""}
        </button>
      </div>

      <TooltipContent
        ref={tooltipRef}
        show={showTooltip && hasMultiple}
        passengers={validPassengers}
        getPassengerName={getPassengerName}
        onMouseEnter={() => {
          setIsHoveringTooltip(true);
          setShowTooltip(true);
        }}
        onMouseLeave={() => {
          setIsHoveringTooltip(false);
          const timer = setTimeout(() => {
            if (!triggerRef.current?.matches(":hover")) {
              setShowTooltip(false);
            }
          }, 200);
          return () => clearTimeout(timer);
        }}
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
          transform: "translateX(-50%)",
          position: "absolute",
        }}
      />
    </div>
  );
};

// Constants for application status types
const TRIP_TYPES = {
  ROUND_TRIP: "Round Trip",
  ONE_WAY: "One Way",
};

const TRIP_STATUS = {
  UPCOMING: "Upcoming",
  COMPLETED: "Completed",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
  IN_PROGRESS: "In Progress",
};

const BOOKING_STATUS = {
  QUICK_HOLD: "Quick Hold",
  TIMED_OUT: "Timed Out",
  PENDING_APPROVAL: "Pending Approval",
  BOOKING_CONFIRMED: "Booking Confirmed",
  BOOKING_REJECTED: "Booking Rejected",
  CANCELLED_BY_USER: "Cancelled By User",
  CANCELLED_BY_SYSTEM: "Cancelled By System",
};

const BOOKING_SOURCE = {
  INTERNAL: "INTERNAL",
  THIRD_PARTY: "THIRD_PARTY",
};

// Visual styling for status badges
const STATUS_STYLES = {
  [TRIP_STATUS.UPCOMING]: "bg-blue-200 text-blue-800",
  [TRIP_STATUS.COMPLETED]: "bg-green-200 text-green-800",
  [TRIP_STATUS.PENDING]: "bg-yellow-200 text-yellow-800",
  [TRIP_STATUS.NOT_VALID]: "bg-red-200 text-red-800",
  [TRIP_STATUS.IN_PROGRESS]: "bg-purple-200 text-purple-800",
};

const BOOKING_STATUS_STYLES = {
  [BOOKING_STATUS.QUICK_HOLD]: "bg-yellow-200 text-yellow-800",
  [BOOKING_STATUS.TIMED_OUT]: "bg-gray-200 text-gray-800",
  [BOOKING_STATUS.PENDING_APPROVAL]: "bg-blue-200 text-blue-800",
  [BOOKING_STATUS.BOOKING_CONFIRMED]: "bg-green-200 text-green-800",
  [BOOKING_STATUS.BOOKING_REJECTED]: "bg-red-200 text-red-800",
  [BOOKING_STATUS.CANCELLED_BY_USER]: "bg-red-200 text-red-800",
  [BOOKING_STATUS.CANCELLED_BY_SYSTEM]: "bg-red-200 text-red-800",
};

const SOURCE_STYLES = {
  [BOOKING_SOURCE.INTERNAL]: "bg-blue-200 text-blue-800",
  [BOOKING_SOURCE.THIRD_PARTY]: "bg-purple-200 text-purple-800",
};

// Map backend booking source to user-friendly labels
const SOURCE_LABELS: Record<string, string> = {
  [BOOKING_SOURCE.INTERNAL]: "Internal",
  [BOOKING_SOURCE.THIRD_PARTY]: "Third-Party",
};

// Map backend status/action to user-friendly labels for display and badge color
const STATUS_LABELS: Record<string, string> = {
  UPCOMING: "Upcoming",
  COMPLETED: "Completed",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
  IN_PROGRESS: "In Progress",
  TIMED_OUT: "Timed Out",
};

const BOOKING_ACTION_LABELS: Record<string, string> = {
  BOOKING_CONFIRMED: "Booking Confirmed",
  PENDING_APPROVAL: "Pending Approval",
  QUICK_HOLD: "Quick Hold",
  BOOKING_REJECTED: "Booking Rejected",
  TIMED_OUT: "Timed Out",
  CANCELLED_BY_USER: "Cancelled By User",
  CANCELLED_BY_SYSTEM: "Cancelled By System",
};

// Filter options for dropdown selects
const FILTER_OPTIONS = {
  BookingStatus: ["All", ...Object.values(BOOKING_STATUS)],
  BookingSource: ["All", ...Object.values(SOURCE_LABELS)],
  AgentNames: [
    "All",
    "Alex Wong",
    "Sophia Miller",
    "William Johnson",
    "Emily Wilson",
    "Daniel Park",
    "Kevin Chen",
  ],
  FlightDate: [
    "Today",
    "Next 7 Days",
    "Next 30 Days",
    "Next 90 Days",
    "All Time",
  ],
};

// Table column headers
const TABLE_HEADERS = [
  "ID",
  "Booking Reference",
  "Flight Date",
  "Passenger",
  "Route",
  "Trip Type",
  "Carrier",
  "Price",
  "Trip Status",
  "Booking Status",
  "Booking Source",
  "Employee",
  "Issued On",
  "Actions",
];

/**
 * Utility function to parse date strings into Date objects
 * @param {string} dateStr - Date string in various formats
 * @returns {Date} JavaScript Date object
 */
const parseDate = (dateStr: string) => {
  // If the date is already in ISO format or similar, just create a new Date
  if (dateStr.includes("-") || dateStr.includes("/")) {
    return new Date(dateStr);
  }

  // For format "MMM D, YYYY"
  const months: { [key: string]: number } = {
    Jan: 0,
    Feb: 1,
    Mar: 2,
    Apr: 3,
    May: 4,
    Jun: 5,
    Jul: 6,
    Aug: 7,
    Sep: 8,
    Oct: 9,
    Nov: 10,
    Dec: 11,
  };

  try {
    const [month, day, year] = dateStr.split(" ");
    return new Date(
      parseInt(year),
      months[month],
      parseInt(day.replace(",", ""))
    );
  } catch (error) {
    console.error("Error parsing date:", dateStr, error);
    return new Date(); // Return current date as fallback
  }
};

// Utility to normalize booking status/action strings for display
function normalizeBookingStatus(status: string): string {
  if (!status) return "-";
  if (status === "BOOKING_CONFIRMED") return "Booking Confirmed";
  if (status === "CANCELLED_BY_USER" || status === "CANCELLED_BY_SYSTEM")
    return "Cancelled";
  return status
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Determines trip status based on booking status and travel date
 * @param {Object} booking - Booking object with bookingAction, bookingStatus and date
 * @returns {string} The determined trip status
 */
// const determineTripStatus = (booking: Booking) => {
//   const { bookingAction, status, date } = booking;

//   // If date is not available or invalid, return PENDING
//   if (!date || date === "-") {
//     return TRIP_STATUS.PENDING;
//   }

//   const travelDate = parseDate(date);
//   const currentDate = new Date();

//   // Reset hours to compare just the dates
//   currentDate.setHours(0, 0, 0, 0);
//   travelDate.setHours(0, 0, 0, 0);

//   // First check if booking is cancelled, rejected or timed out - these are always NOT_VALID
//   if (
//     status === "CANCELLED_BY_USER" ||
//     status === "CANCELLED_BY_SYSTEM" ||
//     status === "BOOKING_REJECTED" ||
//     status === "TIMED_OUT" ||
//     bookingAction === BOOKING_STATUS.REJECTED ||
//     bookingAction === BOOKING_STATUS.TIMED_OUT ||
//     bookingAction === BOOKING_STATUS.CANCELLED
//   ) {
//     return TRIP_STATUS.NOT_VALID;
//   }

//   // For pending approvals and quick holds, status is PENDING
//   if (
//     status === "PENDING_APPROVAL" ||
//     status === "QUICK_HOLD" ||
//     bookingAction === BOOKING_STATUS.PENDING_APPROVAL ||
//     bookingAction === BOOKING_STATUS.QUICK_HOLD
//   ) {
//     return TRIP_STATUS.PENDING;
//   }

//   // For confirmed bookings, status depends on the travel date
//   if (
//     status === "BOOKING_CONFIRMED" ||
//     bookingAction === BOOKING_STATUS.BOOKING_CONFIRMED
//   ) {
//     if (travelDate.getTime() > currentDate.getTime()) {
//       return TRIP_STATUS.UPCOMING;
//     } else if (travelDate.getTime() < currentDate.getTime()) {
//       return TRIP_STATUS.COMPLETED;
//     } else {
//       return TRIP_STATUS.IN_PROGRESS;
//     }
//   }

//   // Default fallback
//   return TRIP_STATUS.PENDING;
// };

// Utility function to determine trip status based on booking status and flight date
const determineTripStatus = (
  bookingStatus: string,
  flightDateStr: string, // Expecting "DD/MM/YYYY"
  fallbackStatus: string, // Still passed but maybe unused if logic is comprehensive
  source: string // Still passed but maybe unused
) => {
  const today = new Date(); // Get current date
  const todayYear = today.getFullYear();
  const todayMonth = today.getMonth(); // 0-indexed (0=Jan, 1=Feb, ...)
  const todayDay = today.getDate();

  console.log({ flightDateStr });

  // Parse flight date components
  // Add error handling for invalid date format
  if (!flightDateStr || !flightDateStr.includes("/")) {
    console.error(
      `[determineTripStatus] Invalid flightDateStr format: ${flightDateStr}. Defaulting trip status.`
    );
    return TRIP_STATUS.NOT_VALID; // Or another appropriate default
  }
  const parts = flightDateStr.split("/");
  if (parts.length !== 3) {
    console.error(
      `[determineTripStatus] Invalid flightDateStr format: ${flightDateStr}. Defaulting trip status.`
    );
    return TRIP_STATUS.NOT_VALID;
  }
  const [dayStr, monthStr, yearStr] = parts; // Correctly parse DD/MM/YYYY format
  const flightYear = parseInt(yearStr);
  const flightMonth = parseInt(monthStr) - 1; // Adjust month to be 0-indexed
  const flightDay = parseInt(dayStr);

  // Validate parsed date components
  if (isNaN(flightYear) || isNaN(flightMonth) || isNaN(flightDay)) {
    console.error(
      `[determineTripStatus] Invalid date components parsed from: ${flightDateStr}. Defaulting trip status.`
    );
    return TRIP_STATUS.NOT_VALID;
  }

  // Compare dates component by component
  let isPast = false;
  let isToday = false;
  let isFuture = false;

  if (flightYear < todayYear) {
    isPast = true;
  } else if (flightYear > todayYear) {
    isFuture = true;
  } else {
    // Years are the same, compare months
    if (flightMonth < todayMonth) {
      isPast = true;
    } else if (flightMonth > todayMonth) {
      isFuture = true;
    } else {
      // Years and months are the same, compare days
      if (flightDay < todayDay) {
        isPast = true;
      } else if (flightDay > todayDay) {
        isFuture = true;
      } else {
        // Years, months, and days are the same
        isToday = true;
      }
    }
  }

  // Map booking statuses to trip statuses based on raw string values and date comparison
  switch (bookingStatus) {
    case "BOOKING_CONFIRMED": // Compare against string key
      if (isPast) return TRIP_STATUS.COMPLETED;
      if (isToday) return TRIP_STATUS.IN_PROGRESS;
      if (isFuture) return TRIP_STATUS.UPCOMING;
      break; // Should only be reached if date comparison somehow fails for all 3

    case "PENDING_APPROVAL": // Compare against string key
    case "QUICK_HOLD": // Compare against string key
      // For pending/hold, status doesn't change based on date (it's just pending until resolved)
      // However, if the flight date is past, it implies it was never approved/confirmed.
      // Let's treat past PENDING/HOLD as NOT_VALID for clarity, unless specific business logic dictates otherwise.
      if (isPast) {
        console.warn(
          `Booking status ${bookingStatus} for past date ${flightDateStr}. Marking as NOT_VALID.`
        );
        return TRIP_STATUS.NOT_VALID;
      }
      return TRIP_STATUS.PENDING; // Otherwise, keep as Pending

    case "BOOKING_REJECTED": // Compare against string key
    case "TIMED_OUT": // Compare against string key
    case "CANCELLED_BY_USER": // Compare against string key
    case "CANCELLED_BY_SYSTEM": // Compare against string key
      return TRIP_STATUS.NOT_VALID;

    default:
      console.warn(
        `Unhandled bookingStatus: ${bookingStatus} for date ${flightDateStr}. Defaulting to NOT_VALID.`
      );
      return TRIP_STATUS.NOT_VALID;
  }

  // Fallback if status is confirmed but date logic fails
  console.warn(
    `Booking ${bookingStatus} on ${flightDateStr} did not match any date condition (Past/Today/Future). Defaulting to NOT_VALID.`
  );
  return TRIP_STATUS.NOT_VALID;
};
/**
 * Component for dropdown filters
 * @param {Object} props - Component properties
 * @param {string} props.label - Label for the dropdown
 * @param {Array<string>} [props.options] - Options for the dropdown
 * @param {Function} [props.onChange] - Change handler
 */
// FilterSelect replaced by FilterDropdown

/**
 * Component for date range filter
 * @param {Object} props - Component properties
 * @param {string} props.label - Label for the date filter
 * @param {Function} [props.onChange] - Change handler
 */
// DateRangeFilter replaced by FilterDropdown

/**
 * Bookings filter card component
 * @param {Object} props - Component properties
 * @param {Function} props.onApplyFilters - Handler for applying filters
 * @param {Function} props.onClearFilters - Handler for clearing filters
 */
const FilterCard = ({
  filters,
  setFilters,
  onApplyFilters,
  onClearFilters,
  filterOptions,
}: {
  filters: any;
  setFilters: (f: any) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  filterOptions: any;
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg pt-5 pb-7 mb-5 sm:px-3 flex items-end space-x-4">
      <FilterDropdown
        label="Booking Status"
        options={filterOptions.BookingStatus}
        value={filters.bookingStatus}
        onChange={(val) =>
          setFilters((prev: any) => ({ ...prev, bookingStatus: val }))
        }
      />
      <FilterDropdown
        label="Employee Name"
        options={filterOptions.AgentNames}
        value={filters.bookingAgent}
        onChange={(val) =>
          setFilters((prev: any) => ({ ...prev, bookingAgent: val }))
        }
      />
      <FilterDropdown
        label="Booking Source"
        options={filterOptions.BookingSource}
        value={filters.bookingSource}
        onChange={(val) =>
          setFilters((prev: any) => ({ ...prev, bookingSource: val }))
        }
      />
      <FilterDropdown
        label="Travel Date"
        options={filterOptions.FlightDate}
        value={filters.travelDateRange || "All Time"}
        onChange={(val) => {
          setFilters((prev: any) => ({ ...prev, travelDateRange: val }));
        }}
      />
      <button
        className="h-[45px] bg-blue-500 text-white text-sm hover:bg-blue-600 transition duration-300 py-2 px-4 rounded-lg"
        onClick={onClearFilters}
      >
        Reset Filters
      </button>
    </div>
  );
};

/**
 * Component for individual booking row
 * @param {Object} props - Component properties
 * @param {Object} props.booking - Booking data
 * @param {Function} props.onEdit - Handler for edit action
 */
const BookingTableRow = ({
  booking,
  onEdit,
}: {
  booking: Booking;
  onEdit: (bookingId: string) => void;
}) => {
  const {
    reference,
    referenceNumber,
    date,
    route,
    airline,
    passenger,
    passengers,
    travelers,
    agent,
    tripType,
    bookingSource,
    status,
    bookingAction,
    source,
    price,
    createdAt,
  } = booking;

  // Normalize trip type from booking data
  const normalizedTripType = tripType?.toString().toUpperCase().trim();
  const isRoundTrip =
    normalizedTripType === "ROUND_TRIP" || normalizedTripType === "ROUND TRIP";
  const isOneWay =
    normalizedTripType === "ONE_WAY" || normalizedTripType === "ONE WAY";

  // Format route based on trip type
  const formattedRoute = isRoundTrip
    ? route.replace("→", "↔")
    : isOneWay
    ? route.replace("↔", "→")
    : route; // fallback

  // Get the current user
  const currentUser = useAppSelector(selectUser);
  const user = currentUser as StoredUser;

  // Prepare passengers data for display
  const getPassengersData = () => {
    // If we have travelers array, use it
    if (
      booking.travelers &&
      Array.isArray(booking.travelers) &&
      booking.travelers.length > 0
    ) {
      return booking.travelers
        .map((t) => {
          if (!t?.traveler) return null;
          const traveler = t.traveler;
          return {
            ...traveler,
            // Fallback to name from traveler object if available
            name:
              traveler.firstName && traveler.lastName
                ? `${traveler.firstName} ${traveler.lastName}`
                : traveler.firstName || traveler.lastName || "Passenger",
          };
        })
        .filter(Boolean); // Remove any null entries
    }

    // Fallback to the passenger string if available
    if (passenger) {
      return [
        {
          name: passenger,
          firstName: passenger.split(" ")[0],
          lastName: passenger.split(" ").slice(1).join(" "),
        },
      ];
    }

    // No passenger data available
    return [];
  };

  const passengersData = getPassengersData();

  return (
    <tr className="border-b border-gray-300 dark:border-gray-700">
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-semibold text-blue-400">
        {reference}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {referenceNumber}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {date}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {travelers && travelers?.length > 1 ? (
          <PassengerDisplay passengers={passengersData} />
        ) : (
          <div className="group">
            {/* Truncated Name */}
            <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
              {passenger || "-"}
            </div>
            <div className="relative">
              {/* Tooltip with Full Name */}
              <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
                {passenger || "-"}
              </div>
            </div>
          </div>
        )}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {formattedRoute}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {tripType}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {airline}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {price}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            booking.tripStatus && STATUS_STYLES[booking.tripStatus]
              ? STATUS_STYLES[booking.tripStatus]
              : "bg-gray-300 text-gray-800"
          }`}
        >
          {booking.tripStatus || "Pending"}
        </span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            BOOKING_STATUS_STYLES[BOOKING_ACTION_LABELS[booking.status]] ||
            "bg-gray-300 text-gray-800"
          }`}
        >
          {normalizeBookingStatus(booking.status)}
        </span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <span
          className={`px-3 py-1 rounded-md text-xs font-medium ${
            SOURCE_STYLES[booking.source] || "bg-gray-300 text-gray-800"
          }`}
        >
          {booking.source === "THIRD_PARTY"
            ? "Third-Party"
            : booking.source === "INTERNAL"
            ? "Internal"
            : booking.source}
        </span>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <div className="group">
          {/* Truncated Name */}
          <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
            {agent || "-"}
          </div>
          <div className="relative">
            {/* Tooltip with Full Name */}
            <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
              {agent || "-"}
            </div>
          </div>
        </div>
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        {createdAt}
      </td>
      <td className="py-4 ps-2 pe-4 whitespace-nowrap font-medium text-gray-800 dark:text-gray-100">
        <div className="flex space-x-2">
          {booking.status !== "PENDING_APPROVAL" ? (
            <button
              className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300"
              onClick={() => onEdit(reference)}
              aria-label={`Edit booking ${reference}`}
            >
              <Edit size={18} />
            </button>
          ) : [
              "master_owner",
              "agency_owner",
              "master_admin",
              "agency_admin",
              "master_accountant",
              "agency_accountant",
            ].includes(user?.roleType as string) ? (
            <button
              className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300"
              onClick={() => onEdit(reference)}
              aria-label={`Edit pending booking ${reference}`}
            >
              <Clock size={18} />
            </button>
          ) : (
            <div className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300">
              <Clock size={18} />
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

/**
 * Bookings table component
 * @param {Object} props - Component properties
 * @param {Array} props.bookings - Array of booking objects
 * @param {Function} props.onEditBooking - Handler for edit booking action
 */
const BookingsTable = ({
  bookings,
  onEditBooking,
  hasMore,
  loadingMore,
  observerRef,
}: {
  bookings: Booking[];
  onEditBooking: (bookingId: string) => void;
  hasMore: boolean;
  loadingMore: boolean;
  observerRef: any; // Using any to accommodate the ref function from useInView
}) => (
  <div className="overflow-x-auto custom-scrollbar max-h-[calc(100vh-400px)]">
    <table className="table-auto w-full">
      {/* Table header */}
      <thead className="text-xs font-semibold capitalize text-gray-800 dark:text-gray-50 bg-gray-50 dark:bg-gray-900/20 border-t border-b border-gray-200 dark:border-gray-700 text-left sticky -top-0.5 z-[5]">
        <tr>
          {TABLE_HEADERS.map((header) => (
            <th
              key={header}
              className="pl-2 p-4 whitespace-nowrap font-semibold text-sm bg-gray-300 dark:bg-gray-700"
            >
              {header}
            </th>
          ))}
        </tr>
      </thead>
      {/* Table body */}
      <tbody className="text-sm divide-y divide-gray-200 dark:divide-gray-700">
        {bookings.map((booking, index) => (
          <BookingTableRow
            key={`${booking.reference}-${index}`}
            booking={booking}
            onEdit={onEditBooking}
          />
        ))}
      </tbody>
    </table>
    {bookings.length === 0 && (
      <div className="text-center py-8">
        <p className="text-lg dark:text-gray-400">No Bookings Found</p>
      </div>
    )}

    <div className="w-full">
      {loadingMore && (
        <div className="py-4">
          <div className="flex justify-center">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse delay-75"></div>
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse delay-150"></div>
            </div>
          </div>
        </div>
      )}
      {!loadingMore && hasMore && (
        <div ref={observerRef} className="w-full h-10" />
      )}
    </div>
  </div>
);

/**
 * BookingDetails component for displaying detailed information about a booking when expanded
 * @param {Object} props - Component properties
 * @param {boolean} props.isOpen - Whether the details section is open
 * @param {Object} props.booking - Booking data to display
 */
const BookingDetails = ({
  isOpen,
  booking,
}: {
  isOpen: boolean;
  booking: Booking;
}) => {
  // Get itinerary from Redux store
  const reduxItinerary = useAppSelector(selectItinerary);

  if (!isOpen || !booking) return null;

  return (
    <div className="bg-gray-750 p-6 rounded-b-lg border-t border-gray-700">
      <div className="grid grid-cols-3 gap-8">
        {/* Flight Details */}
        <div className="col-span-2">
          <h3 className="font-semibold text-white mb-4">Flight Information</h3>

          {/* Outbound Flight */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <div className="bg-red-500/20 dark:text-white text-gray-700 px-3 py-1 rounded-lg text-sm font-semibold">
                OUTBOUND
              </div>
              <span className="ml-3 text-sm text-gray-300">{booking.date}</span>
            </div>

            <div className="flex items-center">
              <div className="flex flex-col items-center">
                <span className="text-lg font-semibold">10:30</span>
                <span className="text-xs text-gray-400">
                  {booking.route.split("→")[0].trim()}
                </span>
              </div>

              <div className="mx-3 flex flex-col items-center">
                <div className="w-20 h-px bg-gray-500 relative">
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-400">
                    15h 45m
                  </div>
                </div>
                <span className="text-xs text-gray-400 mt-2">Direct</span>
              </div>

              <div className="flex flex-col items-center">
                <span className="text-lg font-semibold">2:15</span>
                <span className="text-xs text-gray-400">
                  {booking.route.split("→")[1].trim()}
                </span>
              </div>

              <div className="ml-auto">
                <span className="text-sm">{booking.airline}</span>
                <div className="text-xs text-gray-400">Boeing 787-8</div>
              </div>
            </div>
          </div>

          {/* Return Flight (Only show for Round Trip) */}
          {(reduxItinerary === "round trip" ||
            (!reduxItinerary &&
              booking.tripType === TRIP_TYPES.ROUND_TRIP)) && (
            <div>
              <div className="flex items-center mb-3">
                <div className="bg-blue-500/20 dark:text-white text-gray-700 px-3 py-1 rounded-lg text-sm font-semibold">
                  RETURN
                </div>
                <span className="ml-3 text-sm text-gray-300">
                  {booking.date}
                </span>
              </div>

              <div className="flex items-center">
                <div className="flex flex-col items-center">
                  <span className="text-lg font-semibold">4:30</span>
                  <span className="text-xs text-gray-400">
                    {booking.route.split("→")[1].trim()}
                  </span>
                </div>

                <div className="mx-3 flex flex-col items-center">
                  <div className="w-20 h-px bg-gray-500 relative">
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-400">
                      16h 20m
                    </div>
                  </div>
                  <span className="text-xs text-gray-400 mt-2">Direct</span>
                </div>

                <div className="flex flex-col items-center">
                  <span className="text-lg font-semibold">8:50</span>
                  <span className="text-xs text-gray-400">
                    {booking.route.split("→")[0].trim()}
                  </span>
                </div>

                <div className="ml-auto">
                  <span className="text-sm">{booking.airline}</span>
                  <div className="text-xs text-gray-400">Boeing 787-8</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Passenger Details */}
        <div>
          <h3 className="font-semibold text-white mb-4">Passenger Details</h3>
          <div className="dark:bg-gray-800 p-4 rounded-lg">
            <div className="mb-4">
              <p className="text-sm text-white font-bold">Traveler Name</p>
              <p className="text-sm text-gray-300">Mr. {booking.passenger}</p>
            </div>
            <div className="mb-4">
              <p className="text-sm text-white font-bold">Passport</p>
              <p className="text-sm text-gray-300">********* (United States)</p>
            </div>
            <div className="mb-4">
              <p className="text-sm text-white font-bold">Contact</p>
              <p className="text-sm text-gray-300">+1 (555) 123-4567</p>
              <p className="text-sm text-gray-300"><EMAIL></p>
            </div>
            <div>
              <p className="text-sm text-white font-bold">Booking Class</p>
              <p className="text-sm text-gray-300">Economy • 23kg Baggage</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Navigation tabs component
 * @param {Object} props - Component properties
 * @param {string} props.activeTab - Currently active tab
 * @param {Function} props.onTabChange - Handler for tab change
 * @param {number} props.totalBookings - Total number of bookings
 * @param {boolean} props.showBookingRequestTab - Whether to show the Booking Request tab
 */
const TabNavigation = ({
  activeTab,
  onTabChange,
  totalBookings,
  filteredBookings,
  showBookingRequestTab,
}: {
  activeTab: string;
  onTabChange: (tab: string) => void;
  totalBookings: number;
  filteredBookings: number;
  showBookingRequestTab: boolean;
}) => {
  // Tab options
  const tabs = [
    "All",
    TRIP_STATUS.COMPLETED,
    TRIP_STATUS.IN_PROGRESS,
    TRIP_STATUS.UPCOMING,
    TRIP_STATUS.PENDING,
    TRIP_STATUS.NOT_VALID,
  ];

  return (
    <div className="flex items-center space-x-6">
      <div className="flex items-center">
        <span className="text-2xl font-bold text-red-500 mr-2">
          {activeTab !== "All" ? filteredBookings : totalBookings}
        </span>
        <span className="text-lg font-semibold">Total Bookings</span>
      </div>
      <div className="h-8 w-px bg-gray-300 dark:bg-gray-700"></div>
      {/* Tab Navigation */}
      <div className="flex items-center space-x-2 text-sm">
        {tabs.map((tab) => (
          <span
            key={tab}
            className={`cursor-pointer py-1 px-2 rounded ${
              activeTab === tab
                ? "bg-red-500 text-white"
                : "text-gray-700 hover:bg-gray-300 dark:text-gray-300 dark:hover:bg-gray-700"
            }`}
            onClick={() => onTabChange(tab)}
          >
            {tab}
          </span>
        ))}

        {/* Only show Booking Request tab for Admin and Accountant roles */}
        {showBookingRequestTab && (
          <>
            <div className="h-8 w-px bg-gray-300 dark:bg-gray-700 mx-2"></div>
            <span
              className={`cursor-pointer py-1 px-2 rounded ${
                activeTab === "Booking Request"
                  ? "bg-red-500 text-white"
                  : "text-gray-700 hover:bg-gray-300 dark:text-gray-300 dark:hover:bg-gray-700"
              }`}
              onClick={() => onTabChange("Booking Request")}
            >
              Booking Request
            </span>
          </>
        )}
      </div>
    </div>
  );
};

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  loading?: boolean;
  error?: string;
}

const SearchInput = React.memo(
  ({
    value,
    onChange,
    className = "",
    placeholder = "Search bookings...",
    loading = false,
    error = "",
  }: SearchInputProps) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const debouncedValue = useDebounce(value, 150);

    const handleClear = useCallback(() => {
      onChange("");
      inputRef.current?.focus();
    }, [onChange]);

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    // Emit debounced value to parent when it changes
    useEffect(() => {
      if (debouncedValue !== value) {
        onChange(debouncedValue);
      }
    }, [debouncedValue]);

    // Keep the input focused even when parent components re-render
    useEffect(() => {
      if (document.activeElement !== inputRef.current) {
        inputRef.current?.focus();
      }
    }, []);

    return (
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          className={`w-full mx-auto bg-gray-200 dark:bg-gray-700 dark:text-white rounded-lg py-2 pl-10 pr-4 md:w-64 border-none focus:outline-none focus:ring-2 focus:ring-red-500 hover:ring-2 hover:ring-red-500 hover:border-red-500 ${className} ${
            error ? "border-red-500" : ""
          }`}
          aria-label="Search bookings"
          aria-invalid={!!error}
          aria-describedby={error ? "search-error" : undefined}
          disabled={loading}
        />
        <Search className="absolute left-3 top-2.5 text-gray-400" size={20} />
        {value && (
          <button
            type="button"
            className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
            onClick={handleClear}
            aria-label="Clear search"
          >
            <X size={20} />
          </button>
        )}
        {error && (
          <div id="search-error" className="mt-1 text-sm text-red-600">
            {error}
          </div>
        )}
      </div>
    );
  }
);

/**
 * Main Bookings component
 */
const MyBookingsList = () => {
  // State hooks
  const router = useRouter();
  const dispatch = useDispatch();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [activeTab, setActiveTab] = useState("All");
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchError, setSearchError] = useState<string>("");
  const [totalBookings, setTotalBookings] = useState(0);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [agentNames, setAgentNames] = useState<string[]>([]);
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  // const [filteredBookings, setFilteredBooking] = useState<Booking[]>([]);

  const bookingResult = useSelector(
    (state: RootState) => state.bookingConfirmation.bookingResult
  );
  const travelerData = useSelector(
    (state: RootState) => state.bookingConfirmation.travelerData
  );
  const fullTicket = useSelector(
    (state: RootState) => state.bookingConfirmation.fullTicket
  );
  const passengerCounts = useSelector(
    (state: RootState) => state.bookingConfirmation.passengerCounts
  );
  const bookingType = useSelector(
    (state: RootState) => state.ticketSearchForm.value.bookingType
  );
  const itinerary = useSelector(
    (state: RootState) => state.ticketSearchForm.value.itinerary
  );

  // Use IntersectionObserver via useInView hook
  const { ref, inView } = useInView({
    threshold: 0.5, // Trigger when 50% of the element is visible
    triggerOnce: false,
    rootMargin: "0px 0px 0px 0px", // No extra margin
    initialInView: false,
  });
  const [filters, setFilters] = useState<Filters>({
    bookingStatus: "All",
    bookingAgent: "All",
    bookingSource: "All",
    travelDateRange: "All Time",
  });

  // Get itinerary from Redux store for the main component
  const reduxItinerary = useAppSelector(selectItinerary);

  // Ref to track if we're currently loading more bookings
  const isLoadingMoreRef = useRef(false);

  // Map display status to enum value for API
  const mapStatusToEnum = (displayStatus: string): string | undefined => {
    if (displayStatus === "All") return undefined;
    const entry = Object.entries(BOOKING_STATUS).find(
      ([_, value]) => value === displayStatus
    );
    return entry ? entry[0] : undefined;
  };
  const mapSourceToApiValue = (displayValue: string): string | undefined => {
    if (displayValue === "All") return undefined;
    const entry = Object.entries(SOURCE_LABELS).find(
      ([_, value]) => value === displayValue
    );
    return entry ? entry[0] : undefined;
  };

  // Function to load more bookings when scrolling
  const loadMoreBookings = async () => {
    // Don't load more if already loading or no more data to load
    if (isLoadingMoreRef.current || loadingMore || !nextCursor || !hasMore) {
      return;
    }

    // Set loading flag in ref to prevent multiple simultaneous calls
    isLoadingMoreRef.current = true;
    setLoadingMore(true);

    // Add a small delay to ensure the loading indicator is visible
    await new Promise((resolve) => setTimeout(resolve, 500));

    try {
      // Get user ID and account type to pass to the server
      let userId;
      let accountType;

      if (currentUser.isLogin) {
        const user = currentUser as StoredUser;
        userId = user.id;

        // Determine account type based on user role and role type
        if (user.role === "agency") {
          if (user.roleType === "agency_owner") {
            accountType = "agencyOwner";
          } else {
            accountType = "agencyUser";
          }
        } else if (user.role === "master") {
          if (user.roleType === "master_owner") {
            accountType = "masterOwner";
          } else {
            accountType = "masterUser";
          }
        } else {
          accountType = "affiliate";
        }
      }

      // Fetch more bookings using the cursor with a strict limit of 10
      // Pass userId and accountType to ensure we get all bookings for this user
      // Don't specify bookingType to get both buyer and seller bookings
      // This will include both internal and third-party bookings
      // Prepare filter parameters
      const statusFilter =
        filters.bookingStatus !== "All"
          ? mapStatusToEnum(filters.bookingStatus)
          : undefined;
      const sourceFilter =
        filters.bookingSource !== "All"
          ? mapSourceToApiValue(filters.bookingSource)
          : undefined;
      const searchTerm = debouncedSearchQuery.trim() || undefined;
      const agentNameFilter =
        filters.bookingAgent !== "All" ? filters.bookingAgent : undefined;
      const travelDateRange =
        filters.travelDateRange !== "All Time"
          ? filters.travelDateRange
          : undefined;

      const data = await getAllBookings(
        nextCursor,
        10,
        userId,
        accountType,
        undefined,
        undefined,
        statusFilter,
        sourceFilter,
        agentNameFilter,
        travelDateRange,
        searchTerm
      );

      if (data && data.success && data.results) {
        // Map the new bookings to UI format
        const newBookings = data.results.bookings || [];

        if (newBookings.length === 0) {
          setHasMore(false);
          return;
        }

        // Process each booking to add trip status
        const newMappedBookings = newBookings.map((b: any) => {
          const isDepartureRoundTrip =
            b.ticket.departureTime === b.meta?.departure?.departureTime;
          const isArrivalRoundTrip =
            b.ticket.departureTime === b.meta?.return?.departureTime;
          const price = calculatePrice(b);
          const referenceNumber = b.referenceNumber || "";
          // Create the basic booking object
          const bookingObj = {
            reference: b.requestId || b.id || "-",
            referenceNumber: referenceNumber,
            date: b.ticket?.departureDate
              ? getFormatDateTable(
                  new Date(b.ticket.departureDate).toLocaleDateString()
                )
              : b.ticket?.flightDate
              ? getFormatDateTable(
                  new Date(b.ticket.flightDate).toLocaleDateString()
                )
              : "-",
            route: (() => {
              // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights
              if (
                isReturnFlight &&
                b.meta?.return?.departureAirport &&
                b.meta?.return?.arrivalAirport
              ) {
                return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
              }

              // For departure flights (default)
              if (
                b.meta?.departure?.departureAirport &&
                b.meta?.departure?.arrivalAirport
              ) {
                return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
              }

              // Fallback
              if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
              }

              return "-";
            })(),
            paymentMethod:
              b.source === "THIRD_PARTY"
                ? "Airvilla Wallet"
                : b.payment?.method || b.paymentMethod || "-",
            passenger:
              b.travelers &&
              Array.isArray(b.travelers) &&
              b.travelers.length > 0
                ? (() => {
                    const tr = b.travelers[0].traveler || b.travelers[0];
                    return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                  })()
                : "-",
            // Use the bookedByAgentName if available, otherwise fall back to other agent name fields
            agent:
              b.source === "INTERNAL"
                ? b.agent !== "Unknown Agent"
                  ? b.agent
                  : b.meta?.buyerAgentName !== "Unknown Agent"
                  ? b.meta?.buyerAgentName
                  : b.meta?.agentName !== "Unknown Agent"
                  ? b.meta?.agentName
                  : b.bookedByAgentName !== "Unknown Creator"
                  ? b.bookedByAgentName
                  : "-"
                : b.agent !== "Unknown Agent"
                ? b.agent
                : b.meta?.buyerAgentName !== "Unknown Agent"
                ? b.meta?.buyerAgentName
                : b.meta?.agentName !== "Unknown Agent"
                ? b.meta?.agentName
                : b.bookedByAgentName !== "Unknown Creator"
                ? b.bookedByAgentName
                : "-",
            // Show the agent who created the booking in the Seller Agency column
            sellerAgency:
              b.meta?.bookedByAgentName ||
              b.meta?.agentName ||
              b.agencyAgentId ||
              b.agent ||
              (b.user?.firstName && b.user.lastName
                ? `${b.user.firstName} ${b.user.lastName}`
                : b.meta?.agencyName || "-"),
            tripType: (() => {
              // Get trip type directly from the booking object
              const tripType = b.tripType || b.ticket?.tripType || b.type || "";

              // Normalize the trip type
              const normalizedTripType = String(tripType).toUpperCase().trim();

              // Map to display values
              if (
                normalizedTripType === "ROUND_TRIP" ||
                normalizedTripType === "ROUND TRIP"
              ) {
                return TRIP_TYPES.ROUND_TRIP;
              } else if (
                normalizedTripType === "ONE_WAY" ||
                normalizedTripType === "ONE WAY"
              ) {
                return TRIP_TYPES.ONE_WAY;
              }

              // Default to ONE_WAY if we can't determine the type
              return TRIP_TYPES.ONE_WAY;
            })(),
            status: b.status || "-",
            bookingAction: b.initialHoldType || b.type || "-",
            source: b.source || "-",
            bookingSource: b.bookingSource || b.source || "-",
            bookingStatus: b.status || "-",
            price:
              price ||
              (b.totalAmount && b.totalAmount !== "0"
                ? b.totalAmount
                : b.bookedSeats && b.bookedSeats.length > 0
                ? b.bookedSeats[0].totalPrice
                : b.ticket?.price || "-"),
            airline: (() => {
              // Use the same logic as route to determine if this is a return flight
              const isReturnFlight =
                b.meta?.return?.departureTime &&
                b.ticket?.departureTime === b.meta.return.departureTime;

              // For return flights, use return flight's carrier
              if (isReturnFlight && b.meta?.return?.carrier) {
                return b.meta.return.carrier;
              }

              // For departure flights or fallback
              return b.meta?.departure?.carrier || b.meta?.carrier || "-";
            })(),
            createdAt: b.createdAt
              ? getFormatDateTable(new Date(b.createdAt).toLocaleDateString())
              : "-",
            updatedAt: b.updatedAt
              ? getFormatDateTable(new Date(b.updatedAt).toLocaleDateString())
              : "-",
            cancellationReason: b.cancellationReason || "",
            expiresAt: b.expiresAt || "",
            paymentCompletedAt: b.paymentCompletedAt || "",
            timerDuration: b.timerDuration || "",
            timerStartedAt: b.timerStartedAt || "",
            totalSeats: b.totalSeats || "",
          };

          // Calculate trip status
          let tripStatus;
          try {
            tripStatus = determineTripStatus(
              bookingObj.bookingStatus,
              bookingObj.date,
              bookingObj.source,
              bookingObj.bookingStatus
            );
          } catch (error) {
            console.error(
              `Error calculating trip status for booking ${bookingObj.reference}:`,
              error
            );
            tripStatus = TRIP_STATUS.PENDING;
          }

          return {
            ...bookingObj,
            tripStatus,
          };
        });

        // Update state with new bookings
        setBookings((prevBookings) => [...prevBookings, ...newMappedBookings]);
        setTotalBookings(data.results.bookingsTotal || 0);

        // Update cursor for next page
        const newCursor = data.results.nextCursor || null;
        setNextCursor(newCursor);

        // Check if there are more bookings to load
        setHasMore(!!newCursor);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error loading more bookings:", error);
    } finally {
      // Reset loading states
      setLoadingMore(false);
      isLoadingMoreRef.current = false;
    }
  };

  // Track the last time we loaded more bookings
  const lastLoadTimeRef = useRef<number>(0);

  // Effect to load more bookings when user scrolls to the bottom
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (inView && !loading && !loadingMore && hasMore && nextCursor) {
      // Check if enough time has passed since the last load (at least 1 second)
      const now = Date.now();
      const timeSinceLastLoad = now - lastLoadTimeRef.current;

      if (timeSinceLastLoad < 1000) {
        return; // Don't load if less than 1 second has passed
      }

      // Add a small delay to prevent rapid loading
      timeoutId = setTimeout(() => {
        // Check again before loading to prevent race conditions
        if (!loadingMore && hasMore && nextCursor) {
          lastLoadTimeRef.current = Date.now(); // Update last load time
          loadMoreBookings();
        }
      }, 300);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [inView, loading, loadingMore, hasMore, nextCursor]);

  // Get the current user from Redux store at the component level
  const currentUser = useAppSelector(selectUser);

  // Check if the user has Admin or Accountant role
  const hasAdminOrAccountantRole = isAdminOrAccountant(
    currentUser as StoredUser
  );

  // Fetch agent names from API
  useEffect(() => {
    const fetchAgentNames = async () => {
      try {
        const agentNames = await getBookingAgentNames();
        setAgentNames([
          "All",
          ...agentNames.filter((name): name is string => name !== "All"),
        ]);
      } catch (error) {
        console.error("Error fetching agent names:", error);
        setAgentNames(["All"]);
      }
    };
    fetchAgentNames();
  }, []);

  // Update FILTER_OPTIONS with the latest agentNames
  const filterOptions = useMemo(
    () => ({
      ...FILTER_OPTIONS,
      BookingStatus: ["All", ...Object.values(BOOKING_STATUS)],
      BookingSource: ["All", ...Object.values(SOURCE_LABELS)],
      AgentNames: agentNames.length > 0 ? agentNames : ["All"],
    }),
    [agentNames, BOOKING_STATUS, SOURCE_LABELS]
  );

  // Fetch initial bookings data from API
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        setBookings([]);
        setNextCursor(null);
        setHasMore(true);

        try {
          // Get user ID and account type to pass to the server
          let userId;
          let accountType;

          if (currentUser.isLogin) {
            const user = currentUser as StoredUser;
            userId = user.id;

            // Determine account type based on user role and role type
            if (user.role === "agency") {
              if (user.roleType === "agency_owner") {
                accountType = "agencyOwner";
              } else {
                accountType = "agencyUser";
              }
            } else if (user.role === "master") {
              if (user.roleType === "master_owner") {
                accountType = "masterOwner";
              } else {
                accountType = "masterUser";
              }
            } else {
              accountType = "affiliate";
            }
          }

          // Fetch initial bookings with a strict limit of 20
          // Pass userId and accountType to ensure we get all bookings for this user
          // Don't specify bookingType to get both buyer and seller bookings
          // This will include both internal and third-party bookings

          // Prepare filter parameters
          const statusFilter =
            filters.bookingStatus !== "All"
              ? mapStatusToEnum(filters.bookingStatus)
              : undefined;
          const sourceFilter =
            filters.bookingSource !== "All"
              ? mapSourceToApiValue(filters.bookingSource)
              : undefined;
          const searchTerm = debouncedSearchQuery.trim() || undefined;
          const agentNameFilter =
            filters.bookingAgent !== "All" ? filters.bookingAgent : undefined;
          const travelDateRange =
            filters.travelDateRange !== "All Time"
              ? filters.travelDateRange
              : undefined;

          const data = await getAllBookings(
            null,
            20,
            userId,
            accountType,
            undefined,
            undefined,
            statusFilter,
            sourceFilter,
            agentNameFilter,
            travelDateRange,
            searchTerm
          );

          if (data && data.success && data.results) {
            const bookingsData = data.results.bookings || [];

            // Map the bookings to UI format
            const mappedBookings = bookingsData.map((b: any) => {
              const price = calculatePrice(b);
              const isDepartureRoundTrip =
                b.ticket.departureTime === b.meta?.departure?.departureTime;
              const isArrivalRoundTrip =
                b.ticket.departureTime === b.meta?.return?.departureTime;
              const referenceNumber = b.referenceNumber || "";

              // Create the basic booking object
              const bookingObj = {
                reference: b.requestId || b.id || "-",
                referenceNumber: referenceNumber,
                date: b.ticket?.departureDate
                  ? getFormatDateTable(
                      new Date(b.ticket.departureDate).toLocaleDateString()
                    )
                  : b.ticket?.flightDate
                  ? getFormatDateTable(
                      new Date(b.ticket.flightDate).toLocaleDateString()
                    )
                  : "-",
                route: (() => {
                  // Check if this is a return flight by comparing ticket's departure time with return flight's departure time
                  const isReturnFlight =
                    b.meta?.return?.departureTime &&
                    b.ticket?.departureTime === b.meta.return.departureTime;

                  // For return flights
                  if (
                    isReturnFlight &&
                    b.meta?.return?.departureAirport &&
                    b.meta?.return?.arrivalAirport
                  ) {
                    return `${b.meta.return.departureAirport} ↔ ${b.meta.return.arrivalAirport}`;
                  }

                  // For departure flights (default)
                  if (
                    b.meta?.departure?.departureAirport &&
                    b.meta?.departure?.arrivalAirport
                  ) {
                    return `${b.meta.departure.departureAirport} ↔ ${b.meta.departure.arrivalAirport}`;
                  }

                  // Fallback
                  if (b.meta?.departureAirport && b.meta?.arrivalAirport) {
                    return `${b.meta.departureAirport} ↔ ${b.meta.arrivalAirport}`;
                  }

                  return "-";
                })(),
                paymentMethod:
                  b.source === "THIRD_PARTY"
                    ? "Airvilla Wallet"
                    : b.payment?.method || b.paymentMethod || "-",
                passenger:
                  b.travelers &&
                  Array.isArray(b.travelers) &&
                  b.travelers.length > 0
                    ? (() => {
                        const tr = b.travelers[0].traveler || b.travelers[0];
                        return tr ? `${tr.firstName} ${tr.lastName}` : "-";
                      })()
                    : "-",
                agent: b.meta?.buyerAgentName || b.agencyAgentId || "-",
                tripType: (() => {
                  // First try to get trip type from the booking object directly
                  // Based on the raw data, tripType is a direct property of the booking
                  const tripType = b.tripType || b.type || "";

                  // Normalize the trip type
                  const normalizedTripType = String(tripType)
                    .toUpperCase()
                    .trim();

                  // Map to display values
                  if (
                    normalizedTripType === "ROUND_TRIP" ||
                    normalizedTripType === "ROUND TRIP"
                  ) {
                    return TRIP_TYPES.ROUND_TRIP;
                  } else if (
                    normalizedTripType === "ONE_WAY" ||
                    normalizedTripType === "ONE WAY"
                  ) {
                    return TRIP_TYPES.ONE_WAY;
                  } else if (
                    normalizedTripType === "SUBMIT_BOOKING" ||
                    normalizedTripType === "QUICK_HOLD"
                  ) {
                    // If we only have the booking type, default to ONE_WAY
                    // This is a fallback and might need adjustment based on your business logic
                    return TRIP_TYPES.ONE_WAY;
                  }

                  // Default to ONE_WAY if we can't determine the type
                  return TRIP_TYPES.ONE_WAY;
                })(),
                status: b.status || "-",
                bookingAction: b.initialHoldType || b.type || "-",
                source: b.source || "-",
                // Ensure we capture the booking source correctly
                bookingSource: b.bookingSource || b.source || "-",
                bookingStatus: b.status || "-",
                price:
                  price ||
                  (b.totalAmount && b.totalAmount !== "0"
                    ? b.totalAmount
                    : b.bookedSeats && b.bookedSeats.length > 0
                    ? b.bookedSeats[0].totalPrice
                    : b.ticket?.price || "-"),
                airline: (() => {
                  // Use the same logic as route to determine if this is a return flight
                  const isReturnFlight =
                    b.meta?.return?.departureTime &&
                    b.ticket?.departureTime === b.meta.return.departureTime;

                  // For return flights, use return flight's carrier
                  if (isReturnFlight && b.meta?.return?.carrier) {
                    return b.meta.return.carrier;
                  }

                  // For departure flights or fallback
                  return b.meta?.departure?.carrier || b.meta?.carrier || "-";
                })(),
                createdAt: b.createdAt
                  ? getFormatDateTable(
                      new Date(b.createdAt).toLocaleDateString()
                    )
                  : "-",
                updatedAt: b.updatedAt
                  ? getFormatDateTable(
                      new Date(b.updatedAt).toLocaleDateString()
                    )
                  : "-",
                cancellationReason: b.cancellationReason || "",
                expiresAt: b.expiresAt || "",
                paymentCompletedAt: b.paymentCompletedAt || "",
                timerDuration: b.timerDuration || "",
                timerStartedAt: b.timerStartedAt || "",
                totalSeats: b.totalSeats || "",
                travelers: b.travelers || "",
              };

              // Calculate trip status based on booking data
              let tripStatus;
              try {
                tripStatus = determineTripStatus(
                  bookingObj.bookingStatus,
                  bookingObj.date,
                  bookingObj.source,
                  bookingObj.bookingStatus
                );
              } catch (error) {
                console.error(
                  `Error calculating trip status for booking ${bookingObj.reference}:`,
                  error
                );
                tripStatus = TRIP_STATUS.PENDING; // Default fallback
              }

              // Return the booking object with the calculated trip status
              return {
                ...bookingObj,
                tripStatus: tripStatus,
              };
            });

            // Update state with the mapped bookings
            setBookings(mappedBookings);
            setTotalBookings(data.results.bookingsTotal || 0);
            setNextCursor(data.results.nextCursor || null);
            setHasMore(!!data.results.nextCursor);
          } else {
            console.error(
              "Failed to fetch bookings:",
              data?.message || "Unknown error"
            );
          }
        } catch (err: any) {
          console.error("Error fetching bookings:", err);
        } finally {
          setLoading(false);
        }
      } catch (err) {
        console.error("Unexpected error:", err);
        setLoading(false);
      }
    };

    fetchBookings();
  }, [reduxItinerary, currentUser, filters, debouncedSearchQuery]);

  // Handler for applying filters
  const handleApplyFilters = () => {
    // Reset pagination and trigger re-fetch
    setBookings([]);
    setNextCursor(null);
    setHasMore(true);
    setLoading(true);
  };

  // Handler for clearing filters
  const handleClearFilters = () => {
    setFilters({
      bookingStatus: "All",
      bookingAgent: "All",
      bookingSource: "All",
      travelDateRange: undefined,
    });

    setSearchQuery("");
    setBookings([]);
    setNextCursor(null);
    setHasMore(true);
    setTotalBookings(0);
    setLoading(false);

    // Force a re-render to apply the cleared filters
    setBookings([...bookings]);
  };

  // Handler for editing booking
  const handleEditBooking = async (bookingId: string) => {
    try {
      // Show loading state
      setLoading(true);

      if (!bookingId) {
        console.error("No bookingId provided to getBookingById");
        setLoading(false);
        return;
      }

      // Fetch the full booking data from API
      const bookingData = await getBookingById(bookingId);

      if (bookingData) {
        // Store the booking data in Redux
        dispatch(
          setBookingConfirmationData({
            bookingResult: bookingData,
            // travelerData: travelerData,
            travelerData: bookingData.travelers || travelerData || [],
            ticket: bookingData,
            fullTicket: bookingData,
            passengerCounts: passengerCounts,
          })
        );

        // Navigate to the manage booking page
        // Check if this is a pending approval booking and if the user has the right role
        if (bookingData.status === "BOOKING_CONFIRMED") {
          router.push(
            `/blockseats/list/${bookingId}/checkout/booking-confirmation`
          );
        } else if (
          (bookingData.status === "PENDING_APPROVAL" ||
            bookingData.status === BOOKING_STATUS.PENDING_APPROVAL) &&
          hasAdminOrAccountantRole
        ) {
          // Use the booking-requests entry point for the approval workflow
          router.push(
            `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingId}&entryPoint=booking-requests`
          );
        } else {
          // Use the standard my-bookings entry point
          router.push(
            `/blockseats/singleTicket/payment/ManageBooking?bookingId=${bookingId}&entryPoint=my-bookings`
          );
        }
      } else {
        console.error("Failed to fetch booking data");
      }
    } catch (error) {
      console.error("Error fetching booking data:", error);
    } finally {
      // Hide loading state
      setLoading(false);
    }
  };

  // Handler for tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    // In production, this would filter bookings by status
    // api.getBookingsByStatus(tab).then(...)
  };

  // Filter bookings based on search query, active tab, and filter selections
  const filteredBookings = useMemo(() => {
    const filteredBookings = bookings.filter((booking) => {
    // Search filter
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase();
      return (
        booking.reference.toLowerCase().includes(query) ||
        booking.passenger.toLowerCase().includes(query) ||
        booking.airline.toLowerCase().includes(query) ||
        booking.route.toLowerCase().includes(query)
      );
    }

    // Apply booking source filter if selected
    if (filters.bookingSource !== "All") {
      // Check if the booking source matches the filter
      // Handle both uppercase and normalized formats
      if (
        filters.bookingSource === "Internal" &&
        booking.source !== "INTERNAL" &&
        booking.source !== "Internal"
      ) {
        return false;
      }
      if (
        filters.bookingSource === "Third-Party" &&
        booking.source !== "THIRD_PARTY" &&
        booking.source !== "Third-Party"
      ) {
        return false;
      }
    }

    // Apply booking status filter if selected
    // if (filters.bookingStatus !== "All") {
    //   console.log("Booking status filter applied:", filters.bookingStatus);
    //   console.log("Booking status:", booking.status);
    //   console.log("Booking action:", booking.bookingAction);
    //   if (
    //     booking.status !== filters.bookingStatus &&
    //     booking.bookingAction !== filters.bookingStatus
    //   ) {
    //     return false;
    //   }
    // }

    if (filters.bookingStatus !== "All") {
      // Convert the human-readable label (e.g. "Pending Approval") back to the enum key (e.g. "PENDING_APPROVAL")
      const statusKey = mapStatusToEnum(filters.bookingStatus) || filters.bookingStatus;

      // A booking matches if any of its status / action forms correspond to the selected filter,
      // either in raw enum format or normalised display format.
      const matchesStatus =
        booking.status === statusKey ||
        booking.bookingAction === statusKey ||
        normalizeBookingStatus(booking.status) === filters.bookingStatus ||
        normalizeBookingStatus(booking.bookingAction ?? "") === filters.bookingStatus;

      if (!matchesStatus) {
        return false;
      }
    }

    // Tab filter
    if (activeTab !== "All") {
      if (activeTab === "Booking Request") {
        // Logic for Booking Request - include all bookings awaiting approval
        return (
          booking.status === BOOKING_STATUS.PENDING_APPROVAL ||
          booking.bookingAction === BOOKING_STATUS.PENDING_APPROVAL ||
          booking.status === "PENDING_APPROVAL" || // legacy uppercase value
          booking.bookingAction === "PENDING_APPROVAL"
        );
      } else {
        // Filter by trip status
        return booking.tripStatus === activeTab;
      }
    }

    return true;
  });

    return filteredBookings;
  }, [bookings, activeTab, filters, debouncedSearchQuery]);

  useEffect(() => {
    if (debouncedSearchQuery.length > 0) {
      try {
        const filtered = filteredBookings.filter((booking) => {
          const searchLower = debouncedSearchQuery.toLowerCase();
          return (
            booking.reference?.toLowerCase().includes(searchLower) ||
            booking.passenger?.toLowerCase().includes(searchLower) ||
            booking.route?.toLowerCase().includes(searchLower) ||
            booking.airline?.toLowerCase().includes(searchLower)
          );
        });
      } catch (error) {
        console.error("Error filtering bookings:", error);
        setSearchError("Error occurred while filtering bookings");
      }
    } else {
      setSearchError("");
    }
  }, [filteredBookings, debouncedSearchQuery]);

  return (
    <div className="dark:text-white max-w-7xl mx-auto">
      <div className="w-full">
        <div className="mb-5">
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            My Bookings
          </h1>
        </div>

        <FilterCard
          filters={filters}
          setFilters={setFilters}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          filterOptions={filterOptions}
        />

        {loading ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
            <p className="text-lg">Loading bookings...</p>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg">
            <div className="bg-white dark:bg-gray-800 py-4 px-6 flex items-center justify-between">
              <TabNavigation
                activeTab={activeTab}
                onTabChange={handleTabChange}
                totalBookings={bookings.length}
                filteredBookings={filteredBookings.length}
                showBookingRequestTab={hasAdminOrAccountantRole}
              />
              {/* <div className="relative">
                <input
                  type="text"
                  placeholder="Search bookings..."
                  className="w-full mx-auto bg-gray-200 dark:bg-gray-700 dark:text-white rounded-lg py-2 pl-10 pr-4 md:w-64 border-none focus:outline-none focus:ring-2 focus:ring-red-500 hover:ring-2 hover:ring-red-500 hover:border-red-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search
                  className="absolute left-3 top-2.5 text-gray-400"
                  size={20}
                />
              </div> */}
              <SearchInput
                value={searchQuery}
                onChange={setSearchQuery}
                loading={loading}
                error={searchError}
              />
            </div>
            <BookingsTable
              bookings={filteredBookings}
              onEditBooking={handleEditBooking}
              hasMore={hasMore}
              loadingMore={loadingMore}
              observerRef={ref}
            />

            {selectedBooking && (
              <BookingDetails isOpen={detailsOpen} booking={selectedBooking} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default function MyBookings() {
  // check user's access
  const loading = useAffiliateUserAuth();

  if (loading) {
    return <ProgressLoading />;
  }
  return (
    <div className="pt-8 w-full max-w-[96rem] mx-auto">
      <MyBookingsList />
    </div>
  );
}
