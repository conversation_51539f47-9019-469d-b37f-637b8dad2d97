import React, { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { ChevronDown, Check } from "lucide-react";

interface StableDropdownProps {
  label: string;
  options: { value: string; label: string }[] | string[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export const StableDropdown = React.memo(({
  label,
  options,
  value,
  onChange,
  className = "flex-1",
}: StableDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  
  // Toggle dropdown
  const handleToggle = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(prev => !prev);
  }, []);
  
  // Handle option selection
  const handleSelect = useCallback((optionValue: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange(optionValue);
    setIsOpen(false);
  }, [onChange]);
  
  // Parse options
  const parsedOptions = useMemo(() => {
    return options.map(opt => 
      typeof opt === 'string' 
        ? { value: opt, label: opt }
        : opt
    );
  }, [options]);
  
  // Get selected option
  const selectedOption = useMemo(() => {
    return parsedOptions.find(opt => opt.value === value) || parsedOptions[0] || { value: '', label: '' };
  }, [parsedOptions, value]);
  
  // Handle click outside
  useEffect(() => {
    if (!isOpen) return;
    
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Node;
      if (buttonRef.current?.contains(target) || menuRef.current?.contains(target)) {
        return;
      }
      setIsOpen(false);
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);
  
  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
      </label>
      <button
        ref={buttonRef}
        type="button"
        onClick={handleToggle}
        onMouseDown={(e) => e.preventDefault()}
        className="w-full flex items-center justify-between px-3 py-2 rounded-lg bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200"
      >
        <span className="truncate">{selectedOption.label}</span>
        <ChevronDown className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>
      
      {isOpen && (
        <div 
          ref={menuRef}
          className="absolute left-0 right-0 mt-1 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-y-auto max-h-[200px] custom-scrollbar divide-y-2 divide-gray-200/50 dark:divide-gray-700/50"
        >
          <div className="py-1">
            {parsedOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between transition-colors ${
                  option.value === value 
                    ? 'text-red-500 bg-gray-50 dark:bg-gray-700/50' 
                    : 'text-gray-700 dark:text-gray-300'
                }`}
                onClick={(e) => handleSelect(option.value, e)}
                onMouseDown={(e) => e.preventDefault()}
              >
                <span>{option.label}</span>
                {option.value === value && (
                  <Check className="h-4 w-4 flex-shrink-0" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

StableDropdown.displayName = 'StableDropdown';
