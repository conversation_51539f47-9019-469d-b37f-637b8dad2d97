{
  "compilerOptions": {
    "target": "ESNext",
    "module": "commonjs",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "strictPropertyInitialization": true,
    "noImplicitAny": true,
    // "skipLibCheck": true, // Skips type checking of declaration files for faster builds
    "outDir": "./dist",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "incremental": true,
    "typeRoots": ["./types","./node_modules/@types"],
    "types": ["cookie", "jest"]
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "**/*.test.ts", "dist", "**/*.spec.ts"]
}
