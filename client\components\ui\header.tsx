"use client";
import ThemeToggle from "@/components/common/theme-toggle";
import React, { JS<PERSON>, useCallback, useEffect, useRef, useState } from "react";
import {
  LucideIcon,
  Home,
  Plane,
  Bell,
  ChevronDown,
  Users,
  FileText,
  User,
  LogOut,
  Settings,
  Mail,
  Ticket,
  CreditCard,
  BarChart4,
  Headphones,
  ListChecks,
  Luggage,
  Cog,
  Wallet,
  X,
  CheckCircle,
  Info,
  Trash2,
  Calendar,
  Check,
  AlertCircle,
  Menu,
  Building,
  PieChart,
  Globe,
} from "lucide-react";
import Link from "next/link";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { logoutUser, selectUser } from "@/redux/features/AuthSlice";
import { fetchLogout } from "@/lib/data/authData";
import { useRouter } from "next/navigation";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { setLoading } from "@/redux/features/LoadingSlice";
import Image from "next/image";
import logo from "@/public/images/logo/airvilla_logo_symbol_red.png";
import { getTeamId } from "@/lib/data/teamData";
import clerk from "@/public/images/logo/secured-by-clerk.svg";
import { useNotifications, Notification } from "@/context/NotificationContext";
import { useSocket } from "@/context/SocketContext";
import { getNotifications } from "@/lib/data/notificationData";

// Reusable button component
interface IconButtonProps {
  icon: LucideIcon;
  onClick?: () => void;
  className?: string;
  notifications?: number;
}

// Dropdown menu component for navigation items
interface NavDropdownMenuProps {
  title: string;
  icon: LucideIcon;
  items: {
    icon: React.ReactNode;
    text: string;
    link: string;
    target?: string;
    rel?: string;
  }[];
}

// User info header component
interface UserInfoHeaderProps {
  userInfo: {
    firstName: string;
    lastName: string;
    agencyName: string;
    role: string;
    subRole: string;
    creditBalance: string;
    teamId?: string;
    isLogin: boolean;
  };
  toggleDropdown: () => void;
}

// Import the Notification type from the context
// (Already imported at the top of the file)
import NotificationDropdown from "../notifications/NotificationDropdown";
import { getFormatDate, getFormatTime } from "@/utils/functions/functions";
import { deleteNotification } from "@/lib/data/notificationData";
import { getOwnerCreditBalance } from "@/lib/data/userProfileData";

interface MenuLinkProps {
  icon: LucideIcon;
  text: string;
  link: string;
  highlighted?: boolean;
  isRed?: boolean;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
}

export default function Header() {
  return <NavigationBar />;
}

// Custom hook for managing screen size
const useScreenSize = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1280);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  return isMobile;
};

// Reusable button component
const IconButton = ({
  icon: Icon,
  onClick,
  className = "",
  notifications = 0,
}: IconButtonProps) => (
  <div className="relative">
    <button
      onClick={onClick}
      className={`text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300 ${className}`}
    >
      <Icon size={20} />
    </button>
    {notifications > 0 && (
      <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
        {notifications}
      </span>
    )}
  </div>
);

// Use NotificationContext for notification badge count
// In the NavigationBar or Header, use:
// const { unreadCount } = useNotifications();
// <IconButton ... notifications={unreadCount} />

// Dropdown menu component for navigation items
const NavDropdownMenu = ({
  title,
  icon: Icon,
  items,
}: NavDropdownMenuProps) => {
  const [isOpenMenu, setIsOpenMenu] = React.useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isMobile = useScreenSize();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpenMenu(false);
      }
    };

    // Add event listener when the dropdown is open
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpenMenu(!isOpenMenu);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="w-full flex items-center justify-between px-4 py-2 text-gray-700 hover:text-red-500 hover:bg-gray-200 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-red-500 cursor-pointer rounded-lg transition duration-300"
      >
        <span className="flex items-center justify-center">
          <Icon size={20} className="mr-2" />
          {title}
        </span>
        <ChevronDown size={20} className="ml-1" />
      </button>
      {isOpenMenu && (
        <div
          className={`${
            isMobile ? "relative" : "absolute"
          } z-10 py-2 mt-2 bg-gray-50 dark:bg-gray-700 rounded-md shadow-xl w-full`}
        >
          {items.map((item, index) => (
            <Link
              key={index}
              href={item.link}
              target={item.target}
              rel={item.rel}
              className="flex items-center px-4 py-2 text-sm hover:bg-gray-200 hover:text-red-500 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-red-500"
            >
              {item.icon}
              <span className="ml-2">{item.text}</span>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

// User info header component
const UserInfoHeader = ({ userInfo, toggleDropdown }: UserInfoHeaderProps) => {
  const [isTeamMember, setIsTeamMember] = useState<boolean>(false);
  const user: any = useAppSelector(selectUser);

  useEffect(() => {
    const checkTeamMembership = async () => {
      try {
        const teamIdData = await getTeamId();
        setIsTeamMember(!!teamIdData);
      } catch (error) {
        console.error("Error checking team membership:", error);
        setIsTeamMember(false);
      }
    };

    checkTeamMembership();
  }, []);

  return (
    <div className=" dark:text-gray-200 rounded-lg font-sans">
      <div className="mb-1">
        <div className="flex justify-between space-x-3 px-4 py-1">
          <div className="flex space-x-3 pr-4 py-1">
            <div className="relative">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                <User size={24} className="text-gray-500 dark:text-gray-300" />
              </div>
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-50 dark:border-gray-800"></div>
            </div>
            <div>
              <h2 className="text-xl font-bold">
                {userInfo.firstName} {userInfo.lastName}
              </h2>
              <p className="text-xs text-green-500">Online</p>
            </div>
          </div>
          <X
            size={28}
            onClick={toggleDropdown}
            className="text-gray-700 hover:text-white dark:text-white dark:hover:text-gray-100 bg-transparent hover:bg-red-600 rounded-full p-1 transition duration-300"
          />
        </div>
        <div className="flex items-center justify-between cursor-pointer p-2 rounded text-base ml-2">
          <div className="flex items-center space-x-2">
            <Users size={18} />
            <p className="font-medium text-gray-500 dark:text-gray-300 capitalize">
              {userInfo.role} {userInfo.subRole ? `- ${userInfo.subRole}` : ""}
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between cursor-pointer p-2 rounded text-base ml-2">
          <div className="flex items-center space-x-2">
            <Building size={18} />
            <p className="font-medium text-gray-500 dark:text-gray-300">
              {userInfo.agencyName
                ? userInfo.agencyName
                : user.role === "affiliate"
                ? "Affiliate Account"
                : "AirVilla"}
            </p>
          </div>
        </div>
      </div>
      <div className="border-t border-gray-300 dark:border-gray-600 my-1"></div>
      <div className="mt-2 text-base text-gray-600 dark:text-gray-300">
        <div className="flex items-center space-x-2 px-4 py-2">
          <CreditCard size={18} />
          <span>
            Balance:{" "}
            <span className="font-medium text-green-500 dark:text-green-400 ml-1">
              {userInfo.creditBalance ?? 0} JOD
            </span>
          </span>
        </div>
      </div>
      {/* )} */}
      <div className="border-t border-gray-300 dark:border-gray-600 my-1"></div>
    </div>
  );
};

const MenuLink = ({
  icon: Icon,
  text,
  link,
  highlighted = false,
  isRed = false,
  onClick,
}: MenuLinkProps) => {
  const baseClass = `w-full text-left block px-4 py-2 text-sm ${
    isRed
      ? "text-red-500"
      : highlighted
      ? "text-blue-500 font-medium"
      : "text-gray-500 dark:text-gray-300"
  } hover:bg-gray-200 hover:text-red-500 dark:hover:text-red-500 dark:hover:bg-gray-800 flex items-center space-x-2`;

  return onClick ? (
    <button onClick={onClick} className={baseClass} role="menuitem">
      <Icon size={18} />
      <span>{text}</span>
    </button>
  ) : (
    <Link href={link} className={baseClass} role="menuitem">
      <Icon size={18} />
      <span>{text}</span>
    </Link>
  );
};
// Logo placeholder component
const LogoPlaceholder = () => (
  <Link
    href="/blockseats"
    className="flex items-center justify-center rounded-lg w-10 h-10"
  >
    <Image src={logo} alt="logo" />
  </Link>
);

// Navigation bar component
const NavigationBar = React.memo(() => {
  // Track previous notification count for popup auto-open
  const prevNotificationCount = useRef<number>(0);
  const user: any = useAppSelector(selectUser);
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [isMessageOpen, setIsMessageOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);

  const isMobile = useScreenSize();

  // Always call useNotifications at the top level (React hooks rule)
  const notificationContext = useNotifications();
  const notificationState: Notification[] =
    notificationContext?.notifications || [];
  const unreadCount = notificationContext?.unreadCount || 0;
  const markAsRead = notificationContext?.markAsRead;
  const markAllAsRead = notificationContext?.markAllAsRead;
  const refreshNotifications = notificationContext?.refreshNotifications;

  // Automatically open notification popup when new notification arrives
  useEffect(() => {
    if (notificationState.length > prevNotificationCount.current) {
      // setIsNotificationsOpen(true);
    }
    prevNotificationCount.current = notificationState.length;
  }, [notificationState.length]);

  // Memoized handlers for opening and closing the message popup
  const handleMessageClick = () => {
    setIsMessageOpen((prevState) => !prevState);
    setIsMenuOpen(false); // Close menu
    setIsNotificationsOpen(false); // Close notifications
  };

  const handleNotificationsClick = () => {
    setIsNotificationsOpen((prevState) => !prevState);
    setIsMessageOpen(false); // Close messages
    setIsMenuOpen(false); // Close menu
  };

  const handleDeleteNotification = async (id: string) => {
    try {
      // await deleteNotification(id); // context method
      // if (refreshNotifications) await refreshNotifications();
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  };

  const toggleDropdown = () => {
    setIsMenuOpen((prevState) => !prevState);
    setIsMessageOpen(false); // Close messages
    setIsNotificationsOpen(false); // Close notifications
  };

  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsNavOpen(!isNavOpen);
      }
    };

    // Add event listener when the dropdown is open
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isNavOpen]);

  const dropdownRef2 = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef2.current &&
        !dropdownRef2.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
        setIsMessageOpen(false);
        setIsNotificationsOpen(false);
      }
    };

    // Add event listener when the dropdown is open
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMessageOpen, isNotificationsOpen, isMenuOpen]);
  return (
    <nav className="bg-white dark:bg-gray-800 shadow-lg min-h-[72px] h-[72px] content-center">
      <div className="max-w-7xl mx-auto px-4 xl:px-0">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <LogoPlaceholder />
            {user && user?.agencyName?.length < 15 ? (
              <span className="ml-3 text-lg font-semibold text-gray-700 dark:text-gray-300">
                {user.agencyName ? user.agencyName : "AirVilla"}
              </span>
            ) : (
              <div className="group">
                {/* Truncated agency name */}
                <div className="ml-3 text-lg font-semibold text-gray-700 dark:text-gray-300 truncate max-w-[150px]">
                  {user.agencyName ? user.agencyName : "AirVilla"}
                </div>

                {/* Tooltip with Full agency name */}
                <div className="relative">
                  <div className="absolute left-0 top-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
                    {user.agencyName ? user.agencyName : "AirVilla"}
                  </div>
                </div>
              </div>
            )}
          </div>

          {isMobile ? (
            <IconButton
              icon={isNavOpen ? X : Menu}
              onClick={() => setIsNavOpen(!isNavOpen)}
              className="xl:hidden"
            />
          ) : (
            <>
              <NavItems isMobile={false} />
              <RightSideItems
                onMessageClick={handleMessageClick}
                onNotificationsClick={handleNotificationsClick}
                toggleDropdown={toggleDropdown}
                isMobile={false}
              />
              <div className="relative" ref={dropdownRef2}>
                <div className="absolute top-0 md:top-8 right-0 mt-2 z-40 md:w-96">
                  {isMessageOpen && (
                    <MessagePopup onClose={handleMessageClick} />
                  )}
                  {isNotificationsOpen && (
                    <NotificationsComponent
                      isNotificationsOpen={isNotificationsOpen}
                      onCloseNotifications={handleNotificationsClick}
                      notifications={notificationState}
                      onMarkAsRead={(id: string) =>
                        markAsRead?.(id) ?? Promise.resolve()
                      }
                      onMarkAllAsRead={() =>
                        markAllAsRead?.() ?? Promise.resolve()
                      }
                      onDeleteNotification={(id: string) =>
                        handleDeleteNotification?.(id) ?? Promise.resolve()
                      }
                    />
                  )}
                </div>
                <div className="absolute top-0 md:top-8 right-0 mt-2 z-40 md:w-72">
                  {isMenuOpen && (
                    <UserMenu
                      isMenuOpen={isMenuOpen}
                      toggleDropdown={toggleDropdown}
                    />
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      {isMobile && isNavOpen && (
        <div className="block xl:hidden bg-white dark:bg-gray-800 absolute z-40 w-full">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3" ref={dropdownRef}>
            <NavItems isMobile={true} />
            <RightSideItems
              onMessageClick={handleMessageClick}
              onNotificationsClick={handleNotificationsClick}
              toggleDropdown={toggleDropdown}
              isMobile={true}
            />
            <div className="relative" ref={dropdownRef2}>
              <div className="absolute top-2 xl:top-8 right-0 md:right-1/3 mt-2 z-40 w-full md:w-72">
                {/* {isMessageOpen && <MessagePopup onClose={handleMessageClick} />} */}
                {isNotificationsOpen && (
                  <NotificationsComponent
                    isNotificationsOpen={isNotificationsOpen}
                    onCloseNotifications={handleNotificationsClick}
                    notifications={notificationState}
                    onMarkAsRead={(id: string) =>
                      markAsRead
                        ? Promise.resolve(markAsRead(id))
                        : Promise.resolve()
                    }
                    onMarkAllAsRead={() =>
                      markAllAsRead
                        ? Promise.resolve(markAllAsRead())
                        : Promise.resolve()
                    }
                    onDeleteNotification={(id: string) =>
                      handleDeleteNotification(id)
                    }
                  />
                )}
                {isMenuOpen && (
                  <UserMenu
                    isMenuOpen={isMenuOpen}
                    toggleDropdown={toggleDropdown}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
});

// Navigation items component
const NavItems = ({ isMobile }: { isMobile: boolean }) => {
  const user: any = useAppSelector(selectUser);
  return (
    <div
      className={
        isMobile
          ? "flex flex-col space-y-2"
          : "flex space-y-0 space-x-4 mx-auto"
      }
    >
      <Link
        href="/blockseats"
        className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-200 hover:text-red-500 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-red-500 cursor-pointer rounded-lg transition duration-300"
      >
        <Home size={20} className="mr-2" />
        Home
      </Link>
      <NavDropdownMenu
        title="Ticket Hub"
        icon={Ticket}
        items={[
          ...(user.role === "agency" || user.role === "master"
            ? [
                {
                  icon: <Ticket size={18} />,
                  text: "My Blockseats",
                  link: "/flight-tickets/myTickets",
                },
              ]
            : []),
          {
            icon: <Luggage size={18} />,
            text: "My Bookings",
            link: "/ticket-hub/myBookings",
          },

          ...(user.role === "agency" || user.role === "master"
            ? [
                {
                  icon: <CreditCard size={18} />,
                  text: "My Sales",
                  link: "/ticket-hub/mySales",
                },
              ]
            : []),
          {
            icon: <FileText size={18} />,
            text: "My Manifest",
            link: "/ticket-hub/myManifest",
          },
        ]}
      />
      <NavDropdownMenu
        title={
          user.role === "master"
            ? "Master Hub"
            : user.role === "agency"
            ? "Agency Hub"
            : "Affiliate Hub"
        }
        icon={Cog}
        items={[
          {
            icon: <Settings size={18} />,
            text: "Settings",
            link: "/account-hub/account-overview",
          },
          // Conditionally include "Users" item only if user is "agency"
          ...(user.role === "agency"
            ? [
                {
                  icon: <Users size={18} />,
                  text: "Users",
                  link: "/account-hub/team-management",
                },
              ]
            : []),
          {
            icon: <Wallet size={18} />,
            text: "Wallet",
            link: "/account-hub/wallet",
          },
        ]}
      />

      {user.role === "master" ? (
        <NavDropdownMenu
          title="Master Control"
          icon={Settings}
          items={[
            {
              icon: <Users size={18} />,
              text: "Users",
              link: "/master-control/users",
            },
            {
              icon: <Users size={18} />,
              text: "Team Management",
              link: "/master-control/team-management",
            },
            {
              icon: <FileText size={18} />,
              text: "Blockseat Requests",
              link: "/master-control/ticket-requests",
            },
            {
              icon: <ListChecks size={18} />,
              text: "Blockseats Overview",
              link: "/master-control/tickets-overview",
            },
            {
              icon: <Globe size={18} />,
              text: "Global Bookings",
              link: "/master-control/global-bookings",
            },
            {
              icon: <Wallet size={18} />,
              text: "Wallet Management",
              link: "https://sprightly-crisp-f5111e.netlify.app/",
              target: "_blank",
              rel: "noopener noreferrer",
            },
          ]}
        />
      ) : (
        ""
      )}
    </div>
  );
};

const NotificationsComponent = ({
  isNotificationsOpen,
  onCloseNotifications,
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
}: {
  isNotificationsOpen: boolean;
  onCloseNotifications: () => void;
  notifications: Notification[];
  onMarkAsRead: (id: string) => Promise<void>;
  onMarkAllAsRead: () => Promise<void>;
  onDeleteNotification: (id: string) => Promise<void>;
}) => {
  const [localNotifications, setLocalNotifications] = useState(notifications);

  const unreadCount = notifications.filter((n) => !n.read).length;

  // Keep local notifications in sync with props
  useEffect(() => {
    setLocalNotifications(notifications);
  }, [notifications]);

  const removeNotification = async (id: string) => {
    // Call the delete function passed from parent
    await onDeleteNotification(id);
    // Update local state
    setLocalNotifications(
      localNotifications.filter((notification) => notification.id !== id)
    );
  };

  const markAsClicked = async (id: string) => {
    await onMarkAsRead(id);
    setLocalNotifications(
      localNotifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const deleteNotification = async (id: string) => {
    await onDeleteNotification(id);
    removeNotification(id);
  };

  const markAllAsClicked = async () => {
    await onMarkAllAsRead();
    setLocalNotifications(
      localNotifications.map((notification) => ({
        ...notification,
        read: true,
      }))
    );
  };

  // Icon mapping based on notification type
  const iconMap: { [key: string]: React.ElementType } = {
    info: Info,
    alert: AlertCircle,
    success: Check,
    event: Calendar,
    flight: Plane,
  };

  return (
    <div className="font-sans bg-white text-gray-800 dark:bg-gray-700 dark:text-white rounded-lg shadow-lg">
      {isNotificationsOpen && (
        <div className="rounded-lg shadow-lg bg-white text-gray-800 dark:bg-gray-700 dark:text-white">
          <div className="p-4 flex justify-between items-center border-b border-gray-300 dark:border-gray-600">
            <h2 className="text-lg font-semibold space-x-2">
              <span>Notifications</span>
              {unreadCount > 0 && (
                <span className="bg-blue-500 text-xs font-semibold text-white rounded-full px-2 py-1">
                  {unreadCount} new
                </span>
              )}
            </h2>
            <X
              size={28}
              onClick={onCloseNotifications}
              className="text-gray-700 hover:text-white dark:text-white dark:hover:text-gray-100 bg-transparent hover:bg-red-600 rounded-full p-1 transition duration-300"
            />
          </div>

          <div className="max-h-96 overflow-y-auto custom-scrollbar">
            {localNotifications.length > 0 && (
              <div className="bg-white dark:bg-gray-700">
                {localNotifications.map((notification) => {
                  // Determine which icon to use based on the notification type
                  const IconComponent = iconMap[notification.type] || Bell;
                  return (
                    <div
                      key={`${notification.id}-${notification.createdAt}`}
                      className={`p-4 cursor-pointer ${
                        !notification.read
                          ? "bg-blue-200 dark:bg-blue-900/20 border-b border-blue-300 dark:border-blue-950/50"
                          : "bg-white dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600"
                      }`}
                      onClick={() => markAsClicked(notification.id)}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 relative">
                          <IconComponent
                            className="text-gray-500 dark:text-gray-400 mr-2"
                            size={20}
                          />
                          {!notification.read && (
                            <div className="absolute -top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                          )}
                        </div>
                        <div className="flex-grow">
                          <div className="ml-2 flex items-center justify-between space-x-2">
                            <h3 className="font-semibold">
                              {notification.title}
                            </h3>
                            <div className="ml-2 flex items-center space-x-2">
                              {!notification.read && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onMarkAsRead(notification.id);
                                  }}
                                  className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-300"
                                  title="Mark as Read"
                                >
                                  <CheckCircle size={16} />
                                </button>
                              )}
                              {/* <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteNotification(notification.id);
                                }}
                                className="text-red-400 hover:text-red-500 dark:hover:text-red-300 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-300"
                                title="Delete"
                              >
                                <Trash2 size={16} />
                              </button> */}
                            </div>
                          </div>

                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            {notification.message}
                          </p>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {getFormatTime(notification.createdAt) +
                              (new Date(notification.createdAt).getHours() < 12
                                ? " AM"
                                : " PM")}{" "}
                            - {getFormatDate(notification.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {localNotifications.length === 0 && (
            <div className="p-4 text-center">No notifications</div>
          )}

          {localNotifications.length > 0 && (
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <button
                className="w-full text-center text-sm text-blue-500 hover:underline"
                onClick={markAllAsClicked}
              >
                Mark all as read
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
const UserMenu = React.memo(
  ({
    isMenuOpen,
    toggleDropdown,
  }: {
    isMenuOpen: boolean;
    toggleDropdown: () => void;
  }) => {
    const dispatch = useAppDispatch();
    const user: any = useAppSelector(selectUser);
    const router = useRouter();
    const [ownerCreditBalance, setOwnerCreditBalance] =
      useState<string>("0.00");
    const [userInfo, setUserInfo] = useState({
      firstName: user && user.isLogin ? user.firstName : "",
      lastName: user && user.isLogin ? user.lastName : "",
      agencyName: user && user.isLogin ? user.agencyName : "",
      role: user && user.isLogin ? user.role : "",
      subRole: user && user.isLogin ? user.subRole : "",
      creditBalance: ownerCreditBalance,
      teamId: user && user.isLogin ? user.teamId : "",
      isLogin: user && user.isLogin,
    });

    // Fetch owner's credit balance
    useEffect(() => {
      const getOwnerBalance = async () => {
        if (userInfo && userInfo.isLogin) {
          // Check if user is a team member or agency agent
          const isTeamMember =
            user.role === "master" && user.roleType !== "master_owner";
          const isAgencyAgent =
            user.role === "agency" && user.roleType !== "agency_owner";

          if (isTeamMember || isAgencyAgent) {
            try {
              const balanceData = await getOwnerCreditBalance();

              if (balanceData.success) {
                // Ensure we're working with a number before formatting
                const balanceValue = parseFloat(
                  balanceData?.creditBalance || "0.00"
                );
                const formattedBalance = isNaN(balanceValue)
                  ? "0.00"
                  : balanceValue.toFixed(2);

                setOwnerCreditBalance(formattedBalance);
                setUserInfo((prev) => ({
                  ...prev,
                  creditBalance: formattedBalance,
                }));
              }
            } catch (error) {
              console.error("Error fetching owner balance:", error);
              // Set default values in case of error
              setOwnerCreditBalance("0.00");
              setUserInfo((prev) => ({
                ...prev,
                creditBalance: "0.00",
              }));
            }
          } else {
            // For regular users, use their own credit balance
            // Ensure we're working with a number before formatting
            const userBalance = parseFloat(user.creditBalance || "0");
            const formattedBalance = isNaN(userBalance)
              ? "0.00"
              : userBalance.toFixed(2);

            setOwnerCreditBalance(formattedBalance);
            setUserInfo((prev) => ({
              ...prev,
              creditBalance: formattedBalance,
            }));
          }
        }
      };

      getOwnerBalance();
    }, [user]);

    // Close menu when clicking outside

    // handle logout function
    const handleLogout = async () => {
      dispatch(setLoading(true));
      // clean all cookies
      const logout = await fetchLogout();

      // remove all cookies & set isLogin to false
      if (logout?.success) {
        dispatch(logoutUser());
        // redirect to login page
        router.push("/signin");
      } else if (logout?.results?.verified === false) {
        dispatch(logoutUser());
        router.push("/signup-process/not-verified");
      }

      dispatch(setMsg({ success: logout.success, message: logout.message }));
      dispatch(setLoading(false));
    };

    const allowedRoles = ["master", "agency", "affiliate"];

    const notAllowedSubRoles = ["admin", "accountant", "moderator"];

    return (
      <>
        {isMenuOpen && (
          <div className="rounded-md shadow-lg bg-gray-50 dark:bg-gray-700 ring-1 ring-black ring-opacity-5 z-40">
            <div
              className="py-1"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="options-menu"
            >
              <UserInfoHeader
                userInfo={userInfo}
                toggleDropdown={toggleDropdown}
              />
              {(userInfo.role === "master" || userInfo.role === "agency") && (
                <MenuLink
                  icon={Ticket}
                  text="My Blockseats"
                  link="/flight-tickets/myTickets"
                />
              )}
              {(userInfo.role !== "master" ||
                (userInfo.role === "master" &&
                  userInfo.subRole === undefined)) && (
                <MenuLink
                  icon={Luggage}
                  text="My Bookings"
                  link="/ticket-hub/myBookings"
                />
              )}
              {(userInfo.role === "master" || userInfo.role === "agency") && (
                <MenuLink
                  icon={CreditCard}
                  text="My Sales"
                  link="/ticket-hub/mySales"
                />
              )}
              {
                // allowedRoles.includes(userInfo.role) &&
                //   !notAllowedSubRoles.includes(userInfo.subRole) &&
                //   !userInfo.teamId &&

                (userInfo.role !== "master" ||
                  (userInfo.role === "master" &&
                    userInfo.subRole === undefined)) && (
                  <MenuLink
                    icon={Settings}
                    text="Settings"
                    highlighted
                    link="/account-hub/account-overview"
                  />
                )
              }
              <div className="border-t border-gray-300 dark:border-gray-600 my-1"></div>
              <MenuLink
                icon={Headphones}
                text="Help & Support"
                link="/support"
              />
              <ThemeToggle />
              <div className="border-t border-gray-300 dark:border-gray-600 my-1"></div>
              <MenuLink
                icon={LogOut}
                text="Sign Out"
                isRed={true}
                link=""
                onClick={handleLogout}
              />
              <div className="border-t border-gray-300 dark:border-gray-600 my-1"></div>
              <span className="flex items-center justify-center rounded-lg py-2">
                <Image src={clerk} alt="logo" />
              </span>
            </div>
          </div>
        )}
      </>
    );
  }
);

// Right side items component
const RightSideItems = React.memo(
  ({
    onMessageClick,
    onNotificationsClick,
    toggleDropdown,
    isMobile,
  }: {
    onMessageClick: () => void;
    onNotificationsClick: () => void;
    toggleDropdown: () => void;
    isMobile: boolean;
  }) => {
    const user = useAppSelector(selectUser);
    if (!user) return null;
    const { unreadCount } = useNotifications();

    return (
      <div
        className={
          isMobile
            ? "flex flex-col items-stretch space-y-2"
            : "flex items-center space-y-0 space-x-4"
        }
      >
        <Link href="/blockseats">
          <button className="bg-red-500 text-white hover:bg-red-600 py-2 px-4 rounded-lg transition duration-300 flex justify-center items-center w-full my-4 xl:my-0">
            <Plane size={20} className="mr-2" />
            Search for Flights
          </button>
        </Link>
        <div className="flex items-center justify-center space-x-2">
          {/* <IconButton icon={Mail} onClick={onMessageClick} notifications={4} /> */}
          {/* <NotificationDropdown /> */}
          <IconButton
            icon={Bell}
            onClick={onNotificationsClick}
            notifications={unreadCount}
          />
          <IconButton
            icon={User}
            onClick={toggleDropdown}
            className="dark:bg-gray-800 dark:hover:bg-gray-700"
          />
        </div>
      </div>
    );
  }
);

// Message Popup Component
// This component displays a list of messages in a popup
const MessagePopup = React.memo(({ onClose }: { onClose: () => void }) => {
  // Sample messages data
  const messages = [
    {
      id: 1,
      sender: "John Doe",
      content: "Hi there! Just checking on the flight details.",
      time: "10:30 AM",
    },
    {
      id: 2,
      sender: "Jane Smith",
      content: "Your booking has been confirmed.",
      time: "Yesterday",
    },
    {
      id: 3,
      sender: "Support Team",
      content: "We've processed your refund request.",
      time: "2 days ago",
    },
    {
      id: 4,
      sender: "Alice Johnson",
      content: "Your flight to Paris has been rescheduled.",
      time: "3 days ago",
    },
    {
      id: 5,
      sender: "Bob Williams",
      content: "New promotional offer available for your next booking!",
      time: "4 days ago",
    },
    {
      id: 6,
      sender: "Customer Service",
      content: "How was your recent flight experience?",
      time: "5 days ago",
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-700 rounded-lg shadow-lg overflow-hidden h-full z-40">
      <div className="py-4 px-6 flex justify-between items-center border-b border-gray-300 dark:border-gray-600">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white">
          Messages
        </h3>
        <X
          size={28}
          onClick={onClose}
          className="text-gray-700 hover:text-white dark:text-white dark:hover:text-gray-100 bg-transparent hover:bg-red-600 rounded-full p-1 transition duration-300"
        />
      </div>
      <div
        className="overflow-y-auto custom-scrollbar"
        style={{ maxHeight: "calc(300px)" }}
      >
        {messages.map((message) => (
          <div
            key={message.id}
            className="py-4 px-6 border-b border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            <div className="flex justify-between items-start">
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {message.sender}
              </span>
              <span className="text-xs text-gray-500">{message.time}</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {message.content}
            </p>
          </div>
        ))}
      </div>
      <div className="py-4 px-6">
        <a
          href="#"
          className="block text-center text-blue-500 hover:text-blue-400 hover:underline"
        >
          View All Messages
        </a>
      </div>
    </div>
  );
});
