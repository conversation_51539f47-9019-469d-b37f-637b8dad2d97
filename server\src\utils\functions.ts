import moment from "moment";
import crypto from "crypto";
import { RoleType } from "@prisma/client";

/**
 * Capitalizes the first letter of each word in a string.
 * @param {string} input - The string to capitalize.
 * @returns {string} - The capitalized string.
 */
export const capitalize = (input: string): string => {
  // Normalize whitespace by replacing multiple spaces with a single space
  const normalizedInput = input.replace(/\s+/g, " ").trim();

  // Split the string into words, capitalize the first letter of each, and join them back
  return normalizedInput
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

/**
 * Removes spaces from a phone number string.
 * @param {string} value - The phone number string that may contain spaces.
 * @returns {string} The phone number string with all spaces removed.
 * @example
 * trimPhoneNumber("123 456 789") // returns "123456789"
 */
export const trimPhoneNumber = (value: string) => {
  const num = value.trim().split(" ");
  return num.join("");
};

/**
 * Converts a time string in the format "HH:MM" to an ISO string
 * "YYYY-MM-DDTHH:mm:ss.SSS" without timezone conversion.
 * @param {string} time - The time string in the format "HH:MM".
 * @returns {string} The ISO string in the format "YYYY-MM-DDTHH:mm:ss.SSS".
 * @example
 * convertTimeToISOString("12:34") // returns "2022-12-25T12:34:00.000"
 */
export const convertTimeToISOString = (time: any) => {
  const [hours, minutes] = time.split(":");
  if (!hours || !minutes || parseInt(hours) >= 24 || parseInt(minutes) >= 60) {
    throw new Error("Invalid time format");
  }
  const date = moment().set({
    hour: hours,
    minute: minutes,
    second: 0,
    millisecond: 0,
  });

  // Format to ISO string without timezone conversion
  return date.format("YYYY-MM-DDTHH:mm:ss.SSS");
};

/**
 * Calculates the time difference between departure and arrival times
 * @param departureTime - The departure time as a string
 * @param arrivalTime - The arrival time as a string
 * @returns A formatted string representing the duration in hours and minutes (e.g. "2h 30m")
 */
export const timeDif = (departureTime: string, arrivalTime: string) => {
  // Convert times to moment objects
  const departure = moment(departureTime);
  const arrival = moment(arrivalTime);

  if (!departure.isValid() || !arrival.isValid()) {
    throw new Error("Invalid date format");
  }

  // Calculate the difference between departure and arrival times
  const diff = moment.duration(arrival.diff(departure));
  const hours = Math.floor(diff.asHours());
  const minutes = diff.minutes();

  return `${hours}h ${minutes}m`;
};

// Helper function to calculate date ranges for created time filter
/**
 * Calculates a date range based on a predefined time filter string.
 *
 * @param filter - The time filter string to apply. Valid values are:
 *                "today" - From start of current day
 *                "last 7 days" - From 7 days ago
 *                "last month" - From 1 month ago
 *                "last 12 months" - From 12 months ago
 *                "all time" - No date filtering
 *
 * @returns An object containing the date range filter with:
 *          - gte (greater than or equal) property set to the calculated start date
 *          - Empty object if "all time" is specified or filter is invalid
 *
 * @example
 * ```
 * getCreatedTimeRange("today") // Returns: { gte: [start of current day] }
 * getCreatedTimeRange("last 7 days") // Returns: { gte: [7 days ago] }
 * getCreatedTimeRange("all time") // Returns: {}
 * ```
 */
export const getCreatedTimeRange = (filter: string) => {
  /**
   * Calculates the date range for the created time filter
   * based on the provided filter string.
   * @param filter - The filter string
   * @returns An object with the date range for the created time filter
   */
  const now = new Date();
  switch (filter) {
    case "today":
      return { gte: new Date(now.setHours(0, 0, 0, 0)) };
    case "last 7 days":
      return { gte: new Date(now.setDate(now.getDate() - 7)) };
    case "last month":
      return { gte: new Date(now.setMonth(now.getMonth() - 1)) };
    case "last 12 months":
      return { gte: new Date(now.setMonth(now.getMonth() - 12)) };
    case "all time":
      return {}; // No date filter applied
    default:
      return {}; // Default to no filter
  }
};

/**
 * Combines a date string with a time string.
 * @param {string} dateStr - The date string in 'YYYY-MM-DDTHH:mm:ss.SSS' format.
 * @param {string} timeStr - The time string in 'YYYY-MM-DDTHH:mm:ss.SSS' format.
 * @returns {string} - The combined date-time string in ISO 8601 format.
 */
export function combineDateAndTime(dateStr: string, timeStr: string): string {
  // Parse the date part from dateStr
  const date = moment(dateStr, "YYYY-MM-DDTHH:mm:ss.SSS");

  // Extract the time part from timeStr
  const time = moment(timeStr.toString(), "YYYY-MM-DDTHH:mm:ss.SSS");

  if (!Date.parse(date.format())) throw new Error("Invalid date format");
  if (!Date.parse(time.format())) throw new Error("Invalid time format");

  // Set the time components in the date
  date.set({
    hour: time.hour(),
    minute: time.minute(),
    second: time.second(),
    millisecond: time.millisecond(),
  });

  // Format the combined result
  const combinedDateTime = date.format("YYYY-MM-DDTHH:mm:ss.SSS");

  return combinedDateTime;
}

/**
 * Generates a random hexadecimal token using crypto.randomBytes
 *
 * @returns A Promise that resolves to a 64-character hexadecimal string
 * @throws If there's an error generating random bytes
 *
 * @example
 * ```typescript
 * const token = await generateInvitationToken();
 * // token = "a1b2c3d4..."
 * ```
 */
export async function generateInvitationToken(): Promise<string> {
  try {
    const token = await new Promise<string>((resolve, reject) => {
      crypto.randomBytes(32, (err, buffer) => {
        if (err) {
          reject(
            new Error(`Failed to generate invitation token: ${err.message}`)
          );
          return;
        }

        if (!buffer || buffer.length !== 32) {
          reject(
            new Error("Generated token does not meet length requirements")
          );
          return;
        }

        const hexToken = buffer.toString("hex");
        if (!/^[0-9a-f]{64}$/.test(hexToken)) {
          reject(new Error("Generated token has invalid format"));
          return;
        }

        resolve(hexToken);
      });
    });

    return token;
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error during token generation";
    throw new Error(`Invitation token generation failed: ${errorMessage}`);
  }
}

/**
 * Role mapping configuration for better maintainability and DRY principle
 */
const ROLE_MAPPINGS: Record<string, RoleType> = {
  master: RoleType.master_owner,
  admin: RoleType.master_admin,
  moderator: RoleType.master_moderator,
  accountant: RoleType.master_accountant,
  agency: RoleType.agency_owner,
  affiliate: RoleType.affiliate,
} as const;

/**
 * Validates if a string is a valid role
 */
function isValidRole(role: string): role is keyof typeof ROLE_MAPPINGS {
  return role in ROLE_MAPPINGS;
}

/**
 * Maps a role string to its corresponding RoleType enum value.
 *
 * @param role - The role string to map (case-insensitive).
 * @returns The corresponding RoleType enum value.  Returns one of:
 *          - RoleType.master_admin
 *          - RoleType.master_moderator
 *          - RoleType.master_accountant
 *          - RoleType.agency_admin
 *          - RoleType.affiliate
 * @throws {Error} If the role string is not recognized.
 *
 * @example
 * getRoleType("admin"); // returns RoleType.master_admin
 * getRoleType("Affiliate"); // returns RoleType.affiliate
 * getRoleType("invalidRole"); // throws Error: "Invalid role: invalidRole"
 */
// export function getRoleType(role: string): RoleType {
//   switch (role.toLowerCase()) {
//     case "master":
//     case "admin":
//       return RoleType.master_admin; // Default roleType for master
//     case "moderator":
//       return RoleType.master_moderator; // Default roleType for master
//     case "accountant":
//       return RoleType.master_accountant; // Default roleType for master
//     case "agency":
//       return RoleType.agency_admin; // Default roleType for agency
//     case "affiliate":
//       return RoleType.affiliate; // Default roleType for affiliate
//     default:
//       throw new Error(`Invalid role: ${role}`);
//   }
// }

export function getRoleType(role: string): RoleType {
  // Input validation
  if (typeof role !== "string") {
    throw new Error(`Invalid role type: expected string, got ${typeof role}`);
  }

  if (!role) {
    throw new Error("Role cannot be empty");
  }

  const normalizedRole = role.toLowerCase().trim();

  if (!isValidRole(normalizedRole)) {
    const validRoles = Object.keys(ROLE_MAPPINGS).join(", ");
    throw new Error(`Invalid role: '${role}'. Valid roles are: ${validRoles}`);
  }

  return ROLE_MAPPINGS[normalizedRole];
}

export const formatFlightDate = (dateString?: string | null): string => {
  if (!dateString) {
    // If no date string provided, use current date
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
  }

  // Check if the date is already in YYYY-MM-DD format
  const yyyyMmDdRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (yyyyMmDdRegex.test(dateString)) {
    return dateString;
  }

  // Check if the date is in DD/MM/YYYY format
  const ddMmYyyyRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
  const ddMmYyyyMatch = dateString.match(ddMmYyyyRegex);
  if (ddMmYyyyMatch) {
    const [, day, month, year] = ddMmYyyyMatch;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }

  // Handle ISO date strings
  try {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
    }
  } catch (e) {
    console.error('Error parsing date:', e);
  }

  // If all else fails, return current date
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
};

export const getFormatDateTable = (date: string) => {
  return moment(date).format("DD/MM/YYYY");
};
// ticket format date
export const getFormatTime = (date: string) => {
  return moment(date).format("HH:mm");
};