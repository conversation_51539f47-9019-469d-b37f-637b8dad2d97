"use client";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import "./booking-confirmation-print.css";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import {
  Calendar,
  Printer,
  Download,
  CheckCircle,
  AlertCircle,
  Mail,
  Headphones,
  Ticket,
  FileText,
  CreditCard,
  Hash,
  DollarSign,
} from "lucide-react";
import { useParams, useRouter, usePathname } from "next/navigation";
import {
  setBookingConfirmationData,
  selectFullTicket,
  setBookingType,
  setItinerary,
  selectItinerary,
  selectBookingType,
  selectBookingResult,
  selectTravelerData,
  selectPassengerCounts,
} from "@/redux/features/BookingConfirmationSlice";
import { useAppSelector } from "@/redux/hooks";
import { getBookingById } from "@/lib/data/bookingData";
import { TravelerDto } from "@/utils/types/booking.types";
import {
  capitalizeFirst,
  getFormatDate,
  getFormatDateTable,
  getFormatTime,
  normalizePaymentMethod,
} from "@/utils/functions/functions";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { TravelerForm } from "../TravelerForm";
import { Traveler } from "@/utils/definitions/blockSeatsDefinitions";
import FareBreakdownRow from "@/components/common/FareBreakdownRow";

// Helper function to normalize traveler data
const normalizeTravelerData = (traveler: any): Traveler => {
  if (!traveler) return traveler;

  // Create a copy of the traveler object
  const normalized = { ...traveler };

  // If passportIssuingCountry is not set but issuingCountry is, use that
  if (!normalized.passportIssuingCountry && normalized.issuingCountry) {
    normalized.passportIssuingCountry = normalized.issuingCountry;
  }

  // If passportExpiry is not set but expirationDate is, use that
  if (!normalized.passportExpiry && normalized.expirationDate) {
    normalized.passportExpiry = normalized.expirationDate;
  }

  // If passportNumber is not set but documentNumber is, use that
  if (!normalized.passportNumber && normalized.documentNumber) {
    normalized.passportNumber = normalized.documentNumber;
  }

  return normalized;
};

// Card component
const Card = ({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

// CardHeader component
const CardHeader = ({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`flex flex-col space-y-1.5 p-6 ${className}`} {...props}>
      {children}
    </div>
  );
};

// CardTitle component
const CardTitle = ({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={`text-2xl font-semibold leading-none tracking-tight ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

// CardContent component
const CardContent = ({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div className={`p-6 pt-0 ${className}`} {...props}>
      {children}
    </div>
  );
};

// Selector to get booking confirmation and traveler data from Redux
const useBookingConfirmationData = () => {
  return useSelector((state: RootState) => state.bookingConfirmation);
};

// Reusable component for section headers
const SectionHeading = ({ title }: { title: string }) => (
  <CardTitle className="font-semibold dark:text-white text-gray-700 text-2xl leading-8">
    {title}
  </CardTitle>
);

// Success header component that shows the booking status
const BookingConfirmationHeader = () => (
  <div className="flex flex-col items-center p-8 mb-6 rounded-xl dark:bg-gray-700 bg-white">
    <div className="flex items-center justify-center mb-3">
      <CheckCircle className="h-16 w-16 text-green-500 mr-4" />
      <div className="text-center">
        <h2 className="font-bold dark:text-white text-gray-700 text-3xl leading-10">
          Booking Confirmed!
        </h2>
        <p className="text-gray-500 dark:text-gray-400 mt-1">
          Your booking has been confirmed and your tickets are ready
        </p>
      </div>
    </div>
  </div>
);

const ReceiptHeader = ({ data }: { data: any }) => (
  <div className="rounded-xl dark:bg-gray-700 bg-white shadow-lg mb-6 overflow-hidden p-6">
    <div className="flex w-full items-center justify-between mb-6">
      <div className="flex items-center">
        <img
          src={data.companyLogo}
          alt="Airvilla Charters"
          className="h-12 mr-3"
        />
        <div>
          <h1 className="font-bold dark:text-white text-gray-700 text-2xl">
            {data.companyName}
          </h1>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            {data.companyAddress}
          </p>
        </div>
      </div>
      <div className="flex flex-col items-end">
        <div className="px-4 py-2">
          <h2 className="font-bold dark:text-white text-gray-700 text-xl">
            RECEIPT
          </h2>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            #{data.receipt?.receiptNumber}
          </p>
        </div>
      </div>
    </div>

    <div className="grid grid-cols-3 gap-6">
      <div className="col-span-1 dark:bg-gray-700 bg-gray-100 p-4 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">
          Receipt Date
        </p>
        <p className="text-gray-700 dark:text-white font-medium">
          {getFormatDateTable(data.receipt?.issuedAt)}
        </p>
      </div>
      <div className="col-span-1 dark:bg-gray-700 bg-gray-100 p-4 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">
          Transaction Time
        </p>
        <p className="text-gray-700 dark:text-white font-medium">
          {getFormatTime(data.receipt?.issuedAt)}
        </p>
      </div>
      <div className="col-span-1 dark:bg-gray-700 bg-gray-100 p-4 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">
          Transaction ID
        </p>
        <p className="text-gray-700 dark:text-white font-medium">
          {data.receipt?.transactionId}
        </p>
      </div>
    </div>
  </div>
);

// Booking reference information card
const BookingInformation = ({ data }: { data: any }) => {
  // Robust fallback logic for all booking info fields
  const bookingData = data || data?.bookingResult;
  const bookingId = bookingData.requestId || bookingData.id || "";
  const referenceNumber = bookingData?.referenceNumber || "";
  const paymentMethod =
    bookingData.eTicket?.ticketType === "THIRD_PARTY" ||
    bookingData.booking?.source === "THIRD_PARTY" ||
    data?.source === "THIRD_PARTY"
      ? "Airvilla Wallet"
      : normalizePaymentMethod(data?.payment?.paymentMethod) || "Bank Transfer";
  const bookingDate =
    getFormatDateTable(data?.bookingResult?.eTicket?.issuedAt) ||
    getFormatDateTable(data?.bookingResult?.booking?.createdAt) ||
    getFormatDateTable(data?.createdAt) ||
    "";

  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading title="Booking Information" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-6">
          {[
            {
              label: "Booking ID",
              value: bookingId,
              icon: <FileText className="h-4 w-4 text-red-500 mr-2" />,
            },
            {
              label: "Booking Reference Number",
              value: referenceNumber,
              icon: <Ticket className="h-4 w-4 text-red-500 mr-2" />,
            },
            {
              label: "Payment Method",
              value: paymentMethod,
              icon: <CreditCard className="h-4 w-4 text-red-500 mr-2" />,
            },
            {
              label: "Booking Date",
              value: bookingDate,
              icon: <Calendar className="h-4 w-4 text-red-500 mr-2" />,
            },
          ].map((item, index) => (
            <div
              key={index}
              className="p-4 rounded-lg dark:bg-gray-600 bg-white"
            >
              <div className="flex items-center mb-2">
                {item.icon}
                <p className="text-gray-500 dark:text-gray-400">{item.label}</p>
              </div>
              <p className="text-gray-700 dark:text-white font-semibold text-xl">
                {item.value}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Display field component for showing pre-filled data
const DisplayField = ({ label, value }: { label: string; value: string }) => (
  <div>
    <label className="block dark:text-white text-gray-700 text-sm mb-1 font-medium">
      {label}
    </label>
    <div className="w-full dark:bg-gray-700 bg-gray-100 text-gray-700 dark:text-white p-2 rounded-md">
      {value}
    </div>
  </div>
);

// Date display field with calendar icon
const DateDisplayField = ({
  label,
  value,
}: {
  label: string;
  value: string;
}) => (
  <div>
    <label className="block dark:text-white text-gray-700 text-sm mb-1 font-medium">
      {label}
    </label>
    <div className="relative">
      <div className="w-full dark:bg-gray-700 bg-gray-100 text-gray-700 dark:text-white p-2 rounded-md">
        {value}
      </div>
      <Calendar className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
    </div>
  </div>
);

// Passenger information component with pre-filled data
const TravelerDetails = ({ data }: { data: any }) => {
  // Try to get travelers from travelerData, then from ticket, then from fullTicket
  const travelers = Array.isArray(data?.travelerData)
    ? data.travelerData
    : Array.isArray(data?.ticket?.travelers)
    ? data.ticket.travelers
    : Array.isArray(data?.fullTicket?.travelers)
    ? data.fullTicket.travelers
    : Array.isArray(data?.travelers)
    ? data.travelers
    : [];
  const [expandedTraveler, setExpandedTraveler] = useState<number>(0);
  const [editingTraveler, setEditingTraveler] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [currentTravelerFormData, setCurrentTravelerFormData] =
    useState<Traveler | null>(null);
  const [openTravelerIndex, setOpenTravelerIndex] = useState<number | null>(
    null
  );
  const isEditing = editingTraveler === expandedTraveler;

  const [travelersInfo, setTravelersInfo] = useState<any[]>(() => {
    return travelers.map((traveler: any) => {
      const travelerData = traveler.traveler || traveler;
      const normalized = {
        ...travelerData,
        // Map API fields to form fields with proper fallbacks
        passportNumber:
          travelerData.passportNumber || travelerData.documentNumber,
        issuingCountry:
          travelerData.issuingCountry ||
          travelerData.issuingCountry ||
          travelerData.nationality ||
          "",
        passportExpiry:
          travelerData.passportExpiry || travelerData.expirationDate,
        contactEmail: travelerData.contactEmail || travelerData.email,
        contactPhone: travelerData.contactPhone || travelerData.phone,
        // Include the original traveler object for reference
        _original: traveler,
      };

      // Ensure all required fields have values
      if (!normalized.passportIssuingCountry && normalized.nationality) {
        normalized.passportIssuingCountry = normalized.nationality;
      }

      return normalized;
    });
  });

  const handleTravelerUpdate = useCallback((updatedTraveler: Traveler) => {
    setCurrentTravelerFormData((prev) => {
      if (!prev) return updatedTraveler;
      // Merge updated fields and errors
      const newErrors = {
        ...(prev.errors || {}),
        ...(updatedTraveler.errors || {}),
      };
      return { ...prev, ...updatedTraveler, errors: newErrors };
    });
  }, []);

  if (!travelers.length) {
    return (
      <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
        <CardHeader>
          <SectionHeading title="Traveler Details" />
        </CardHeader>
        <CardContent>
          <div className="dark:bg-gray-600 bg-gray-100 p-5 rounded-lg">
            <div className="text-white">No traveler data available.</div>
          </div>
        </CardContent>
      </Card>
    );
  }
  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading title="Traveler Details" />
      </CardHeader>
      <CardContent>
        {/* <div className="dark:bg-gray-600 bg-white p-5 rounded-lg"> */}
        {travelersInfo.map((traveler: any, idx: number) => (
          // <div key={idx} className="mb-8">
          //   <div className="mb-4">
          //     <span className="dark:text-white text-gray-700 font-semibold text-lg">
          //       Traveler {idx + 1}: {traveler?.firstName || traveler?.traveler?.firstName} {traveler?.lastName || traveler?.traveler?.lastName}
          //     </span>
          //   </div>
          //   <div className="h-px dark:bg-gray-500 bg-gray-200 mb-5"></div>
          //   {/* Personal Information Section */}
          //   <div className="mb-5">
          //     <h3 className="dark:text-white text-gray-700 mb-4 text-lg font-semibold">
          //       Personal Information
          //     </h3>
          //     <div className="grid grid-cols-2 gap-4 mb-4">
          //       <DisplayField
          //         label="Title"
          //         value={traveler?.title || traveler?.traveler?.title || ""}
          //       />
          //       <DisplayField
          //         label="Gender"
          //         value={traveler?.gender || traveler?.traveler?.gender || ""}
          //       />
          //     </div>
          //     <div className="grid grid-cols-2 gap-4 mb-4">
          //       <DisplayField
          //         label="First Name"
          //         value={
          //           traveler?.firstName || traveler?.traveler?.firstName || ""
          //         }
          //       />
          //       <DisplayField
          //         label="Last Name"
          //         value={
          //           traveler?.lastName || traveler?.traveler?.lastName || ""
          //         }
          //       />
          //     </div>
          //     <div className="grid grid-cols-2 gap-4">
          //       <DateDisplayField
          //         label="Date of Birth"
          //         value={
          //           getFormatDate(traveler?.dateOfBirth) ||
          //           getFormatDate(traveler?.traveler?.dateOfBirth) ||
          //           ""
          //         }
          //       />
          //       <DisplayField
          //         label="Nationality"
          //         value={
          //           traveler?.nationality ||
          //           traveler?.traveler?.nationality ||
          //           ""
          //         }
          //       />
          //     </div>
          //   </div>
          //   <div className="h-px dark:bg-gray-500 bg-gray-200 mb-5"></div>
          //   {/* Identification Documents Section */}
          //   <div className="mb-5">
          //     <h3 className="dark:text-white text-gray-700 mb-4 text-lg font-semibold">
          //       Identification Documents
          //     </h3>
          //     <div className="grid grid-cols-2 gap-4 mb-4">
          //       <DisplayField
          //         label="Passport Number"
          //         value={
          //           traveler?.documentNumber ||
          //           traveler?.traveler?.documentNumber ||
          //           ""
          //         }
          //       />
          //       <DisplayField
          //         label="Passport Issuing Country"
          //         value={
          //           traveler?.issuingCountry ||
          //           traveler?.traveler?.issuingCountry ||
          //           ""
          //         }
          //       />
          //     </div>
          //     <DateDisplayField
          //       label="Passport Expiry"
          //       value={
          //         getFormatDate(traveler?.expirationDate) ||
          //         getFormatDate(traveler?.traveler?.expirationDate) ||
          //         ""
          //       }
          //     />
          //   </div>
          //   <div className="h-px dark:bg-gray-500 bg-gray-200 mb-5"></div>
          //   {/* Contact Information Section */}
          //   <div>
          //     <h3 className="dark:text-white text-gray-700 mb-4 text-lg font-semibold">
          //       Contact Information
          //     </h3>
          //     <div className="mb-4">
          //       <DisplayField
          //         label="Email"
          //         value={
          //           traveler?.contactEmail ||
          //           traveler?.traveler?.contactEmail ||
          //           ""
          //         }
          //       />
          //     </div>
          //     <DisplayField
          //       label="Phone Number"
          //       value={
          //         traveler?.contactPhone ||
          //         traveler?.traveler?.contactPhone ||
          //         ""
          //       }
          //     />
          //   </div>
          // </div>
          <div key={idx}>
            {isEditing &&
            currentTravelerFormData &&
            expandedTraveler === idx ? (
              <TravelerForm
                key={`edit-${idx}`}
                travelerNumber={idx + 1}
                // traveler={traveler?.traveler}
                traveler={{
                  ...normalizeTravelerData(traveler?.traveler || traveler),
                  // Ensure passportIssuingCountry is set from either field
                  issuingCountry: normalizeTravelerData(
                    traveler?.traveler || traveler
                  )?.issuingCountry,
                  passportExpiry: normalizeTravelerData(
                    traveler?.traveler || traveler
                  )?.passportExpiry,
                  passportNumber: normalizeTravelerData(
                    traveler?.traveler || traveler
                  )?.passportNumber,
                }}
                isOpen={openTravelerIndex === idx}
                onToggle={() =>
                  setOpenTravelerIndex(openTravelerIndex === idx ? null : idx)
                }
                readOnly={false}
                onUpdate={handleTravelerUpdate}
              />
            ) : (
              // Keep the existing read-only view here
              <TravelerForm
                key={`view-${idx}`}
                travelerNumber={idx + 1}
                // traveler={traveler?.traveler}
                traveler={{
                  ...normalizeTravelerData(traveler?.traveler || traveler),
                  // Ensure passportIssuingCountry is set from either field
                  issuingCountry:
                    normalizeTravelerData(
                      traveler?.traveler ||
                        traveler ||
                        traveler?.traveler?._original
                    )?.issuingCountry ||
                    normalizeTravelerData(
                      traveler?.traveler ||
                        traveler ||
                        traveler?.traveler?._original
                    )?.issuingCountry ||
                    "",
                  passportExpiry: normalizeTravelerData(
                    traveler?.traveler || traveler
                  )?.passportExpiry,
                  passportNumber: normalizeTravelerData(
                    traveler?.traveler || traveler
                  )?.passportNumber,
                }}
                isOpen={openTravelerIndex === idx}
                onToggle={() =>
                  setOpenTravelerIndex(openTravelerIndex === idx ? null : idx)
                }
                readOnly={true}
                onUpdate={() => {}}
              />
            )}
          </div>
        ))}
        {/* </div> */}
      </CardContent>
    </Card>
  );
};

// Information alert component
const InfoAlert = ({
  icon,
  title,
  text,
  iconColor,
}: {
  icon: any;
  title: string;
  text: string;
  iconColor: string;
}) => (
  <div className="p-4 rounded-lg flex items-start gap-3 dark:bg-gray-600 bg-white">
    {icon &&
      React.cloneElement(icon, {
        className: `h-5 w-5 text-${iconColor} flex-shrink-0 mt-0.5`,
      })}
    <div>
      <p className="dark:text-white text-gray-700 font-semibold mb-1">
        {title}
      </p>
      <p className="text-gray-500 dark:text-gray-400 text-sm">{text}</p>
    </div>
  </div>
);

// Agent payment details component
const AgentPaymentDetails = ({ booking }: { booking: any }) => {
  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading title="Submitted Payment Details" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-6 mb-6">
          {[
            {
              label: "Payment Method",
              value:
                booking?.payment === null
                  ? "Airvilla Wallet"
                  : normalizePaymentMethod(booking?.payment?.paymentMethod) ||
                    "N/A",
              icon: <CreditCard className="h-4 w-4 text-red-500" />,
            },
            {
              label: "Reference Number",
              value: booking?.payment?.paymentReference || "N/A",
              icon: <Hash className="h-4 w-4 text-red-500" />,
            },
            {
              label: "Transaction Date",
              value:
                booking?.payment?.createdAt || booking?.createdAt
                  ? getFormatDateTable(
                      booking?.payment?.createdAt || booking?.createdAt
                    )
                  : "N/A",
              icon: <Calendar className="h-4 w-4 text-red-500" />,
            },
            {
              label: "Payment Amount",
              value:
                `${Number(
                  booking?.payment?.amount ??
                    booking.bookingResult?.booking?.bookedSeats?.[0]?.totalPrice
                )?.toFixed(2)} ${booking?.payment?.currency || "JOD"}` || "N/A",
              icon: <DollarSign className="h-4 w-4 text-red-500" />,
            },
          ].map((item, index) => (
            <div
              key={index}
              className="p-4 rounded-lg dark:bg-gray-600 bg-white"
            >
              <div className="flex items-center mb-2">
                {item.icon}
                <p className="dark:text-gray-300 text-gray-700 ml-2">
                  {item.label}
                </p>
              </div>
              <p className="dark:text-white text-gray-700 font-semibold text-xl">
                {item.value}
              </p>
            </div>
          ))}
        </div>
        {booking?.payment?.paymentMeta?.remarks && (
          <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
            <div className="flex items-center mb-2">
              <FileText className="h-4 w-4 text-red-500 mr-2" />
              <p className="dark:text-gray-300 text-gray-500">Remarks</p>
            </div>
            <p className="dark:text-white text-gray-700 break-words">
              {booking?.payment?.paymentMeta?.remarks}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
// Travel advisories and requirements component
const AdditionalInformation = () => (
  <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
    <CardHeader>
      <SectionHeading title="Important Information" />
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        <InfoAlert
          icon={<AlertCircle />}
          iconColor="red-500"
          title="Modification Policy"
          text="Please note that traveler details cannot be modified within 72 hours before the scheduled flight departure time."
        />

        <InfoAlert
          icon={<AlertCircle />}
          iconColor="blue-500"
          title="Check-in Requirements"
          text="Please arrive at the airport at least 3 hours before your scheduled departure time for international flights."
        />

        <InfoAlert
          icon={<AlertCircle />}
          iconColor="yellow-500"
          title="Travel Documents"
          text="Remember to bring your passport and any required visas or travel documents for your destination."
        />
      </div>
    </CardContent>
  </Card>
);

// Pricing section component showing fare breakdown
const PaymentSummary = ({
  data,
  handleDocumentAction,
}: {
  data: any;
  handleDocumentAction: (
    type: "e-ticket" | "receipt",
    action: "download" | "print"
  ) => Promise<void>;
}) => {
  const router = useRouter();
  const [fetchedTicket, setFetchedTicket] = useState<any>(null);

  useEffect(() => {
    const fetchTicket = async () => {
      const ticketId = data.ticket?.id;
      try {
        const ticket = await getBookingById(ticketId as string);
        setFetchedTicket(ticket);
      } catch (error) {
        console.error("Error fetching ticket:", error);
      }
    };
    fetchTicket();
  }, [fetchedTicket]);
  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );

  // Now you can access the data
  const { bookingResult } = bookingConfirmation;

  const booking = data.bookingResult?.booking || data.bookingResult;
  const ticket = booking?.ticket || {};
  const segments = ticket?.segments || [];
  const flightClasses =
    bookingConfirmation?.ticket?.ticket?.flightClasses ||
    bookingConfirmation?.fullTicket?.ticket?.flightClasses ||
    ticket?.flightClasses ||
    [];
  const returnFlightClasses =
    bookingConfirmation?.ticket?.ticket?.returnFlightClasses ||
    bookingConfirmation?.fullTicket?.ticket?.returnFlightClasses ||
    ticket?.returnFlightClasses ||
    [];
  const flightClass = flightClasses[0] || {};
  const returnFlightClass = returnFlightClasses[0] || {};
  const price = flightClass?.price || {};
  const returnPrice = returnFlightClass?.price || {};

  // Get passenger counts from booking data or default to 1 adult
  const passengerCounts = data?.travelerData || {};

  const calculatedPassengerCounts = useMemo(() => {
    // First, try to get from bookingResult.travelers if available
    const travelers = bookingResult?.travelers || [];
    if (travelers.length > 0) {
      const now = new Date();

      const getAge = (birthDate: string) => {
        const birth = new Date(birthDate);
        let age = now.getFullYear() - birth.getFullYear();
        const monthDiff = now.getMonth() - birth.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && now.getDate() < birth.getDate())
        ) {
          age--;
        }
        return age;
      };

      let adults = 0;
      let children = 0;
      let infants = 0;

      travelers.forEach((traveler: any) => {
        const dob = traveler?.traveler?.dateOfBirth;
        if (!dob) {
          // If no DOB, assume adult
          adults++;
          return;
        }

        const age = getAge(dob);
        if (age < 2) {
          infants++;
        } else if (age < 12) {
          children++;
        } else {
          adults++;
        }
      });

      return {
        adults,
        children,
        infants,
        travelClass: flightClass?.type || "Economy",
      };
    }

    // Fallback to searchParams if available
    const searchParams = data?.searchParams || {};
    if (searchParams.adults || searchParams.children || searchParams.infants) {
      return {
        adults: parseInt(searchParams.adults) || 0,
        children: parseInt(searchParams.children) || 0,
        infants: parseInt(searchParams.infants) || 0,
        travelClass: flightClass?.type || "Economy",
      };
    }

    // Default to 1 adult if no other data is available
    return {
      adults: 1,
      children: 0,
      infants: 0,
      travelClass: flightClass?.type || "Economy",
    };
  }, [bookingResult, data?.searchParams, flightClass?.type]);

  // Add this near your other useMemo hooks
  const priceDetails = useMemo(() => {
    // Get the price object from flight class or use default values
    const prices = flightClass?.price || {
      adult: 0,
      child: 0,
      infant: 0,
      tax: 0,
      currency: "USD", // Default currency
    };

    const { adults, children, infants } = calculatedPassengerCounts;
    // Calculate base fares
    const adultFare = adults * (prices.adult || 0);
    const childFare = children * (prices.child || 0);
    const infantFare = infants * (prices.infant || 0);
    const totalBaseFare = adultFare + childFare + infantFare;

    // Calculate taxes
    const totalTax = (prices.tax || 0) * (adults + children); // Typically infants don't pay taxes

    return {
      adultFare,
      childFare,
      infantFare,
      totalBaseFare,
      tax: totalTax,
      total: totalBaseFare + totalTax,
      currency: prices.currency,
    };
  }, [flightClass, calculatedPassengerCounts]);

  // Check if return ticket exists
  const hasReturnTicket = returnFlightClasses.length > 0;

  // Get currency and prices
  const currency = price?.currency || "JOD";
  const adultPrice = price?.adult || 0;
  const childPrice = price?.child || 0;
  const infantPrice = price?.infant || 0;
  const returnAdultPrice = returnPrice?.adult || 0;
  const returnChildPrice = returnPrice?.child || 0;
  const returnInfantPrice = returnPrice?.infant || 0;
  const departureTaxRate = price?.tax / 100 || 0;
  const returnTaxRate = returnPrice?.tax / 100 || 0;

  // Calculate fares based on passenger counts
  const departureAdultsFare =
    adultPrice * (calculatedPassengerCounts?.adults || 0);
  const departureChildrenFare =
    childPrice * (calculatedPassengerCounts?.children || 0);
  const departureInfantsFare =
    infantPrice * (calculatedPassengerCounts?.infants || 0);
  const returnAdultsFare =
    returnAdultPrice * (calculatedPassengerCounts?.adults || 0);
  const returnChildrenFare =
    returnChildPrice * (calculatedPassengerCounts?.children || 0);
  const returnInfantsFare =
    returnInfantPrice * (calculatedPassengerCounts?.infants || 0);

  // Calculate base fares
  const outboundBaseFare =
    departureAdultsFare + departureChildrenFare + departureInfantsFare;
  const returnBaseFare =
    returnAdultsFare + returnChildrenFare + returnInfantsFare;
  console.log("departureAdultsFare", departureAdultsFare);
  console.log("departureChildrenFare", departureChildrenFare);
  console.log("departureInfantsFare", departureInfantsFare);
  console.log("outboundBaseFare", outboundBaseFare);

  // Calculate taxes separately for departure and return
  const departureTax = outboundBaseFare * departureTaxRate;
  const returnTax = hasReturnTicket ? returnBaseFare * returnTaxRate : 0;

  // Calculate taxes (for both outbound and return if applicable)
  const totalBaseFare = outboundBaseFare + returnBaseFare;
  const taxes = departureTax + returnTax;

  const outboundFareBreakdown = [
    {
      label: "Adult",
      count: calculatedPassengerCounts.adults,
      perPersonValue: adultPrice,
      value: departureAdultsFare,
    },
    {
      label: "Child",
      count: calculatedPassengerCounts.children,
      perPersonValue: childPrice,
      value: departureChildrenFare,
    },
    {
      label: "Infant",
      count: calculatedPassengerCounts.infants,
      perPersonValue: infantPrice,
      value: departureInfantsFare,
    },
  ].filter((i) => i.count > 0);

  const returnFareBreakdown = hasReturnTicket
    ? [
        {
          label: "Adult",
          count: calculatedPassengerCounts.adults,
          perPersonValue: returnAdultPrice,
          value: returnAdultsFare,
        },
        {
          label: "Child",
          count: calculatedPassengerCounts.children,
          perPersonValue: returnChildPrice,
          value: returnChildrenFare,
        },
        {
          label: "Infant",
          count: calculatedPassengerCounts.infants,
          perPersonValue: returnInfantPrice,
          value: returnInfantsFare,
        },
      ].filter((i) => i.count > 0)
    : [];

  // Fixed transaction fee
  const transactionFee = 0;
  // Calculate total with fallback to bookedSeats totalPrice if available
  const total =
    totalBaseFare + taxes + transactionFee ||
    (bookingResult?.bookedSeats && bookingResult.bookedSeats.length > 0
      ? parseFloat(bookingResult.bookedSeats[0].totalPrice)
      : 0);

  return (
    <Card className="border-0 shadow-lg dark:bg-gray-800 bg-gray-200">
      <CardHeader className="pb-3">
        <SectionHeading title="Booking Summary" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Payment confirmation card */}
          <div className="p-4 rounded-lg mb-4 dark:bg-gray-700 bg-gray-100">
            <div className="flex items-center mb-4">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <h3 className="font-semibold dark:text-white text-gray-600">
                Payment Successful
              </h3>
            </div>
            <div className="h-px dark:bg-gray-600 bg-gray-200 w-full mb-3"></div>
            <div className="text-sm dark:text-gray-200 text-gray-600">
              <p className="flex items-center justify-between mb-2">
                <span className="text-gray-600 dark:text-gray-200">
                  Transaction ID:
                </span>
                <span>
                  {data?.bookingResult?.receipt?.id || "TRN-7C64VBPOAM"}
                </span>
              </p>
              <p className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-200">
                  Transaction Date:
                </span>
                <span>
                  {getFormatDateTable(data?.bookingResult?.receipt?.issuedAt) ||
                    getFormatDateTable(data?.transaction?.date) ||
                    getFormatDateTable(data?.payment?.transactionDate) ||
                    getFormatDateTable(data?.transactionDate) ||
                    "18/05/2025"}
                </span>
              </p>
            </div>
          </div>

          {/* Price breakdown card */}
          <div className="space-y-3 p-4 rounded-lg dark:bg-gray-700 bg-gray-100">
            <h3 className="font-semibold mb-2 dark:text-white text-gray-700">
              Price Details
            </h3>
            <div className="h-px dark:bg-gray-600 bg-gray-200 w-full mb-3"></div>

            {
              <>
                {/* OUTBOUND FARE */}
                <div className="flex justify-start mb-2">
                  <span className="bg-red-500/20 dark:text-white text-gray-600 px-3 py-1 rounded-md font-medium text-sm inline-block">
                    OUTBOUND
                  </span>
                </div>
                <div className="block mb-3">
                  {outboundFareBreakdown.map((item: any) => (
                    <FareBreakdownRow
                      key={`departure-${item.label}`}
                      item={item}
                      currency={currency}
                    />
                  ))}
                </div>
                {/* RETURN FARE - Only show if return ticket exists */}
                {hasReturnTicket && (
                  <>
                    <div className="flex justify-start mb-2 mt-4">
                      <span className="bg-blue-500/20 dark:text-white text-gray-600 px-3 py-1 rounded-md font-medium text-sm inline-block">
                        RETURN
                      </span>
                    </div>
                    <div className="block mb-3">
                      {returnFareBreakdown
                        .filter((item: any) => item.count > 0)
                        .map((item: any) => (
                          <FareBreakdownRow
                            key={`return-${item.label}`}
                            item={item}
                            currency={currency}
                          />
                        ))}
                    </div>
                  </>
                )}
                {/* Taxes */}
                <div className="h-px dark:bg-gray-600 bg-gray-200 w-full my-2"></div>
                <div className="flex justify-between mt-4">
                  <span className="dark:text-gray-300 text-gray-600">
                    Taxes
                  </span>
                  <span className="dark:text-white text-gray-600">
                    {taxes > 0 ? `${taxes.toFixed(2)} ${currency}` : "JOD"}
                  </span>
                </div>
                {/* Transaction Fee */}
                <div className="flex justify-between">
                  <span className="dark:text-gray-300 text-gray-600">
                    Transaction Fee
                  </span>
                  <span className="dark:text-white text-gray-600">
                    {transactionFee > 0
                      ? `${transactionFee.toFixed(2)} ${currency}`
                      : `0.00 ${currency}`}
                  </span>
                </div>
                {/* Total */}
                <div className="h-px dark:bg-gray-600 bg-gray-200 w-full my-2"></div>
                <div className="pt-1 flex justify-between font-semibold">
                  <span className="dark:text-white text-gray-600">Total</span>
                  <span className="dark:text-white text-gray-600">
                    {total > 0 ? `${total.toFixed(2)} ${currency}` : "N/A"}
                  </span>
                </div>
              </>
            }
          </div>

          {/* Separator */}
          <div className="h-px dark:bg-gray-500 bg-gray-200 w-full my-4"></div>

          {/* Booking Actions card */}
          <div className="p-4 rounded-lg mb-4 dark:bg-gray-700 bg-white">
            <h3 className="font-semibold mb-3 dark:text-white text-gray-700">
              Booking Actions
            </h3>
            <div className="h-px dark:bg-gray-500 bg-gray-200 w-full mb-4"></div>
            <div className="space-y-3">
              <button
                onClick={() => router.push("/support?category=ticket")}
                className="w-full py-3 px-6 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors duration-300"
              >
                Request Modification
              </button>
              <button
                onClick={() => router.push("/ticket-hub/myBookings")}
                className="w-full py-3 px-6 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 transition-colors duration-300"
              >
                My Bookings
              </button>
            </div>
          </div>

          {/* Separator line between Booking Actions and Travel Documents */}
          <div className="h-px dark:bg-gray-500 bg-gray-200 w-full my-4"></div>

          {/* Travel Documents section */}
          <div className="p-5 rounded-lg dark:bg-gray-700 bg-gray-100">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold dark:text-white text-gray-700">
                Ticket & Receipt
              </h3>
            </div>
            <div className="h-px dark:bg-gray-500 bg-gray-200 w-full mb-4"></div>

            <div
              className="dark:bg-gray-600 bg-white rounded-lg p-4 mb-4"
              id="eticket-section"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Ticket className="h-5 w-5 text-red-500 mr-2" />
                  <span className="dark:text-white text-gray-700 font-medium">
                    E-Ticket & Receipt
                  </span>
                </div>
                <div className="text-sm dark:text-gray-400 text-gray-600">
                  PDF Format
                </div>
              </div>
              <div className="h-px dark:bg-gray-500 bg-gray-200 w-full my-3"></div>
              <p className="dark:text-gray-300 text-gray-500 text-sm mb-3">
                This includes your electronic ticket with all flight and booking
                details, along with the receipt for the flight tickets
                associated with this transaction.
              </p>

              <div className="flex gap-3">
                <button
                  onClick={() => handleDocumentAction("e-ticket", "print")}
                  className="flex-1 flex items-center justify-center py-2 px-3 bg-gray-700 text-white text-sm font-medium rounded-md hover:bg-blue-500 transition-colors duration-300 print-hide"
                >
                  <Download className="h-3.5 w-3.5 mr-1.5" />
                  Download
                </button>
              </div>
            </div>
          </div>

          {/* Separator line between Travel Documents and Support section */}
          <div className="h-px dark:bg-gray-500 bg-gray-200 w-full my-4"></div>

          {/* Support contact section */}
          <div className="space-y-3 p-4 rounded-lg dark:bg-gray-700 bg-gray-100">
            <h3 className="font-semibold mb-2 dark:text-white text-gray-700">
              Need Help?
            </h3>
            <div className="h-px dark:bg-gray-500 bg-gray-200 w-full mb-3"></div>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="h-4 w-4 text-red-500 mr-3" />
                <a
                  href="mailto:<EMAIL>"
                  className="dark:text-white text-gray-600 hover:text-red-500 transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <Headphones className="h-4 w-4 text-red-500 mr-3" />
                <a
                  href="/support"
                  className="dark:text-white text-gray-600 hover:text-red-500 transition-colors"
                >
                  Help & Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Flight card props interface
interface FlightCardProps {
  type: string;
  date: string;
  origin: string;
  destination: string;
  airline: string;
  duration: string;
  stops: string;
  departure: string;
  arrival: string;
  bgColor?: string;
}

// Flight information card component
const FlightCard = ({
  type,
  date,
  origin,
  destination,
  airline,
  duration,
  stops,
  departure,
  arrival,
  bgColor = "bg-red-500/20",
}: FlightCardProps) => (
  <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
    <div className="flex justify-between items-center mb-4">
      <div
        className={`${bgColor} dark:text-white text-gray-600 px-3 py-1 rounded-md font-medium text-sm`}
      >
        {type}
      </div>
      <div className="dark:text-white text-gray-600">{date}</div>
    </div>

    <div className="flex justify-between items-start mb-3">
      <div>
        <div className="flex items-center">
          <span className="text-xl font-bold dark:text-white text-gray-600 mr-2">
            {origin}
          </span>
          <span className="dark:text-white text-gray-600 mx-2">→</span>
          <span className="text-xl font-bold dark:text-white text-gray-600">
            {destination}
          </span>
        </div>
        <div className="dark:text-white text-gray-600 text-sm mt-1">
          {stops === "0" ? "Direct Flight" : `${stops} Stops`} • {duration}
        </div>
      </div>
      <div className="text-right dark:text-white text-gray-600 font-medium">
        {airline}
      </div>
    </div>

    <div className="flex dark:text-white text-gray-600 text-sm">
      <div className="mr-4">Departure: {departure}</div>
      <div>•</div>
      <div className="ml-4">Arrival: {arrival}</div>
    </div>
  </div>
);

// Divider component between flight cards
const FlightSeparator = () => (
  <div className="flex items-center justify-center py-2 mb-3">
    <div className="h-px dark:bg-gray-600 bg-gray-200 flex-grow"></div>
    <div className="mx-3 flex items-center justify-center rounded-full dark:bg-gray-600 bg-gray-200 w-6 h-6">
      <span className="dark:text-white text-gray-600 text-sm">↕</span>
    </div>
    <div className="h-px dark:bg-gray-600 bg-gray-200 flex-grow"></div>
  </div>
);

// Flight itinerary component
const ItineraryCard = ({ data }: { data: any }) => {
  // Extract ticket and passengerCounts from the parent data object if needed (as in ManageBooking)
  const booking = data.bookingResult?.booking || data.bookingResult || data;
  const ticket = booking?.ticket || data?.ticket || {};
  const segments = ticket?.segments || [];
  const flightClasses = ticket?.flightClasses || [];
  const flightClass = flightClasses[0] || {};
  // Get passenger counts from booking data or default to 1 adult
  const passengerCounts = data?.travelerData || data?.travelers || {};

  const calculatedPassengerCounts = useMemo(() => {
    // First, try to get from bookingResult.travelers if available
    const travelers = passengerCounts || [];
    if (travelers.length > 0) {
      const now = new Date();

      const getAge = (birthDate: string) => {
        const birth = new Date(birthDate);
        let age = now.getFullYear() - birth.getFullYear();
        const monthDiff = now.getMonth() - birth.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && now.getDate() < birth.getDate())
        ) {
          age--;
        }
        return age;
      };

      let adults = 0;
      let children = 0;
      let infants = 0;

      travelers.forEach((traveler: any) => {
        const dob = traveler?.traveler?.dateOfBirth;
        if (!dob) {
          // If no DOB, assume adult
          adults++;
          return;
        }

        const age = getAge(dob);
        if (age < 2) {
          infants++;
        } else if (age < 12) {
          children++;
        } else {
          adults++;
        }
      });

      return {
        adults,
        children,
        infants,
        travelClass: flightClass?.type || "Economy",
      };
    }

    // Fallback to searchParams if available
    const searchParams = data?.searchParams || {};
    if (searchParams.adults || searchParams.children || searchParams.infants) {
      return {
        adults: parseInt(searchParams.adults) || 0,
        children: parseInt(searchParams.children) || 0,
        infants: parseInt(searchParams.infants) || 0,
        travelClass: flightClass?.type || "Economy",
      };
    }

    // Default to 1 adult if no other data is available
    return {
      adults: 1,
      children: 0,
      infants: 0,
      travelClass: flightClass?.type || "Economy",
    };
  }, [booking, data?.searchParams, flightClass?.type]);

  // Get the first segment for flight details
  const firstSegment = segments[0] || {};
  // Get baggage allowance from flight class
  const carryOnAllowed = flightClass?.carryOnAllowed || 0;
  const carryOnWeight = flightClass?.carryOnWeight || 0;
  const checkedAllowed = flightClass?.checkedAllowed || 0;
  const checkedWeight = flightClass?.checkedWeight || 0;

  // Format baggage display (if needed for future use)
  const baggageDisplay = [];
  if (checkedAllowed > 0) {
    baggageDisplay.push(`${checkedAllowed} x ${checkedWeight} kg Checked`);
  }
  if (carryOnAllowed > 0) {
    baggageDisplay.push(`${carryOnAllowed} x ${carryOnWeight} kg Carry-on`);
  }

  // Get flight details with fallbacks
  const flightDate = ticket?.flightDate
    ? getFormatDate(ticket.flightDate)
    : "June 1, 2025";
  const origin =
    ticket?.departure?.airportCode ||
    firstSegment?.departure?.airportCode ||
    "AMM";
  const destination =
    ticket?.arrival?.airportCode || firstSegment?.arrival?.airportCode || "IST";

  console.log("origin", origin);
  console.log("destination", destination);

  const getAirlineInfo = (obj: any): string | undefined => {
    if (!obj) return undefined;
    const carrier = obj.carrier || obj[0]?.carrier;
    const flightNumber = obj.flightNumber || obj[0]?.flightNumber;
    return carrier && carrier !== "undefined" && carrier !== "null"
      ? carrier
      : undefined;
  };
  const airline = (() => {
    const sources = [
      data?.fullTicket?.segments?.[0],
      data?.ticket?.segments?.[0],
      data?.meta?.departure,
      data?.meta,
      ticket?.segments?.[0],
    ].filter(Boolean); // Remove any undefined/null sources

    for (const source of sources) {
      const carrier = getAirlineInfo(source);
      if (carrier) {
        const flightNumber = source.flightNumber || source[0]?.flightNumber;
        return flightNumber ? `${carrier} (${flightNumber})` : carrier;
      }
    }

    return "N/A";
  })();

  const duration = ticket?.duration || firstSegment?.duration || "2h 15m";
  const stops = ticket?.stops || firstSegment?.stops || "0";
  const departureTime =
    ticket?.departureTime || firstSegment?.departureTime
      ? getFormatTime(ticket?.departureTime || firstSegment?.departureTime)
      : "06:45";
  const arrivalTime =
    ticket?.arrivalTime || firstSegment?.arrivalTime
      ? getFormatTime(ticket?.arrivalTime || firstSegment?.arrivalTime)
      : "09:00";

  // Format traveler count with singular/plural form
  const formatTravelerCount = (count: number, type: string): string => {
    if (!count) return "";
    try {
      const singular = type.endsWith("s") ? type.slice(0, -1) : type;
      return `${count} ${count === 1 ? singular : type}`;
    } catch (error) {
      console.error("Error formatting traveler count:", error);
      return `${count} ${type}`;
    }
  };

  console.log("calculatedPassengerCounts", calculatedPassengerCounts);

  return (
    <Card className="border-0 shadow-lg mb-6 dark:bg-gray-700 bg-gray-100">
      <CardHeader>
        <SectionHeading title="Traveler Itinerary" />
      </CardHeader>
      <CardContent>
        {/* Outbound Flight */}
        <div className="mb-6">
          <FlightCard
            type="OUTBOUND"
            date={flightDate}
            origin={origin}
            destination={destination}
            airline={airline}
            duration={duration}
            stops={stops}
            departure={departureTime}
            arrival={arrivalTime}
            bgColor="bg-red-500/20"
          />
        </div>
        {/* No return flight for ONE_WAY. Add logic for round trip if needed. */}
        {/* Passenger & Baggage */}
        <div>
          <div className="p-4 rounded-lg dark:bg-gray-600 bg-white">
            <div className="flex justify-between items-center mb-3">
              <div className="dark:text-white text-gray-600 font-medium">
                Passenger & Baggage
              </div>
            </div>
            <div className="h-px dark:bg-gray-400 bg-gray-200 w-full mb-3"></div>
            <div className="flex justify-between items-center">
              <div className="dark:text-white text-gray-600 flex items-center">
                {calculatedPassengerCounts.adults +
                  calculatedPassengerCounts.children +
                  calculatedPassengerCounts.infants}{" "}
                {calculatedPassengerCounts.adults +
                  calculatedPassengerCounts.children +
                  calculatedPassengerCounts.infants >
                1
                  ? "Travelers"
                  : "Traveler"}{" "}
                • {calculatedPassengerCounts.adults}{" "}
                {calculatedPassengerCounts.adults === 1 ? "Adult" : "Adults"}
                {calculatedPassengerCounts.children > 0 &&
                  ` • ${calculatedPassengerCounts.children} ${
                    calculatedPassengerCounts.children === 1
                      ? "Child"
                      : "Children"
                  }`}
                {calculatedPassengerCounts.infants > 0 &&
                  ` • ${calculatedPassengerCounts.infants} ${
                    calculatedPassengerCounts.infants === 1
                      ? "Infant"
                      : "Infants"
                  }`}
              </div>
              <div className="dark:text-gray-300 text-gray-600 text-sm flex items-center gap-2">
                <p className="capitalize">
                  {passengerCounts?.travelClass ||
                    flightClass?.type ||
                    "Economy"}
                </p>
                {baggageDisplay.length > 0 ? (
                  baggageDisplay.map((item, index) => (
                    <span key={index}>
                      {" • "}
                      {item}
                    </span>
                  ))
                ) : (
                  <span>• No baggage included</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component for the booking confirmation page

// ...other component imports remain the same

const BookingConfirmedPage = () => {
  const dispatch = useDispatch();
  // const bookingResult = useSelector(selectBookingResult);
  // const travelerData = useSelector(selectTravelerData);
  // const fullTicket = useSelector(selectFullTicket);
  // const passengerCounts = useSelector(selectPassengerCounts);
  // const bookingType = useSelector(selectBookingType);
  // const itinerary = useSelector(selectItinerary);

  const params = useParams();
  const pathname = usePathname();
  // Try to get bookingId from params (preferred), fallback to path segment if necessary
  const bookingId = params.bookingId || pathname.split("/")[3];
  const travelerData2 = useSelector(selectTravelerData);
  const passengerCounts2 = useSelector(selectPassengerCounts);
  const bookingConfirmation = useAppSelector(
    (state: RootState) => state.bookingConfirmation
  );

  // Now you can access the data
  const { bookingResult } = bookingConfirmation;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchedTicket, setFetchedTicket] = useState<any>(null);
  const [isDocumentProcessing, setIsDocumentProcessing] = useState<{
    type: "e-ticket" | "receipt" | null;
    action: "download" | "print" | null;
  }>({ type: null, action: null });
  const [bookingData, setBookingData] = useState<any>(null);

  useEffect(() => {
    if (!bookingId) {
      setError(
        "No booking ID provided. Please check your link or try again from My Bookings."
      );
      return;
    }
    setLoading(true);
    getBookingById(bookingId as string)
      .then((booking) => {
        setBookingData(booking);
        dispatch(
          setBookingConfirmationData({
            bookingResult: booking,
            travelerData:
              (booking as any).travelerData ||
              (booking as any).travelers ||
              null,
            ticket: booking,
            fullTicket: fetchedTicket,
            passengerCounts: (booking as any).passengerCounts || {},
          })
        );
        setLoading(false);
      })
      .catch((err) => {
        setError(
          err?.message === "Booking not found."
            ? "Booking not found. Please check your booking reference or try again from My Bookings."
            : err?.message || "Failed to fetch booking data"
        );
        setLoading(false);
      });
  }, [bookingId, dispatch]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-8 flex items-center justify-center">
        <div>Loading booking confirmation...</div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-8 flex items-center justify-center">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  // Use local bookingData if available, fallback to Redux for legacy/flow
  const booking = bookingData || {};
  const travelers = booking?.travelers || [];

  const travelerData = travelers.some((t: any) => t.traveler)
    ? travelers.map((t: any) => t.traveler) // Use nested traveler data if available
    : travelerData2 || [];

  const handleDocumentAction = async (
    type: "e-ticket" | "receipt",
    action: "download" | "print"
  ): Promise<void> => {
    try {
      // Set document processing state
      setIsDocumentProcessing({ type, action });

      // Get the container element that wraps the booking confirmation content
      // Try multiple selectors to find the booking content
      const containerSelectors = [
        ".print-container",
        ".receipt-container",
        ".booking-container",
        ".booking-content",
        "body > div", // Fallback to first div in body
        "body", // Last resort use body
      ];

      let container: HTMLElement | null = null;
      for (const selector of containerSelectors) {
        container = document.querySelector(selector) as HTMLElement;
        if (container) {
          break;
        }
      }

      if (!container) {
        console.error(
          "Container element not found. Tried selectors:",
          containerSelectors
        );
        dispatch(
          setMsg({
            message:
              "Failed to generate document: Could not find booking content",
            success: false,
          })
        );
        setIsDocumentProcessing({ type: null, action: null });
        return;
      }

      // Get flight data from the correct structure
      const ticket = booking?.fullTicket || {};
      const bookingResult = booking?.bookingResult || {};

      // Handle API response structure when coming from tables
      const apiTicket = bookingResult?.ticket || {};

      // Determine if we're using data from Redux or from API response
      const effectiveTicket =
        Object.keys(ticket).length > 0 ? ticket : apiTicket;

      const segments = effectiveTicket?.segments || [];
      // Get the first segment for flight details
      const firstSegment = segments[0] || {};

      // Get baggage allowance from flight class
      const carryOnAllowed = firstSegment?.flightClass?.carryOnAllowed || 0;
      const carryOnWeight = firstSegment?.flightClass?.carryOnWeight || 0;
      const checkedAllowed = firstSegment?.flightClass?.checkedAllowed || 0;
      const checkedWeight = firstSegment?.flightClass?.checkedWeight || 0;

      // Format baggage display
      const baggageDisplay = [];
      if (checkedAllowed > 0) {
        baggageDisplay.push(`${checkedAllowed} x ${checkedWeight} kg Checked`);
      }
      if (carryOnAllowed > 0) {
        baggageDisplay.push(`${carryOnAllowed} x ${carryOnWeight} kg Carry-on`);
      }

      if (action === "download") {
        const html2pdf = (await import("html2pdf.js")).default;

        // Clone the container to avoid modifying the original
        const element = container.cloneNode(true) as HTMLElement;
        // element.style.padding = "20px"; // Add some padding for better print layout

        const travelerDataArr = Array.isArray(travelerData)
          ? travelerData.map((item: any) => item.traveler || {})
          : [];

        let travelerCards = "";

        travelerDataArr.forEach((traveler: TravelerDto, index: number) => {
          const t = traveler || {};
          travelerCards += `<div class="card">
        <div class="card-header">
          <h2 class="card-title">Traveler Details</h2>
        </div>
        <div class="card-content">
          <div class="passenger-section">
            <div class="section-header">
              <span style="font-size: 18px; font-weight: 600;">Traveler ${
                index + 1
              }</span>
            </div>
            <div class="divider"></div>
  
            <div style="margin-bottom: 24px;">
              <h3 class="section-title">Personal Information</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Title</label>
                  <div class="field-value">${capitalizeFirst(t.title)}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Gender</label>
                  <div class="field-value">${capitalizeFirst(t.gender)}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">First Name</label>
                  <div class="field-value">${t.firstName}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Last Name</label>
                  <div class="field-value">${t.lastName}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Date of Birth</label>
                  <div class="field-value">${getFormatDateTable(
                    t.dateOfBirth
                  )}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Nationality</label>
                  <div class="field-value">${t.nationality}</div>
                </div>
              </div>
            </div>
  
            <div class="divider"></div>
  
            <div style="margin-bottom: 24px;">
              <h3 class="section-title">Identification Documents</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Passport Number</label>
                  <div class="field-value">${t.documentNumber}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Passport Issuing Country</label>
                  <div class="field-value">${t.issuingCountry}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Passport Expiry</label>
                <div class="field-value">${getFormatDateTable(
                  t.expirationDate
                )}</div>
              </div>
            </div>
  
            <div class="divider"></div>
  
            <div>
              <h3 class="section-title">Contact Information</h3>
              <div style="margin-bottom: 16px;">
                <div class="form-field">
                  <label class="field-label">Email</label>
                  <div class="field-value">${t.contactEmail}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Phone Number</label>
                <div class="field-value">${t.contactPhone}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
        });

        // Set the HTML content
        element.innerHTML = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flight Booking Receipt - Airvilla Charters</title>
  <style>
    /* Print-optimized styles with light colors to preserve ink */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: Arial, sans-serif;
    }
    
    body {
      background-color: white;
      color: #333;
      line-height: 1.5;
    }
    
    .container {
      max-width: 1024px;
      margin: 0 auto;
      padding: 20px;
    }
    
    @media print {
      .container {
        padding: 0;
      }
    }
    
    /* Header styles */
    .receipt-header {
      border: 1px solid #ddd;
      border-radius: 12px;
      background-color: #ffffff;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .header-row {
      display: flex;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    .company-info {
      display: flex;
      align-items: center;
    }
    
    .company-details {
      margin-left: 12px;
    }
    
    .company-name {
      font-weight: bold;
      font-size: 24px;
    }
    
    .company-address {
      color: #555;
      font-size: 14px;
    }
    
    .receipt-label {
      text-align: right;
      padding: 8px 16px;
    }
    
    .receipt-label h2 {
      font-weight: bold;
      font-size: 20px;
    }
    
    .receipt-label p {
      color: #555;
      font-size: 14px;
    }
    
    .metadata-row {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
    }
    
    .metadata-box {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .metadata-label {
      color: #555;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .metadata-value {
      font-weight: 500;
      font-size: 14px;
    }
    
    /* Grid layout */
    .content-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 32px;
    }
    
    .left-column, .right-column {
      background-color: #f8f8f8;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 24px;
      background-color: #fff;
    }
    
    .card-header {
      border-bottom: 1px solid #eee;
      padding: 16px;
    }
    
    .card-title {
      font-weight: bold;
      font-size: 20px;
    }
    
    .card-content {
      padding: 16px;
    }
    
    /* Booking information */
    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }
    
    .info-box {
      padding: 16px;
      border-radius: 8px;
      background-color: #f0f0f0;
    }
    
    .info-label {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: #555;
    }
    
    .info-label svg {
      margin-right: 8px;
    }
    
    .info-value {
      font-weight: 600;
      font-size: 18px;
    }
    
    /* Flight card */
    .flight-card {
      padding: 16px;
      border-radius: 8px;
      background-color: #f0f0f0;
      margin-bottom: 24px;
    }
    
    .flight-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .flight-type {
      padding: 4px 12px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
    }
    
    .outbound {
      background-color: #ffe6e6;
      color: #c73232;
    }
    
    .return {
      background-color: #e6f0ff;
      color: #2952a3;
    }
    
    .flight-date {
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    
    .flight-date svg {
      margin-right: 8px;
    }
    
    .flight-route {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
    }
    
    .flight-codes {
      display: flex;
      align-items: center;
    }
    
    .flight-code {
      font-size: 20px;
      font-weight: bold;
    }
    
    .flight-arrow {
      margin: 0 8px;
      color: #777;
    }
    
    .flight-airline {
      text-align: right;
      font-weight: 500;
    }
    
    .flight-meta {
      color: #777;
      font-size: 14px;
      margin-top: 4px;
    }
    
    .flight-times {
      display: flex;
      font-size: 14px;
    }
    
    .time-item {
      display: flex;
      align-items: center;
      margin-right: 16px;
    }
    
    .time-item svg {
      margin-right: 4px;
    }
    
    .time-separator {
      margin: 0 16px;
    }
    
    /* Flight separator */
    .flight-separator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 0;
      margin-bottom: 12px;
    }
    
    .separator-line {
      height: 1px;
      background-color: #ddd;
      flex-grow: 1;
    }
    
    .separator-circle {
      margin: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #eee;
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
    
    /* Passenger section */
    .passenger-section {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 1px;
      background-color: #ddd;
      width: 100%;
      margin: 12px 0;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .form-field {
      margin-bottom: 8px;
    }
    
    .field-label {
      display: block;
      font-size: 14px;
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    .field-value {
      width: 100%;
      background-color: #fff;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .section-title {
      font-weight: bold;
      margin-bottom: 12px;
      font-size: 18px;
    }
    
    /* Payment details */
    .payment-status {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .payment-status svg {
      margin-right: 8px;
      color: #22c55e;
    }
    
    .price-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .price-label {
      color: #555;
    }
    
    .price-value {
      font-weight: 500;
    }
    
    .price-total {
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      padding-top: 4px;
    }
    
    .document-section {
      background-color: #f0f0f0;
      padding: 16px;
      border-radius: 8px;
    }
    
    .document-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    
    .document-title {
      display: flex;
      align-items: center;
    }
    
    .document-title svg {
      margin-right: 8px;
      color: #c73232;
    }
    
    .document-format {
      font-size: 14px;
      color: #777;
    }
    
    .document-description {
      color: #555;
      font-size: 14px;
      margin-bottom: 12px;
    }
    
    .button-row {
      display: flex;
      gap: 12px;
    }
    
    .button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
    
    .button:hover {
      background-color: #eee;
    }
    
    .button svg {
      margin-right: 6px;
    }
    
    .support-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .support-item svg {
      margin-right: 12px;
      color: #c73232;
    }
    
    .support-link {
      color: #333;
      text-decoration: none;
    }
    
    .support-link:hover {
      color: #c73232;
    }

    /* Icon classes */
    .icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }

    .icon-sm {
      width: 14px;
      height: 14px;
    }

    .icon-lg {
      width: 20px;
      height: 20px;
    }
    
    /* Receipt Footer */
    .receipt-footer {
      margin-top: 40px;
      border-top: 1px solid #ddd;
      padding-top: 24px;
      font-size: 14px;
    }
    
    .footer-content {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .footer-logo {
      flex: 1;
    }
    
    .footer-info {
      flex: 3;
      padding-left: 20px;
    }
    
    .footer-legal {
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    
    .footer-text {
      color: #555;
      margin-bottom: 4px;
    }
    
    .footer-validation {
      flex: 2;
      text-align: right;
    }
    
    .verification-seal {
      display: inline-flex;
      align-items: center;
      background-color: #f8f8f8;
      padding: 8px 14px;
      border-radius: 4px;
      border: 1px dashed #c73232;
      margin-bottom: 12px;
    }
    
    .verification-seal span {
      font-weight: 600;
      margin-left: 8px;
      color: #c73232;
    }
    
    .footer-date {
      color: #555;
      font-size: 13px;
    }
    
    .footer-disclaimer {
      border-top: 1px solid #eee;
      padding-top: 16px;
      padding-bottom: 6px;
      font-size: 12px;
      color: #777;
      text-align: center;
    }
    
    .footer-disclaimer p {
      margin-bottom: 6px;
    }
    
    .footer-contact-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-top: 16px;
      margin-bottom: 16px;
    }
    
    .footer-contact-item {
      display: flex;
      align-items: center;
    }
    
    .footer-contact-label {
      margin-left: 8px;
      font-weight: 500;
    }
    
    .footer-badges {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .footer-badge {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      background-color: #f8f8f8;
      border-radius: 4px;
      font-size: 12px;
      color: #555;
      font-weight: 500;
    }
    
    .footer-badge svg {
      margin-right: 6px;
    }

    /* For printing */
    @media print {
      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      
      .card, .receipt-header, .left-column, .right-column {
        break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Receipt Header -->
    <div class="receipt-header">
      <div class="header-row">
        <div class="company-info">
          <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="48">
          <div class="company-details">
            <h1 class="company-name">Airvilla Charters</h1>
            <p class="company-address">Wasfi At-Tall St. 101, Amman, Jordan</p>
          </div>
        </div>
        <div class="receipt-label">
          <h2>RECEIPT</h2>
          <p>#
          ${bookingResult?.receipt?.receiptNumber}
          </p>
        </div>
      </div>
      
      <div class="metadata-row">
        <div class="metadata-box">
          <p class="metadata-label">Receipt Date</p>
          <p class="metadata-value">${getFormatDateTable(
            bookingResult?.receipt?.createdAt
          )}</p>
        </div>
        <div class="metadata-box">
          <p class="metadata-label">Transaction Time</p>
          <p class="metadata-value">${getFormatTime(
            bookingResult?.receipt?.createdAt
          )}</p>
        </div>
        <div class="metadata-box">
          <p class="metadata-label">Transaction ID</p>
          <p class="metadata-value info-value">${
            bookingResult?.booking?.transactionId ?? "N/A"
          }</p>
        </div>
      </div>
    </div>
    
    <!-- Main Content Grid -->
    <div class="content-grid">
      <!-- Left Column -->
      <div class="left-column">
        <!-- Booking Information -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Booking Information</h2>
          </div>
          <div class="card-content">
            <div class="info-grid">
              <div class="info-box">
                <div class="info-label">
                  Booking ID
                </div>
                <div class="info-value">${
                  bookingResult?.eTicket?.bookingId
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Booking Reference Number
                </div>
                <div class="info-value">${bookingResult?.referenceNumber}</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Payment Method
                </div>
                <div class="info-value">${
                  // bookingResult?.booking?.source === "THIRD_PARTY"
                  //   ? "Airvilla Wallet"
                  //   : normalizePaymentMethod(
                  //       bookingResult?.paymentMethod ||
                  //         bookingResult?.payment?.paymentMethod ||
                  //         bookingResult?.data?.fullTicket?.payment
                  //           ?.paymentMethod ||
                  //         "N/A"
                  //     )
                  bookingResult?.booking?.source === "THIRD_PARTY" ||
                  booking?.bookingType === "THIRD_PARTY"
                    ? "Airvilla Wallet"
                    : normalizePaymentMethod(
                        bookingResult?.paymentMethod ||
                          bookingResult?.payment?.paymentMethod ||
                          bookingResult?.data?.fullTicket?.payment
                            ?.paymentMethod ||
                          "N/A"
                      )
                }</div>
              </div>
              <div class="info-box">
                <div class="info-label">
                  Booking Date
                </div>
                <div class="info-value">${getFormatDateTable(
                  bookingResult?.createdAt || "N/A"
                )}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Purchased Itinerary -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Purchased Itinerary</h2>
          </div>
          <div class="card-content">
            <!-- Outbound Flight -->
            <div class="flight-card">
              <div class="flight-header">
                <div class="flight-type outbound">OUTBOUND</div>
                <div class="flight-date">
                  ${getFormatDateTable(
                    bookingResult?.booking?.ticket?.flightDate ||
                      booking?.fullTicket?.ticket?.flightDate ||
                      "N/A"
                  )}
                </div>
              </div>
              
              <div class="flight-route">
                <div>
                  <div class="flight-codes">
                    <span class="flight-code">
                      ${bookingResult?.booking?.departure?.airportCode || "N/A"}
                    </span>
                    <span class="flight-arrow">→</span>
                    <span class="flight-code">${
                      bookingResult?.booking?.arrival?.airportCode || "N/A"
                    }</span>
                  </div>
                  <div class="flight-meta">
                    ${
                      bookingResult?.booking?.ticket?.stops === "0"
                        ? "Direct Flight"
                        : `${bookingResult?.booking?.ticket?.stops} Stops`
                    } • ${bookingResult?.booking?.ticket?.duration || "N/A"}  
                  </div>
                </div>
                <div class="flight-airline">
                  ${bookingResult?.booking?.airline?.name || "N/A"} (${
          bookingResult?.booking?.flightNumber || "N/A"
        })
                </div>
              </div>
              
              <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                <div class="time-item" style="margin-right: 0;">
                  Departure: ${getFormatTime(
                    bookingResult?.booking?.departureTime ||
                      booking?.fullTicket?.ticket?.departureTime ||
                      "N/A"
                  )}
                </div>
                <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                <div class="time-item" style="margin-left: 0;">
                  Arrival: ${getFormatTime(
                    bookingResult?.booking?.arrivalTime ||
                      booking?.fullTicket?.ticket?.arrivalTime ||
                      "N/A"
                  )}
                </div>
              </div>
            </div>
            
            <!-- Flight Separator -->
            <div class="flight-separator">
              <div class="separator-line"></div>
              <div class="separator-circle">↕</div>
              <div class="separator-line"></div>
            </div>
            
            <!-- Return Flight -->
            <div class="flight-card">
              <div class="flight-header">
                <div class="flight-type return">RETURN</div>
                <div class="flight-date">
                  ${getFormatDateTable(
                    bookingResult?.booking?.ticket?.flightDate
                  )}
                </div>
              </div>
              
              <div class="flight-route">
                <div>
                  <div class="flight-codes">
                    <span class="flight-code">${
                      bookingResult?.booking?.arrivalAirport?.iataCode || "N/A"
                    }</span>
                    <span class="flight-arrow">→</span>
                    <span class="flight-code">${
                      bookingResult?.booking?.departureAirport?.iataCode ||
                      "N/A"
                    }</span>
                  </div>
                  <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            } 
                  </div>
                </div>
                <div class="flight-airline">
                  ${bookingResult?.booking?.airline?.name || "N/A"} (${
          bookingResult?.booking?.flightNumber || "N/A"
        })
                </div>
              </div>
              
              <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                <div class="time-item" style="margin-right: 0;">
                  Departure: ${getFormatTime(
                    bookingResult?.booking?.arrivalTime ||
                      booking?.fullTicket?.ticket?.arrivalTime ||
                      "N/A"
                  )}
                </div>
                <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                <div class="time-item" style="margin-left: 0;">
                  Arrival: ${getFormatTime(
                    bookingResult?.booking?.departureTime ||
                      booking?.fullTicket?.ticket?.departureTime ||
                      "N/A"
                  )}
                </div>
              </div>
            </div>
            
            <!-- Passenger & Baggage -->
            <div class="flight-card">
              <h3 class="section-title" style="margin-bottom: 8px;">Passenger & Baggage</h3>
              <div class="divider"></div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; padding-bottom: 4px;">
                <div style="font-weight: 500;">${
                  bookingResult?.passengerCounts?.adults
                } Adult</div>
                <div style="font-size: 14px; color: #555;">${capitalizeFirst(
                  bookingResult?.passengerCounts?.[0]?.travelClass || "Economy"
                )}
                <span>${baggageDisplay.length > 0 ? " • " : ""}${
          baggageDisplay.length > 0
            ? baggageDisplay.map((item) => item).join(" • ")
            : "No baggage included"
        }</span></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Traveler Details -->
        ${travelerCards}
      </div>
      
      <!-- Right Column -->
      <div class="right-column">
        <!-- Payment Details -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Payment Details</h2>
          </div>
          <div class="card-content">
            <!-- Payment confirmation card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <div class="payment-status">
                <h3 style="font-weight: 600; font-size: 16px;">Payment Successful</h3>
              </div>
              <div class="divider"></div>
              <div style="font-size: 14px;">
                <div class="price-item">
                  <span class="price-label">Transaction ID:</span>
                  <div style="font-size: 12px;">${
                    booking?.bookingResult?.booking?.transactionId || "N/A"
                  }</div>
                </div>
                <div>
                  <span class="price-label">Transaction Date:</span>
                  <div style="font-size: 12px;">${getFormatDateTable(
                    booking?.bookingResult?.booking?.transactionDate || "N/A"
                  )}</div>
                </div>
              </div>
            </div>
            
            <!-- Price breakdown card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Price Details</h3>
              <div class="divider"></div>
              
              <!-- Price details will be populated dynamically -->
              <div class="price-item">
                <span class="price-label" style="font-weight: 600; font-size: 16px;">Total</span>
                <span>${
                  booking?.bookingResult?.totalAmount ||
                  bookingResult?.bookedSeats?.[0]?.totalPrice ||
                  "0.00"
                } ${
          booking?.bookingResult?.currency ||
          bookingResult?.meta?.pricing?.departure?.currency ||
          "JOD"
        }
                </span>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Receipt Information card -->
            <div class="info-box" style="margin-bottom: 16px;">
              <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Receipt Information</h3>
              <div class="divider"></div>
              <div style="margin-top: 16px;">
                <div class="price-item">
                  <span class="price-label" style="font-size: 12px;">Receipt Number:</span>
                  <div style="font-size: 12px;">${
                    bookingResult?.receipt?.receiptNumber
                  }</div>
                </div>
                <div>
                  <span class="price-label" style="font-size: 12px;">Booking ID:</span>
                  <div style="font-size: 12px;">${
                    bookingResult?.receipt?.bookingId
                  }</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Footer Section - Placed properly after content-grid but before closing container div -->
    <div class="receipt-footer">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="72">
        </div>
        <div class="footer-info">
          <p class="footer-legal">OFFICIAL RECEIPT DOCUMENT</p>
          <p class="footer-text">Thank you for choosing Airvilla Charters. This document serves as your official receipt and booking confirmation.</p>
          <p class="footer-text">Please retain this document for your records and present it when required.</p>
        </div>
        <div class="footer-validation">
          <div class="verification-seal">
            <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="#c73232" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span>Verified & Approved</span>
          </div>
          <div class="footer-date">Generated on: ${getFormatDate(
            new Date().toString()
          )} • ${getFormatTime(new Date().toString())}</div>
        </div>
      </div>
      
      <div class="footer-contact-grid">
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
          <span class="footer-contact-label"><EMAIL></span>
        </div>
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="9" y1="21" x2="9" y2="9"></line>
          </svg>
          <span class="footer-contact-label">www.airvilla-charters.travel</span>
        </div>
        <div class="footer-contact-item">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
          <span class="footer-contact-label">Secure Booking Platform</span>
        </div>
      </div>
      
      <div class="footer-badges">
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          Secure Transaction
        </div>
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 6v6l4 2"></path>
          </svg>
          24/7 Support
        </div>
        <div class="footer-badge">
          <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
          </svg>
          IATA Certified
        </div>
      </div>
      
      <div class="footer-disclaimer">
        <p>This document contains confidential information and is intended for the named recipient only and Authorized Agents. Unauthorized reproduction is strictly prohibited.</p>
        <p> 2025 Airvilla LLC. All rights reserved.</p>
      </div>
    </div>
  </div>`;
        // Add print-specific styles
        const style = document.createElement("style");
        style.textContent = `
          @page {
            size: A4;
          }
          @media print {
            body { 
              margin: 0; 
              padding: 0; 
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print { 
              display: none !important; 
            }
            .container { 
              width: 100% !important; 
              max-width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              box-shadow: none !important;
            }
            .card {
              page-break-inside: avoid;
              break-inside: avoid;
            }
          }
          body { 
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        `;
        element.prepend(style);

        // Set options for PDF generation
        const opt = {
          margin: [10, 0, 10, 0], // top, right, bottom, left
          filename: `booking-confirmation-${
            booking.bookingResult?.id || "ticket"
          }.pdf`,
          image: {
            type: "jpeg",
            quality: 0.98,
          },
          html2canvas: {
            scale: 2,
            useCORS: true,
            logging: true,
            letterRendering: true,
            scrollY: 0,
            scrollX: 0,
            windowWidth: document.documentElement.offsetWidth,
            allowTaint: true,
          },
          jsPDF: {
            unit: "mm",
            format: "a4",
            orientation: "portrait",
          },
          pagebreak: {
            mode: ["avoid-all", "css", "legacy"],
          },
        };

        // Create a temporary container for PDF generation
        const tempContainer = document.createElement("div");
        // tempContainer.style.position = "absolute";
        // tempContainer.style.left = "-9999px";
        tempContainer.appendChild(element);
        document.body.appendChild(tempContainer);

        try {
          // Generate and download PDF
          await html2pdf()
            .set(opt)
            .from(element)
            .save()
            .then(() => {
              dispatch(
                setMsg({
                  message: "Document downloaded successfully",
                  success: true,
                })
              );
            });
        } catch (error: any) {
          console.error("Error generating PDF:", error);
          dispatch(
            setMsg({
              message: `Failed to generate PDF: ${error.message}`,
              success: false,
            })
          );
        } finally {
          // Clean up the temporary container and reset processing state
          if (document.body.contains(tempContainer)) {
            document.body.removeChild(tempContainer);
          }
          setIsDocumentProcessing({ type: null, action: null });
        }
      } else if (action === "print") {
        // Declare printWindow at the function scope level
        let printWindow: Window | null = null;

        // Get flight data from the correct structure
        const ticket = booking?.fullTicket || {};
        const bookingResult = booking?.bookingResult || {};

        // Handle API response structure when coming from tables
        const apiTicket = bookingResult?.ticket || {};

        // Determine if we're using data from Redux or from API response
        const effectiveTicket =
          Object.keys(ticket).length > 0 ? ticket : apiTicket;

        const segments = effectiveTicket?.segments || [];
        // Get the first segment for flight details
        const firstSegment = segments[0] || {};

        const baggages =
          bookingResult?.ticket?.flightClasses?.[0] ||
          booking?.ticket?.flightClasses?.[0];

        // Get baggage allowance from flight class
        const carryOnAllowed =
          firstSegment?.flightClass?.carryOnAllowed ??
          baggages?.carryOnAllowed ??
          0;
        const carryOnWeight =
          firstSegment?.flightClass?.carryOnWeight ??
          baggages?.carryOnWeight ??
          0;
        const checkedAllowed =
          firstSegment?.flightClass?.checkedAllowed ??
          baggages?.checkedAllowed ??
          0;
        const checkedWeight =
          firstSegment?.flightClass?.checkedWeight ??
          baggages?.checkedWeight ??
          0;

        // Format baggage display
        const baggageDisplay = [];
        if (checkedAllowed > 0) {
          baggageDisplay.push(
            `${checkedAllowed} x ${checkedWeight} kg Checked`
          );
        }
        if (carryOnAllowed > 0) {
          baggageDisplay.push(
            `${carryOnAllowed} x ${carryOnWeight} kg Carry-on`
          );
        }

        const travelerData = Array.isArray(booking.travelerData)
          ? booking.travelerData.map((item: any) => item.traveler || {})
          : [];

        const isRoundTrip =
          bookingResult?.tripType === "ROUND_TRIP" ||
          booking?.fullTicket?.tripType === "ROUND_TRIP" ||
          bookingResult?.meta?.tripType === "ROUND_TRIP";

        const flightClasses =
          bookingConfirmation?.ticket?.ticket?.flightClasses ||
          bookingConfirmation?.fullTicket?.ticket?.flightClasses ||
          effectiveTicket?.flightClasses ||
          [];
        const returnFlightClasses =
          bookingConfirmation?.ticket?.ticket?.returnFlightClasses ||
          bookingConfirmation?.fullTicket?.ticket?.returnFlightClasses ||
          effectiveTicket?.returnFlightClasses ||
          [];
        const flightClass = flightClasses[0] || {};
        const returnFlightClass = returnFlightClasses[0] || {};
        const price = flightClass?.price || {};
        const returnPrice = returnFlightClass?.price || {};

        // Get passenger counts from booking data or default to 1 adult
        const passengerCounts = booking?.travelerData || {};

        const calculatedPassengerCounts = (() => {
          // Calculate from booking travelers if available
          const travelers =
            booking?.travelers ||
            passengerCounts ||
            booking?.bookingResult?.travelers ||
            [];

          if (travelers.length > 0) {
            const now = new Date();

            const getAge = (birthDate: string) => {
              const birth = new Date(birthDate);
              let age = now.getFullYear() - birth.getFullYear();
              const monthDiff = now.getMonth() - birth.getMonth();
              if (
                monthDiff < 0 ||
                (monthDiff === 0 && now.getDate() < birth.getDate())
              ) {
                age--;
              }
              return age;
            };

            let adults = 0;
            let children = 0;
            let infants = 0;

            travelers.forEach((traveler: any) => {
              const dob = traveler?.traveler?.dateOfBirth;
              if (!dob) {
                // If no DOB, assume adult
                adults++;
                return;
              }

              const age = getAge(dob);
              if (age < 2) {
                infants++;
              } else if (age < 12) {
                children++;
              } else {
                adults++;
              }
            });

            return {
              adults,
              children,
              infants,
              travelClass:
                booking?.bookingResult?.travelClass ||
                booking?.fullTicket?.ticket?.flightClasses[0]?.type ||
                "Economy",
            };
          }

          // Fallback to 1 adult by default
          return {
            adults: 1,
            children: 0,
            infants: 0,
            travelClass: "Economy",
          };
          // }, [passengerCounts, booking]);
        })();

        // Check if return ticket exists
        const hasReturnTicket = returnFlightClasses.length > 0;

        // Get currency and prices
        const currency = price?.currency || "JOD";
        const adultPrice = price?.adult || 0;
        const childPrice = price?.child || 0;
        const infantPrice = price?.infant || 0;
        const returnAdultPrice = returnPrice?.adult || 0;
        const returnChildPrice = returnPrice?.child || 0;
        const returnInfantPrice = returnPrice?.infant || 0;
        const taxRate = price?.tax / 100 || 0;

        // Calculate fares based on passenger counts
        const adultFare = adultPrice * (calculatedPassengerCounts?.adults || 0);
        const childFare =
          childPrice * (calculatedPassengerCounts?.children || 0);
        const infantFare =
          infantPrice * (calculatedPassengerCounts?.infants || 0);
        const returnAdultFare =
          returnAdultPrice * (calculatedPassengerCounts?.adults || 0);
        const returnChildFare =
          returnChildPrice * (calculatedPassengerCounts?.children || 0);
        const returnInfantFare =
          returnInfantPrice * (calculatedPassengerCounts?.infants || 0);

        // Calculate base fares
        const outboundBaseFare = adultFare + childFare + infantFare;
        const returnBaseFare =
          returnAdultFare + returnChildFare + returnInfantFare;

        // Calculate taxes (for both outbound and return if applicable)
        const totalBaseFare =
          outboundBaseFare + (hasReturnTicket ? returnBaseFare : 0);
        const taxes = totalBaseFare * taxRate;

        // Fixed transaction fee
        const transactionFee = 0;
        // Calculate total with fallback to bookedSeats totalPrice if available
        const total =
          totalBaseFare + taxes + transactionFee ||
          (bookingResult?.bookedSeats && bookingResult.bookedSeats.length > 0
            ? parseFloat(bookingResult.bookedSeats[0].totalPrice)
            : 0);

        const outboundFareBreakdown = [
          {
            label: "Adult",
            count: calculatedPassengerCounts.adults,
            perPersonValue: adultPrice,
            value: adultFare,
          },
          {
            label: "Child",
            count: calculatedPassengerCounts.children,
            perPersonValue: childPrice,
            value: childFare,
          },
          {
            label: "Infant",
            count: calculatedPassengerCounts.infants,
            perPersonValue: infantPrice,
            value: infantFare,
          },
        ].filter((i) => i.count > 0);

        const returnFareBreakdown = hasReturnTicket
          ? [
              {
                label: "Adult",
                count: calculatedPassengerCounts.adults,
                perPersonValue: returnAdultPrice,
                value: returnAdultFare,
              },
              {
                label: "Child",
                count: calculatedPassengerCounts.children,
                perPersonValue: returnChildPrice,
                value: returnChildFare,
              },
              {
                label: "Infant",
                count: calculatedPassengerCounts.infants,
                perPersonValue: returnInfantPrice,
                value: returnInfantFare,
              },
            ].filter((i) => i.count > 0)
          : [];

        const origin =
          booking?.ticket?.departure?.airportCode ||
          booking?.ticket?.arrival?.airportCode ||
          "AMM";
        const destination =
          booking?.ticket?.arrival?.airportCode ||
          booking?.ticket?.arrival?.airportCode ||
          "IST";

        const getAirlineInfo = (obj: any): string | undefined => {
          if (!obj) return undefined;
          const carrier = obj.carrier || obj[0]?.carrier;
          const flightNumber = obj.flightNumber || obj[0]?.flightNumber;
          return carrier && carrier !== "undefined" && carrier !== "null"
            ? carrier
            : undefined;
        };
        const airline = (() => {
          const sources = [
            // bookingConfirmation?.fullTicket?.segments?.[0],
            // bookingConfirmation?.ticket?.segments?.[0],
            booking?.ticket?.segments?.[0],
            booking?.ticket?.ticket?.segments?.[0],
            booking?.fullTicket?.segments?.[0],
            booking?.fullTicket?.ticket?.segments?.[0],
            // booking?.meta?.departure,
            // booking?.meta,
            // ticket?.segments?.[0],
          ].filter(Boolean); // Remove any undefined/null sources

          for (const source of sources) {
            const carrier = getAirlineInfo(source);
            if (carrier) {
              const flightNumber =
                source.flightNumber || source[0]?.flightNumber;
              return flightNumber ? `${carrier} (${flightNumber})` : carrier;
            }
          }

          return "N/A";
        })();

        let travelerCards = "";

        try {
          // Open the print window
          printWindow = window.open("", "_blank");

          if (!printWindow) {
            throw new Error(
              "Could not open print window. Please allow popups for this site."
            );
          }

          // Get the container element that wraps the booking confirmation content
          // const container = document.querySelector(".container");
          for (const selector of containerSelectors) {
            container = document.querySelector(selector);
            if (container) break;
          }

          if (!container) {
            throw new Error("Booking details container not found");
          }

          // Clone the container and its styles
          const element = container.cloneNode(true) as HTMLElement;
          element.style.padding = "20px"; // Add some padding for better print layout

          // Add print-specific styles
          const style = document.createElement("style");
          style.textContent = `
            @page {
              size: A4;
              margin: 10mm;
            }
            body { 
              margin: 0; 
              padding: 20px; 
              font-family: Arial, sans-serif;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print { 
              display: none !important; 
            }
            .container { 
              width: 100% !important; 
              max-width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
              box-shadow: none !important;
            }
            .card {
              page-break-inside: avoid;
              break-inside: avoid;
              margin-bottom: 20px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              overflow: hidden;
            }
            .card-header {
              border-bottom: 1px solid #eee;
              padding: 16px;
            }
            .card-title {
              margin: 0;
              font-size: 20px;
              font-weight: 600;
            }
            .card-content {
              padding: 16px;
            }
            @media print {
              body { 
                margin: 0; 
                padding: 0; 
              }
              .no-print { 
                display: none !important; 
              }
              .container { 
                width: 100% !important; 
                margin: 0 !important;
                padding: 0 !important;
              }
              .card {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 10mm;
              }
              .card:last-child {
                margin-bottom: 0;
              }
            }
          `;

          // Create a new document for printing
          const printDocument = printWindow.document;
          printDocument.open();
          printDocument.write(`<!DOCTYPE html>
            <html>
              <head>
                <title>Booking Confirmation - ${
                  booking.bookingResult?.id || ""
                }</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>${style.textContent}</style>
              </head>
              <body>
                ${element.outerHTML}
                <script>
                  // Close the print window after printing
                  window.onload = function() {
                    setTimeout(function() {
                      window.print();
                      setTimeout(function() {
                        window.close();
                      }, 100);
                    }, 200);
                  };
                </script>
              </body>
            </html>
          `);

          printDocument.close();
          printWindow.focus();
        } catch (error: any) {
          console.error("Error preparing print:", error);
          dispatch(
            setMsg({
              message: `Failed to prepare print: ${error.message}`,
              success: false,
            })
          );
          setIsDocumentProcessing({ type: null, action: null });
        }
        travelers?.forEach((traveler: any, index: number) => {
          const t = traveler?.traveler || {};

          travelerCards += `
        <div class="card-content">
          <div class="passenger-section">
            <div class="section-header">
              <span style="font-size: 18px; font-weight: 600;">Traveler ${
                index + 1
              }: ${t?.firstName || "N/A"} ${t?.lastName || "N/A"}
              </span>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Personal Information</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Title</label>
                  <div class="field-value">${capitalizeFirst(t.title)}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Gender</label>
                  <div class="field-value">${capitalizeFirst(t.gender)}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">First Name</label>
                  <div class="field-value">${t.firstName}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Last Name</label>
                  <div class="field-value">${t.lastName}</div>
                </div>
              </div>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Date of Birth</label>
                  <div class="field-value">${getFormatDateTable(
                    t.dateOfBirth
                  )}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Nationality</label>
                  <div class="field-value">${t.nationality}</div>
                </div>
              </div>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Identification Documents</h3>
              <div class="form-grid">
                <div class="form-field">
                  <label class="field-label">Passport Number</label>
                  <div class="field-value">${t.documentNumber}</div>
                </div>
                <div class="form-field">
                  <label class="field-label">Passport Issuing Country</label>
                  <div class="field-value">${t.issuingCountry}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Passport Expiry</label>
                <div class="field-value">${getFormatDateTable(
                  t.expirationDate
                )}</div>
              </div>
            </div>
  
            <div class="card" style="margin-bottom: 24px; padding: 16px;">
              <h3 class="section-title">Contact Information</h3>
              <div style="margin-bottom: 16px;">
                <div class="form-field">
                  <label class="field-label">Email</label>
                  <div class="field-value">${t.contactEmail}</div>
                </div>
              </div>
              <div class="form-field">
                <label class="field-label">Phone Number</label>
                <div class="field-value">${t.contactPhone}</div>
              </div>
            </div>
          </div>
        </div>
    `;
        });

        if (!printWindow) {
          throw new Error("Print window is not available");
        }

        // Write the closing HTML and close the document
        printWindow.document.write(`<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Booking Receipt - Airvilla Charters</title>
    <style>
      /* Print-optimized styles with light colors to preserve ink */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: Arial, sans-serif;
      }
      
      body {
        background-color: white;
        color: #333;
        line-height: 1.5;
      }
      
      .container {
        max-width: 1024px;
        margin: 0 auto;
        padding: 20px;
      }
      
      @media print {
        .container {
          padding: 0;
        }
      }
      
      /* Header styles */
      .receipt-header {
        border: 1px solid #ddd;
        border-radius: 12px;
        background-color: #ffffff;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      
      .header-row {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
      }
      
      .company-info {
        display: flex;
        align-items: center;
      }
      
      .company-details {
        margin-left: 12px;
      }
      
      .company-name {
        font-weight: bold;
        font-size: 24px;
      }
      
      .company-address {
        color: #555;
        font-size: 14px;
      }
      
      .receipt-label {
        text-align: right;
        padding: 8px 16px;
      }
      
      .receipt-label h2 {
        font-weight: bold;
        font-size: 20px;
      }
      
      .receipt-label p {
        color: #555;
        font-size: 14px;
      }
      
      .metadata-row {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
      }
      
      .metadata-box {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .metadata-label {
        color: #555;
        font-size: 14px;
        margin-bottom: 8px;
      }
      
      .metadata-value {
        font-weight: 500;
        font-size: 14px;
      }
      
      /* Grid layout */
      .content-grid {
        display: flex;
        flex-direction: column-reverse;
        gap: 24px;
        width: 100%;
        max-width: 100%;
      }
      
      .left-column, .right-column {
        background-color: #f8f8f8;
        border-radius: 12px;
        padding: 48px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      
      .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 8px;
        background-color: #fff;
      }
      
      .card-header {
        border-bottom: 1px solid #eee;
        padding: 16px;
      }
      
      .card-title {
        font-weight: bold;
        font-size: 20px;
      }
      
      .card-content {
        padding: 16px;
      }
      
      /* Booking information */
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
      }
      
      .info-box {
        padding: 16px;
        border-radius: 8px;
        background-color: #f0f0f0;
      }
      
      .info-label {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: #555;
      }
      
      .info-label svg {
        margin-right: 8px;
      }
      
      .info-value {
        font-weight: 600;
        font-size: 18px;
      }
      
      /* Flight card */
      .flight-card {
        padding: 16px;
        border-radius: 8px;
        background-color: #f0f0f0;
        margin-bottom: 24px;
      }
      
      .flight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .flight-type {
        padding: 4px 12px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
      }
      
      .outbound {
        background-color: #ffe6e6;
        color: #c73232;
      }
      
      .return {
        background-color: #e6f0ff;
        color: #2952a3;
      }
      
      .flight-date {
        font-weight: 500;
        display: flex;
        align-items: center;
      }
      
      .flight-date svg {
        margin-right: 8px;
      }
      
      .flight-route {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }
      
      .flight-codes {
        display: flex;
        align-items: center;
      }
      
      .flight-code {
        font-size: 20px;
        font-weight: bold;
      }
      
      .flight-arrow {
        margin: 0 8px;
        color: #777;
      }
      
      .flight-airline {
        text-align: right;
        font-weight: 500;
      }
      
      .flight-meta {
        color: #777;
        font-size: 14px;
        margin-top: 4px;
      }
      
      .flight-times {
        display: flex;
        font-size: 14px;
      }
      
      .time-item {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      
      .time-item svg {
        margin-right: 4px;
      }
      
      .time-separator {
        margin: 0 16px;
      }
      
      /* Flight separator */
      .flight-separator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 0;
        margin-bottom: 12px;
      }
      
      .separator-line {
        height: 1px;
        background-color: #ddd;
        flex-grow: 1;
      }
      
      .separator-circle {
        margin: 0 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #eee;
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }
      
      /* Passenger section */
      .passenger-section {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .divider {
        height: 1px;
        background-color: #ddd;
        width: 100%;
        margin: 12px 0;
      }
      
      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .form-field {
        margin-bottom: 8px;
      }
      
      .field-label {
        display: block;
        font-size: 14px;
        margin-bottom: 4px;
        font-weight: 500;
      }
      
      .field-value {
        width: 100%;
        background-color: #eee;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
      }
      
      .section-title {
        font-weight: bold;
        margin-bottom: 12px;
        font-size: 18px;
      }
      
      /* Payment details */
      .payment-status {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
      }
      
      .payment-status svg {
        margin-right: 8px;
        color: #22c55e;
      }
      
      .price-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      
      .price-label {
        color: #555;
      }
      
      .price-value {
        font-weight: 500;
      }
      
      .price-total {
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        padding-top: 4px;
      }
      
      .document-section {
        background-color: #f0f0f0;
        padding: 16px;
        border-radius: 8px;
      }
      
      .document-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
      }
      
      .document-title {
        display: flex;
        align-items: center;
      }
      
      .document-title svg {
        margin-right: 8px;
        color: #c73232;
      }
      
      .document-format {
        font-size: 14px;
        color: #777;
      }
      
      .document-description {
        color: #555;
        font-size: 14px;
        margin-bottom: 12px;
      }
      
      .button-row {
        display: flex;
        gap: 12px;
      }
      
      .button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        background-color: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
      }
      
      .button:hover {
        background-color: #eee;
      }
      
      .button svg {
        margin-right: 6px;
      }
      
      .support-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
      }
      
      .support-item svg {
        margin-right: 12px;
        color: #c73232;
      }
      
      .support-link {
        color: #333;
        text-decoration: none;
      }
      
      .support-link:hover {
        color: #c73232;
      }
  
      /* Icon classes */
      .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin-right: 4px;
      }
  
      .icon-sm {
        width: 14px;
        height: 14px;
      }
  
      .icon-lg {
        width: 20px;
        height: 20px;
      }
      
      /* Receipt Footer */
      .receipt-footer {
        margin-top: 40px;
        border-top: 1px solid #ddd;
        padding-top: 24px;
        font-size: 14px;
      }
      
      .footer-content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      
      .footer-logo {
        flex: 1;
      }
      
      .footer-info {
        flex: 3;
        padding-left: 20px;
      }
      
      .footer-legal {
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }
      
      .footer-text {
        color: #555;
        margin-bottom: 4px;
      }
      
      .footer-validation {
        flex: 2;
        text-align: right;
      }
      
      .verification-seal {
        display: inline-flex;
        align-items: center;
        background-color: #f8f8f8;
        padding: 8px 14px;
        border-radius: 4px;
        border: 1px dashed #c73232;
        margin-bottom: 12px;
      }
      
      .verification-seal span {
        font-weight: 600;
        margin-left: 8px;
        color: #c73232;
      }
      
      .footer-date {
        color: #555;
        font-size: 13px;
      }
      
      .footer-disclaimer {
        border-top: 1px solid #eee;
        padding-top: 16px;
        padding-bottom: 6px;
        font-size: 12px;
        color: #777;
        text-align: center;
      }
      
      .footer-disclaimer p {
        margin-bottom: 6px;
      }
      
      .footer-contact-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin-top: 16px;
        margin-bottom: 16px;
      }
      
      .footer-contact-item {
        display: flex;
        align-items: center;
      }
      
      .footer-contact-label {
        margin-left: 8px;
        font-weight: 500;
      }
      
      .footer-badges {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .footer-badge {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        background-color: #f8f8f8;
        border-radius: 4px;
        font-size: 12px;
        color: #555;
        font-weight: 500;
      }
      
      .footer-badge svg {
        margin-right: 6px;
      }
  
      /* For printing */
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        
        .card, .receipt-header, .left-column, .right-column {
          break-inside: avoid;
        }
      }
    </style>
  </head>
  <body>
    <div class="container print-container">
      <!-- Receipt Header -->
      <div class="receipt-header">
        <div class="header-row">
          <div class="company-info">
            <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="48">
            <div class="company-details">
              <h1 class="company-name">Airvilla Charters</h1>
              <p class="company-address">Wasfi At-Tall St. 101, Amman, Jordan</p>
            </div>
          </div>
          <div class="receipt-label">
            <h2>ELECTRONIC TICKET RECEIPT</h2>
            <p>#
            ${
              booking?.Receipt?.[0]?.receiptNumber ||
              bookingResult?.Receipt?.[0]?.receiptNumber ||
              bookingResult?.receipt?.receiptNumber
            }
            </p>
          </div>
        </div>
        
        <div class="metadata-row">
          <div class="metadata-box">
            <p class="metadata-label">Receipt Date</p>
            <p class="metadata-value info-value">${getFormatDateTable(
              booking?.Receipt?.[0]?.createdAt ||
                bookingResult?.Receipt?.[0]?.createdAt ||
                bookingResult?.receipt?.createdAt ||
                booking?.fullTicket?.createdAt
            )}</p>
          </div>
          <div class="metadata-box">
            <p class="metadata-label">Transaction Time</p>
            <p class="metadata-value info-value">${getFormatTime(
              booking?.fullTicket?.transactionDate ||
                bookingResult?.Receipt?.[0]?.createdAt ||
                bookingResult?.receipt?.createdAt
            )}</p>
          </div>
          <div class="metadata-box">
            <p class="metadata-label">Transaction ID</p>
            <p class="metadata-value info-value">${
              booking?.transactionId ||
              booking?.Receipt?.[0]?.transactionId ||
              bookingResult?.Receipt?.[0]?.transactionId ||
              bookingResult?.receipt?.transactionId ||
              booking?.fullTicket?.transactionId ||
              "N/A"
            }</p>
          </div>
        </div>
      </div>
      
      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Left Column -->
        <div class="left-column">
          <!-- Booking Information -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Booking Information</h2>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-box">
                  <div class="info-label">
                    Booking ID
                  </div>
                  <div class="info-value">${
                    booking?.eTickets?.[0]?.bookingId ||
                    bookingResult?.eTickets?.[0]?.bookingId ||
                    booking?.Receipt?.[0]?.bookingId
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Booking Reference Number
                  </div>
                  <div class="info-value">${
                    bookingResult?.referenceNumber
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Payment Method
                  </div>
                  <div class="info-value">${
                    booking?.source === "THIRD_PARTY" ||
                    bookingResult?.booking?.source === "THIRD_PARTY" ||
                    booking?.bookingType === "THIRD_PARTY"
                      ? "Airvilla Wallet"
                      : normalizePaymentMethod(
                          bookingResult?.paymentMethod ||
                            bookingResult?.payment?.paymentMethod ||
                            booking?.payment?.paymentMethod ||
                            bookingResult?.data?.fullTicket?.payment
                              ?.paymentMethod ||
                            "N/A"
                        )
                  }</div>
                </div>
                <div class="info-box">
                  <div class="info-label">
                    Booking Date
                  </div>
                  <div class="info-value">${getFormatDateTable(
                    bookingResult?.createdAt ||
                      booking?.createdAt ||
                      booking?.timerStartedAt ||
                      "N/A"
                  )}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Purchased Itinerary -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Purchased Itinerary</h2>
            </div>
            <div class="card-content">
              <!-- Outbound Flight -->
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-type outbound">OUTBOUND</div>
                  <div class="flight-date">
                     ${getFormatDateTable(
                       bookingResult?.booking?.ticket?.flightDate ||
                         booking?.fullTicket?.ticket?.flightDate ||
                         booking?.ticket?.flightDate ||
                         "N/A"
                     )}
                  </div>
                </div>
                
                <div class="flight-route">
                  <div>
                    <div class="flight-codes">
                      <span class="flight-code">
                        ${
                          booking?.ticket?.departure?.airportCode ||
                          booking?.meta?.departure?.departureAirport ||
                          booking?.departure?.airportCode ||
                          bookingResult?.booking?.departure?.airportCode ||
                          booking?.fullTicket?.meta?.departure
                            ?.departureAirport ||
                          "N/A"
                        }
                      </span>
                      <span class="flight-arrow">→</span>
                      <span class="flight-code">${
                        booking?.ticket?.arrival?.airportCode ||
                        booking?.meta?.departure?.arrivalAirport ||
                        booking?.arrival?.airportCode ||
                        bookingResult?.booking?.arrival?.airportCode ||
                        booking?.fullTicket?.meta?.departure?.arrivalAirport ||
                        "N/A"
                      }</span>
                    </div>
                    <div class="flight-meta">
                      ${
                        booking?.ticket?.stops === 0 ||
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              booking?.ticket?.stops ||
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              booking?.ticket?.duration ||
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            } 
                    </div>
                  </div>
                  <div class="flight-airline">
                    ${airline || "N/A"}
                  </div>
                </div>
                
                <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                  <div class="time-item" style="margin-right: 0;">
                    Departure: ${getFormatTime(
                      booking?.ticket?.departureTime ||
                        booking?.ticket?.segments?.[0]?.departureTime ||
                        bookingResult?.booking?.departureTime ||
                        booking?.fullTicket?.ticket?.departureTime ||
                        "N/A"
                    )}
                  </div>
                  <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                  <div class="time-item" style="margin-left: 0;">
                    Arrival: ${getFormatTime(
                      booking?.ticket?.arrivalTime ||
                        booking?.ticket?.segments?.[0]?.arrivalTime ||
                        bookingResult?.booking?.arrivalTime ||
                        booking?.fullTicket?.ticket?.arrivalTime ||
                        "N/A"
                    )}
                  </div>
                </div>
              </div>
              
              ${
                isRoundTrip
                  ? `
              <!-- Flight Separator -->
              <div class="flight-separator">
                <div class="separator-line"></div>
                <div class="separator-circle">↕</div>
                <div class="separator-line"></div>
              </div>
              
              <!-- Return Flight -->
              <div class="flight-card">
                <div class="flight-header">
                  <div class="flight-type return">RETURN</div>
                  <div class="flight-date">
                    ${getFormatDateTable(
                      booking?.ticket?.flightDate ||
                        booking?.ticket?.segments?.[0]?.flightDate ||
                        bookingResult?.booking?.ticket?.flightDate ||
                        booking?.fullTicket?.ticket?.flightDate ||
                        "N/A"
                    )}
                  </div>
                </div>
                
                <div class="flight-route">
                  <div>
                    <div class="flight-codes">
                      <span class="flight-code">${
                        bookingResult?.booking?.arrivalAirport?.iataCode ||
                        "N/A"
                      }</span>
                      <span class="flight-arrow">→</span>
                      <span class="flight-code">${
                        bookingResult?.booking?.departureAirport?.iataCode ||
                        "N/A"
                      }</span>
                    </div>
                    <div class="flight-meta">
                      ${
                        booking?.fullTicket?.ticket?.stops === 0 ||
                        bookingResult?.booking?.ticket?.stops === "0"
                          ? "Direct Flight"
                          : `${
                              bookingResult?.booking?.ticket?.stops ||
                              booking?.fullTicket?.ticket?.stops ||
                              "N/A"
                            } Stops`
                      } • 
                            ${
                              bookingResult?.booking?.ticket?.duration ||
                              booking?.fullTicket?.ticket?.duration ||
                              "N/A"
                            }
                    </div>
                  </div>
                  <div class="flight-airline">
                    ${airline || "N/A"}
                  </div>
                </div>
                
                <div class="flight-times" style="display: flex; align-items: center; justify-content: start;">
                  <div class="time-item" style="margin-right: 0;">
                    Departure: ${getFormatTime(
                      booking?.ticket?.arrivalTime ||
                        booking?.ticket?.segments?.[0]?.arrivalTime ||
                        bookingResult?.booking?.arrivalTime ||
                        booking?.fullTicket?.ticket?.arrivalTime
                    )}
                  </div>
                  <div style="display: flex; align-items: center; margin: 0 16px;">•</div>
                  <div class="time-item" style="margin-left: 0;">
                    Arrival: ${getFormatTime(
                      booking?.ticket?.departureTime ||
                        booking?.ticket?.segments?.[0]?.departureTime ||
                        bookingResult?.booking?.departureTime ||
                        booking?.fullTicket?.ticket?.departureTime
                    )}
                  </div>
                </div>
              </div>`
                  : ""
              }
              
              <!-- Passenger & Baggage -->
              <div class="flight-card">
                <h3 class="section-title" style="margin-bottom: 8px;">Passenger & Baggage</h3>
                <div class="divider"></div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; padding-bottom: 4px;">
                  <div style="font-weight: 500;">${
                    calculatedPassengerCounts.adults > 0
                      ? `${calculatedPassengerCounts.adults} ${
                          calculatedPassengerCounts.adults === 1
                            ? "Adult"
                            : "Adults"
                        }`
                      : ""
                  }
                ${
                  calculatedPassengerCounts.children > 0
                    ? ` • ${calculatedPassengerCounts.children} ${
                        calculatedPassengerCounts.children === 1
                          ? "Child"
                          : "Children"
                      }`
                    : ""
                }
                ${
                  calculatedPassengerCounts.infants > 0
                    ? ` • ${calculatedPassengerCounts.infants} ${
                        calculatedPassengerCounts.infants === 1
                          ? "Infant"
                          : "Infants"
                      }`
                    : ""
                }</div>
                  <div style="font-size: 14px; color: #555;">
                   <p>
                  ${
                    capitalizeFirst(
                      bookingResult?.passengerCounts?.[0]?.travelClass
                    ) ||
                    capitalizeFirst(flightClass?.type) ||
                    (bookingResult?.bookedSeats &&
                    bookingResult.bookedSeats.length > 0
                      ? capitalizeFirst(
                          bookingResult.bookedSeats[0].flightClass
                        )
                      : "Economy")
                  }
                  ${
                    baggageDisplay.length > 0
                      ? ` • ${baggageDisplay.join(" • ")}`
                      : " • No baggage included"
                  }
                </p>
                </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Traveler Details -->
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Traveler Details</h2>
            <div class="dark:text-white text-gray-700 text-sm">
              ${travelers.length}
              ${travelers.length === 1 ? "Traveler" : "Travelers"}
            </div>
          </div>
          ${travelerCards}
          </div>
        </div>
        
        <!-- Right Column -->
        <div class="right-column">
          <!-- Payment Details -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Payment Details</h2>
            </div>
            <div class="card-content">
              <!-- Payment confirmation card -->
              <div class="info-box" style="margin-bottom: 16px;">
                <div class="payment-status">
                  <h3 style="font-weight: 600; font-size: 16px;">Payment Successful</h3>
                </div>
                <div class="divider"></div>
                <div style="font-size: 18px; display: grid; grid-template-columns: 3fr 1fr;">
                <section>  
                <div>
                    <span class="price-label" style="font-size: 14px;">Transaction ID:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      booking?.transactionId ||
                      bookingResult?.transactionId ||
                      booking?.bookingResult?.booking?.transactionId ||
                      bookingResult?.booking?.transactionId ||
                      booking?.fullTicket?.transactionId ||
                      "N/A"
                    }</div>
                  </div>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Transaction Date:</span>
                    <div class="price-value" style="font-size: 16px;">${getFormatDateTable(
                      booking?.timerStartedAt ||
                        booking?.bookingResult?.booking?.transactionDate ||
                        bookingResult?.booking?.transactionDate ||
                        booking?.fullTicket?.transactionDate ||
                        bookingResult?.transactionDate ||
                        bookingResult?.payment?.createdAt ||
                        "N/A"
                    )}</div>
                  </div>
                  </section>
                  <section>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Receipt Number:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      booking?.Receipt?.[0]?.receiptNumber ||
                      bookingResult?.receipt?.receiptNumber ||
                      bookingResult?.Receipt?.[0]?.receiptNumber
                    }</div>
                  </div>
                  <div>
                    <span class="price-label" style="font-size: 14px;">Booking ID:</span>
                    <div class="price-value" style="font-size: 16px;">${
                      booking?.Receipt?.[0]?.bookingId ||
                      bookingResult?.receipt?.bookingId ||
                      bookingResult?.Receipt?.[0]?.bookingId
                    }</div>
                  </div>
                  </section>
                </div>
              </div>
              
                <!-- Price breakdown card -->
                <div class="info-box" style="margin-bottom: 16px;">
                  <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 8px;">Price Details</h3>
                  <div class="divider"></div>
                  
                  <!-- Price details will be populated dynamically -->
                  <span class="flight-type outbound" style="display: inline-block; margin-bottom: 8px;">OUTBOUND</span>
                  <div className="block mb-3">
                  ${outboundFareBreakdown
                    .map(
                      (item) => `
                    <div class="price-item">
                      <span class="price-label">
                        ${item.count} ${item.label}${item.count > 1 ? "s" : ""} ×
                        ${item.perPersonValue.toFixed(2)} ${currency}
                      </span>
                      <span>${item.value.toFixed(2)} ${currency}</span>
                    </div>`
                    )
                    .join("")}
                  </div>
                  ${
                    isRoundTrip
                      ? `<span class="flight-type return" style="display: inline-block; margin-bottom: 8px;">RETURN</span>
                  <div class="price-item">
                  <span class="price-label">Base Fare</span>
                  <span>${
                    returnBaseFare >= 0
                      ? `${returnBaseFare.toFixed(2)} ${currency}`
                      : "N/A"
                  }</span>
                  </div>`
                      : ""
                  }
                  <div class="divider"></div>
                  <div class="price-item">
                  <span class="price-label">Taxes</span>
                  <span>${
                    taxes >= 0 ? `${taxes.toFixed(2)} ${currency}` : "N/A"
                  }</span>
                  </div>
                  <div class="price-item">
                  <span class="price-label">Transaction Fee</span>
                  <span>${
                    transactionFee >= 0
                      ? `${transactionFee.toFixed(2)} ${currency}`
                      : "N/A"
                  }</span>
                  </div>
                  <div class="divider"></div>
                  <div class="price-item"  style="font-weight: 600; font-size: 16px;">
                    <span class="price-label">Total</span>
                    <span>${
                      Number(
                        bookingResult?.bookedSeats?.[0]?.totalPrice ||
                          booking?.bookingResult?.totalAmount ||
                          total
                      ).toFixed(2) || "0.00"
                    } 
                    ${
                      booking?.bookingResult?.currency ||
                      bookingResult?.meta?.pricing?.departure?.currency ||
                      "JOD"
                    }
                    </span>
                  </div>
                </div>            
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer Section - Placed properly after content-grid but before closing container div -->
      <div class="receipt-footer">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/images/logo/airvilla-charter.png" alt="Airvilla Charters" height="72">
          </div>
          <div class="footer-info">
            <p class="footer-legal">ELECTRONIC TICKET RECEIPT</p>
            <p class="footer-text">Thank you for choosing Airvilla Charters. This document serves as your official receipt and booking confirmation.</p>
            <p class="footer-text">Please retain this document for your records and present it when required.</p>
          </div>
          <div class="footer-validation">
            <div class="verification-seal">
              <svg class="icon-lg" viewBox="0 0 24 24" fill="none" stroke="#c73232" stroke-width="2">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              <span>Verified & Approved</span>
            </div>
            <div class="footer-date">Generated on: ${getFormatDate(
              new Date().toString()
            )} • ${getFormatTime(new Date().toString())}</div>
          </div>
        </div>
        
        <div class="footer-contact-grid">
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <span class="footer-contact-label"><EMAIL></span>
          </div>
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
            <span class="footer-contact-label">www.airvilla-charters.travel</span>
          </div>
          <div class="footer-contact-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="#777" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            <span class="footer-contact-label">Secure Booking Platform</span>
          </div>
        </div>
        
        <div class="footer-badges">
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            Secure Transaction
          </div>
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
            24/7 Support
          </div>
          <div class="footer-badge">
            <svg class="icon-sm" viewBox="0 0 24 24" fill="none" stroke="#555" stroke-width="2">
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            IATA Certified
          </div>
        </div>
        
        <div class="footer-disclaimer">
          <p>This document contains confidential information and is intended for the named recipient only and Authorized Agents. Unauthorized reproduction is strictly prohibited.</p>
          <p> 2025 Airvilla LLC. All rights reserved.</p>
        </div>
      </div>
    </div>`);
      }
    } catch (error) {
      console.error("Error generating receipt:", error);
      dispatch(
        setMsg({
          message: `Error generating document: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
          success: false,
        })
      );
    } finally {
      setIsDocumentProcessing({ type: null, action: null });
    }
    return; // Explicitly return void
  };
  return (
    <div
      id="booking-confirmation-page"
      className="min-h-screen dark:bg-gray-900 bg-gray-200/50 dark:text-white text-gray-600 p-8"
    >
      <div className="print-section">
        <div className="max-w-6xl mx-auto print-section">
          {(booking.bookingResult?.booking?.status === "BOOKING_CONFIRMED" ||
            booking.bookingResult?.status === "BOOKING_CONFIRMED" ||
            booking?.status === "BOOKING_CONFIRMED") && (
            <BookingConfirmationHeader />
          )}
          {booking.bookingResult?.success === "success" &&
            booking.bookingResult?.booking?.receipt &&
            isDocumentProcessing && <ReceiptHeader data={booking} />}
          {booking.bookingResult?.success === "success" &&
            booking.bookingResult?.booking?.source === "INTERNAL" && (
              <section className="flex items-center gap-3">
                <h1 className="font-bold text-3xl leading-10 text-gray-700 dark:text-white">
                  Master Booking Control
                </h1>
              </section>
            )}
          <div className="grid grid-cols-3 gap-8">
            <div className="col-span-2">
              <div className="bg-gray-200 dark:bg-gray-800 p-6 rounded-xl shadow-lg">
                <BookingInformation data={booking} />
                {booking?.source === "INTERNAL" && (
                  <AgentPaymentDetails booking={booking} />
                )}
                <ItineraryCard data={booking} />
                <TravelerDetails data={booking} />
                <AdditionalInformation />
              </div>
            </div>
            <div className="col-span-1">
              <PaymentSummary
                data={booking}
                handleDocumentAction={handleDocumentAction}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmedPage;
