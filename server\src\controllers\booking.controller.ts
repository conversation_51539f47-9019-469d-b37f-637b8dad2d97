import { Request, Response, NextFunction } from "express";
import {
  BookingService,
  collectAgencyNames,
  collectGlobalNames,
  collectSellerAgentNames,
} from "../services/booking.service";
import { CreateInternalBookingDto, BookingMeta } from "../types/booking.types";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
import { getAssociatedAccountIds } from "../utils/association";
import { BookingWithUser, BookingsResponse } from "../types/booking.response";
import { BookingSource, BookingStatus, Prisma } from "@prisma/client";
import { getCreatedTimeRange } from "../utils/functions";
import { getAgentAgencyIdFromRequest } from "../utils/agent.utils";

const bookingService = new BookingService();

// Create internal booking
export const createInternalBookingHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // userAuth middleware populates req.agencyAgent if the user is an agent
    // authorizeAgent middleware ensures req.agencyAgent exists here
    const agentId = await getAgentAgencyIdFromRequest(req, prisma);

    if (!agentId) {
      // This should theoretically not happen due to authorizeAgent middleware,
      // but it's good practice to check.
      return res.status(403).json({
        message: "Forbidden: Agent ID not found after authorization.",
      });
    }

    const bookingData: CreateInternalBookingDto = req.body;

    // Optional: Add input validation here using Joi or similar
    // const { error } = validateCreateBooking(bookingData);
    // if (error) return res.status(400).json({ message: error.details[0].message });

    const newBooking = await bookingService.createInternalBooking(
      agentId,
      bookingData
    );

    res.status(201).json({
      message: "Booking request created successfully.",
      booking: newBooking, // Send back the created booking details
    });
  } catch (error) {
    next(error); // Pass error to the global error handler
  }
};

// Identify booking type (internal or third-party)
export const identifyBookingTypeHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { ticketId } = req.body;
    // If accountType is affiliate, always return THIRD_PARTY
    if (req.accountType === "affiliate") {
      return res.status(200).json({ bookingType: "THIRD_PARTY" });
    }
    // Otherwise, require agent agency ID
    const agentAgencyId = await getAgentAgencyIdFromRequest(req, prisma);
    if (!agentAgencyId) {
      return res
        .status(403)
        .json({ message: "Forbidden: Agent agency ID not found." });
    }
    if (!ticketId) {
      return res.status(400).json({ message: "Missing ticketId in request." });
    }
    const bookingType = await bookingService.identifyBookingType(
      agentAgencyId,
      ticketId
    );
    return res.status(200).json({ bookingType });
  } catch (error) {
    next(error);
  }
};

// Get a single booking by its ID
export const getBookingByIdHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const booking = await bookingService.getBookingById(req.params.id);
    if (!booking) return res.status(404).json({ message: "Booking not found" });
    res.status(200).json({ success: true, data: booking });
  } catch (error) {
    next(error);
  }
};

// Get all bookings for a user (optionally filter by status)
export const getBookingsByUserHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { userId } = req.params;
    const { status } = req.query;
    const bookings = await bookingService.getBookingsByUser(
      userId,
      status as string | undefined
    );
    if (!bookings)
      return res.status(404).json({ message: "Bookings not found" });
    res.status(200).json({ success: true, data: bookings });
  } catch (error) {
    next(error);
  }
};

// Get all bookings by status (e.g., for admin dashboard)
export const getBookingsByStatusHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { status } = req.params;
    const bookings = await bookingService.getBookingsByStatus(status);
    if (!bookings)
      return res.status(404).json({ message: "Bookings not found" });
    res.status(200).json({ success: true, data: bookings });
  } catch (error) {
    next(error);
  }
};

// Get status of a specific booking by ID
export const getBookingStatusByIdHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const status = await bookingService.getBookingStatus(id);
    if (!status)
      return res.status(404).json({ message: "Booking status not found" });
    res.status(200).json({ success: true, data: status });
  } catch (error) {
    next(error);
  }
};

// Cancel a booking
export const cancelBookingHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const canceledBy = (req as AuthRequest).user?.id || "system"; // Adjust based on your auth
    const booking = await bookingService.cancelBooking(id, canceledBy);
    if (!booking) return res.status(404).json({ message: "Booking not found" });

    // Return response in the format expected by frontend
    res.status(200).json({
      success: true,
      data: {
        bookingId: booking.id,
        status: booking.status,
        requestId: booking.requestId,
        cancellationReason: booking.cancellationReason,
        meta: booking.meta,
        updatedAt: booking.updatedAt,
      },
    });
  } catch (error: any) {
    if (error.code === "INVALID_BOOKING_STATE") {
      return res.status(400).json({
        success: false,
        code: error.code,
        message: error.message,
        details: error.details,
      });
    }
    next(error);
  }
};

// Refund a booking
export const refundBookingHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const canceledBy = (req as AuthRequest).user?.id;
    const booking = await bookingService.refundBooking(id, canceledBy);
    if (!booking)
      return res
        .status(404)
        .json({ success: false, message: "Booking not found" });

    // Return response in the format expected by frontend
    res.status(200).json({
      success: true,
      data: {
        bookingId: booking.id,
        status: booking.status,
        requestId: booking.requestId,
        cancellationReason: booking.cancellationReason,
        meta: booking.meta,
        updatedAt: booking.updatedAt,
      },
    });
  } catch (error: any) {
    if (error.code === "INVALID_BOOKING_STATE") {
      return res.status(400).json({
        success: false,
        code: error.code,
        message: error.message,
        details: error.details,
      });
    }
    next(error);
  }
};

// Add this function
export const confirmBookingHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { bookingId } = req.body;
    if (!bookingId)
      return res
        .status(400)
        .json({ success: false, message: "Missing bookingId" });
    const result = await bookingService.confirmBooking(bookingId);
    if (result) {
      return res
        .status(200)
        .json({ success: true, requestId: result.requestId, booking: result });
    } else {
      return res
        .status(400)
        .json({ success: false, message: "Booking confirmation failed" });
    }
  } catch (error) {
    next(error);
  }
};

export const getMyBookingsAgentNamesHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    let agentNames: string[] = [];

    agentNames = await collectAgencyNames(prisma, req);

    const sortedAgentNames = [...new Set(agentNames)]
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b));

    res.status(200).json(sortedAgentNames);
  } catch (error) {
    console.error("Error fetching agent names:", error);
    next(error);
  }
};

export const getMySalesAgentNamesHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    let agentNames: string[] = [];

    agentNames = await collectSellerAgentNames(prisma, req);
    const finalAgentNames = ["External Agent", ...agentNames];

    const sortedAgentNames = [...new Set(finalAgentNames)]
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b));

    res.status(200).json(sortedAgentNames);
  } catch (error) {
    console.error("Error fetching agent names:", error);
    next(error);
  }
};

export const getGlobalBookingsAgentNamesHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    let agentNames: string[] = [];

    // For global admins, fetch all agent names
    agentNames = await collectGlobalNames(prisma);

    const sortedAgentNames = [...new Set(agentNames)]
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b));

    res.status(200).json(sortedAgentNames);
  } catch (error) {
    console.error("Error fetching agent names:", error);
    next(error);
  }
};

/**
 * Get unique carrier names from bookings (via FlightSegments)
 */
export const getCarrierNamesHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Fetch distinct carriers directly from FlightSegment table
    const segments = await prisma.flightSegment.findMany({
      distinct: ["carrier"],
      select: { carrier: true },
      where: { carrier: { not: "" } },
    });

    const carrierNames = segments.map((s) => s.carrier).filter(Boolean);

    // Also include any carriers stored in booking.meta (optional)
    const metas = await prisma.booking.findMany({
      where: { meta: { not: Prisma.DbNull } },
      select: { meta: true },
    });

    metas.forEach((b) => {
      const m = b.meta as any;
      if (m?.departure?.carrier) carrierNames.push(m.departure.carrier);
      if (m?.return?.carrier) carrierNames.push(m.return.carrier);
    });

    const uniqueCarriers = Array.from(new Set(carrierNames));

    return res
      .status(200)
      .json({ success: true, carrierNames: uniqueCarriers });
  } catch (error) {
    next(error);
  }
};

/**
 * Get unique agency names from Agency table and booking meta
 */
export const getAgencyNamesHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Names from Agency table
    // const agencies = await prisma.agency.findMany({ select: { name: true } });
    // const names: string[] = agencies.map((a: any) => a.name).filter(Boolean);
    const names: string[] = [];

    // Names inside booking.meta
    const metas = await prisma.booking.findMany({
      where: { meta: { not: Prisma.DbNull } },
      select: { meta: true },
    });

    metas.forEach((b) => {
      const m = b.meta as any;
      if (m?.buyerAgencyName) names.push(m.buyerAgencyName);
      if (m?.returnSeller?.agencyName) names.push(m.returnSeller.agencyName);
      if (m?.departureSeller?.agencyName)
        names.push(m.departureSeller.agencyName);
    });

    const uniqueAgencyNames = Array.from(new Set(names));

    return res
      .status(200)
      .json({ success: true, agencyNames: uniqueAgencyNames });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all bookings (admin, paginated)
 */
export const getAllBookingsHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Prefer authenticated user/session info
    const user = (req as any).user;
    const accountType = (req as any).accountType || req.query.accountType;
    const userId = (user && user.id) || req.query.userId;
    const agencyId = (user && user.agencyId) || req.query.agencyId;
    const {
      pageSize = 20,
      cursor,
      bookingType,
      status,
      source,
      agentName,
      travelDateRange,
      search,
    } = req.query;
    const pageSizeNum = Number(pageSize) || 20;
    const limitedPageSize = Math.min(pageSizeNum, 100);

    // Pass cursor and filtering parameters to service
    const bookings = await bookingService.getAllMyBookings(
      1,
      limitedPageSize,
      cursor as string | undefined,
      userId as string | undefined,
      accountType as string | undefined,
      agencyId as string | undefined,
      bookingType as string | undefined,
      status as string | string[] | undefined,
      source as string | string[] | undefined,
      agentName as string | string[] | undefined,
      travelDateRange as string | undefined,
      search as string | undefined
    );

    // Determine the owner's agency ID based on the user's role
    let ownerAgencyId;
    if (userId && accountType) {
      // If the user is already an agency owner, use their ID directly
      if (accountType === "agencyOwner" || accountType === "masterOwner") {
        ownerAgencyId = userId;
      }
      // If the user is an agency agent, find their owner's agency ID
      else if (accountType === "agencyUser") {
        try {
          const agencyAgent = await prisma.agencyAgent.findUnique({
            where: { id: userId },
            select: { agencyId: true },
          });

          if (agencyAgent && agencyAgent.agencyId) {
            ownerAgencyId = agencyAgent.agencyId;
          }
        } catch (error) {
          console.error("Error finding agency agent's owner:", error);
        }
      }
      // If the user is a team member, find their owner's agency ID
      else if (accountType === "masterUser") {
        try {
          const teamMember = await prisma.teamMember.findUnique({
            where: { id: userId },
            select: { teamId: true },
          });
          if (!teamMember) {
            throw new Error("Team member not found");
          }
          if (teamMember && teamMember.teamId) {
            ownerAgencyId = teamMember.teamId;
          }
        } catch (error) {
          console.error("Error finding team member's owner:", error);
        }
      }
      // If the user is an affiliate, they should only see their own bookings
      else if (accountType === "affiliate") {
        ownerAgencyId = userId;
      }
    } else if (agencyId) {
      ownerAgencyId = agencyId;
    }

    // Build the where clause for filtering
    let whereClause: any = {};

    // Filter by agency ID and booking type if provided
    if (ownerAgencyId) {
      if (bookingType === "buyer") {
        // Only show bookings where the agency is the buyer
        whereClause.OR = [
          { buyerAgencyId: ownerAgencyId },
          // Also include internal bookings where the agency is involved
          {
            source: "INTERNAL",
            OR: [
              { buyerAgencyId: ownerAgencyId },
              { sellerAgencyId: ownerAgencyId },
            ],
          },
        ];
      } else if (bookingType === "seller") {
        // Only show bookings where the agency is the seller
        whereClause.OR = [
          { sellerAgencyId: ownerAgencyId },
          // Also include internal bookings where the agency is involved
          {
            source: "INTERNAL",
            OR: [
              { buyerAgencyId: ownerAgencyId },
              { sellerAgencyId: ownerAgencyId },
            ],
          },
        ];
      } else {
        // Default behavior (show all bookings related to the agency)
        whereClause.OR = [
          { buyerAgencyId: ownerAgencyId },
          { sellerAgencyId: ownerAgencyId },
          // Explicitly include internal bookings
          { source: "INTERNAL", buyerAgencyId: ownerAgencyId },
          { source: "INTERNAL", sellerAgencyId: ownerAgencyId },
        ];
      }
    }

    // Add status filter if provided
    if (status) {
      whereClause.status = { in: Array.isArray(status) ? status : [status] };
    }

    // Add source filter if provided
    if (source) {
      whereClause.source = { in: Array.isArray(source) ? source : [source] };
    }

    // Add agent name filter if provided
    if (agentName) {
      const agentNames = Array.isArray(agentName) ? agentName : [agentName];
      whereClause.OR = whereClause.OR || [];

      // Add a condition for each agent name
      agentNames.forEach((name) => {
        whereClause.OR.push(
          {
            meta: {
              path: ["agentName"],
              equals: name,
            },
          },
          {
            meta: {
              path: ["bookedByAgentName"],
              equals: name,
            },
          }
        );
      });
    }

    // Add date range filter if provided
    if (travelDateRange && typeof travelDateRange === "string") {
      const dateRange = getCreatedTimeRange(travelDateRange.toLowerCase());
      whereClause = {
        AND: [whereClause, { createdAt: dateRange }],
      };
    }

    // Add search filter if provided
    if (search) {
      const searchTerm = `%${search}%`;
      whereClause.OR = whereClause.OR || [];
      whereClause.OR.push(
        {
          requestId: { contains: searchTerm, mode: "insensitive" },
        },
        {
          referenceNumber: { contains: searchTerm, mode: "insensitive" },
        },
        {
          eTickets: {
            some: {
              eTicketNumber: { contains: searchTerm, mode: "insensitive" },
            },
          },
        },
        {
          travelers: {
            some: {
              traveler: {
                OR: [
                  { firstName: { contains: searchTerm, mode: "insensitive" } },
                  { lastName: { contains: searchTerm, mode: "insensitive" } },
                ],
              },
            },
          },
        },
        {
          ticket: {
            segments: {
              some: {
                OR: [
                  {
                    flightNumber: {
                      contains: searchTerm,
                      mode: "insensitive",
                    },
                  },
                  {
                    carrier: {
                      contains: searchTerm,
                      mode: "insensitive",
                    },
                  },
                  {
                    departure: {
                      airportCode: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  },
                  {
                    arrival: {
                      airportCode: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  },
                ],
              },
            },
          },
        }
      );
    }
    // Get the total number of bookings with the same filtering criteria
    const bookingsTotal = await prisma.booking.count({
      where: whereClause,
    });

    // Compute nextCursor for pagination (null if no more pages)
    let nextCursor = null;
    if (bookings.length === limitedPageSize) {
      nextCursor = bookings[bookings.length - 1].id;
    }

    // Map bookings to include agent's full name and remove sensitive user data
    const mappedBookings = bookings.map((booking) => ({
      ...booking,
      agent: booking.user
        ? `${booking.user.firstName || ""} ${booking.user.lastName || ""}`.trim()
        : "Unknown Agent",
      // Remove the user object to avoid exposing unnecessary data
      user: undefined,
    }));

    return res.status(200).json({
      success: true,
      results: {
        bookings,
        bookingsTotal,
        nextCursor,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all bookings with cursor-based pagination (admin only)
 */
export const getAllGlobalBookingsHandler = async (
  req: Request,
  res: Response<BookingsResponse>,
  next: NextFunction
) => {
  const pageSize = 10; // Default page size

  try {
    // --- ACCESS CONTROL: Only master admin and their accounts allowed ---
    const user = (req as any).user;
    const accountType = (req as any).accountType || (req as any).user?.type;
    const teamMember = (req as any).teamMember;

    const allowedMasterRoles = [
      "master_admin",
      "master_accountant",
      "master_owner",
      "master_moderator",
    ];
    const allowedMasterAccountTypes = ["masterOwner", "masterUser"];

    // Check if user is a master user or team member with master role
    const isMasterUser =
      user &&
      allowedMasterRoles.includes(user.roleType) &&
      allowedMasterAccountTypes.includes(accountType);
    const isMasterTeamMember =
      teamMember &&
      allowedMasterRoles.includes(teamMember.roleType) &&
      allowedMasterAccountTypes.includes(accountType);

    if (!isMasterUser && !isMasterTeamMember) {
      return res.status(403).json({
        success: false,
        message:
          "Access denied. Only master admin and their accounts can access global bookings.",
      } as any);
    }

    // Get the cursor from query params
    const cursor = req.query.cursor as string | undefined;

    const {
      status,
      source,
      issuedOn,
      flightDate,
      tripType,
      carrier,
      agency,
      agent,
      paymentMethod,
      search,
    } = req.query;

    const filters = {
      status,
      source,
      issuedOn,
      flightDate,
      tripType,
      carrier,
      agency,
      agent,
      paymentMethod,
      search,
    } as Record<string, string | string[]>;

    // Build the query with proper typing
    const query = {
      take: pageSize,
      orderBy: { createdAt: "desc" as const },
      include: {
        ticket: true,
        travelers: { include: { traveler: true } },
        bookedSeats: true,
        payment: true,
        bookingHistoryLogs: true,
        notifications: true,
        user: {
          // Include the user who created the booking
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    };

    // If we have a cursor, use it for pagination
    if (cursor) {
      (query as any).cursor = { id: cursor };
      (query as any).skip = 1; // Skip the cursor itself
    }

    // First, get the user's agency ID to determine access level
    let userAgencyId: string | null = null;
    const currentUser = (req as any).user;

    // Build the query with proper access control
    const finalQuery: any = {
      ...query,
      where: {},
    };

    // If user is part of an agency, ensure they can see all bookings for their agency
    if (userAgencyId) {
      const agentIds = await prisma.agencyAgent
        .findMany({
          where: { agencyId: userAgencyId },
          select: { id: true },
        })
        .then((agents) => agents.map((a) => a.id));

      finalQuery.where.OR = [
        { buyerAgencyId: userAgencyId },
        { sellerAgencyId: userAgencyId },
        { userId: userAgencyId },
        { agencyAgentId: { in: agentIds } },
        // Include bookings created by any agent in the agency
        {
          user: {
            id: { in: agentIds },
          },
        },
      ];
    }

    const where = finalQuery.where;

    const applyEqualityFilter = (field: string, value: any) => {
      if (!value) return;
      if (Array.isArray(value)) {
        where[field] = { in: value };
      } else {
        where[field] = value;
      }
    };

    // Normalize tripType label from query (e.g. "One Way" ➜ "ONE_WAY")
    if (filters.tripType) {
      const normalizeTripType = (val: string) => {
        switch (val.trim().toLowerCase()) {
          case "one way":
            return "ONE_WAY";
          case "round trip":
            return "ROUND_TRIP";
          default:
            return val; // assume already correct enum
        }
      };
      if (Array.isArray(filters.tripType)) {
        filters.tripType = filters.tripType.map(normalizeTripType);
      } else {
        filters.tripType = normalizeTripType(filters.tripType as string);
      }
    }

    /*
     * Normalize booking status from UI labels to DB enum values.
     * Accept single value or array. Unknown values are left untouched.
     */
    if (filters.status) {
      const mapStatus = (val: string): string | string[] => {
        const normalized = val.trim().toLowerCase();
        switch (normalized) {
          case "confirmed":
            return "BOOKING_CONFIRMED";
          case "pending approval":
            return "PENDING_APPROVAL";
          case "quick hold":
            return "QUICK_HOLD";
          case "rejected":
            return "BOOKING_REJECTED";
          case "timed out":
            return "TIMED_OUT";
          case "cancelled":
          case "canceled":
            // Map generic "Cancelled" to both possible DB values
            return ["CANCELLED_BY_USER", "CANCELLED_BY_SYSTEM"];
          default:
            return val; // assume already correct enum
        }
      };

      if (Array.isArray(filters.status)) {
        filters.status = filters.status.flatMap((s) => mapStatus(s) as any);
      } else {
        filters.status = mapStatus(filters.status as string);
      }
    }

    if (filters.source) {
      const mapSource = (val: string): string => {
        const normalized = val.trim().toLowerCase();
        switch (normalized) {
          case "internal":
            return "INTERNAL";
          case "third party":
          case "third-party":
            return "THIRD_PARTY";
          default:
            return val; // assume already correct enum
        }
      };

      if (Array.isArray(filters.source)) {
        filters.source = filters.source.map((s) => mapSource(s) as any);
      } else {
        filters.source = mapSource(filters.source as string);
      }
    }

    // carrier filter
    if (filters.carrier) {
      const carrierCond = Array.isArray(filters.carrier)
        ? { in: filters.carrier }
        : filters.carrier;

      where.ticket = {
        ...(where.ticket || {}),
        is: {
          segments: {
            some: { carrier: carrierCond },
          },
        },
      } as any;
    }

    if (filters.agency) {
      const agencyNames = Array.isArray(filters.agency)
        ? filters.agency
        : [filters.agency];

      where.OR = [
        ...(where.OR ?? []),
        ...agencyNames.flatMap((name) => [
          { meta: { path: ["buyerAgencyName"], equals: name } },
          { meta: { path: ["ownerName"], equals: name } },
          { meta: { path: ["returnSeller", "agencyName"], equals: name } },
          { meta: { path: ["departureSeller", "agencyName"], equals: name } },
        ]),
      ];
    }

    if (filters.agent) {
      const agentNames = Array.isArray(filters.agent)
        ? filters.agent
        : [filters.agent];

      where.OR = [
        ...(where.OR ?? []),
        ...agentNames.flatMap((name) => [
          { meta: { path: ["buyerAgentName"], equals: name } },
          // { meta: { path: ["returnSeller", "name"], equals: name } },
          // { meta: { path: ["departureSeller", "name"], equals: name } },
        ]),
      ];
    }

    applyEqualityFilter("status", filters.status);
    applyEqualityFilter("source", filters.source);
    applyEqualityFilter("tripType", filters.tripType);

    // Simple LIKE search across requestId / id if search term present
    if (filters.search) {
      where.OR = [
        { id: { contains: filters.search, mode: "insensitive" } },
        { requestId: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    // Date range helpers (expects strings like "Today", "Next 7 Days" etc.)
    const mapRelativeRange = (label: string) => {
      const today = new Date();
      switch (label) {
        case "Today":
          return {
            gte: new Date(today.setHours(0, 0, 0, 0)),
            lte: new Date(today.setHours(23, 59, 59, 999)),
          };
        case "Next 7 Days":
          return { gte: today, lte: new Date(+today + 7 * 86400000) };
        case "Next 30 Days":
          return { gte: today, lte: new Date(+today + 30 * 86400000) };
        case "Next 90 Days":
          return { gte: today, lte: new Date(+today + 90 * 86400000) };
        case "Last 7 Days":
          return { lte: today, gte: new Date(+today - 7 * 86400000) };
        case "Last Month":
          return { lte: today, gte: new Date(+today - 30 * 86400000) };
        case "Last 3 Months":
          return { lte: today, gte: new Date(+today - 90 * 86400000) };
        case "Last 6 Months":
          return { lte: today, gte: new Date(+today - 180 * 86400000) };
        case "Last 12 Months":
          return { lte: today, gte: new Date(+today - 365 * 86400000) };
        default:
          return undefined;
      }
    };
    const getFirst = (v: string | string[] | undefined) =>
      Array.isArray(v) ? v[0] : v;
    const flightDateValue = getFirst(filters.flightDate);

    if (flightDateValue && flightDateValue !== "All Time") {
      const range = mapRelativeRange(flightDateValue);
      const dateToString = (d: Date) => d.toISOString().split("T")[0];

      if (range) {
        console.log(dateToString(range.gte as Date));

        where.ticket = {
          is: {
            flightDate: {
              gte: dateToString(range.gte as Date),
              lte: dateToString(range.lte as Date),
            },
          },
        } as any;
      }
    }

    // Add date range filter if provided
    if (issuedOn && typeof issuedOn === "string") {
      const dateRange = getCreatedTimeRange(issuedOn.toLowerCase());
      if (dateRange) where.createdAt = dateRange;
    }

    // Add payment method filter if provided
    if (paymentMethod) {
      const paymentMethods = (
        Array.isArray(paymentMethod) ? paymentMethod : [paymentMethod]
      ).map((method) =>
        typeof method === "string"
          ? method.replace(/\s+/g, "").replace(/^./, (c) => c.toLowerCase())
          : method
      );
      where.payment = {
        paymentMethod: {
          in: paymentMethods,
          mode: "insensitive",
        },
      };
    }

    // Add search filter if provided
    if (search) {
      const searchTerm = `%${search}%`;
      where.OR = where.OR || [];
      where.OR.push(
        {
          requestId: { contains: searchTerm, mode: "insensitive" },
        },
        {
          referenceNumber: { contains: searchTerm, mode: "insensitive" },
        },
        {
          eTickets: {
            some: {
              eTicketNumber: { contains: searchTerm, mode: "insensitive" },
            },
          },
        },
        {
          travelers: {
            some: {
              traveler: {
                OR: [
                  { firstName: { contains: searchTerm, mode: "insensitive" } },
                  { lastName: { contains: searchTerm, mode: "insensitive" } },
                ],
              },
            },
          },
        },
        {
          ticket: {
            segments: {
              some: {
                OR: [
                  {
                    flightNumber: {
                      contains: searchTerm,
                      mode: "insensitive",
                    },
                  },
                  {
                    carrier: {
                      contains: searchTerm,
                      mode: "insensitive",
                    },
                  },
                  {
                    departure: {
                      airportCode: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  },
                  {
                    arrival: {
                      airportCode: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  },
                ],
              },
            },
          },
        }
      );
    }

    // First, get the bookings with necessary relations
    const [bookings, totalCount] = await Promise.all([
      prisma.booking.findMany({
        ...finalQuery,
        include: {
          ticket: {
            include: {
              flightClasses: {
                select: {
                  price: true,
                },
              },
            },
          },
          travelers: { include: { traveler: true } },
          bookedSeats: true,
          payment: true,
          bookingHistoryLogs: true,
          notifications: true,
          Receipt: true,
          eTickets: true,
        },
      }) as Promise<BookingWithUser[]>,
      prisma.booking.count({ where: finalQuery.where }),
    ]);

    // Map bookings to include agent's full name and handle user data
    const mappedBookings: BookingWithUser[] = [];

    for (const booking of bookings) {
      try {
        // Get the creator of the booking (either user or agency agent)
        const creator = booking.agencyAgentId
          ? await prisma.agencyAgent.findUnique({
              where: { id: booking.agencyAgentId },
              include: {
                address: true,
              },
            })
          : booking.userId && booking.userId !== null
            ? await prisma.user.findUnique({
                where: { id: booking.userId },
                include: {
                  agents: {
                    include: {
                      address: true,
                    },
                  },
                },
              })
            : null;

        const sellerAgency = await prisma.user.findUnique({
          where: { id: booking.sellerAgencyId || booking.ticket.owner?.id },
          select: {
            id: true,
            agencyName: true,
          },
        });

        // Prefer the user who created the booking, fall back to the user associated with the booking
        const agentUser = creator;
        const ticketOwner = booking.ticket.owner;
        const agentName = agentUser
          ? `${agentUser.firstName || ""} ${agentUser.lastName || ""}`.trim()
          : "Unknown Agent";

        // Get agency name from the creator's agency agent record if available
        const agencyName =
          (creator as any)?.agencyAgent?.[0]?.agency?.agencyName ||
          booking.agencyName ||
          (booking.user as any)?.agencyName ||
          null;

        // Get the booking creator's name
        const createdByName = creator
          ? `${creator.firstName || ""} ${creator.lastName || ""}`.trim()
          : "Unknown Creator";

        // Get the booking owner's name (agency or user)
        const ownerName =
          ticketOwner ||
          (booking.sellerAgencyId
            ? `${sellerAgency?.agencyName || ""}`.trim()
            : "Unknown Owner");

        // Create a new booking object with the additional fields
        const bookingWithMeta: BookingWithUser = {
          ...booking,
          // Add agent and agency name at the root level for backward compatibility
          agent: agentName,
          // Add all meta data in a single object
          meta: {
            ...(typeof booking.meta === "object" ? booking.meta : {}),
            // Ensure we have the agent name in meta
            agentName: agentName,
            // Ensure we have the agency name in meta
            agencyName: agencyName || booking.agencyName,
            // Include the creator's name
            bookedByAgentName: createdByName,
            // Include the owner's name
            ownerName: ownerName,
          },
          // Keep user info but remove sensitive data
          user: agentUser
            ? {
                id: agentUser.id,
                firstName: agentUser.firstName,
                lastName: agentUser.lastName,
                email: agentUser.email,
                role: (agentUser as any).role,
                roleType: (agentUser as any).roleType,
                agencyName: agencyName,
              }
            : undefined,
        };

        mappedBookings.push(bookingWithMeta);
      } catch (error) {
        console.error(`Error processing booking ${booking.id}:`, error);
        // Push the original booking if there was an error
        mappedBookings.push(booking as BookingWithUser);
      }
    }

    // Calculate next cursor
    let nextCursor = null;
    if (bookings.length === pageSize) {
      nextCursor = bookings[bookings.length - 1].id;
    }

    return res.status(200).json({
      success: true,
      results: {
        bookings: mappedBookings,
        bookingsTotal: totalCount,
        nextCursor,
      },
    });
  } catch (error) {
    console.error("Error in getAllGlobalBookingsHandler:", error);
    next(error);
  }
};

/**
 * Approve a booking (admin)
 */
export const approveBookingHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const booking = await bookingService.approveBooking(id);
    if (!booking) return res.status(404).json({ message: "Booking not found" });
    res.status(200).json({ success: true, data: booking });
  } catch (error) {
    next(error);
  }
};

/**
 * Reject a booking (admin)
 */
export const rejectBookingHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const booking = await bookingService.rejectBooking(id);
    if (!booking) return res.status(404).json({ message: "Booking not found" });
    res.status(200).json({ success: true, data: booking });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all sales bookings with status 'CANCELLED_BY_USER' or 'BOOKING_CONFIRMED'
 */
export const getAllSalesHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Prefer authenticated user/session info
    const user = (req as any).user;
    const teamMember = (req as any).teamMember;
    const accountType = (req as any).accountType || req.query.accountType;

    // Get user ID, agency ID, and team ID with fallbacks
    const userId = (user && user.id) || req.query.userId;
    const agencyId =
      (user && user.agencyId) ||
      (teamMember && teamMember.agencyId) ||
      req.query.agencyId;
    const teamId =
      (user && user.teamId) ||
      (teamMember && teamMember.teamId) ||
      req.query.teamId;

    const {
      pageSize = 20,
      cursor: rawCursor,
      bookingType,
      status,
      source,
      agentName,
      travelDateRange,
      paymentMethod,
      saleDate,
      search,
    } = req.query;
    const pageSizeNum = Number(pageSize) || 20;
    const limitedPageSize = Math.min(pageSizeNum, 20);
    const cursor = typeof rawCursor === "string" ? rawCursor : undefined;

    // Helper function to get the creator ID (user ID) for a team member
    async function getTeamMemberCreatorId(
      teamMemberId: string
    ): Promise<string | null> {
      try {
        const member = await prisma.teamMember.findUnique({
          where: { id: teamMemberId },
          select: { createdById: true, createdByTeamMemberId: true },
        });

        if (!member) return null;

        // If created by a user, return the user ID
        if (member.createdById) {
          return member.createdById;
        }

        // If created by another team member, recursively find the user ID
        if (member.createdByTeamMemberId) {
          return getTeamMemberCreatorId(member.createdByTeamMemberId);
        }

        return null;
      } catch (error) {
        console.error("Error finding team member creator:", error);
        return null;
      }
    }

    // Determine the sellerAgencyId based on user type
    let sellerAgencyId: string | undefined;

    if (teamMember) {
      // For team members, first try to get the creator's user ID
      let creatorUserId: string | null = null;

      if (teamMember.createdById) {
        // Directly created by a user
        creatorUserId = teamMember.createdById;
      } else if (teamMember.createdByTeamMemberId) {
        // Created by another team member, find the root user
        creatorUserId = await getTeamMemberCreatorId(
          teamMember.createdByTeamMemberId
        );
      }

      // Use creator's user ID if found, otherwise fall back to team member's own IDs
      sellerAgencyId =
        creatorUserId ||
        teamMember.agencyId ||
        teamMember.teamId ||
        teamMember.id;
    } else if (user) {
      // For regular users, use their agencyId or teamId or userId
      sellerAgencyId = user.agencyId || user.teamId || user.id;
    } else {
      // Fallback to query parameters if available
      sellerAgencyId =
        (agencyId as string) || (teamId as string) || (userId as string);
    }

    if (!sellerAgencyId) {
      return res.status(400).json({
        success: false,
        message: "Could not determine seller agency ID",
      });
    }

    // Get all bookings with the specified statuses
    const whereClause: {
      OR: any[];
      AND: any[]; // Temporarily using any[] to allow different filter types
    } = {
      OR: [
        { status: BookingStatus.CANCELLED_BY_USER },
        { status: BookingStatus.BOOKING_CONFIRMED },
      ],
      AND: [
        {
          OR: [{ sellerAgencyId: sellerAgencyId }],
        },
      ],
    };

    // Add status filter if provided
    if (status) {
      const statuses = Array.isArray(status) ? status : [status];
      whereClause.AND.push({
        OR: statuses.map((s) => ({ status: s })),
      });
    }

    // Add source filter if provided
    if (source) {
      const sources = Array.isArray(source) ? source : [source];
      whereClause.AND.push({
        OR: sources.map((s) => ({ source: s })),
      });
    }

    // Add agent name filter if provided
    if (agentName) {
      let agentNames = Array.isArray(agentName) ? agentName : [agentName];
      whereClause.OR = whereClause.OR || [];

      // If the special label "External Agent" is requested, expand it to match any third-party booking
      if (agentNames.includes("External Agent")) {
        // Remove the placeholder to avoid equality checks against the literal string
        agentNames = agentNames.filter((n) => n !== "External Agent");

        // Add a clause that matches all third-party bookings regardless of real name
        whereClause.AND.push({ source: BookingSource.THIRD_PARTY });
      }

      // Add a condition for each agent name
      agentNames.forEach((name) => {
        whereClause.OR.push(
          {
            meta: {
              path: ["agentName"],
              equals: name,
            },
          },
          {
            meta: {
              path: ["bookedByAgentName"],
              equals: name,
            },
          },
          {
            AND: [
              {
                user: {
                  firstName:
                    typeof name === "string" && name.includes(" ")
                      ? name.split(" ")[0] || ""
                      : name || "",
                },
              },
              {
                user: {
                  lastName:
                    typeof name === "string" && name.includes(" ")
                      ? name.split(" ")[1] || ""
                      : "",
                },
              },
            ],
          }
        );
      });
    }

    // Add date range filter if provided
    if (travelDateRange && typeof travelDateRange === "string") {
      const dateRange = getCreatedTimeRange(travelDateRange.toLowerCase());
      whereClause.AND.push({ createdAt: dateRange });
    }

    // Add payment method filter if provided
    if (paymentMethod) {
      // Normalize incoming payment method values (e.g. "Airvilla Wallet" -> "airvillaWallet")
      const normalizedMethods = (
        Array.isArray(paymentMethod) ? paymentMethod : [paymentMethod]
      ).map((method) =>
        typeof method === "string"
          ? method.replace(/\s+/g, "").replace(/^./, (c) => c.toLowerCase())
          : method
      );

      const hasAirvillaWallet = normalizedMethods.includes("airvillaWallet");

      // If filtering for Airvilla Wallet, include bookings that came from Third-Party
      // because third-party wallet sales may not have a payment record but can be
      // identified by their source alone.
      if (hasAirvillaWallet) {
        whereClause.AND.push({
          OR: [
            {
              payment: {
                paymentMethod: {
                  in: normalizedMethods,
                  mode: "insensitive",
                },
              },
            },
            {
              AND: [
                { source: BookingSource.THIRD_PARTY },
                { payment: { is: null } },
              ],
            },
          ],
        });
      } else {
        // Standard payment-method filtering for all other methods
        whereClause.AND.push({
          payment: {
            paymentMethod: {
              in: normalizedMethods,
              mode: "insensitive",
            },
          },
        });
      }
    }

    // Add sale date range filter if provided
    if (saleDate && typeof saleDate === "string") {
      const dateRange = getCreatedTimeRange(saleDate.toLowerCase());
      whereClause.AND.push({ createdAt: dateRange });
    }

    // Add search filter if provided
    if (search) {
      const searchTerm = `%${search}%`;
      whereClause.OR = whereClause.OR || [];
      whereClause.OR.push(
        {
          requestId: { contains: searchTerm, mode: "insensitive" },
        },
        {
          referenceNumber: { contains: searchTerm, mode: "insensitive" },
        },
        {
          eTickets: {
            some: {
              eTicketNumber: { contains: searchTerm, mode: "insensitive" },
            },
          },
        },
        {
          travelers: {
            some: {
              traveler: {
                OR: [
                  { firstName: { contains: searchTerm, mode: "insensitive" } },
                  { lastName: { contains: searchTerm, mode: "insensitive" } },
                ],
              },
            },
          },
        },
        {
          ticket: {
            segments: {
              some: {
                OR: [
                  {
                    flightNumber: {
                      contains: searchTerm,
                      mode: "insensitive",
                    },
                  },
                  {
                    carrier: {
                      contains: searchTerm,
                      mode: "insensitive",
                    },
                  },
                  {
                    departure: {
                      airportCode: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  },
                  {
                    arrival: {
                      airportCode: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  },
                ],
              },
            },
          },
        }
      );
    }

    // First, get the bookings without the user relation to avoid the required field error
    const bookings = await prisma.booking.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: limitedPageSize + 1, // Fetch one extra to determine if there are more items
      ...(cursor ? { cursor: { id: cursor }, skip: 1 } : {}),
      include: {
        travelers: {
          include: {
            traveler: true,
          },
        },
        bookedSeats: true,
        ticket: {
          include: {
            flightClasses: {
              select: {
                price: true,
              },
            },
          },
        },
        payment: true,
      },
      // Don't include user relation here to avoid the required field error
    });

    // Then, fetch user data separately for each booking that has a userId
    const bookingsWithUsers = await Promise.all(
      bookings.map(async (booking) => {
        if (!booking.userId) {
          return {
            ...booking,
            user: null,
          };
        }

        const user =
          booking.userId && booking.userId !== null
            ? await prisma.user.findUnique({
                where: { id: booking.userId },
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              })
            : null;

        return {
          ...booking,
          user,
        };
      })
    );

    // Handle the case where user might be null in the response
    const bookingsWithSafeUser = bookingsWithUsers.map((booking) => ({
      ...booking,
      user: booking.user || {
        id: null,
        firstName: "Unknown",
        lastName: "User",
        email: null,
      },
    }));

    // Determine if there are more items
    const hasMore = bookingsWithUsers.length > limitedPageSize;
    const result = hasMore ? bookingsWithUsers.slice(0, -1) : bookingsWithUsers;
    const nextCursor = hasMore ? result[result.length - 1]?.id : null;

    // Get total count for pagination
    const totalCount = await prisma.booking.count({
      where: whereClause,
    });

    // Map bookings to include agent's full name and remove sensitive user data
    const mappedBookings = result.map((booking: any) => {
      const isThirdParty = booking.source === BookingSource.THIRD_PARTY;

      const sanitizedMeta = {
        ...booking.meta,
        ...(isThirdParty && {
          buyerAgencyId: undefined,
          buyerAgencyName: undefined,
          buyerAgentName: undefined,
        }),
      };

      return {
        ...booking,
        agent: booking.user
          ? isThirdParty
            ? "External Agent"
            : `${booking.user.firstName || ""} ${booking.user.lastName || ""}`.trim()
          : "Unknown Agent",
        // Hide buyer info if third-party; otherwise keep existing / previously masked value
        buyerAgentName: isThirdParty
          ? "External Agent"
          : booking.buyerAgencyName && booking.buyerAgencyId !== sellerAgencyId
            ? "External Agent"
            : booking.buyerAgentName,
        buyerAgencyName: isThirdParty ? undefined : booking.buyerAgencyName,
        buyerAgencyId: isThirdParty ? undefined : booking.buyerAgencyId,
        // Attach sanitized meta
        meta: sanitizedMeta,
        // Remove the user object to avoid exposing unnecessary data
        user: undefined,
      };
    });

    return res.status(200).json({
      success: true,
      results: {
        bookings: mappedBookings,
        bookingsTotal: totalCount,
        nextCursor,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Timeout a booking (system/admin)
 */
export const timeoutBookingHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const booking = await bookingService.timeoutBooking(id);
    if (!booking) return res.status(404).json({ message: "Booking not found" });
    res.status(200).json({ success: true, data: booking });
  } catch (error) {
    next(error);
  }
};

/**
 * Update traveler information for a booking
 */
export const updateTravelerInfoHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const travelerInfo = req.body;

    if (!travelerInfo) {
      return res.status(400).json({
        success: false,
        message: "Traveler information is required",
      });
    }

    const updatedBooking = await bookingService.updateTravelerInfo(
      id,
      travelerInfo
    );

    if (!updatedBooking) {
      return res.status(404).json({
        success: false,
        message: "Booking not found",
      });
    }

    res.status(200).json({
      success: true,
      data: updatedBooking,
      message: "Traveler information updated successfully",
    });
  } catch (error) {
    console.error("Error updating traveler information:", error);
    next(error);
  }
};

/**
 * Release seat for a booking (agent/system)
 */
export const releaseSeatHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const booking = await bookingService.releaseSeat(id);
    if (!booking) return res.status(404).json({ message: "Booking not found" });
    res.status(200).json({
      success: true,
      data: booking,
      message: "Your booking seat has been released successfully.",
    });
  } catch (error) {
    if (
      error instanceof Error &&
      error.message === "Booking is already timed out."
    ) {
      // Use 409 Conflict for already timed out
      return res.status(409).json({
        error: error.message,
        code: "ALREADY_TIMED_OUT",
        success: false,
      });
    }
    next(error);
  }
};

/**
 * Complete payment for a booking
 */
export const completePaymentHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const paymentDetails = req.body;

    // Process the payment
    const booking = await bookingService.completePayment(id, paymentDetails);

    if (!booking) {
      return res
        .status(404)
        .json({ isSuccess: false, message: "Booking not found" });
    }

    // Return success response with booking data
    return res.status(200).json({
      isSuccess: true,
      booking,
      message:
        "Payment completed successfully. Booking status updated to Pending Approval.",
    });
  } catch (error) {
    console.error("Payment completion error:", error);

    // Return detailed error message
    return res.status(500).json({
      isSuccess: false,
      message:
        error instanceof Error
          ? error.message
          : "Unknown error processing payment",
    });
  }
};

/**
 * Handles third-party booking creation.
 * Expects agent to be authenticated and booking data in req.body.
 */
export const createThirdPartyBookingHandler = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    let agentId: string | undefined;
    if (req.accountType === "masterUser" && req.teamMember) {
      agentId = req.teamMember.id;
    } else if (req.accountType === "masterOwner" && req.user) {
      agentId = req.user.id;
    } else if (req.accountType === "agencyOwner" && req.user) {
      agentId = req.user.id;
    } else if (req.accountType === "agencyUser" && req.agencyAgent) {
      agentId = req.agencyAgent.id;
    } else if (req.accountType === "affiliate" && req.user) {
      agentId = req.user.id;
    }

    if (!agentId) {
      return res
        .status(403)
        .json({ message: "Forbidden: Agent ID not found." });
    }
    // You may want to validate req.body here
    const bookingData = req.body;
    const result = await bookingService.createThirdPartyBooking(
      agentId,
      bookingData
    );

    res.status(201).json({
      message: "Third-party booking created successfully.",
      ...result,
    });
  } catch (error: any) {
    next(error);
  }
};

/**
 * Reschedules a booking to a new flight ticket.
 */
export const rescheduleBookingHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;

    const booking = await bookingService.rescheduleBooking(id);

    return res.status(200).json({
      success: true,
      data: {
        bookingId: booking.id,
        status: booking.status,
        requestId: booking.requestId,
        meta: booking.meta,
        updatedAt: booking.updatedAt,
      },
      message: "Booking has been successfully rescheduled.",
    });
  } catch (error: any) {
    if (error.code === "INVALID_BOOKING_STATE") {
      return res.status(400).json({
        success: false,
        code: error.code,
        message: error.message,
        details: error.details,
      });
    }
    next(error);
  }
};

/**
 * Gets ticket information for rescheduling a booking.
 */
export const getTicketForRescheduleHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;

    const ticketInfo = await bookingService.getTicketForReschedule(id);

    return res.status(200).json({
      success: true,
      data: ticketInfo,
    });
  } catch (error: any) {
    if (error.code === "INVALID_BOOKING_STATE") {
      return res.status(400).json({
        success: false,
        code: error.code,
        message: error.message,
        details: error.details,
      });
    }
    next(error);
  }
};
