import { MasterUserResultType } from "./masterDefinitions";

export type Role = "master" | "moderator" | "accountant";

export type Department =
  | "customer_support"
  | "management"
  | "finance"
  | "marketing"
  | "sales"
  | "it"
  | "operations";

export interface TeamMember {
  id: string;
  refId: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  role: Role;
  subRole?: string;
  password?: string;
  confirmPassword?: string;
  department: Department;
  status: "inactive" | "active";
  teamId: string;
  invitedById: string;
  accountStatus: "accepted" | "pending" | "rejected";
  createdAt: Date;
  updatedAt: Date;
  deactivationDate?: Date | null;
  logo?: string;
  agencyName?: string;
  nationality?: string;
  dateOfBirth?: string;
  gender?: string;
  website?: string;
  phoneNumber?: string;
  phoneNumberVerified?: boolean;
  subscriptionStatus: "active" | "inactive";
  lastLogin?: Date | null;
  verified?: boolean;
  invitation?: TeamInvitation;
  team?: any;
}

export interface TeamInvitation {
  id: string;
  email: string;
  role: Role;
  department: Department;
  expiresAt: Date;
  status: "pending" | "accepted" | "expired";
}

export interface ActionCellsProps {
  teamMember: TeamMember;
  onEdit: () => void;
  onDelete: () => void;
  setSelectedUser: (teamMember: TeamMember) => void;
}

export interface UserStatus {
  userId: string;
  status: string;
  value: string;
}

export interface TeamManagementTableListProps {
  user: MasterUserResultType | any;
  setUserStatus: React.Dispatch<React.SetStateAction<UserStatus>>;
  onUpdate: (updatedUser: TeamMember) => void;
}
