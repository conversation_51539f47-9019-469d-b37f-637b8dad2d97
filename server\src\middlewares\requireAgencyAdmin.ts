import { Response, NextFunction } from "express";
import { AuthRequest } from "../utils/definitions";
import { UserRole, hasPermission, PERMISSIONS } from "../utils/types/auth";

/**
 * Middleware to ensure the authenticated user has agency admin or owner privileges.
 * Should be used AFTER userAuth middleware.
 */
export const requireAgencyAdmin = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user || req.agencyAgent;
    
    // Check if user is authenticated
    if (!user) {
      return res.status(401).json({ 
        success: false,
        message: "Unauthorized: Authentication required" 
      });
    }

    // Check if user has the required role (agency_owner or agency_admin)
    const hasAdminAccess = 
    user.role === "master" ||
    user.roleType === "agency_owner" || 
    user.subRole === "admin";

    if (!hasAdminAccess) {
      return res.status(403).json({ 
        success: false,
        message: "Forbidden: Insufficient permissions. Requires agency admin or owner role." 
      });
    }

    // Check if the user is trying to access resources from their own agency
    if (req.params.agencyId && user.agencyId !== req.params.agencyId) {
      return res.status(403).json({
        success: false,
        message: "Forbidden: You can only manage resources in your own agency"
      });
    }

    // User is authorized, proceed to the next middleware/route handler
    next();
  } catch (error) {
    console.error("Authorization error:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred during authorization"
    });
  }
};

/**
 * Middleware factory function to check for specific permissions
 */
export const requirePermission = (requiredPermission: string) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const user = req.user || req.agencyAgent;
      
      if (!user) {
        return res.status(401).json({ 
          success: false,
          message: "Unauthorized: Authentication required" 
        });
      }

      // For master users, check if they have the required permission
      if (user.role === 'master') {
        // Master users typically have all permissions
        return next();
      }

      // For agency users, check their role type and permissions
      if (user.roleType === 'agency_owner' || user.subRole === 'admin') {
        // Agency owners and admins typically have all agency permissions
        return next();
      }

      // For other roles, check specific permissions if needed
      // This can be expanded based on your permission structure
      const hasAccess = hasPermission(
        user.roleType as any, 
        requiredPermission,
        user.agencyTier as any
      );

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: `Forbidden: Requires ${requiredPermission} permission`
        });
      }

      next();
    } catch (error) {
      console.error("Permission check error:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred during permission check"
      });
    }
  };
};
