import { useEffect, useCallback, useState } from "react";
import OriginalFlatpickr from "react-flatpickr";
import nationalities from "@/utils/nationalities.json";
import countries from "@/utils/countries.json";
// import { travelerFormValidation } from "@/utils/validators/travelerFormValiditions";
import { travelerFormValidation } from "@/utils/validators/travelerFormValidations";
import {
  Traveler,
  TravelerType,
} from "@/utils/definitions/blockSeatsDefinitions";
import Joi from "joi";
import TravelerDropdown from "./TravelerDropdown";
import moment from "moment";
import monthSelectPlugin from "flatpickr/dist/plugins/monthSelect/index";
import "flatpickr/dist/plugins/monthSelect/style.css";
import { Calendar } from "lucide-react";
import { getFormatDateTable } from "@/utils/functions/functions";
const Flatpickr = OriginalFlatpickr as any; // Type assertion here

// Helper to display date as DD-MM-YYYY from ISO (YYYY-MM-DD)
function formatDisplayDate(isoDate: string): string {
  if (!isoDate) return "";
  const [year, month, day] = isoDate.split("-");
  if (!year || !month || !day) return isoDate;
  return `${day}-${month}-${year}`;
}

export const TravelerForm = ({
  travelerNumber,
  traveler,
  onUpdate,
  isOpen = false,
  onToggle,
  readOnly = false,
}: {
  travelerNumber: number;
  traveler: Traveler;
  onUpdate: (updatedTraveler: Traveler) => void;
  isOpen?: boolean;
  onToggle?: () => void;
  readOnly?: boolean;
}) => {
  const toggleAccordion = () => {
    if (onToggle) {
      onToggle();
    }
  };
  const today = new Date();

  // Helper function to determine if a field should have a red border
  const shouldShowRedBorder = (fieldName: string, value: any): boolean => {
    const requiredFields = [
      "title",
      "firstName",
      "lastName",
      "dateOfBirth",
      "nationality",
      "passportNumber",
      "issuingCountry",
      "passportExpiry",
      "gender",
    ];

    // Show red border if:
    // 1. Field is required AND
    // 2. Field is empty/null/undefined OR
    // 3. Field has validation errors
    const isRequired = requiredFields.includes(fieldName);
    const isEmpty = !value || String(value).trim() === "";
    const hasError = Boolean(
      traveler?.errors?.[fieldName as keyof typeof traveler.errors]
    );

    return isRequired && (isEmpty || hasError);
  };

  // Helper function to get the border classes for form fields
  const getFieldBorderClasses = (fieldName: string, value: any) => {
    if (readOnly) return "border border-gray-300 dark:border-gray-600";

    const showRedBorder = shouldShowRedBorder(fieldName, value);
    return showRedBorder
      ? "border-2 border-red-500"
      : "border border-gray-300 dark:border-gray-600";
  };

  const getDateLimit = (monthsOffset: number): string => {
    const date = new Date(today);
    date.setMonth(today.getMonth() + monthsOffset);
    return date.toISOString().split("T")[0];
  };
  const pastMonthsAllowed = 3;
  const futureMonthsAllowed = 3;

  const minDate = getDateLimit(-pastMonthsAllowed);
  const maxDate = getDateLimit(futureMonthsAllowed);
  const formattedToday = today.toISOString().split("T")[0];

  // Format date as DD/MM/YYYY for display
  const formatDateForDisplay = (dateString: string): string => {
    if (!dateString) return "";

    // Handle ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS.sssZ)
    if (dateString.includes("T")) {
      const [datePart] = dateString.split("T");
      const [year, month, day] = datePart.split("-");
      return `${day}/${month}/${year}`;
    }

    // Handle YYYY-MM-DD format
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split("-");
      return `${day}/${month}/${year}`;
    }

    // If it's already in DD/MM/YYYY format, return as is
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateString)) {
      return dateString;
    }

    return dateString;
  };
  const [transactionDate, setTransactionDate] =
    useState<string>(formattedToday);
  const [DateOfBirthInput, setDateOfBirthInput] = useState<string>(
    formatDateForDisplay(traveler.dateOfBirth)
  );
  const [passportExpiryInput, setPassportExpiryInput] = useState<string>(
    formatDateForDisplay(traveler.passportExpiry)
  );
  // Handle Transaction Date input changes (keyboard only, DD/MM/YYYY)
  const handlePassportExpiryInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value = e.target.value.replace(/[^\d/]/g, ""); // Only numbers and slashes
    // Auto-insert slashes and zero-pad day/month
    if (value.length === 2 && !value.includes("/")) value += "/";
    if (value.length === 5 && value.split("/").length < 3) value += "/";
    // Enforce DD/MM/YYYY
    if (value.length > 10) value = value.slice(0, 10);

    // Validate day and month as user types
    const parts = value.split("/");
    let [day, month, year] = parts;
    let valid = true;
    if (day && day.length === 2) {
      const dayNum = parseInt(day, 10);
      if (dayNum < 1 || dayNum > 31) {
        valid = false;
        day = dayNum > 31 ? "31" : dayNum < 1 ? "01" : day.padStart(2, "0");
      } else {
        day = day.padStart(2, "0");
      }
    }
    if (month && month.length === 2) {
      const monthNum = parseInt(month, 10);
      if (monthNum < 1 || monthNum > 12) {
        valid = false;
        month =
          monthNum > 12 ? "12" : monthNum < 1 ? "01" : month.padStart(2, "0");
      } else {
        month = month.padStart(2, "0");
      }
    }

    // Reconstruct value if we fixed anything
    if (parts.length > 1) {
      value = `${day || ""}${month !== undefined ? "/" + month : ""}${
        year !== undefined ? "/" + year : ""
      }`;
      value = value.slice(0, 10);
    }

    setPassportExpiryInput(value);

    // If we have a complete and valid date, update the traveler object
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(value) && valid && year && month && day) {
      const isoDate = `${year}-${month}-${day}`;
      const inputDate = new Date(isoDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to compare dates only

      // Validate that passport expiry is not in the past
      if (inputDate < today) {
        onUpdate({
          ...traveler,
          errors: {
            ...traveler.errors,
            passportExpiry: "Passport expiry date cannot be in the past.",
          },
        });
      } else {
        onUpdate({
          ...traveler,
          passportExpiry: isoDate,
          errors: {
            ...traveler.errors,
            passportExpiry: "",
          },
        });
      }
    } else if (value.length === 10) {
      onUpdate({
        ...traveler,
        errors: {
          ...traveler.errors,
          passportExpiry: "Invalid date: day must be 1-31 and month 1-12.",
        },
      });
    } else if (value.length === 0) {
      onUpdate({
        ...traveler,
        passportExpiry: "",
        errors: {
          ...traveler.errors,
          passportExpiry: "Passport expiry is required",
        },
      });
    } else {
      onUpdate({
        ...traveler,
        errors: {
          ...traveler.errors,
          passportExpiry: "",
        },
      });
    }
  };

  // Handle Date of Birth input changes (keyboard only, DD/MM/YYYY)
  const handleDateOfBirthInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value = e.target.value.replace(/[^\d/]/g, ""); // Only numbers and slashes
    // Auto-insert slashes and zero-pad day/month
    if (value.length === 2 && !value.includes("/")) value += "/";
    if (value.length === 5 && value.split("/").length < 3) value += "/";
    // Enforce DD/MM/YYYY
    if (value.length > 10) value = value.slice(0, 10);

    // Validate day and month as user types
    const parts = value.split("/");
    let [day, month, year] = parts;
    let valid = true;

    if (day && day.length === 2) {
      const dayNum = parseInt(day, 10);
      if (dayNum < 1 || dayNum > 31) {
        valid = false;
        day = dayNum > 31 ? "31" : dayNum < 1 ? "01" : day.padStart(2, "0");
      } else {
        day = day.padStart(2, "0");
      }
    }

    if (month && month.length === 2) {
      const monthNum = parseInt(month, 10);
      if (monthNum < 1 || monthNum > 12) {
        valid = false;
        month =
          monthNum > 12 ? "12" : monthNum < 1 ? "01" : month.padStart(2, "0");
      } else {
        month = month.padStart(2, "0");
      }
    }

    // Reconstruct value if we fixed anything
    if (parts.length > 1) {
      value = `${day || ""}${month !== undefined ? "/" + month : ""}${
        year !== undefined ? "/" + year : ""
      }`;
      value = value.slice(0, 10);
    }

    setDateOfBirthInput(value);

    // If we have a complete and valid date, update the traveler object
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(value) && valid && year && month && day) {
      const isoDate = `${year}-${month}-${day}`;
      const inputDate = new Date(isoDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to compare dates only

      // Validate that date of birth is not in the future
      if (inputDate > today) {
        onUpdate({
          ...traveler,
          errors: {
            ...traveler.errors,
            dateOfBirth: "Date of birth cannot be in the future.",
          },
        });
      } else {
        onUpdate({
          ...traveler,
          dateOfBirth: isoDate,
          errors: {
            ...traveler.errors,
            dateOfBirth: "",
          },
        });
      }
    } else if (value.length === 10) {
      onUpdate({
        ...traveler,
        errors: {
          ...traveler.errors,
          dateOfBirth: "Invalid date: day must be 1-31 and month 1-12.",
        },
      });
    } else if (value.length === 0) {
      onUpdate({
        ...traveler,
        dateOfBirth: "",
        errors: {
          ...traveler.errors,
          dateOfBirth: "Date of birth is required",
        },
      });
    } else {
      onUpdate({
        ...traveler,
        errors: {
          ...traveler.errors,
          dateOfBirth: "",
        },
      });
    }
  };

  // Add this function to calculate age from date of birth
  const calculateAge = (dob: string): number => {
    if (!dob) return 0;

    const birthDate = new Date(dob);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  // Add this function to get traveler type based on age
  const getTravelerTypeByAge = (age: number): TravelerType => {
    if (age < 2) return TravelerType.INFANT;
    if (age < 12) return TravelerType.CHILD;
    return TravelerType.ADULT;
  };

  // In the component, add this effect to update traveler type when date of birth changes
  useEffect(() => {
    if (traveler.dateOfBirth) {
      const age = calculateAge(traveler.dateOfBirth);
      const newType = getTravelerTypeByAge(age);

      if (newType !== traveler.type) {
        handleInputChange("type", newType);
      }
    }
  }, [traveler.dateOfBirth]);

  // Initialize date values when component mounts or traveler data changes
  useEffect(() => {
    if (traveler.dateOfBirth) {
      setDateOfBirthInput(formatDateForDisplay(traveler.dateOfBirth));
    }
    if (traveler.passportExpiry) {
      setPassportExpiryInput(formatDateForDisplay(traveler.passportExpiry));
    }
  }, [traveler.dateOfBirth, traveler.passportExpiry]);

  const handleInputChange = useCallback(
    (field: keyof Traveler, value: string) => {
      // Special handling for date fields
      if (field === "dateOfBirth") {
        // Use the existing date of birth handler
        const e = { target: { value } } as React.ChangeEvent<HTMLInputElement>;
        handleDateOfBirthInputChange(e);
        return;
      } else if (field === "passportExpiry") {
        // Use the existing passport expiry handler
        const e = { target: { value } } as React.ChangeEvent<HTMLInputElement>;
        handlePassportExpiryInputChange(e);
        return;
      } else if (field === "issuingCountry") {
        // Handle both passportIssuingCountry and issuingCountry
        const updatedTraveler = {
          ...traveler,
          // passportIssuingCountry: value,
          // issuingCountry: value  // Also update issuingCountry for backward compatibility
          [field]: value,
        };
        return onUpdate(updatedTraveler);
      }

      // For non-date fields, proceed with normal handling
      const updatedTraveler = { ...traveler, [field]: value };
      const newErrors = { ...traveler.errors };

      try {
        // Special handling for type field
        let fieldSchema;
        if (field === "type") {
          fieldSchema = Joi.object({
            [field]: Joi.string()
              .valid(...Object.values(TravelerType))
              .required(),
          });
        } else {
          // For other fields, use the existing validation
          fieldSchema = Joi.object({
            [field]: travelerFormValidation.extract(field),
          });
        }

        const { error } = fieldSchema.validate(
          { [field]: value },
          { abortEarly: true, allowUnknown: true }
        );

        if (error) {
          // Add the error for this field
          newErrors[field] = error.details[0].message;
        } else {
          // Clear any existing error for this field
          delete newErrors[field];
        }
      } catch (err) {
        console.error("Validation error:", err);
      }

      // Update the traveler with the new value and errors
      onUpdate({
        ...updatedTraveler,
        errors: newErrors,
      });
    },
    [traveler, onUpdate]
  );

  useEffect(() => {
    const updatedTraveler = { ...traveler };

    // Set default values if they don't exist
    if (!updatedTraveler.title) updatedTraveler.title = "";
    if (!updatedTraveler.gender) updatedTraveler.gender = "";
    if (!updatedTraveler.nationality) updatedTraveler.nationality = "";
    if (!updatedTraveler.type) updatedTraveler.type = TravelerType.ADULT; // Set default traveler type
    // Ensure both passportIssuingCountry and issuingCountry are in sync
    // if (updatedTraveler.passportIssuingCountry && !updatedTraveler.issuingCountry) {
    //   updatedTraveler.issuingCountry = updatedTraveler.passportIssuingCountry;
    // } else if (updatedTraveler.issuingCountry && !updatedTraveler.passportIssuingCountry) {
    //   updatedTraveler.passportIssuingCountry = updatedTraveler.issuingCountry;
    // } else if (!updatedTraveler.passportIssuingCountry && !updatedTraveler.issuingCountry) {
    //   updatedTraveler.passportIssuingCountry = "";
    //   updatedTraveler.issuingCountry = "";
    // }
    if (!updatedTraveler.issuingCountry) updatedTraveler.issuingCountry = "";
    if (!updatedTraveler.passportNumber) updatedTraveler.passportNumber = "";
    if (!updatedTraveler.contactEmail) updatedTraveler.contactEmail = "";
    if (!updatedTraveler.contactPhone) updatedTraveler.contactPhone = "";
    if (!updatedTraveler.errors) updatedTraveler.errors = {};

    // Only update if there are changes
    if (JSON.stringify(updatedTraveler) !== JSON.stringify(traveler)) {
      onUpdate(updatedTraveler);
    }
  }, []); // Empty dependency array means this runs once on mount

  const parseDateSafely = (
    dateString: string | null | undefined
  ): Date | undefined => {
    if (!dateString) return undefined;

    const formats = [
      moment.ISO_8601,
      "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ",
      "MMMM D, YYYY", // Added this format for "September 6, 2024"
      "M/D/YYYY", // Also added this common format just in case
      "MM/DD/YYYY",
      "DD-MM-YYYY",
    ];

    for (const format of formats) {
      const parsed = moment(dateString, format, true);
      if (parsed.isValid()) {
        return parsed.toDate();
      }
    }

    console.warn(`Unable to parse date: ${dateString}`);
    return undefined;
  };

  return (
    <div className="mb-2">
      <div
        onClick={toggleAccordion}
        className="flex justify-between items-center mb-4 bg-white dark:bg-gray-600 p-1 py-2 px-6 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors duration-300"
      >
        <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white overflow-hidden">
          Traveler {travelerNumber}: {traveler?.firstName} {traveler?.lastName}
        </h3>
        <div className="flex items-end justify-center">
          <div className="relative w-3 h-3 mx-4">
            <span className="absolute top-1/2 left-0 w-full h-0.5 bg-gray-500 dark:bg-gray-300 transition-transform duration-500 transform -translate-y-1/2"></span>
            <span
              className={`absolute top-0 left-1/2 w-0.5 h-full bg-gray-500 dark:bg-gray-300 transition-transform duration-500 transform origin-center -translate-x-1/2 ${
                isOpen ? "rotate-90" : "rotate-0"
              }`}
            ></span>
          </div>
        </div>
      </div>
      <div
        className={`overflow-hidden transition-max-height duration-1000 ease-in-out ${
          isOpen ? "max-h-[5000px]" : "max-h-0"
        }`}
      >
        <form>
          {/* Personal Information */}
          <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 space-y-4">
            <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white">
              Personal Information
            </h3>
            {/* Title */}
            <div className="flex flex-col justify-between md:flex-row flex-wrap md:flex-nowrap md:space-x-1 space-y-4 md:space-y-0 gap-2">
              <div className="w-full md:w-1/2">
                <TravelerDropdown
                  id={`title_${travelerNumber}`}
                  label="Title"
                  value={traveler?.title}
                  options={["Mr", "Mrs", "Ms"]}
                  readOnly={readOnly}
                  required
                  onChange={(value) => {
                    // Create a new traveler object with updated title
                    const updatedTraveler = { ...traveler, title: value };

                    // Set gender based on title
                    updatedTraveler.gender = value === "Mr" ? "male" : "female";

                    // Update the traveler object
                    onUpdate(updatedTraveler);
                  }}
                  error={traveler?.errors?.title}
                  travelerNumber={travelerNumber}
                  showRedBorder={shouldShowRedBorder("title", traveler?.title)}
                />
              </div>

              {/* Gender */}
              <div className="w-full md:w-1/2">
                <label
                  htmlFor={`gender_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Gender {travelerNumber === 1 ? "" : travelerNumber}{" "}
                  <span className="text-red-600 dark:text-red-400">*</span>
                </label>
                <input
                  type="text"
                  name={`gender_${travelerNumber}`}
                  id={`gender_${travelerNumber}`}
                  required
                  aria-label="Gender"
                  aria-required="true"
                  title="Gender is automatically set based on title selection"
                  placeholder="Select Title first"
                  value={traveler?.gender}
                  readOnly
                  disabled
                  className={`w-full border-none cursor-not-allowed opacity-70 ${
                    traveler?.errors?.gender
                      ? "border border-red-500 ring-2 ring-red-500"
                      : ""
                  } ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white capitalize placeholder:text-gray-500 dark:placeholder:text-gray-300`}
                />
                {traveler?.errors?.gender && (
                  <p className="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                    {traveler?.errors?.gender}
                  </p>
                )}
              </div>
            </div>

            <div className="flex flex-col justify-between md:flex-row flex-wrap md:flex-nowrap space-y-4 md:space-y-0  gap-2">
              <div className="w-full md:w-1/2">
                {/* First Name */}
                <label
                  htmlFor={`first_name_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  First Name {travelerNumber === 1 ? "" : travelerNumber}
                  <span className="text-red-600 dark:text-red-400">*</span>
                </label>
                <input
                  id={`first_name_${travelerNumber}`}
                  name={`first_name_${travelerNumber}`}
                  autoComplete="given-name"
                  autoCorrect="true"
                  autoCapitalize="true"
                  placeholder="First name"
                  required
                  readOnly={readOnly}
                  aria-label="First name"
                  aria-required="true"
                  type="text"
                  pattern="[A-Za-z\s]+"
                  title="Please enter your first name using letters only."
                  maxLength={50}
                  spellCheck="true"
                  value={traveler?.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  className={`w-full flex-1 p-2 ${getFieldBorderClasses(
                    "firstName",
                    traveler?.firstName
                  )} ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-0 focus:ring-red-200 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400`}
                />
                {traveler?.errors?.firstName && (
                  <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                    {traveler?.errors?.firstName}
                  </p>
                )}
              </div>
              <div className="w-full  md:w-1/2">
                {/* Last Name */}
                <label
                  htmlFor={`last_name_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Last Name {travelerNumber === 1 ? "" : travelerNumber}
                  <span className="text-red-600 dark:text-red-400">*</span>
                </label>
                <input
                  id={`last_name_${travelerNumber}`}
                  name={`last_name_${travelerNumber}`}
                  autoComplete="family-name"
                  autoCorrect="true"
                  autoCapitalize="true"
                  required
                  readOnly={readOnly}
                  aria-label="Last name"
                  aria-required="true"
                  type="text"
                  pattern="[A-Za-z\s]+"
                  title="Please enter your last name using letters only."
                  maxLength={50}
                  spellCheck="true"
                  placeholder="Last name"
                  value={traveler?.lastName}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  className={`w-full flex-1 p-2 ${getFieldBorderClasses(
                    "lastName",
                    traveler?.lastName
                  )} ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-0 focus:ring-red-200 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white mt-2 md:mt-0 space-x-0 placeholder:text-gray-500 dark:placeholder:text-gray-400`}
                />
                {traveler?.errors?.lastName && (
                  <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                    {traveler?.errors?.lastName}
                  </p>
                )}
              </div>
            </div>

            {/* Date of Birth & Nationality */}
            <div className="flex flex-col justify-between md:flex-row flex-wrap md:flex-nowrap md:space-x-1 space-y-4 md:space-y-0 gap-2">
              {/* Date of Birth */}
              <div className="relative w-full md:w-1/2">
                <label
                  htmlFor={`date_of_birth_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Date of Birth {travelerNumber === 1 ? "" : travelerNumber}
                  <span className="text-red-600 dark:text-red-400">*</span>
                </label>
                <div className="relative">
                  <input
                    id={`date_of_birth_${travelerNumber}`}
                    name={`date_of_birth_${travelerNumber}`}
                    required
                    readOnly={readOnly}
                    aria-label="Date of Birth"
                    aria-required="true"
                    type="text"
                    className={`w-full p-2 ${getFieldBorderClasses(
                      "dateOfBirth",
                      traveler?.dateOfBirth
                    )} ${
                      readOnly ? "cursor-not-allowed opacity-70" : ""
                    } focus:border-red-500 focus:outline-none focus:ring-0 focus:ring-red-200 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400`}
                    // value={traveler.dateOfBirth}
                    value={DateOfBirthInput}
                    // onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                    onChange={handleDateOfBirthInputChange}
                    placeholder="DD/MM/YYYY"
                    maxLength={10}
                    autoComplete="off"
                    inputMode="numeric"
                  />
                  <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </span>
                </div>
                {traveler?.errors?.dateOfBirth && (
                  <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                    {traveler?.errors?.dateOfBirth}
                  </p>
                )}
              </div>
              {/* Nationality */}
              <div className="w-full md:w-1/2">
                <TravelerDropdown
                  id={`nationality_${travelerNumber}`}
                  label="Nationality"
                  value={traveler?.nationality}
                  onChange={(value) => handleInputChange("nationality", value)}
                  options={nationalities}
                  required
                  readOnly={readOnly}
                  error={traveler?.errors?.nationality}
                  travelerNumber={travelerNumber}
                  showRedBorder={shouldShowRedBorder("nationality",traveler?.nationality)}
                />
              </div>
            </div>

            {/* Traveler Type */}
            <div className="w-full">
              <label
                htmlFor={`type_${travelerNumber}`}
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Traveler Type {travelerNumber === 1 ? "" : travelerNumber}
                <span className="text-red-600 dark:text-red-400">*</span>
              </label>
              <input
                type="text"
                id={`type_${travelerNumber}`}
                name={`type_${travelerNumber}`}
                value={
                  traveler.type || traveler.dateOfBirth
                    ? traveler.type === TravelerType.ADULT ||
                      getTravelerTypeByAge(
                        calculateAge(traveler.dateOfBirth)
                      ) === TravelerType.ADULT
                      ? "Adult (12+ years)"
                      : traveler.type === TravelerType.CHILD ||
                        getTravelerTypeByAge(
                          calculateAge(traveler.dateOfBirth)
                        ) === TravelerType.CHILD
                      ? "Child (2-11 years)"
                      : "Infant (Under 2 years)"
                    : ""
                }
                disabled
                className={`w-full p-2 border-none rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white cursor-not-allowed ${
                  readOnly ? "cursor-not-allowed opacity-70" : ""
                }`}
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Automatically determined by date of birth
              </p>
            </div>
          </section>
          {/* Identification Information */}
          <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 space-y-4">
            <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white">
              Identification Information
            </h3>

            <div className="flex flex-col justify-between md:flex-row flex-wrap md:flex-nowrap md:space-x-1 space-y-4 md:space-y-0 gap-2">
              {/* Passport Issuing Country */}
              <div className="w-full md:w-1/2">
                <TravelerDropdown
                  id={`passport_issuing_country_${travelerNumber}`}
                  label="Passport Issuing Country"
                  value={traveler?.issuingCountry ?? traveler?.issuingCountry}
                  onChange={(value) => {
                    handleInputChange("issuingCountry", value);
                  }}
                  options={countries}
                  required
                  readOnly={readOnly}
                  error={traveler?.errors?.issuingCountry}
                  travelerNumber={travelerNumber}
                  showRedBorder={shouldShowRedBorder("issuingCountry",traveler?.issuingCountry)}
                />
              </div>
              {/* Passport Number */}
              <div className="w-full md:w-1/2">
                <label
                  htmlFor={`passport_number_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Passport Number {travelerNumber === 1 ? "" : travelerNumber}
                  <span className="text-red-600 dark:text-red-400">*</span>
                </label>
                <input
                  type="text"
                  id={`passport_number_${travelerNumber}`}
                  name={`passport_number_${travelerNumber}`}
                  required
                  readOnly={readOnly}
                  aria-label="Passport Number"
                  aria-required="true"
                  title="Please select your passport number."
                  placeholder="Enter passport number"
                  value={traveler?.passportNumber}
                  onChange={(e) =>
                    handleInputChange("passportNumber", e.target.value)
                  }
                  className={`w-full p-2
                  ${getFieldBorderClasses(
                    "passportNumber",
                    traveler?.passportNumber
                  )}
                  ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-0 focus:ring-red-500 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400`}
                />
                {traveler?.errors?.passportNumber && (
                  <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                    {traveler?.errors?.passportNumber}
                  </p>
                )}
              </div>
            </div>
            {/* Passport Expiry */}
            <div className="w-full">
              <label
                htmlFor={`passport_expiry_${travelerNumber}`}
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Passport Expiry {travelerNumber === 1 ? "" : travelerNumber}
                <span className="text-red-600 dark:text-red-400">*</span>
              </label>
              <div className="relative">
                <input
                  id={`passport_expiry_${travelerNumber}`}
                  name={`passport_expiry_${travelerNumber}`}
                  required
                  readOnly={readOnly}
                  aria-label="Passport Expiry"
                  aria-required="true"
                  type="text"
                  className={`w-full p-2 
                  ${getFieldBorderClasses(
                    "passportExpiry",
                    traveler?.passportExpiry
                  )}
                  ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-0 focus:ring-red-500 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400`}
                  // value={traveler.passportExpiry}
                  value={passportExpiryInput}
                  // onChange={(e) => handleInputChange("passportExpiry", e.target.value)}
                  onChange={handlePassportExpiryInputChange}
                  placeholder="DD/MM/YYYY"
                  maxLength={10}
                  autoComplete="off"
                  inputMode="numeric"
                />
                <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Calendar className="h-5 w-5 text-gray-400" />
                </span>
              </div>
              {traveler?.errors?.passportExpiry && (
                <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                  {traveler?.errors?.passportExpiry}
                </p>
              )}
            </div>
          </section>
          {/* Contact Information */}
          <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 space-y-4">
            <h3 className="text-xl md:text-2xl leading-8 mb-2 font-bold text-gray-700 dark:text-white">
              Contact Information
            </h3>
            <div className="flex flex-col justify-between md:flex-row flex-wrap md:flex-nowrap md:space-x-1 space-y-4 md:space-y-0 gap-2">
              {/* Email */}
              <div className="w-full md:w-1/2">
                <label
                  htmlFor={`contactEmail_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Email {travelerNumber === 1 ? "" : travelerNumber}{" "}
                  {/* <span className="text-red-600 dark:text-red-400">*</span> */}
                </label>
                <input
                  type="email"
                  id={`contactEmail_${travelerNumber}`}
                  name={`contactEmail_${travelerNumber}`}
                  // required
                  readOnly={readOnly}
                  aria-label="Email"
                  aria-required="true"
                  title="Please enter your email"
                  placeholder="Enter email address"
                  value={traveler?.contactEmail || ""}
                  onChange={(e) =>
                    handleInputChange("contactEmail", e.target.value)
                  }
                  className={`w-full border-none ${
                    traveler?.errors?.contactEmail
                      ? "border border-red-500 ring-2 ring-red-500"
                      : ""
                  } ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400`}
                />
                {traveler?.errors?.contactEmail && (
                  <p className="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                    {traveler?.errors?.contactEmail}
                  </p>
                )}
              </div>
              {/* contact Number */}
              <div className="w-full md:w-1/2">
                <label
                  htmlFor={`contact_number_${travelerNumber}`}
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Contact Number {travelerNumber === 1 ? "" : travelerNumber}
                  {/* <span className="text-red-600 dark:text-red-400">*</span> */}
                </label>
                <input
                  type="tel"
                  id={`contact_number_${travelerNumber}`}
                  name={`contact_number_${travelerNumber}`}
                  // required
                  readOnly={readOnly}
                  aria-label="Contact Number"
                  aria-required="true"
                  title="Please enter your contact number"
                  placeholder="Enter contact number"
                  value={traveler?.contactPhone || ""}
                  onChange={(e) =>
                    handleInputChange("contactPhone", e.target.value)
                  }
                  className={`w-full border-none ${
                    traveler?.errors?.contactPhone
                      ? "border border-red-500 ring-2 ring-red-500"
                      : ""
                  } ${
                    readOnly ? "cursor-not-allowed opacity-70" : ""
                  } focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 hover:border-red-500 rounded-lg bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-700 dark:placeholder:text-gray-400`}
                />
                {traveler?.errors?.contactPhone && (
                  <p className="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                    {traveler?.errors?.contactPhone}
                  </p>
                )}
              </div>
            </div>
          </section>
        </form>
      </div>
    </div>
  );
};
