import axios, {
  AxiosError,
  <PERSON>xiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import { store } from "@/redux/store";
import { handleApiError } from "@/utils/functions/apiErrorHandler";
import { logoutUser } from "@/redux/features/AuthSlice";

interface ApiError extends Error {
  response?: {
    data?: {
      message?: string;
    };
  };
  message: string;
}

// Create a base API instance with common configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.SERVER_URL || "http://localhost:3000/api",
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token if available
apiClient.interceptors.request.use(
  (config) => {
    // const { auth } = store.getState();
    // console.log("Auth state:", auth); // Add this line

    if (config.data instanceof FormData) {
      delete config.headers["Content-Type"];
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      store.dispatch(logoutUser());
      // Redirect to login or handle as needed
      if (typeof window !== "undefined") {
        // window.location.href = "/login";
      }
    //   return Promise.reject({
    //     message: "Session expired. Please log in again.",
    //   });
    }

    // Handle other errors
    handleApiError(error);
    return Promise.reject(error);
  }
);

// Generic API request function
export const apiRequest = async <T>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response = await apiClient.request<T>({
      ...config,
      headers: {
        ...config.headers,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
        const message = error.response?.data?.message || error.message || 'An error occurred';
        throw { message };
      } else if (error instanceof Error) {
        throw { message: error.message };
      }
      throw { message: 'An unknown error occurred' };
  }
};

// Specific API methods
export const api = {
  get: <T>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: "GET", url }),

  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: "POST", url, data }),

  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: "PUT", url, data }),

  delete: <T>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: "DELETE", url }),
};
