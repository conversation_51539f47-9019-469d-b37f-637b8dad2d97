import axios, { AxiosRequestConfig, AxiosError } from "axios";
import agencyUrl from "../endpoints/agencyEndpoint";

export const fetchData = async (
  url: string,
  options: AxiosRequestConfig = {}
) => {
  try {
    // Merge default options with provided options
    const axiosOptions: AxiosRequestConfig = {
      url,
      withCredentials: true,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    const response = await axios.request(axiosOptions);

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      // const errorData = error.response.data;
      // throw new Error(
      //   errorData.message || `Error: ${error.response.statusText}`
      // );
      throw error.response.data;
    } else {
      throw new Error("Network error occurred.");
    }
  }
};

// CREATE AGENT
export const createAgent = async (data: any) => {
  const url = agencyUrl.createAgent;
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: data,
  };

  return await fetchData(url, options);
};

// UPDATE AGENT
export const updateAgent = async (id: string, data: any) => {
  const url = agencyUrl.updateAgent(id);
  const options = {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: data,
  };
  return await fetchData(url, options);
};

// DELETE AGENT
export const deleteAgent = async (id: string) => {
  const url = agencyUrl.deleteAgent(id);
  const options = {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  };
  return await fetchData(url, options);
};

// GET AGENTS (For agency users)
export const fetchAllAgents = async (cursor?: string) => {
  const url = cursor
    ? `${agencyUrl.fetchAllAgents}?cursor=${cursor}`
    : agencyUrl.fetchAllAgents;

  return await fetchData(url);
};

// SEARCH AGENTS
export const searchAgents = async (input: string) => {
  const url = `${agencyUrl.searchAgents}?query=${input}`;
  return await fetchData(url);
};
