"use client";
import { countryCodes } from "@/utils/data/countries";
import React, { useState, useEffect, useRef } from "react";
import { Check, ChevronDown, Phone } from "lucide-react";

interface Country {
  name: string;
  code: string;
}

const PhoneNumberField = ({
  getPhoneNumber,
}: {
  getPhoneNumber: (pn: string) => void;
}) => {
  const [selectedCountry, setSelectedCountry] = useState(countryCodes[0]);
  const [menuActive, setMenuActive] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);
  const phoneInputRef = useRef<HTMLInputElement>(null);

  // Filter countries based on search input
  const filteredCountries =
    searchValue.trim() === ""
      ? countryCodes
      : countryCodes.filter(
          (country) =>
            country.name.toLowerCase().includes(searchValue.toLowerCase()) ||
            country.code.toLowerCase().includes(searchValue.toLowerCase())
        );

  // Handle click outside to close dropdown
  const handleClickOutside = (event: MouseEvent) => {
    if (
      menuRef.current &&
      !menuRef.current.contains(event.target as Node) &&
      !(event.target instanceof HTMLInputElement)
    ) {
      setMenuActive(false);
      setSearchValue("");
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Handle country selection
  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setMenuActive(false);
    setSearchValue("");
    const pn = `${country.code}${phoneNumber}`;
    getPhoneNumber(pn);

    // Focus the phone input after selecting a country
    if (phoneInputRef.current) {
      phoneInputRef.current.focus();
    }
  };

  // Handle phone number input change
  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    const pn = `${selectedCountry.code}${input}`;
    getPhoneNumber(pn);
    setPhoneNumber(input);
  };

  return (
    <div className="flex w-full">
      {/* Country Code Dropdown */}
      <div className="relative" ref={menuRef}>
        <div
          onClick={() => {
            setMenuActive(true);
            setSearchValue("");
          }}
          className={`btn py-0 pl-0 w-full justify-between min-w-[5.5rem] h-[45px] bg-gray-300 dark:bg-gray-600 hover:border hover:border-red-500 hover:ring-1 hover:ring-red-500 text-gray-800 hover:text-gray-900 dark:text-white dark:hover:text-gray-200 rounded-l-lg ${
            menuActive ? "border border-red-500 ring-1 ring-red-500" : ""
          }`}
        >
          <span className="flex-1 items-center px-3">
            <span className="text-sm font-medium">{selectedCountry.code}</span>
          </span>
          <ChevronDown
            className="text-gray-500 dark:text-gray-400 ml-1 mr-2"
            size={16}
          />
        </div>

        {/* Dropdown Menu */}
        {menuActive && (
          <div className="z-20 absolute top-full left-0 w-64 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1">
            <div className="p-2 border-b border-gray-200 dark:border-gray-700">
              <input
                className="w-full p-1.5 text-sm text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 border-none rounded-md focus:ring-2 focus:ring-red-500"
                placeholder="Search countries..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
              />
            </div>
            <div className="font-medium text-sm text-gray-600 dark:text-gray-300 focus:outline-none max-h-60 overflow-auto custom-scrollbar">
              {filteredCountries.length === 0 && (
                <div className="text-center py-3">
                  <span>No Results</span>
                </div>
              )}
              {filteredCountries.length > 0 &&
                filteredCountries.map((country, idx) => {
                  const isSelected = selectedCountry.code === country.code;

                  return (
                    <button
                      key={idx}
                      type="button"
                      className={`flex items-center justify-between text-sm w-full py-2 px-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${
                        isSelected ? "text-red-500" : ""
                      }`}
                      onClick={() => handleCountrySelect(country)}
                    >
                      <div className="text-start">
                        <div className="font-bold">
                          {country.name}{" "}
                          <span className="font-normal">({country.code})</span>
                        </div>
                      </div>
                      <Check
                        className={`shrink-0 mr-2 text-red-500 ${
                          !isSelected && "invisible"
                        }`}
                        size={18}
                      />
                    </button>
                  );
                })}
            </div>
          </div>
        )}
      </div>

      {/* Phone Number Input */}
      <div className="relative flex-1">
        <span className="absolute inset-y-0 left-3 flex items-center text-gray-500 dark:text-gray-400">
          <Phone size={18} />
        </span>
        <input
          type="tel"
          id="phone-input"
          ref={phoneInputRef}
          className="block pl-10 pr-4 py-1.5 md:py-2.5 w-full rounded-r-lg border-0 outline-none transition-all duration-300 focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white placeholder:text-xs md:placeholder:text-sm"
          placeholder="1234567890"
          value={phoneNumber}
          onChange={handlePhoneNumberChange}
          required
        />
      </div>
    </div>
  );
};

export default PhoneNumberField;
