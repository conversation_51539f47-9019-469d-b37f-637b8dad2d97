import { StoredUser } from "@/utils/definitions/authDefinitions";

/**
 * Check if the user has Admin role (either master_admin or agency_admin)
 * @param user The user object from Redux store
 * @returns boolean indicating if the user has Admin role
 */
export const isAdmin = (user: StoredUser): boolean => {
  if (!user) return false;
  
  // Check for master_admin or agency_admin roleType
  if (user.roleType) {
    return (
      user.roleType === "master_admin" || 
      user.roleType === "agency_admin" ||
      user.roleType === "master_owner" || 
      user.roleType === "agency_owner"
    );
  }
  
  // Fallback to role and subRole check
  if (user.role === "master" && user.subRole === "admin") return true;
  if (user.role === "agency" && user.subRole === "admin") return true;
  
  return false;
};

/**
 * Check if the user has Accountant role (either master_accountant or agency_accountant)
 * @param user The user object from Redux store
 * @returns boolean indicating if the user has Accountant role
 */
export const isAccountant = (user: StoredUser): boolean => {
  if (!user) return false;
  
  // Check for master_accountant or agency_accountant roleType
  if (user.roleType) {
    return (
      user.roleType === "master_accountant" || 
      user.roleType === "agency_accountant"
    );
  }
  
  // Fallback to role and subRole check
  if (user.role === "master" && user.subRole === "accountant") return true;
  if (user.role === "agency" && user.subRole === "accountant") return true;
  
  return false;
};

/**
 * Check if the user has Admin or Accountant role
 * @param user The user object from Redux store
 * @returns boolean indicating if the user has Admin or Accountant role
 */
export const isAdminOrAccountant = (user: StoredUser): boolean => {
  return isAdmin(user) || isAccountant(user);
};
