import <PERSON><PERSON> from "joi";
import { parsePhoneNumberFromString } from "libphonenumber-js";

// Name pattern for validation - allows letters, apostrophes, hyphens, and spaces
const NAME_PATTERN = /^[A-Za-z\s'-]+$/;

// Passport number pattern - typically alphanumeric with possible special characters
const PASSPORT_PATTERN = /^[A-Z0-9]{6,15}$/;

// Add custom validation options
export const validationOptions = {
  abortEarly: false, // Return all errors, not just the first one
  allowUnknown: true, // Allow unknown keys that will be ignored
  stripUnknown: true, // Remove unknown elements from objects
};

/**
 * Traveler validation schema for server-side validation
 *
 * Validation rules:
 * - First name and last name: 2-30 characters, letters, spaces, apostrophes, and hyphens only
 * - Email: Valid email format with TLD validation disabled
 * - Phone number: Valid international format using libphonenumber-js
 * - Date of birth: Valid date in the past
 * - Nationality: Required string, automatically converted to uppercase
 * - Passport number: 6-15 characters, uppercase letters and numbers only
 * - Passport issuing country: Required string, automatically converted to uppercase
 * - Passport expiry: Valid date in the future
 */
export const travelerValidation = Joi.object({
  title: Joi.string().valid("Mr", "Mrs").required().messages({
    "any.only": "Title must be either 'Mr' or 'Mrs'",
    "any.required": "Title is required",
  }),
  gender: Joi.string().valid("male", "female").required().messages({
    "any.only": "Gender must be either 'male' or 'female'",
    "any.required": "Gender is required",
  }),
  firstName: Joi.string()
    .min(3)
    .max(30)
    .pattern(NAME_PATTERN)
    .required()
    .messages({
      "string.empty": "First name cannot be empty",
      "string.min": "First name must be at least 3 characters long",
      "string.max": "First name must be at most 30 characters long",
      "string.pattern.base":
        "First name can only contain letters, apostrophes, hyphens, and spaces",
      "any.required": "First name is required",
    }),
  lastName: Joi.string()
    .min(3)
    .max(30)
    .pattern(NAME_PATTERN)
    .required()
    .messages({
      "string.empty": "Last name cannot be empty",
      "string.min": "Last name must be at least 3 characters long",
      "string.max": "Last name must be at most 30 characters long",
      "string.pattern.base":
        "Last name can only contain letters, apostrophes, hyphens, and spaces",
      "any.required": "Last name is required",
    }),
  contactEmail: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "string.empty": "Email cannot be empty",
      "string.email": "Please enter a valid email address",
      "any.required": "Email is required",
    }),
  contactPhone: Joi.string()
    .custom((value, helpers) => {
      try {
        // Attempt to parse the phone number
        const phoneNumber = parsePhoneNumberFromString(value);

        // Check if phoneNumber exists and is valid
        if (!phoneNumber || !phoneNumber.isValid()) {
          return helpers.message({ custom: "Invalid phone number" });
        }

        // Return the parsed phone number in E.164 format
        return phoneNumber.format("E.164");
      } catch (err) {
        // Handle parsing errors
        return helpers.error("any.custom");
      }
    })
    .required()
    .messages({
      "string.empty": "Phone number cannot be empty",
      "any.required": "Phone number is required",
      "any.custom": "Invalid phone number",
    }),
  dateOfBirth: Joi.date()
    .required()
    .max("now") // Must be in the past
    .messages({
      "date.base": "Date of birth must be a valid date",
      "date.max": "Date of birth cannot be in the future",
      "any.required": "Date of birth is required",
    }),
  nationality: Joi.string().required().uppercase().trim().messages({
    "string.empty": "Nationality cannot be empty",
    "any.required": "Nationality is required",
  }),
  documentType: Joi.string().valid("passport", "id_card").required().messages({
    "any.only": "Document type must be either 'passport' or 'id_card'",
    "any.required": "Document type is required",
  }),
  documentNumber: Joi.string()
    .required()
    .min(6)
    .max(15)
    .pattern(PASSPORT_PATTERN)
    .uppercase()
    .trim()
    .messages({
      "string.empty": "Document number cannot be empty",
      "string.min": "Document number must be at least 6 characters long",
      "string.max": "Document number must be at most 15 characters long",
      "string.pattern.base":
        "Document number can only contain uppercase letters and numbers",
      "any.required": "Document number is required",
    }),
  issuingCountry: Joi.string().required().uppercase().trim().messages({
    "string.empty": "Issuing country cannot be empty",
    "any.required": "Issuing country is required",
  }),
  expirationDate: Joi.date()
    .required()
    .min("now") // Must be in the future
    .messages({
      "date.base": "Expiration date must be a valid date",
      "date.min": "Expiration date must be in the future",
      "any.required": "Expiration date is required",
    }),
}).options({ abortEarly: false });

// Helper function to validate traveler data
export const validateTravelerData = (travelerData: any) => {
  return travelerValidation.validate(travelerData, validationOptions);
};

// Helper function to validate a single field
export const validateTravelerField = (field: string, value: any) => {
  // Create a schema for just this field
  const fieldSchema = Joi.object({
    [field]: travelerValidation.extract(field),
  });

  return fieldSchema.validate({ [field]: value }, validationOptions);
};
