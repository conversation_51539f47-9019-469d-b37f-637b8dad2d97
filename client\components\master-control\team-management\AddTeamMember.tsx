import React, { useState, useEffect } from "react";
import ModalBlank from "../../common/modal-blank";
import {
  X,
  UserCircle,
  Building,
  Trash2,
  AlertOctagon,
  Edit2,
  Ban,
  CheckCircle,
} from "lucide-react";
import RoleDropdown from "./RoleDropdown";
import DepartmentDropdown from "./DepartmentDropdown";
import {
  createTeamMember,
  updateTeamMember,
  removeTeamMember,
} from "@/lib/data/teamData";
import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { TeamMember } from "@/utils/definitions/teamDefinitions";
import PasswordField from "@/components/common/PasswordField";
import useDarkMode from "@/components/hooks/useDarkMode";
import { PasswordStrength } from "@/utils/definitions/agentsDefinitions";
import { checkPasswordStrength } from "@/utils/passwordStrength";
const generateInputClassName = (hasError: boolean) => {
  const baseClasses =
    "pl-10 pr-4 md:py-2.5 outline-none transition-all duration-300 placeholder:text-xs md:placeholder:text-sm w-full bg-gray-300 dark:bg-gray-800 rounded-lg py-2 text-gray-900 dark:text-white focus:outline-none border-0 focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed";
  const errorClasses = hasError ? "border-red-500" : "";
  return `${baseClasses} ${errorClasses}`;
};

// Enums matching Prisma schema
enum TeamMemberRole {
  ADMIN = "admin",
  MODERATOR = "moderator",
  ACCOUNTANT = "accountant",
}

// Import Department enum from definitions instead of redefining it
import { Department } from "@/utils/definitions/agentsDefinitions";

interface AddTeamMemberProps {
  dangerModalOpen: boolean;
  setDangerModalOpen: (open: boolean) => void;
  teamId: string;
  onSuccess?: (updatedUser: TeamMember) => void;
  selectedUser?: any;
}

export default function AddTeamMember({
  dangerModalOpen,
  setDangerModalOpen,
  teamId,
  onSuccess,
  selectedUser,
}: AddTeamMemberProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSuspendConfirm, setShowSuspendConfirm] = useState(false);

  const darkMode = useDarkMode();
  const isDarkMode = typeof darkMode === "boolean" ? darkMode : false;

  const getInitialPasswordStrength = (): PasswordStrength => ({
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  });
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>(
    getInitialPasswordStrength()
  );
  const [confirmPasswordStrength, setConfirmPasswordStrength] = useState<
    PasswordStrength | undefined
  >(getInitialPasswordStrength());
  const [debouncedPassword, setDebouncedPassword] = useState<string>("");
  const [debouncedConfirmPassword, setDebouncedConfirmPassword] =
    useState<string>("");

  // Add useEffect to reset error and password strengths when modal opens or selectedUser changes
  useEffect(() => {
    setError(""); // Reset error message
    setSuccessMessage(""); // Reset success message
    setPasswordStrength(getInitialPasswordStrength()); // Reset password strength
    setConfirmPasswordStrength(getInitialPasswordStrength()); // Reset confirm password strength
    setDebouncedPassword(""); // Reset debounced password
    setDebouncedConfirmPassword(""); // Reset debounced confirm password
  }, [dangerModalOpen, selectedUser]);

  // Debounce password strength check
  useEffect(() => {
    const handler = setTimeout(() => {
      setPasswordStrength(checkPasswordStrength(debouncedPassword, isDarkMode));
      setConfirmPasswordStrength(
        checkPasswordStrength(debouncedConfirmPassword, isDarkMode)
      );
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [debouncedPassword, debouncedConfirmPassword, isDarkMode]);

  const user: any = useAppSelector(selectUser);
  const dispatch = useAppDispatch();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    subRole: "",
    department: "",
  });

  const [formErrors, setFormErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    subRole: "",
    department: "",
  });

  const [currentUserRole, setCurrentUserRole] = useState<string | null>(null);
  const [currentUserSubRole, setCurrentUserSubRole] = useState<string | null>(
    null
  );

  useEffect(() => {
    // Fetch current user's role when component mounts
    const fetchCurrentUserRole = async () => {
      try {
        setCurrentUserRole(user.role);
        setCurrentUserSubRole(user.subRole);
      } catch (error) {
        console.error("Error fetching user role:", error);
      }
    };
    fetchCurrentUserRole();
  }, []);

  const getAvailableRoles = () => {
    // Check if current user is master with admin subRole
    if (
      currentUserRole === "master" &&
      (currentUserSubRole === "admin" || currentUserSubRole === undefined)
    ) {
      // Master admin can create any role including admin
      return Object.values(TeamMemberRole);
    } else if (
      currentUserRole === "master" &&
      (currentUserSubRole === "moderator" ||
        currentUserSubRole === "accountant")
    ) {
      // Master moderator and accountant cannot create admin roles
      return Object.values(TeamMemberRole).filter(
        (role) => role !== TeamMemberRole.ADMIN
      );
    }
    // Default case - only show non-admin roles
    return Object.values(TeamMemberRole).filter(
      (role) => role !== TeamMemberRole.ADMIN
    );
  };

  const validateField = (name: string, value: string) => {
    let error = "";
    switch (name) {
      case "firstName":
        if (!value) error = "First name is required";
        break;
      case "lastName":
        if (!value) error = "Last name is required";
        break;
      case "email":
        if (!value) error = "Please enter your email";
        break;
      case "password":
        if (!selectedUser && !value) error = "Please enter your password";
        break;
      case "confirmPassword":
        if (!selectedUser && !value) error = "Please confirm your password";
        else if (value !== formData.password)
          error = "The passwords you entered don’t match. Please try again";
        break;
    }
    return error;
  };

  // Update form data when selectedUser changes
  useEffect(() => {
    if (selectedUser) {
      setFormData({
        firstName: selectedUser.firstName || "",
        lastName: selectedUser.lastName || "",
        email: selectedUser.email || "",
        password: "",
        confirmPassword: "",
        subRole: selectedUser.subRole || "",
        department: selectedUser.department || "",
      });
      setIsEditMode(false); // Reset edit mode when user changes
    } else {
      // Reset form when no user is selected
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        confirmPassword: "",
        subRole: "",
        department: "",
      });
    }
  }, [selectedUser]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Validate the field
    const error = validateField(name, value);
    setFormErrors((prev) => ({
      ...prev,
      [name]: error,
    }));

    // Clear main error when user starts typing
    setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user has permission to create/edit admin role
    if (
      formData.subRole === TeamMemberRole.ADMIN ||
      user.roleType === "master_owner"
    ) {
      if (
        user.roleType !== "master_owner" &&
        (currentUserSubRole === undefined || currentUserSubRole !== "admin")
      ) {
        setError("You are not authorized to modify this account");
        return;
      }
    }

    // Only validate passwords if this is a new user or passwords are being changed
    if (!selectedUser || formData.password || formData.confirmPassword) {
      if (formData.password !== formData.confirmPassword) {
        setError("The passwords you entered don’t match. Please try again");
        return;
      }
    }

    setLoading(true);

    try {
      let response;
      if (selectedUser && isEditMode) {
        // Update existing team member
        response = await updateTeamMember(selectedUser.id, {
          ...formData,
          id: selectedUser.id,
          refId: selectedUser.refId,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          role: selectedUser.role,
          subRole: formData.subRole,
          department: formData.department,
          teamId,
          // Only include password if it was changed
          ...(formData.password ? { password: formData.password } : {}),
          ...(formData.confirmPassword
            ? { confirmPassword: formData.confirmPassword }
            : {}),
        });
        if (response?.data) {
          const updatedUser = response.data;

          if (onSuccess) {
            onSuccess(updatedUser);
          }
        }
      } else {
        // Create new team member
        response = await createTeamMember({
          ...formData,
          teamId,
          role: "master", // Explicitly set role as master
        });
      }

      if (response) {
        setSuccessMessage(
          response?.data?.message ||
            (selectedUser
              ? "Team member updated successfully!"
              : "Team member added successfully!")
        );
        dispatch(
          setMsg({
            message:
              response?.message ||
              response?.data?.message ||
              "Team member added successfully",
            success: true,
          })
        );
        // Reset form data after successful submission
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          confirmPassword: "",
          subRole: "",
          department: "",
        });

        // Clear any errors
        setFormErrors({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          confirmPassword: "",
          subRole: "",
          department: "",
        });

        // Call onSuccess callback if provided
        if (onSuccess) {
          const teamMemberData = {
            ...response?.teamMember, // Use optional chaining
          };
          onSuccess(teamMemberData);
        }

        // Close the modal
        setDangerModalOpen(false);
      }
    } catch (err: any) {
      const errorMessages =
        err.errors?.[0]?.message ||
        err.message ||
        "An error occurred while processing your request";
      console.error("Error creating team member:", err);
      setError(errorMessages);
      dispatch(
        setMsg({
          message: errorMessages,
          success: false,
        })
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleSuspendClick = () => {
    setShowSuspendConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);
      const response = await removeTeamMember(selectedUser.id);
      if (response) {
        setSuccessMessage("Team member deleted successfully!");
        dispatch(
          setMsg({
            message: "Team member deleted successfully",
            success: true,
          })
        );
        // Create a new object with the deleted status
        const deletedUser = {
          ...selectedUser,
          accountStatus: "rejected",
          status: "rejected",
          deleted: true, // Add this flag
        };

        if (onSuccess) {
          onSuccess(deletedUser);
        }

        setShowDeleteConfirm(false);
        setDangerModalOpen(false);
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message ||
        "You are not authorized to delete this account";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleSuspendConfirm = async (user: TeamMember) => {
    const newStatus = user.status === "active" ? "inactive" : "active";
    try {
      setLoading(true);
      const response = await updateTeamMember(selectedUser.id, {
        ...selectedUser,
        status: newStatus,
      });

      if (response) {
        // Update the selected user's status immediately
        if (selectedUser) {
          selectedUser.status = newStatus;
        }

        setSuccessMessage(
          `Team member ${
            newStatus === "active" ? "activated" : "deactivated"
          } successfully!`
        );
        dispatch(
          setMsg({
            message: `Team member ${
              newStatus === "active" ? "activated" : "deactivated"
            } successfully!`,
            success: true,
          })
        );

        // Call onSuccess to trigger parent component refresh
        if (onSuccess) {
          onSuccess(selectedUser);
        }

        setShowSuspendConfirm(false);
        setDangerModalOpen(false);
      }
    } catch (error: any) {
      const errorMessage =
        error?.message ||
        error.response?.data?.message ||
        `Failed to ${
          newStatus === "active" ? "activate" : "deactivate"
        } team member`;
      setError(errorMessage);
      dispatch(
        setMsg({
          message: errorMessage,
          success: false,
        })
      );
    } finally {
      setLoading(false);
    }
  };

  // Add useEffect to reset error when modal opens or selectedUser changes
  useEffect(() => {
    setError(""); // Reset error message
    setSuccessMessage(""); // Reset success message if you're using it
  }, [dangerModalOpen, selectedUser]);

  return (
    <ModalBlank isOpen={dangerModalOpen} setIsOpen={setDangerModalOpen}>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto custom-scrollbar">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg max-w-md w-full">
          <form onSubmit={handleSubmit} className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {selectedUser
                  ? isEditMode
                    ? "Edit Master User"
                    : "View Master User"
                  : "Add Master User"}
              </h2>
              <button
                type="button"
                onClick={() => setDangerModalOpen(false)}
                className="bg-red-500 hover:bg-red-600 text-white rounded-lg w-8 h-8 flex items-center justify-center transition-colors duration-200"
              >
                <X size={20} />
              </button>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg flex items-center">
                {error}
              </div>
            )}

            <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
              <div className="flex items-center mb-2">
                <UserCircle size={20} className="text-green-500 mr-2" />
                <span className="text-gray-900 dark:text-white font-semibold">
                  Master User Information
                </span>
              </div>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full bg-gray-300 dark:bg-gray-800 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none border-0 focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      placeholder="Enter first name"
                      disabled={selectedUser && !isEditMode}
                      required
                    />
                    {formErrors.firstName && (
                      <p className="text-red-400 text-sm mt-1 flex items-center">
                        {formErrors.firstName}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                      Last Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full bg-gray-300 dark:bg-gray-800 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none border-0 focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      placeholder="Enter last name"
                      disabled={selectedUser && !isEditMode}
                      required
                    />
                    {formErrors.lastName && (
                      <p className="text-red-400 text-sm mt-1 flex items-center">
                        {formErrors.lastName}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full bg-gray-300 dark:bg-gray-800 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none border-0 focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Enter email address"
                    disabled={selectedUser && !isEditMode}
                    required
                  />
                  {formErrors.email && (
                    <p className="text-red-400 text-sm mt-1 flex items-center">
                      {formErrors.email}
                    </p>
                  )}
                </div>

                {(!selectedUser || isEditMode) && (
                  <>
                    {/* Password input */}
                    <PasswordField
                      name="password"
                      label="Password"
                      placeholder="Enter password"
                      value={formData.password}
                      showPassword={showPassword}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const newPassword = e.target.value;
                        setFormData({ ...formData, password: newPassword });
                        // Immediately update password strength
                        setDebouncedPassword(newPassword);
                      }}
                      onToggleVisibility={() => setShowPassword(!showPassword)}
                      strength={passwordStrength}
                      required={!selectedUser}
                      tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
                      formErrors={formErrors?.password}
                      labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
                      inputClassName={generateInputClassName(
                        !!formErrors?.password
                      )}
                      strengthClassName="bg-gray-200 dark:bg-gray-700"
                    />

                    {/* Confirm Password input */}
                    <PasswordField
                      name="confirmPassword"
                      label="Confirm Password"
                      placeholder="Enter confirm password"
                      value={formData.confirmPassword}
                      showPassword={showConfirmPassword}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const newConfirmPassword = e.target.value;
                        setFormData({
                          ...formData,
                          confirmPassword: newConfirmPassword,
                        });
                        // Immediately update password strength
                        setDebouncedConfirmPassword(newConfirmPassword);
                      }}
                      onToggleVisibility={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      strength={confirmPasswordStrength}
                      required={!selectedUser}
                      tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
                      formErrors={formErrors?.confirmPassword}
                      labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
                      inputClassName={generateInputClassName(
                        !!formErrors?.confirmPassword
                      )}
                      strengthClassName="bg-gray-200 dark:bg-gray-700"
                    />
                  </>
                )}
              </div>
            </div>

            <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
              <div className="flex items-center mb-2">
                <Building size={20} className="text-blue-500 mr-2" />
                <span className="text-gray-900 dark:text-white font-semibold">
                  Position Information
                </span>
              </div>
              <div className="space-y-4">
                <div>
                  <RoleDropdown
                    name="subRole"
                    value={formData.subRole}
                    onChange={handleInputChange}
                    disabled={selectedUser && !isEditMode}
                    options={getAvailableRoles()}
                    required
                    formErrors={formErrors.subRole}
                  />
                </div>

                <div>
                  <DepartmentDropdown
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    disabled={selectedUser && !isEditMode}
                    options={Object.values(Department)}
                    required
                    formErrors={formErrors.department}
                  />
                </div>
              </div>
            </div>

            {selectedUser && !isEditMode ? (
              <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
                <div className="flex items-center mb-2">
                  <AlertOctagon size={20} className="text-yellow-500 mr-2" />
                  <span className="text-gray-900 dark:text-white font-semibold">
                    Actions
                  </span>
                </div>
                <div className="grid gap-4">
                  {/* Row 1: View and Edit (md:grid-cols-2) */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <button
                      type="button"
                      onClick={() => setIsEditMode(true)}
                      className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg px-4 py-2 transition-colors duration-200"
                    >
                      <Edit2 size={16} />
                      <span>Edit</span>
                    </button>

                    <button
                      type="button"
                      onClick={handleDeleteClick}
                      className="flex items-center justify-center space-x-2 bg-red-500 hover:bg-red-600 text-white rounded-lg px-4 py-2 transition-colors duration-200"
                    >
                      <Trash2 size={16} />
                      <span>Delete</span>
                    </button>
                  </div>
                  <button
                    type="button"
                    onClick={handleSuspendClick}
                    className={`flex items-center justify-center space-x-1 ${
                      selectedUser.status === "active"
                        ? "bg-orange-500 hover:bg-orange-600"
                        : "bg-green-500 hover:bg-green-600"
                    } text-white rounded-lg px-2 py-2 transition-colors duration-200`}
                  >
                    {selectedUser.status === "active" ? (
                      <Ban size={16} />
                    ) : (
                      <CheckCircle size={16} />
                    )}
                    <span>
                      {selectedUser.status === "active"
                        ? "Disable Login"
                        : "Enable Login"}
                    </span>
                  </button>
                </div>
              </div>
            ) : null}

            <div className="flex justify-end space-x-4">
              {selectedUser && isEditMode ? (
                <button
                  type="button"
                  onClick={() => setIsEditMode(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"
                >
                  Cancel
                </button>
              ) : (
                <button
                  type="button"
                  onClick={() => setDangerModalOpen(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200"
                >
                  {!selectedUser || isEditMode ? "Cancel" : "Close"}
                </button>
              )}
              {(!selectedUser || isEditMode) && (
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading
                    ? "Processing..."
                    : selectedUser
                    ? "Save Changes"
                    : "Add Master User"}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]">
          <div className="dark:bg-gray-800 bg-gray-100 rounded-2xl shadow-lg max-w-sm w-full p-6 text-center">
            <div className="mb-4">
              <div className="w-12 h-12 rounded-full bg-red-500 mx-auto flex items-center justify-center">
                <UserCircle size={32} className="text-white" />
              </div>
            </div>
            <h2 className="text-xl font-bold dark:text-white text-gray-800 mb-2">
              Delete Master User
            </h2>
            <p className="dark:text-gray-300 text-gray-700 mb-6">
              Are you sure you want to delete this master user? This action
              cannot be undone.
            </p>
            <div className="flex justify-center space-x-4">
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(false)}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 focus:outline-none transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <X size={16} />
                <span>Cancel</span>
              </button>
              <button
                type="button"
                onClick={handleDeleteConfirm}
                className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <Trash2 size={16} />
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Suspend Confirmation Modal */}
      {showSuspendConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]">
          <div className="dark:bg-gray-800 bg-gray-100 rounded-2xl shadow-lg max-w-sm w-full p-6 text-center">
            <div className="mb-4">
              <div
                className={`w-12 h-12 rounded-full ${
                  selectedUser?.status === "active"
                    ? "bg-orange-500"
                    : "bg-green-500"
                } mx-auto flex items-center justify-center`}
              >
                {selectedUser?.status === "active" ? (
                  <Ban size={32} className="text-white" />
                ) : (
                  <CheckCircle size={32} className="text-white" />
                )}
              </div>
            </div>
            <h2 className="text-xl font-bold dark:text-white text-gray-800 mb-2">
              {selectedUser?.status === "active" ? "Disable" : "Enable"} Master
              User
            </h2>
            <p className="dark:text-gray-300 text-gray-700 mb-6">
              Are you sure you want to{" "}
              {selectedUser?.status === "active" ? "disable" : "enable"} this
              master user?{" "}
              {selectedUser?.status === "active"
                ? " They will not be able to access their account."
                : " This will restore their account access."}
            </p>
            <div className="flex justify-center space-x-4">
              <button
                type="button"
                onClick={() => setShowSuspendConfirm(false)}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 focus:outline-none transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <X size={16} />
                <span>Cancel</span>
              </button>
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  handleSuspendConfirm(selectedUser);
                }}
                className={`px-6 py-2 text-white rounded-lg ${
                  selectedUser?.status === "active"
                    ? "bg-orange-500 hover:bg-orange-600"
                    : "bg-green-500 hover:bg-green-600"
                } focus:outline-none transition-colors duration-200 flex items-center justify-center space-x-2`}
              >
                {selectedUser?.status === "active" ? (
                  <Ban size={16} />
                ) : (
                  <CheckCircle size={16} />
                )}
                <span>
                  {selectedUser?.status === "active" ? "Disable" : "Enable"}
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
    </ModalBlank>
  );
}
