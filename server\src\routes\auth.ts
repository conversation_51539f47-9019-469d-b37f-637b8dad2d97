import { Router } from "express";
const router = Router();
import {
  signup,
  login,
  logout,
  emailVerify,
  sendEmailVerification,
  sendResetPassword,
  resetPassword,
} from "../controllers/authController";
import { loginRateLimit,apiLimiter } from "../middlewares/rateLimit";
import userAuth from "../middlewares/userAuth";

/**
 * @openapi
 * /auth/signup:
 *   post:
 *     tags:
 *       - Auth
 *     summary: Register a new user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Registration successful
 *       400:
 *         description: Validation error
 */
router.post("/signup", apiLimiter, signup);

/**
 * @openapi
 * /auth/login:
 *   post:
 *     tags:
 *       - Auth
 *     summary: Log in a user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post("/login", apiLimiter, login);
router.get("/logout", userAuth, logout);

/**
 * @openapi
 * /auth/email/verify/{token}:
 *   get:
 *     tags:
 *       - Auth
 *     summary: Verify user email address
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Email verified
 *       400:
 *         description: Invalid or expired token
 */
router.get("/email/verify/:token", emailVerify);

/**
 * @openapi
 * /auth/email/sendVerification:
 *   get:
 *     tags:
 *       - Auth
 *     summary: Send email verification to user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Verification email sent
 *       401:
 *         description: Unauthorized
 */
router.get("/email/sendVerification", userAuth, sendEmailVerification);

/**
 * @openapi
 * /auth/password/reset:
 *   post:
 *     tags:
 *       - Auth
 *     summary: Send password reset email
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Reset email sent
 *       400:
 *         description: Validation error
 */
router.post("/password/reset", apiLimiter, sendResetPassword);

/**
 * @openapi
 * /auth/password/reset/{token}:
 *   put:
 *     tags:
 *       - Auth
 *     summary: Reset password using token
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password reset successful
 *       400:
 *         description: Invalid or expired token
 */
router.put("/password/reset/:token", apiLimiter, resetPassword);

export default router;
