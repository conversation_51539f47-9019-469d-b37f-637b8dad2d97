import { prisma } from "../../prisma";
import { getIO } from "../../socket";
import { updateAgencyTicketsToHold } from "./handleAgencyTickets";

/**
 * Updates the access status of all agency agents when an agency owner's account is suspended
 * @param agencyId - The ID of the agency owner whose account is suspended
 */
export const handleAgencySuspension = async (agencyId: string) => {
  try {
    // Update all agency agents' access status
    await prisma.agencyAgent.updateMany({
      where: {
        agencyId: agencyId,
      },
      data: {
        status: "inactive",
        accountStatus: "suspended",
      },
    });

    // Update tickets to HOLD
    await updateAgencyTicketsToHold(agencyId, "suspended");

    // Send session expiration event to all agency agents
    const agents = await prisma.agencyAgent.findMany({
      where: { agencyId: agencyId },
    });
    agents.forEach((agent: any) => {
      const io = getIO();
      io.to(agent.id).emit("sessionExpiration", {
        message: "Your agency account has been suspended.",
      });
    });
  } catch (error) {
    console.error("Error in handleAgencySuspension:", error);
    throw error;
  }
};
