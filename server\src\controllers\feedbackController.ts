import { AuthRequest } from "../utils/definitions";
import { Response } from "express";
import getAffiliateAccess from "../utils/access-check/getAffiliateAccess";
import { emailSupportTicket } from "../utils/email/emailSupportTicket";

interface FeedbackData {
  subject: string;
  description: string;
  category: string;
  customFields?: Record<string, any>;
  severity?: string;
}

/**
 * Handles the submission of feedback from an authenticated user.
 * Sends the feedback data to a Google Apps Script web app for storage.
 *
 * @param {AuthRequest} req - The request object containing the user's authentication information and the feedback data.
 * @param {Response} res - The response object used to send the feedback submission result.
 * @returns {Promise<Response>} - A promise that resolves to the response object containing the feedback submission result.
 */
export const submitFeedback = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  // Extract the feedback data from the request body
  const { subject, description, category, customFields, severity } =
    req.body as FeedbackData;
  const files = req.files as Express.Multer.File[];
  try {
    // Ensure user is authenticated (should be handled by userAuth middleware)
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    // Validate required fields
    if (!subject || !description || !category) {
      return res.status(400).json({
        success: false,
        message: "Subject, description, and category are required fields",
      });
    }

    // Prepare the data to be sent to the Google Apps Script web app
    const data = {
      user: `${req.user.firstName} ${req.user.lastName}`,
      email: req.user.email,
      subject,
      description,
      category,
      severity,
      customFields: customFields || {},
      files: files || [],
      date: new Date(),
    };

    try {
      // Send the data to email
      await emailSupportTicket(data);

      // Respond with the status and result from the Google Apps Script web app
      return res.status(200).json({
        success: true,
        message: "Feedback submitted successfully",
      });
    } catch (emailError) {
      // Log any error that occurs and return an error response
      console.error("Error sending feedback email:", emailError);
      return res.status(500).json({
        success: false,
        error: "Internal server error",
        message: "Failed to submit feedback. Please try again.",
      });
    }
  } catch (error) {
    // Log any error that occurs and return an error response
    console.error("Error in submitFeedback:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};
