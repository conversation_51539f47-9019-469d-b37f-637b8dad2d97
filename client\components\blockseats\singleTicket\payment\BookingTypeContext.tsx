"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";

// Define the type for bookingType (adjust as needed)
type BookingType = "INTERNAL" | "THIRD_PARTY" | null;

interface BookingTypeContextProps {
  bookingType: BookingType;
  setBookingType: (type: BookingType) => void;
}

const BookingTypeContext = createContext<BookingTypeContextProps | undefined>(undefined);

export const useBookingType = () => {
  const context = useContext(BookingTypeContext);
  if (!context) {
    throw new Error("useBookingType must be used within a BookingTypeProvider");
  }
  return context;
};

export const BookingTypeProvider = ({ children }: { children: ReactNode }) => {
  const [bookingType, setBookingType] = useState<BookingType>(null);
  return (
    <BookingTypeContext.Provider value={{ bookingType, setBookingType }}>
      {children}
    </BookingTypeContext.Provider>
  );
};
