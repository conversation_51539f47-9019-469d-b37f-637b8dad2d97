import { Router } from "express";
const router = Router();
import {
  // deleteUserInfo,
  softDeleteSingleUser,
  getUserInfo,
  updateUserEmail,
  updateUserInfo,
  updateUserPassword,
  verifyUpdatedEmail,
  getOwnerCreditBalance,
} from "../controllers/userController";
import {
  deleteUsersTicket,
  getAllUsersTickets,
  getSingleUsersTicket,
  updateTicketStatus,
  updateValidTicket,
  withdrawUpdateReqValidTicket,
} from "../controllers/userTicketController";
import userAuth from "../middlewares/userAuth";
import { enterpriseApiLimiter } from "../middlewares/rateLimit";

// auth the user and login
router.use(userAuth);
router.use(enterpriseApiLimiter);

// get and update profile
/**
 * @openapi
 * /user/profile:
 *   get:
 *     tags:
 *       - User
 *     summary: Get user profile
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile data
 *   put:
 *     tags:
 *       - User
 *     summary: Update user profile
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Profile updated
 */
router.get("/profile", getUserInfo);
router.put("/profile", updateUserInfo);
// router.post("/profile/delete", deleteUserInfo);
/**
 * @openapi
 * /user/email:
 *   post:
 *     tags:
 *       - User
 *     summary: Update user email
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email update requested
 *
 * /user/email/verify/{token}:
 *   get:
 *     tags:
 *       - User
 *     summary: Verify updated email
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Email verified
 */
router.post("/email", updateUserEmail);
router.get("/email/verify/:token", verifyUpdatedEmail);
/**
 * @openapi
 * /user/password:
 *   put:
 *     tags:
 *       - User
 *     summary: Update user password
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               oldPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password updated
 */
router.put("/password", updateUserPassword);

// Account deactivation
/**
 * @openapi
 * /user/profile/deactivate:
 *   post:
 *     tags:
 *       - User
 *     summary: Deactivate user account
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Account deactivated
 */
router.post("/profile/deactivate", softDeleteSingleUser);

// // admin users
// router.delete("/profile/delete", softDeleteSingleUser);

// user tickets
/**
 * @openapi
 * /user/tickets:
 *   post:
 *     tags:
 *       - User
 *     summary: Get all user tickets
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of tickets
 *
 * /user/tickets/{ticketId}:
 *   get:
 *     tags:
 *       - User
 *     summary: Get single user ticket
 *     parameters:
 *       - in: path
 *         name: ticketId
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ticket details
 */
router.post("/tickets", getAllUsersTickets);
router.get("/tickets/:ticketId", getSingleUsersTicket);
/**
 * @openapi
 * /user/tickets/{refId}/status:
 *   put:
 *     tags:
 *       - User
 *     summary: Update ticket status
 *     parameters:
 *       - in: path
 *         name: refId
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Ticket status updated
 *
 * /user/tickets/{refId}/update_request:
 *   put:
 *     tags:
 *       - User
 *     summary: Update valid ticket
 *     parameters:
 *       - in: path
 *         name: refId
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Ticket update request submitted
 *
 * /user/tickets/{refId}/update_request/withdraw:
 *   get:
 *     tags:
 *       - User
 *     summary: Withdraw ticket update request
 *     parameters:
 *       - in: path
 *         name: refId
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ticket update request withdrawn
 *
 * /user/tickets/{ticketId}:
 *   delete:
 *     tags:
 *       - User
 *     summary: Delete user ticket
 *     parameters:
 *       - in: path
 *         name: ticketId
 *         required: true
 *         schema:
 *           type: string
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ticket deleted
 */
router.put("/tickets/:refId/status", updateTicketStatus);
router.put("/tickets/:refId/update_request", updateValidTicket);
router.get(
  "/tickets/:refId/update_request/withdraw",
  withdrawUpdateReqValidTicket
);
router.delete("/tickets/:ticketId", deleteUsersTicket);
router.get("/profile/balance", getOwnerCreditBalance);

export default router;
