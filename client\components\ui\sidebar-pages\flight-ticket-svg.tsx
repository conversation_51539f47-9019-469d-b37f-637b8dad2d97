import React from "react";

const FlightTicketSvg = ({ segments }: { segments: any }) => {
  return (
    <>
      <svg
        fill="#000000"
        width="800px"
        height="800px"
        viewBox="0 0 122.88 122.88"
        version="1.1"
        id="Layer_1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        enableBackground="new 0 0 122.88 122.88"
        xmlSpace="preserve"
        className="shrink-0 h-6 w-6"
      >
        <g>
          <path
            className={`fill-current ${
              segments.includes("flight-tickets")
                ? "text-red-600 "
                : "text-slate-400"
            }`}
            d="M121.27,45.01l-76.26,76.26c-0.04,0.04-0.09,0.08-0.14,0.1c-0.5,0.48-1.08,0.84-1.69,1.09c-0.68,0.28-1.39,0.42-2.11,0.42 c-0.72,0-1.43-0.14-2.11-0.42c-0.63-0.26-1.23-0.65-1.75-1.16l-10.05-10.04c-0.34-0.34-0.53-0.78-0.58-1.25 c-0.04-0.44,0.06-0.9,0.3-1.3l0.03-0.05c0.59-0.89,1-1.86,1.24-2.87c0.25-1.02,0.32-2.08,0.22-3.13l0-0.01 c-0.11-1.03-0.39-2.05-0.84-3.01c-0.43-0.91-1.03-1.78-1.79-2.54c-0.76-0.76-1.62-1.36-2.54-1.79c-0.96-0.46-1.98-0.74-3.01-0.84 c-1.01-0.1-2.06-0.03-3.07,0.21c-0.99,0.24-1.94,0.64-2.81,1.2c-0.41,0.31-0.89,0.44-1.37,0.4c-0.48-0.03-0.96-0.24-1.32-0.6 L1.61,85.66c-0.53-0.53-0.92-1.14-1.19-1.78C0.14,83.21,0,82.49,0,81.77c0-0.72,0.14-1.44,0.42-2.11c0.26-0.64,0.65-1.23,1.16-1.75 L77.87,1.61c0.53-0.53,1.14-0.92,1.78-1.19C80.33,0.14,81.05,0,81.77,0c0.72,0,1.44,0.14,2.11,0.42c0.65,0.27,1.26,0.66,1.78,1.19 L95.75,11.7c0.32,0.32,0.52,0.75,0.57,1.2c0.05,0.43-0.03,0.89-0.26,1.29l-0.02,0.03c-0.54,0.87-0.92,1.81-1.14,2.79 c-0.22,1-0.27,2.04-0.15,3.05l0,0.01c0.12,1,0.41,1.98,0.87,2.91c0.43,0.88,1.02,1.72,1.77,2.47l0,0.01 c0.73,0.75,1.56,1.33,2.44,1.77c0.92,0.45,1.92,0.74,2.93,0.86c0.99,0.12,2,0.08,2.99-0.13c0.97-0.2,1.92-0.56,2.79-1.08 c0.41-0.26,0.89-0.38,1.37-0.34c0.46,0.04,0.91,0.23,1.26,0.59l10.09,10.09c0.53,0.53,0.92,1.14,1.19,1.78 c0.28,0.68,0.42,1.4,0.42,2.11c0,0.72-0.14,1.44-0.42,2.11c-0.26,0.63-0.65,1.23-1.16,1.75L121.27,45.01L121.27,45.01z M56.6,74.81 c-0.19,1.26-0.73,2.45-1.89,3.61c-0.09,0.09-0.22,0.15-0.36,0.15c-0.13,0-0.25-0.05-0.35-0.14l-2.41-2.37 c-0.07-0.07-0.12-0.17-0.14-0.27c-0.11-0.7-0.19-1.3-0.26-1.79l-0.07-0.55c-0.13-0.07-0.25-0.15-0.37-0.23 c-0.18-0.13-0.36-0.28-0.53-0.44l-0.03-0.03c-0.15-0.15-0.28-0.3-0.4-0.47l-0.02-0.03c-0.07-0.1-0.14-0.2-0.2-0.31l-0.65-0.07 c-0.45-0.04-0.97-0.1-1.51-0.16c-0.11-0.01-0.21-0.06-0.29-0.15l-2.6-2.64c-0.19-0.19-0.19-0.51,0-0.7c1.3-1.3,2.48-1.88,3.72-2.07 c1.14-0.17,2.29-0.01,3.62,0.25l4.92-4.92l-14.23-5.53c-0.08-0.02-0.15-0.06-0.21-0.12c-0.19-0.19-0.19-0.51,0-0.7 c1.09-1.09,1.77-1.94,2.28-2.58c1.54-1.94,1.75-2.21,5.57-1.33l2.29-2.29c0.19-0.19,0.51-0.19,0.7,0l2.3,2.3 c0.19,0.19,0.19,0.51,0,0.7l-0.42,0.42l1.6,0.37l2.59-2.59c0.19-0.19,0.51-0.19,0.7,0l2.3,2.3c0.19,0.19,0.19,0.51,0,0.7 l-0.72,0.72l2.34,0.54l5.66-5.66c0.53-0.53,1.28-0.72,2.06-0.61c0.73,0.1,1.49,0.48,2.11,1.09l0.01,0.01c0.62,0.62,1,1.39,1.1,2.13 c0.11,0.78-0.09,1.53-0.61,2.06l-5.66,5.66l0.54,2.31l0.71-0.71c0.19-0.19,0.51-0.19,0.7,0l2.3,2.3c0.19,0.19,0.19,0.51,0,0.7 l-2.58,2.58l0.37,1.6l0.41-0.41c0.19-0.19,0.51-0.19,0.7,0l2.3,2.3c0.19,0.19,0.19,0.51,0,0.7l-2.28,2.28 c0.86,3.83,0.5,4.1-1.37,5.51c-0.66,0.5-1.53,1.16-2.64,2.26c-0.05,0.05-0.11,0.08-0.17,0.11c-0.25,0.1-0.54-0.03-0.64-0.28 l-5.44-14.15l-4.96,4.96C56.66,72.45,56.77,73.65,56.6,74.81L56.6,74.81z M19.64,65.7l37.58,37.58l46.04-46.04L65.68,19.66 L19.64,65.7L19.64,65.7z M54.35,106.14L16.77,68.57L4.54,80.8c-0.13,0.13-0.23,0.28-0.29,0.45c-0.07,0.18-0.11,0.37-0.11,0.55 c0,0.2,0.03,0.38,0.1,0.55c0.07,0.16,0.17,0.32,0.3,0.45l8.92,8.92c1.06-0.52,2.18-0.89,3.31-1.11c1.27-0.24,2.56-0.3,3.84-0.17 c1.5,0.14,2.97,0.53,4.34,1.18c1.35,0.64,2.6,1.51,3.71,2.62c1.09,1.09,1.95,2.34,2.58,3.67c0.66,1.39,1.06,2.88,1.22,4.38 c0.13,1.28,0.08,2.58-0.17,3.84c-0.22,1.14-0.59,2.25-1.11,3.31l8.92,8.92c0.13,0.13,0.28,0.23,0.45,0.29 c0.18,0.07,0.37,0.11,0.55,0.11c0.19,0,0.38-0.04,0.55-0.11c0.16-0.07,0.32-0.17,0.45-0.3L54.35,106.14L54.35,106.14z M68.55,16.79 l37.58,37.58l12.25-12.25c0.05-0.05,0.1-0.08,0.16-0.1c0.07-0.1,0.13-0.2,0.17-0.31c0.07-0.18,0.11-0.37,0.11-0.55 c0-0.19-0.03-0.37-0.1-0.53l-0.01-0.02c-0.06-0.16-0.16-0.3-0.28-0.43l-9.03-9.03c-1.06,0.47-2.16,0.8-3.29,0.99 c-1.24,0.2-2.5,0.23-3.76,0.08c-1.47-0.17-2.89-0.58-4.23-1.23c-1.3-0.63-2.51-1.48-3.59-2.56c-1.08-1.08-1.93-2.29-2.56-3.59 c-0.65-1.35-1.07-2.78-1.24-4.24c-0.15-1.25-0.12-2.51,0.08-3.75c0.19-1.13,0.52-2.23,0.99-3.29l-9.01-9.01 c-0.12-0.12-0.27-0.22-0.43-0.29l-0.02-0.01c-0.18-0.07-0.37-0.11-0.55-0.11c-0.2,0-0.38,0.03-0.55,0.1 c-0.16,0.07-0.32,0.17-0.45,0.3L68.55,16.79L68.55,16.79z"
          />
        </g>
      </svg>
    </>
  );
};

export default FlightTicketSvg;
