import { useEffect, useRef, useState } from 'react';

// Global map to store dropdown states
const dropdownStates = new Map<string, boolean>();
const dropdownListeners = new Map<string, Set<() => void>>();

// Hook to manage dropdown state globally
export function useDropdownState(id: string) {
  const [, forceUpdate] = useState({});
  const idRef = useRef(id);
  
  // Get current state
  const isOpen = dropdownStates.get(id) || false;
  
  // Set state function
  const setIsOpen = (value: boolean | ((prev: boolean) => boolean)) => {
    const currentValue = dropdownStates.get(id) || false;
    const newValue = typeof value === 'function' ? value(currentValue) : value;
    
    if (currentValue !== newValue) {
      dropdownStates.set(id, newValue);
      
      // Notify all listeners
      const listeners = dropdownListeners.get(id);
      if (listeners) {
        listeners.forEach(listener => listener());
      }
    }
  };
  
  // Subscribe to changes
  useEffect(() => {
    const currentId = idRef.current;
    
    // Create listener set if it doesn't exist
    if (!dropdownListeners.has(currentId)) {
      dropdownListeners.set(currentId, new Set());
    }
    
    // Add this component's update function
    const listener = () => forceUpdate({});
    dropdownListeners.get(currentId)!.add(listener);
    
    // Cleanup
    return () => {
      const listeners = dropdownListeners.get(currentId);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          dropdownListeners.delete(currentId);
          dropdownStates.delete(currentId);
        }
      }
    };
  }, []);
  
  return { isOpen, setIsOpen };
}
