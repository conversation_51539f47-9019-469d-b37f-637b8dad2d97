import React, { useState } from "react";

import ReactDOM from "react-dom";
import { createRoot } from "react-dom/client";
import { useSelector } from "react-redux";
import {
  X,
  CreditCard,
  AlertCircle,
  Calendar,
  CheckCircle,
  ChevronDown,
  Check,
  Loader,
} from "lucide-react";
import { Menu, Transition } from "@headlessui/react";
import { completePayment, createInternalBooking } from "@/lib/data/bookingData";
import { CreateInternalBookingDto } from "@/utils/types/booking.types";
import {
  selectFullTicket,
  selectPassengerCounts,
  selectBookingResult,
  selectTravelerData,
} from "@/redux/features/BookingConfirmationSlice";
import {
  FlightClassesRes,
  FlightTicketRes,
} from "@/utils/definitions/blockSeatsDefinitions";
import { Booking } from "@/utils/types/booking.types";
import PopupConfirmation from "./PopupConfirmation";
import { IsolatedDropdown } from "@/components/common/IsolatedDropdown";
import { getFormatDateTable } from "@/utils/functions/functions";
import PopupMessage from "@/components/common/PopupMessage";

// Type definitions
export interface PaymentMethod {
  value: string;
  label: string;
}

export type PendingBookingData = {
  ticketId: string;
  userId: string;
  agencyAgentId: string;
  type: string;
  source: string;
  travelers: any[];
  seats: any[];
  ticket: FlightTicketRes;
  flightClasses: FlightClassesRes[];
  returnFlightClasses: FlightClassesRes[] | {};
  returnFlightClass: FlightClassesRes | {};
  flightClass: FlightClassesRes;
  price: number;
  returnPrice: number | undefined;
  passengerCounts: { adults: number; children: number; infants: number };
  bookingResult: Booking;
  departureTaxRate: number;
  returnTaxRate: number;
  totalBaseFare: number;
  outboundBaseFare: number;
  returnBaseFare: number;
  taxes: number;
  transactionFee: number;
  total: number;
};

export interface PaymentDialogProps {
  onShowConfirmAction?: () => void;
  initialVisibility?: boolean;
  defaultPaymentMethod?: string;
  paymentMethods?: PaymentMethod[];
  currencies?: string[];
  defaultCurrency?: string;
  bookingPrice?: string;
  systemCurrency?: string;
  pastMonthsAllowed?: number;
  futureMonthsAllowed?: number;
  remarksMaxLength?: number;
  onClose?: () => void;
  onSubmit?: (paymentData: PaymentData) => void;
  warningMessage?: string;
  submitButtonText?: string;
  cancelButtonText?: string;
  // pendingBookingData?: Partial<CreateInternalBookingDto>;
  pendingBookingData?: PendingBookingData;
}

export interface PaymentData {
  paymentMethod: string;
  referenceNumber: string;
  transactionDate: string;
  formattedDate: string;
  amount: string;
  currency: string;
  remarks: string;
  bookingPrice: string;
  systemCurrency: string;
}

const PaymentDialog: React.FC<PaymentDialogProps> = ({
  initialVisibility = true,
  pendingBookingData,

  defaultPaymentMethod = "cashDeposit",
  paymentMethods = [
    { value: "cashDeposit", label: "Cash Deposit" },
    { value: "cliq", label: "Cliq" },
    { value: "cheque", label: "Cheque" },
    { value: "bankTransfer", label: "Bank Transfer" },
    { value: "creditCard", label: "Credit Card" },
  ],
  currencies = ["JOD", "USD", "EUR", "GBP", "AED", "SAR"],
  defaultCurrency = "JOD",
  bookingPrice = "1060.00",
  systemCurrency = "JOD",
  pastMonthsAllowed = 3,
  futureMonthsAllowed = 3,
  remarksMaxLength = 200,
  onClose = () => {},
  onSubmit = () => {},
  warningMessage = "Please ensure all payment information is accurate and complete. Incorrect details may result in booking rejection or processing delays.",
  submitButtonText = "Submit",
  cancelButtonText = "Cancel",
}) => {
  // Booking price and currency from Redux
  const bookingResult = useSelector(selectBookingResult);
  const reduxCurrency = useSelector((state: any) => state.booking?.currency);
  const currencyToShow = reduxCurrency || systemCurrency;
  const [isVisible, setIsVisible] = useState(initialVisibility);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState(defaultPaymentMethod);
  const [remarks, setRemarks] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [currency, setCurrency] = useState<string>(defaultCurrency);
  const [referenceNumber, setReferenceNumber] = useState<string>("");

  // ... (rest of the code remains the same)
  // Validation and submission state
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [submitting, setSubmitting] = useState(false);
  const [showResultPopup, setShowResultPopup] = useState<
    null | "success" | "error"
  >(null);

  const today = new Date();

  const getDateLimit = (monthsOffset: number): string => {
    const date = new Date(today);
    date.setMonth(today.getMonth() + monthsOffset);
    return date.toISOString().split("T")[0];
  };

  const minDate = getDateLimit(-pastMonthsAllowed);
  const maxDate = getDateLimit(futureMonthsAllowed);
  const formattedToday = today.toISOString().split("T")[0];

  // Format date as DD/MM/YYYY for display
  const formatDateForDisplay = (dateString: string): string => {
    if (!dateString) return "";
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const [year, month, day] = dateString.split("-");
      return `${day}/${month}/${year}`;
    }
    return dateString;
  };

  const [transactionDate, setTransactionDate] =
    useState<string>(formattedToday);
  const [transactionDateInput, setTransactionDateInput] = useState<string>("");

  // Format amount with commas for display
  const formatAmount = (value: string): string => {
    // Remove all non-digit characters except decimal point
    const numericValue = value.replace(/[^\d.]/g, "");

    // Split into whole and decimal parts
    const parts = numericValue.split(".");
    let wholePart = parts[0];
    const decimalPart = parts.length > 1 ? `.${parts[1]}` : "";

    // Add commas as thousand separators
    if (wholePart) {
      wholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    return wholePart + decimalPart;
  };

  // Handle amount input changes with validation
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // Remove all non-digit characters except decimal point
    const numericValue = value.replace(/[^\d.]/g, "");

    // Format with commas
    const formattedValue = formatAmount(numericValue);

    setAmount(formattedValue);
  };

  // Handle reference number input with validation
  const handleReferenceNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    // Allow only alphanumeric, dash, and underscore
    if (/^[a-zA-Z0-9-_]*$/.test(value)) {
      setReferenceNumber(value);
    }
  };

  // Parse amount string to number (removes commas)
  const parseAmount = (amountStr: string): number => {
    return parseFloat(amountStr.replace(/,/g, "")) || 0;
  };

  const validate = () => {
    const newErrors: { [key: string]: string } = {};

    // Payment method validation
    if (!paymentMethod) {
      newErrors.paymentMethod = "Payment method is required.";
    }

    // Reference number validation
    if (!referenceNumber.trim()) {
      newErrors.referenceNumber = "Reference number is required.";
    } else if (referenceNumber.length < 4) {
      newErrors.referenceNumber =
        "Reference number must be at least 4 characters.";
    } else if (referenceNumber.length > 30) {
      newErrors.referenceNumber =
        "Reference number cannot exceed 30 characters.";
    }

    // Transaction date validation
    if (!transactionDate) {
      newErrors.transactionDate = "Transaction date is required.";
    } else {
      const selectedDate = new Date(transactionDate);
      const minDateObj = new Date(minDate);
      const maxDateObj = new Date(maxDate);

      if (selectedDate < minDateObj || selectedDate > maxDateObj) {
        newErrors.transactionDate = `Date must be between ${getFormatDateTable(
          minDate
        )} and ${getFormatDateTable(maxDate)}`;
      }
    }

    // Amount validation
    if (!amount.trim()) {
      newErrors.amount = "Payment amount is required.";
    } else {
      const amountValue = parseAmount(amount);
      if (isNaN(amountValue) || amountValue <= 0) {
        newErrors.amount = "Amount must be greater than 0.";
      } else if (amountValue > 50000) {
        newErrors.amount = "Amount cannot exceed 50,000.";
      }
    }

    return newErrors;
  };

  const handleSubmit = async (): Promise<void> => {
    if (submitting) return;

    // Final validation before submission
    const validationErrors = validate();
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      // Scroll to the first error
      const firstError = Object.keys(validationErrors)[0];
      const errorElement = document.getElementById(`error-${firstError}`);
      errorElement?.scrollIntoView({ behavior: "smooth", block: "center" });
      return;
    }
    setSubmitting(true);
    try {
      const formattedDate = formatDateForDisplay(transactionDate);
      const paymentData: PaymentData = {
        paymentMethod,
        referenceNumber,
        transactionDate,
        formattedDate,
        amount,
        currency,
        remarks,
        bookingPrice,
        systemCurrency,
      };
      // Remove empty return fields before merging
      const cleanedBookingData = { ...pendingBookingData };
      console.log(cleanedBookingData);
      if (cleanedBookingData && typeof cleanedBookingData === "object") {
        if (
          cleanedBookingData.returnFlightClass &&
          typeof cleanedBookingData.returnFlightClass === "object" &&
          Object.keys(cleanedBookingData.returnFlightClass).length === 0
        ) {
          delete cleanedBookingData.returnFlightClass;
        }
        if (
          Array.isArray(cleanedBookingData.returnFlightClasses) &&
          cleanedBookingData.returnFlightClasses.length === 0
        ) {
          delete cleanedBookingData.returnFlightClasses;
        }
        if (
          cleanedBookingData.returnPrice &&
          typeof cleanedBookingData.returnPrice === "object" &&
          Object.keys(cleanedBookingData.returnPrice).length === 0
        ) {
          delete cleanedBookingData.returnPrice;
        }
        if (
          cleanedBookingData.returnBaseFare === 0 ||
          cleanedBookingData.returnBaseFare === undefined
        ) {
          delete cleanedBookingData.returnBaseFare;
        }
      }
      // Merge paymentData with cleanedBookingData for full CreateInternalBookingDto
      // Only check root-level agencyAgentId, not ticket.agencyAgentId
      // Patch: If ticketId is missing but ticket.id exists, set ticketId from ticket.id
      if (
        cleanedBookingData &&
        cleanedBookingData.ticketId === undefined &&
        cleanedBookingData.ticket &&
        cleanedBookingData.ticket.id
      ) {
        cleanedBookingData.ticketId = cleanedBookingData.ticket.id;
      }
      // Patch: If userId is missing but bookingResult.userId exists, set userId from bookingResult.userId
      if (
        cleanedBookingData &&
        cleanedBookingData.userId === undefined &&
        cleanedBookingData.bookingResult
      ) {
        // Handle both array (round-trip) and single booking result
        if (Array.isArray(cleanedBookingData.bookingResult)) {
          // For round-trip, try to get userId from either the first or second booking result
          cleanedBookingData.userId =
            cleanedBookingData.bookingResult[0]?.userId ||
            cleanedBookingData.bookingResult[1]?.userId;
        } else if ((cleanedBookingData as any).bookingResult?.userId) {
          // For single trip, get userId directly
          cleanedBookingData.userId = (
            cleanedBookingData as any
          ).bookingResult.userId;
        }
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.type === undefined &&
        cleanedBookingData.bookingResult
      ) {
        // Handle both array (round-trip) and single booking result
        if (Array.isArray(cleanedBookingData.bookingResult)) {
          // For round-trip, try to get type from either the first or second booking result
          cleanedBookingData.type =
            cleanedBookingData.bookingResult[0]?.type ||
            cleanedBookingData.bookingResult[1]?.type;
        } else if ((cleanedBookingData as any).bookingResult?.type) {
          // For single trip, get type directly
          cleanedBookingData.type = (
            cleanedBookingData as any
          ).bookingResult.type;
        }
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.source === undefined &&
        cleanedBookingData.bookingResult
      ) {
        // Handle both array (round-trip) and single booking result
        if (Array.isArray(cleanedBookingData.bookingResult)) {
          // For round-trip, try to get source from either the first or second booking result
          cleanedBookingData.source =
            cleanedBookingData.bookingResult[0]?.source ||
            cleanedBookingData.bookingResult[1]?.source;
        } else if ((cleanedBookingData as any).bookingResult?.source) {
          // For single trip, get source directly
          cleanedBookingData.source = (
            cleanedBookingData as any
          ).bookingResult.source;
        }
      }
      if (
        cleanedBookingData &&
        !Array.isArray(cleanedBookingData.travelers) &&
        cleanedBookingData.bookingResult
      ) {
        // Handle both array (round-trip) and single booking result
        if (Array.isArray(cleanedBookingData.bookingResult)) {
          // For round-trip, combine travelers from both booking results
          const travelers1 =
            cleanedBookingData.bookingResult[0]?.travelers || [];
          const travelers2 =
            cleanedBookingData.bookingResult[1]?.travelers || [];
          cleanedBookingData.travelers = [...travelers1, ...travelers2];
        } else if ((cleanedBookingData as any).bookingResult?.travelers) {
          // For single trip, get travelers directly
          cleanedBookingData.travelers = (
            cleanedBookingData as any
          ).bookingResult.travelers;
        }
      }
      if (
        cleanedBookingData &&
        !Array.isArray(cleanedBookingData.seats) &&
        cleanedBookingData.bookingResult
      ) {
        // Handle both array (round-trip) and single booking result
        if (Array.isArray(cleanedBookingData.bookingResult)) {
          // For round-trip, combine seats from both booking results
          const seats1 = cleanedBookingData.bookingResult[0]?.bookedSeats || [];
          const seats2 = cleanedBookingData.bookingResult[1]?.bookedSeats || [];
          cleanedBookingData.seats = [...seats1, ...seats2];
        } else if ((cleanedBookingData as any).bookingResult?.bookedSeats) {
          // For single trip, get seats directly
          cleanedBookingData.seats = (
            cleanedBookingData as any
          ).bookingResult.bookedSeats;
        }
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.ticket === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.ticket
      ) {
        cleanedBookingData.ticket = (
          cleanedBookingData as any
        ).bookingResult.ticket;
      }
      if (
        cleanedBookingData &&
        !Array.isArray(cleanedBookingData.flightClasses) &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.flightClasses
      ) {
        cleanedBookingData.flightClasses = (
          cleanedBookingData as any
        ).bookingResult.flightClasses;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.flightClass === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.flightClass
      ) {
        cleanedBookingData.flightClass = (
          cleanedBookingData as any
        ).bookingResult.flightClass;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.price === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.price
      ) {
        cleanedBookingData.price = (
          cleanedBookingData as any
        ).bookingResult.price;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.passengerCounts === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.passengerCounts
      ) {
        cleanedBookingData.passengerCounts = (
          cleanedBookingData as any
        ).bookingResult.passengerCounts;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.bookingResult === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.bookingResult
      ) {
        cleanedBookingData.bookingResult = (
          cleanedBookingData as any
        ).bookingResult.bookingResult;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.departureTaxRate === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.departureTaxRate
      ) {
        cleanedBookingData.departureTaxRate = (
          cleanedBookingData as any
        ).bookingResult.departureTaxRate;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.returnTaxRate === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.returnTaxRate
      ) {
        cleanedBookingData.returnTaxRate = (
          cleanedBookingData as any
        ).bookingResult.returnTaxRate;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.totalBaseFare === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.totalBaseFare
      ) {
        cleanedBookingData.totalBaseFare = (
          cleanedBookingData as any
        ).bookingResult.totalBaseFare;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.outboundBaseFare === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.outboundBaseFare
      ) {
        cleanedBookingData.outboundBaseFare = (
          cleanedBookingData as any
        ).bookingResult.outboundBaseFare;
      }
      if (
        cleanedBookingData &&
        !Array.isArray(cleanedBookingData.taxes) &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.taxes
      ) {
        cleanedBookingData.taxes = (
          cleanedBookingData as any
        ).bookingResult.taxes;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.transactionFee === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.transactionFee
      ) {
        cleanedBookingData.transactionFee = (
          cleanedBookingData as any
        ).bookingResult.transactionFee;
      }
      if (
        cleanedBookingData &&
        cleanedBookingData.total === undefined &&
        cleanedBookingData.bookingResult &&
        (cleanedBookingData as any).bookingResult?.total
      ) {
        cleanedBookingData.total = (
          cleanedBookingData as any
        ).bookingResult.total;
      }
      let missingField = "";
      if (!cleanedBookingData) missingField = "cleanedBookingData";
      else if (cleanedBookingData.ticketId === undefined)
        missingField = "ticketId";
      else if (cleanedBookingData.userId === undefined) missingField = "userId";
      else if (cleanedBookingData.type === undefined) missingField = "type";
      else if (cleanedBookingData.source === undefined) missingField = "source";
      else if (
        !Array.isArray(cleanedBookingData.travelers) ||
        cleanedBookingData.travelers.length === 0
      )
        missingField = "travelers";
      else if (
        !Array.isArray(cleanedBookingData.seats) ||
        cleanedBookingData.seats.length === 0
      )
        missingField = "seats";
      else if (cleanedBookingData.ticket === undefined) missingField = "ticket";
      else if (!Array.isArray(cleanedBookingData.flightClasses))
        missingField = "flightClasses";
      else if (cleanedBookingData.flightClass === undefined)
        missingField = "flightClass";
      else if (cleanedBookingData.price === undefined) missingField = "price";
      else if (cleanedBookingData.passengerCounts === undefined)
        missingField = "passengerCounts";
      else if (cleanedBookingData.bookingResult === undefined)
        missingField = "bookingResult";
      else if (cleanedBookingData.departureTaxRate === undefined)
        missingField = "departureTaxRate";
      else if (cleanedBookingData.returnTaxRate === undefined)
        missingField = "returnTaxRate";
      else if (cleanedBookingData.totalBaseFare === undefined)
        missingField = "totalBaseFare";
      else if (cleanedBookingData.outboundBaseFare === undefined)
        missingField = "outboundBaseFare";
      else if (
        cleanedBookingData.returnBaseFare === undefined &&
        cleanedBookingData.bookingResult?.returnTicketId
      )
        missingField = "returnBaseFare";
      else if (cleanedBookingData.taxes === undefined) missingField = "taxes";
      else if (cleanedBookingData.transactionFee === undefined)
        missingField = "transactionFee";
      else if (cleanedBookingData.total === undefined) missingField = "total";
      if (missingField) {
        console.error(
          "Missing required field in cleanedBookingData:",
          missingField,
          cleanedBookingData
        );
        setErrors((prev) => ({
          ...prev,
          form: `Booking data is missing required field: ${missingField}. Please try again or contact support.`,
        }));
        setSubmitting(false);
        return;
      }

      // Submit payment to backend
      let bookingId: string | undefined;
      if (Array.isArray(pendingBookingData?.bookingResult)) {
        // For round-trip, use the ID from the first booking result
        bookingId = pendingBookingData?.bookingResult[0]?.id;
      } else {
        // For single trip, get ID directly
        bookingId = pendingBookingData?.bookingResult?.id;
      }
      if (!bookingId) {
        setErrors((prev) => ({
          ...prev,
          form: "Booking ID is missing. Cannot submit payment.",
        }));
        setSubmitting(false);
        return;
      }

      // Show processing state
      setIsProcessing(true);

      // Create a container for the popup
      const popupContainer = document.createElement("div");
      document.body.appendChild(popupContainer);
      const root = createRoot(popupContainer);

      // Function to close the popup
      const closePopup = () => {
        root.unmount();
        if (document.body.contains(popupContainer)) {
          document.body.removeChild(popupContainer);
        }
      };

      // Show loading popup
      root.render(
        <div className="fixed inset-0 z-50">
          <PopupMessage
            icon={<Loader className="h-12 w-12 text-red-500 animate-spin" />}
            message="Your booking request has been submitted successfully and is now pending approval"
            hideCloseButton={true}
          />
        </div>
      );

      try {
        // Wait for 5 seconds before processing payment
        await new Promise((resolve) => setTimeout(resolve, 5000));
        if (!pendingBookingData) {
          throw new Error("Booking data is missing");
        }
        // Call the payment endpoint
        // Handle both single and round-trip bookings
        if (Array.isArray(pendingBookingData?.bookingResult)) {
          // For round-trip, process both departure and return bookings
          const [departureBooking, returnBooking] =
            pendingBookingData.bookingResult;

          // Process departure booking
          await completePayment(departureBooking.id, {
            ...paymentData,
            // Add any booking-specific data if needed
          });

          // Process return booking if it exists
          if (returnBooking?.id) {
            await completePayment(returnBooking.id, {
              ...paymentData,
              // Add any booking-specific data if needed
            });
          }
        } else {
          // For single trip, process the single booking
          await completePayment(bookingId, paymentData);
        }

        // Close the popup and redirect on success
        setTimeout(() => {
          closePopup();
          window.location.href = "/ticket-hub/myBookings";
        }, 10000);
      } catch (error) {
        console.error("Payment API error:", error);
        let errorMessage = "There was an issue submitting your booking request";

        // Type guard to check if error is an object with response property
        if (error && typeof error === "object" && "response" in error) {
          const axiosError = error as { response?: { data?: unknown } };
          const responseData = axiosError.response?.data;

          if (
            responseData &&
            typeof responseData === "object" &&
            "message" in responseData
          ) {
            // Handle { isSuccess: false, message: "..." } format
            const data = responseData as { message: string };
            errorMessage = data.message;
          } else if (typeof responseData === "string") {
            // Handle direct message string
            errorMessage = responseData;
          }
        }
        // Handle standard Error objects
        else if (error instanceof Error) {
          errorMessage = error.message;
        }
        
        // Create a container for the popup
        const popupContainer = document.createElement("div");
        document.body.appendChild(popupContainer);
        const root = createRoot(popupContainer);

        // Show error popup
        root.render(
          // On failure, show error popup and redirect to Manage Internal Booking
          <PopupMessage
            icon={<AlertCircle className="h-10 w-10 text-red-500" />}
            message={errorMessage}
            onClose={() => {
              root.unmount();
              if (document.body.contains(popupContainer)) {
                document.body.removeChild(popupContainer);
              }
              window.location.href =
                "/blockseats/singleTicket/payment/ManageBooking";
            }}
          />
        );
        // Close the popup and redirect on failure
        setTimeout(() => {
          root.unmount();
          if (document.body.contains(popupContainer)) {
            document.body.removeChild(popupContainer);
          }
          closePopup();
          window.location.href =
            "/blockseats/singleTicket/payment/ManageBooking";
        }, 10000);
      } finally {
        setIsProcessing(false);
      }
      setIsVisible(false);
    } catch (error) {
      // Handle any exceptions during the payment process
      if (error instanceof Error) {
        console.error("Payment submission error:", error.message);
      }
      // Create a container for the popup
      const popupContainer = document.createElement("div");
      document.body.appendChild(popupContainer);
      const root = createRoot(popupContainer);

      // Show error popup
      root.render(
        // On failure, show error popup and redirect to Manage Internal Booking
        <PopupMessage
          icon={<AlertCircle className="h-10 w-10 text-red-500" />}
          message="There was an issue submitting your booking request"
        />
      );
      // Redirect to Manage Booking on error
      setTimeout(() => {
        window.location.href = "/blockseats/singleTicket/payment/ManageBooking";
      }, 10000);
      console.error("Payment submission error:", error);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle Transaction Date input changes (keyboard only, DD/MM/YYYY)
  const handleTransactionDateInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    let value = e.target.value.replace(/[^\d/]/g, ""); // Only numbers and slashes
    // Auto-insert slashes and zero-pad day/month
    if (value.length === 2 && !value.includes("/")) value += "/";
    if (value.length === 5 && value.split("/").length < 3) value += "/";
    // Enforce DD/MM/YYYY
    if (value.length > 10) value = value.slice(0, 10);

    // Validate day and month as user types
    const parts = value.split("/");
    let [day, month, year] = parts;
    let valid = true;
    if (day && day.length === 2) {
      const dayNum = parseInt(day, 10);
      if (dayNum < 1 || dayNum > 31) {
        valid = false;
        day = dayNum > 31 ? "31" : dayNum < 1 ? "01" : day;
      }
    }
    if (month && month.length === 2) {
      const monthNum = parseInt(month, 10);
      if (monthNum < 1 || monthNum > 12) {
        valid = false;
        month = monthNum > 12 ? "12" : monthNum < 1 ? "01" : month;
      }
    }
    // Reconstruct value if we fixed anything
    if (parts.length > 1) {
      value = `${day || ""}${month !== undefined ? "/" + month : ""}${
        year !== undefined ? "/" + year : ""
      }`;
      value = value.slice(0, 10);
    }
    setTransactionDateInput(value);
    // Optionally, update transactionDate in YYYY-MM-DD if valid
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(value) && valid) {
      setErrors((prev) => ({ ...prev, transactionDate: "" }));
      setTransactionDate(`${year}-${month}-${day}`);
    } else if (value.length === 10) {
      setErrors((prev) => ({
        ...prev,
        transactionDate: "Invalid date: day must be 1-31 and month 1-12.",
      }));
    } else {
      setErrors((prev) => ({ ...prev, transactionDate: "" }));
    }
  };

  // Handle closing the modal
  const handleClose = (): void => {
    setIsVisible(false);
    onClose();
  };

  // Don't render anything if modal is closed
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 overflow-y-auto custom-scrollbar">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg max-w-md w-full">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold dark:text-white">
              Payment Information
            </h2>
            <button
              onClick={handleClose}
              className="bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white rounded-lg w-8 h-8 flex items-center justify-center transition-colors duration-200"
              aria-label="Close dialog"
            >
              <X size={20} />
            </button>
          </div>
          {errors.form && (
            <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/50 rounded-lg">
              {/* Form-level error display */}
              <p className="text-red-600 dark:text-red-400 text-sm flex items-center">
                {errors.form}
              </p>
            </div>
          )}
          {/* Payment Details Section */}
          <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
            <div className="flex items-center mb-4">
              <CreditCard size={20} className="text-green-500 mr-2" />
              <h3 className="text-gray-600 dark:text-white text-lg font-medium">
                Payment Details
              </h3>
            </div>

            <div className="space-y-4">
              {/* Payment Method Selector */}
              <div className="mb-4">
                <IsolatedDropdown
                  label="Payment Method"
                  options={paymentMethods}
                  value={paymentMethod}
                  onChange={setPaymentMethod}
                />
                {errors.paymentMethod && (
                  <p
                    id="error-paymentMethod"
                    className="mt-1 text-sm text-red-500"
                  >
                    {errors.paymentMethod}
                  </p>
                )}
              </div>

              {/* Transaction fields */}
              <div>
                <label className="block text-gray-700 mb-1 text-sm dark:text-gray-300">
                  Reference Number <span className="text-red-500">*</span>
                </label>
                <div className="relative w-full">
                  <input
                    type="text"
                    id="referenceNumber"
                    value={referenceNumber}
                    onChange={handleReferenceNumberChange}
                    maxLength={30}
                    className={`w-full rounded-lg p-2 bg-gray-200 dark:bg-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 border-none ${
                      errors.referenceNumber ? "border border-red-500" : ""
                    }`}
                    placeholder="Enter reference number (4-30 chars)"
                  />
                  <div className="absolute right-2 bottom-2 text-xs text-gray-400">
                    {referenceNumber.length}/30
                  </div>
                </div>
                {errors.referenceNumber && (
                  <p
                    id="error-referenceNumber"
                    className="mt-1 text-xs text-red-500"
                  >
                    {errors.referenceNumber}
                  </p>
                )}
              </div>
              {/* Transaction Date */}
              <div className="mb-4">
                <label
                  htmlFor="transactionDate"
                  className="block text-gray-700 mb-1 text-sm dark:text-gray-300"
                >
                  Transaction Date <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    id="transactionDate"
                    type="text"
                    className={`block w-full rounded-lg border px-3 py-2 pr-10 bg-gray-200 dark:bg-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 border-none ${
                      errors.transactionDate
                        ? "border-red-500"
                        : "border-gray-600"
                    }`}
                    value={transactionDateInput}
                    onChange={handleTransactionDateInputChange}
                    placeholder="DD/MM/YYYY"
                    maxLength={10}
                    autoComplete="off"
                    inputMode="numeric"
                  />
                  <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </span>
                </div>
                {errors.transactionDate && (
                  <div
                    id="error-transactionDate"
                    className="text-red-500 text-xs mt-1"
                  >
                    {errors.transactionDate}
                  </div>
                )}
              </div>

              {/* Booking Price (display only) */}
              <div>
                <label className="block text-gray-700 mb-1 text-sm dark:text-gray-300">
                  Base Fare
                </label>
                <div className="flex items-center bg-gray-200 dark:bg-gray-600 rounded-lg p-2 dark:text-white border-none">
                  <span className="font-medium">
                    {Number(pendingBookingData?.total).toFixed(2)}{" "}
                    {currencyToShow}
                  </span>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Listed fare for this booking. Used if no custom payment is
                  entered.
                </p>
              </div>

              {/* Payment Amount with Currency */}
              <div>
                <label className="block text-gray-700 mb-1 text-sm dark:text-gray-300">
                  Final Charge ( total ) <span className="text-red-500">*</span>
                </label>
                <div className="relative flex">
                  <div className="w-24 mr-2">
                    <IsolatedDropdown
                      label="Currency"
                      options={currencies.map((curr) => ({
                        value: curr,
                        label: curr,
                      }))}
                      value={currency}
                      showLabel={false}
                      onChange={setCurrency}
                    />
                  </div>
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      id="amount"
                      value={amount}
                      onChange={handleAmountChange}
                      className={`w-full rounded-lg p-2 bg-gray-200 dark:bg-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 border-none ${
                        errors.amount ? "border border-red-500" : ""
                      }`}
                      placeholder="0.00"
                      inputMode="decimal"
                    />
                    <span className="absolute right-3 top-2.5 text-sm text-gray-500">
                      {currency}
                    </span>
                  </div>
                </div>
                {errors.amount && (
                  <p id="error-amount" className="mt-1 text-xs text-red-500">
                    {errors.amount}
                  </p>
                )}
                <p className="text-xs text-gray-400 mt-1">
                  This field overrides the booking price. Leave blank to charge
                  the default amount.
                </p>
              </div>

              {/* Remarks Section */}
              <div>
                <label className="block text-gray-700 mb-1 text-sm dark:text-gray-300">
                  Remarks
                </label>
                <textarea
                  placeholder={`Enter any special instructions or notes (max ${remarksMaxLength} characters)`}
                  maxLength={remarksMaxLength}
                  value={remarks}
                  onChange={(e) => setRemarks(e.target.value)}
                  className="w-full rounded-lg p-2 h-20 bg-gray-200 dark:bg-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 border-none custom-scrollbar"
                />
                <div className="flex justify-end mt-1">
                  <span className="text-xs text-gray-700 dark:text-gray-300">
                    {remarks.length}/{remarksMaxLength} characters
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertCircle
                className="text-red-500 flex-shrink-0 mt-0.5"
                size={20}
              />
              <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                {warningMessage}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
              disabled={submitting}
            >
              {cancelButtonText}
            </button>
            <button
              onClick={handleSubmit}
              className="px-4 py-2 bg-red-500 text-white rounded-xl hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
              disabled={submitting}
            >
              {submitting ? "Submitting..." : submitButtonText}
            </button>
          </div>

          {/* Result Popups */}
          {showResultPopup === "success" && (
            <div className="fixed inset-0 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 max-w-sm w-full flex flex-col items-center">
                <span className="text-green-600 text-3xl mb-2">✔️</span>
                <p className="text-lg font-semibold mb-2 text-center">
                  Your booking request has been submitted successfully and is
                  now pending approval
                </p>
                <p className="text-gray-500 text-center">
                  Redirecting to My Bookings...
                </p>
              </div>
            </div>
          )}
          {showResultPopup === "error" && (
            <div className="fixed inset-0 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 max-w-sm w-full flex flex-col items-center">
                <span className="text-red-600 text-3xl mb-2">❌</span>
                <p className="text-lg font-semibold mb-2 text-center">
                  There was an issue submitting your booking request
                </p>
                <p className="text-gray-500 text-center">
                  Redirecting to Manage Internal Booking...
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentDialog;
