import { formattedPrice } from "@/utils/functions/functions";

// Helper component for a single fare breakdown row
export default function FareBreakdownRow({
    item,
    currency,
  }: {
    item: { label: string; value: number; perPersonValue: number; count: number };
    currency: string;
  }) {
    const [first, second] = item.label.trim().split(" ");
    const displayLabel = second || first;
    return (
      <div className="flex justify-between mb-1">
        <span className="dark:text-gray-300 text-gray-700">
          {`${item.count} ${displayLabel}${
            item.count > 1 ? "s" : ""
          } × ${item.perPersonValue.toFixed(2)} ${currency}`}
        </span>
        <span className="dark:text-white text-gray-700">
          {formattedPrice(item.value, currency)}
        </span>
      </div>
    );
  }