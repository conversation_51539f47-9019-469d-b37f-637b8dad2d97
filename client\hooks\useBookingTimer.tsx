"use client";

// Enhanced hook: exposes isExpired, accepts onExpire, and supports custom expiry.
import { useEffect, useState, useRef, useCallback } from "react";
import { useSocket } from "@/context/SocketContext";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";
import { BOOKING_TIMEOUT_SECONDS } from "@/utils/constants/bookingsTimeoutConstants";
import {
  startBookingSession,
  validateBookingSession,
} from "@/lib/data/bookingSession";
import { redirectWithParams } from "@/utils/functions/redirectWithParams";

interface UseBookingTimerOptions {
  expiryTimestamp?: number; // UTC ms
  expiresAt?: number;
  onExpire?: () => void;
  redirectOnExpire?: boolean; // Whether to redirect to /blockseats/list when timer expires
}

export const useBookingTimer = (options?: UseBookingTimerOptions) => {
  const [timeLeft, setTimeLeft] = useState(BOOKING_TIMEOUT_SECONDS);
  const [token, setToken] = useState<string | null>(
    typeof window !== "undefined" ? localStorage.getItem("booking_token") : null
  );
  const [isExpired, setIsExpired] = useState(false);
  const { socket } = useSocket();
  const user = useAppSelector(selectUser);
  const onExpireRef = useRef<(() => void) | undefined>(options?.onExpire);
  onExpireRef.current = options?.onExpire;

  useEffect(() => {
    let expiryTime = options?.expiryTimestamp;
    const initializeSession = async () => {
      try {
        const storedToken = localStorage.getItem("booking_token");
        const storedExpiry = localStorage.getItem("booking_expiry");
        if (expiryTime !== undefined) {
          setTimeLeft(
            Math.max(0, Math.floor((expiryTime - Date.now()) / 1000))
          );
        } else if (
          storedToken &&
          storedExpiry &&
          parseInt(storedExpiry) > Date.now()
        ) {
          setToken(storedToken);
          expiryTime = parseInt(storedExpiry);
          if (expiryTime !== undefined) {
            setTimeLeft(
              Math.max(0, Math.floor((expiryTime - Date.now()) / 1000))
            );
          } else {
            setTimeLeft(BOOKING_TIMEOUT_SECONDS);
          }
        } else {
          // Use the API utility to start the booking session
          const data = await startBookingSession();
          if (data.token && data.expiryTime) {
            localStorage.setItem("booking_token", data.token);
            localStorage.setItem("booking_expiry", data.expiryTime.toString());
            setToken(data.token);
            expiryTime = data.expiryTime;
            if (expiryTime !== undefined) {
              setTimeLeft(
                Math.max(0, Math.floor((expiryTime - Date.now()) / 1000))
              );
            } else {
              setTimeLeft(BOOKING_TIMEOUT_SECONDS);
            }
          }
        }
      } catch (error) {
        console.error("Failed to initialize booking session:", error);
      }
    };

    initializeSession();

    // Handle timer expiration
    const handleExpiration = () => {
      localStorage.removeItem("booking_token");
      localStorage.removeItem("booking_expiry");
      setIsExpired(true);

      // Call the onExpire callback if provided
      if (onExpireRef.current) onExpireRef.current();

      // Redirect to /blockseats/list if redirectOnExpire is true
      if (options?.redirectOnExpire && typeof window !== "undefined") {
        // Use the utility function to redirect with preserved parameters
        redirectWithParams("/blockseats/list", 2000); // 2 second delay
      }
    };

    // Listen for session expiration from server
    socket?.on("bookingSessionExpired", handleExpiration);

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          handleExpiration();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
      socket?.off("bookingSessionExpired");
    };
  }, [socket, user, options?.expiryTimestamp, options?.redirectOnExpire]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Function to clear the booking session
  const clearBookingSession = useCallback((force: boolean = false) => {
    // Check if we're in a booking flow by examining the URL
    const isInBookingFlow =
      typeof window !== "undefined" &&
      (window.location.pathname.includes("/checkout") ||
        window.location.pathname.match(/\/blockseats\/list\/[^\/]+$/));

    // Only clear the session if we're not in a booking flow or if force=true
    if (force || !isInBookingFlow) {
      localStorage.removeItem("booking_token");
      localStorage.removeItem("booking_expiry");
      setToken(null);
      setTimeLeft(BOOKING_TIMEOUT_SECONDS);
      setIsExpired(false);
    }
  }, []);

  return {
    timeLeft,
    formattedTime: formatTime(timeLeft),
    token,
    isExpired,
    clearBookingSession,
  };
};
