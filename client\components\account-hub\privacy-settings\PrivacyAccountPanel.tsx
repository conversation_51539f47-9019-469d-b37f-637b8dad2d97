"use client";

import { useCallback, useEffect, useState } from "react";

import {
  fetchUpdateUserEmail,
  fetchUpdateUserPassword,
  fetchUserProfile,
} from "@/lib/data/userProfileData";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { useAppDispatch } from "@/redux/hooks";
import ProgressLoading from "../../utils/ProgressLoading";
import { UserProfileResultType } from "@/utils/definitions/userProfileDefinitions";

import { setLoading } from "@/redux/features/LoadingSlice";
import DeleteUserAlert from "./DeleteUserAlert";
import { Mail, Eye, EyeOff } from "lucide-react";
import HardDeleteUserButton from "@/components/common/HardDeleteUser";
import DeactivateAccountButton from "@/components/common/DeactivateAccountButton";
import { PasswordStrength } from "@/utils/definitions/agentsDefinitions";
import useDarkMode from "@/components/hooks/useDarkMode";
import { checkPasswordStrength } from "@/utils/passwordStrength";
import Pass<PERSON><PERSON>ield from "@/components/common/PasswordField";

const generateInputClassName = (hasError: boolean) => {
  const baseClasses =
    "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-1.5 md:py-2.5 bg-gray-100 dark:bg-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-2 w-full outline-none transition-all duration-300 focus:ring-2 focus:ring-red-500 focus:border-red-500 border-none";
  const errorClasses = hasError ? "border-red-500" : "";
  return `${baseClasses} ${errorClasses}`;
};

export default function PrivacyAccountPanel() {
  const [userInfo, setUserInfo] = useState<UserProfileResultType | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // email
  const [emailSend, setEmailSend] = useState<boolean>(false);
  const [newEmail, setNewEmail] = useState<{ newEmail: string }>({
    newEmail: "",
  });
  const [validationErrorEmail, setValidationErrorEmail] = useState<string>("");

  // password
  const [updatePasswordForm, setUpdatePasswordForm] = useState<{
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
  }>({
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });
  const [validationErrorPassword, setValidationErrorPassword] = useState<{
    currentPassword?: string;
    newPassword?: string;
    confirmNewPassword?: string;
  }>({});

  // Individual password visibility states
  const [visibility, setVisibility] = useState({
    current: false,
    new: false,
    confirm: false,
    groupToggled: false,
  });

  // Handle individual password toggle
  const toggleSinglePassword = useCallback((field: keyof typeof visibility) => {
    setVisibility((prev) => {
      const newState = {
        ...prev,
        [field]: !prev[field],
        groupToggled: false,
      };
      return newState;
    });
  }, []);

  // const [passwordStrength, setPasswordStrength] = useState(0);

  const darkMode = useDarkMode();
  const isDarkMode = typeof darkMode === "boolean" ? darkMode : false;

  const getInitialPasswordStrength = (): PasswordStrength => ({
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  });
  const [currentDebouncedPassword, setCurrentDebouncedPassword] =
    useState<string>("");
  const [newPasswordStrength, setNewPasswordStrength] =
    useState<PasswordStrength>(getInitialPasswordStrength());
  const [newDebouncedPassword, setNewDebouncedPassword] = useState<string>("");
  const [confirmNewPasswordStrength, setConfirmNewPasswordStrength] =
    useState<PasswordStrength>(getInitialPasswordStrength());
  const [confirmNewDebouncedPassword, setConfirmNewDebouncedPassword] =
    useState<string>("");

  // Debounce password strength check
  useEffect(() => {
    const handler = setTimeout(() => {
      setNewPasswordStrength(
        checkPasswordStrength(newDebouncedPassword, isDarkMode)
      );
      setConfirmNewPasswordStrength(
        checkPasswordStrength(confirmNewDebouncedPassword, isDarkMode)
      );
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [
    currentDebouncedPassword,
    newDebouncedPassword,
    confirmNewDebouncedPassword,
    isDarkMode,
  ]);

  // delete user
  const [dangerModalOpen, setDangerModalOpen] = useState<boolean>(false);

  // hooks
  const dispatch = useAppDispatch();

  // ######## functions ########
  const fetchUserInfo = async () => {
    setIsLoading(true);
    // Fetch user profile data
    const data = await fetchUserProfile();

    // Set user info
    if (data.success && data.results) {
      setUserInfo(data.results);
    }
    // Display message
    dispatch(
      setMsg({
        success: data.success,
        message: data.message,
      })
    );
    setIsLoading(false);
  };

  // Updates the user profile by sending a PUT request to the server.
  const updateEmail = async (): Promise<void> => {
    // Set loading state to true
    dispatch(setLoading(true));

    // Send PUT request to server to update user profile
    const data = await fetchUpdateUserEmail(newEmail);

    // If the request is successful
    if (data.success) {
      // Update the user information in the Redux store
      setValidationErrorEmail("");
      setNewEmail({ newEmail: "" });
      dispatch(
        setMsg({
          success: data.success,
          message: `Email verification sent successfully, please check your email ${newEmail.newEmail}`,
        })
      );
    }

    // If there are validation errors
    if (data.validationError) {
      // Update the validation error state
      setValidationErrorEmail(data.validationError);
      dispatch(setMsg({ success: false, message: data.validationError }));
    }

    // Set loading state to false
    dispatch(setLoading(false));
  };

  // update user password
  const updatePassword = async (): Promise<void> => {
    // Set loading state to true
    dispatch(setLoading(true));
    // Send PUT request to server to update user profile
    const data = await fetchUpdateUserPassword(updatePasswordForm);
    // If the request is successful
    if (data.success) {
      // reset the form
      setUpdatePasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmNewPassword: "",
      });
      setValidationErrorPassword({});
    }

    // If there are validation errors
    if (data.validationError) {
      // Update the validation error state
      setValidationErrorPassword(data.validationError);
    }

    // Display the appropriate message
    dispatch(
      setMsg({
        success: data.success,
        message: data.message,
      })
    );
    // Set loading state to false
    dispatch(setLoading(false));
  };

  // ######## useEffect ########
  useEffect(() => {
    fetchUserInfo();
  }, []);

  // Loading
  if (isLoading) {
    return <ProgressLoading />;
  }

  // User not found
  if (userInfo === null) {
    return (
      <div className="w-full h-[80vh] flex justify-center items-center">
        <h1 className="text-red-700 text-xl font-bold">User not found</h1>
      </div>
    );
  }

  const user = userInfo as UserProfileResultType;
  const isMasterAdmin =
    user?.role === "master" && user?.roleType === "master_admin";

  return (
    <div className="space-y-8">
      {/* Account Security Section */}
      <h3 className="text-xl md:text-2xl font-semibold mb-4 text-gray-800 dark:text-white">
        Account Security
      </h3>
      <section className="bg-white dark:bg-gray-700 p-6 rounded-lg">
        {/* Password Update */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium dark:text-gray-300">
            Update Password
          </h4>
          {/* Current Password */}
          <PasswordField
            name="currentPassword"
            label="Current Password"
            placeholder="Enter your current password"
            value={updatePasswordForm.currentPassword}
            // showPassword={showCurrentPassword}
            showPassword={visibility.current}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = e.target.value;
              setUpdatePasswordForm((prev) => ({
                ...prev,
                currentPassword: newValue,
              }));
              // Immediately update password strength
              setCurrentDebouncedPassword(newValue);
            }}
            onToggleVisibility={() => {
              toggleSinglePassword("current");
            }}
            required
            tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
            formErrors={validationErrorPassword?.currentPassword}
            labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            inputClassName={generateInputClassName(
              !!validationErrorPassword?.currentPassword
            )}
            strengthClassName="bg-gray-200 dark:bg-gray-800"
          />

          {/* New Password */}
          <PasswordField
            name="newPassword"
            label="New Password"
            placeholder="Enter new password"
            value={updatePasswordForm.newPassword}
            // showPassword={showNewPassword}
            showPassword={visibility.new}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = e.target.value;
              setUpdatePasswordForm((prev) => ({
                ...prev,
                newPassword: newValue,
              }));
              // Immediately update password strength
              setNewDebouncedPassword(newValue);
            }}
            onToggleVisibility={() => {
              toggleSinglePassword("new");
            }}
            strength={newPasswordStrength}
            required
            tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
            formErrors={validationErrorPassword?.newPassword}
            labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            inputClassName={generateInputClassName(
              !!validationErrorPassword?.newPassword
            )}
            strengthClassName="bg-gray-200 dark:bg-gray-800"
          />

          {/* Confirm New Password */}
          <PasswordField
            name="confirmNewPassword"
            label="Confirm New Password"
            placeholder="Confirm new password"
            value={updatePasswordForm.confirmNewPassword}
            // showPassword={showConfirmNewPassword}
            showPassword={visibility.confirm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = e.target.value;
              setUpdatePasswordForm((prev) => ({
                ...prev,
                confirmNewPassword: newValue,
              }));
              // Immediately update password strength
              setConfirmNewDebouncedPassword(newValue);
            }}
            onToggleVisibility={() => {
              toggleSinglePassword("confirm");
            }}
            strength={confirmNewPasswordStrength}
            required
            tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
            formErrors={validationErrorPassword?.confirmNewPassword}
            labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            inputClassName={generateInputClassName(
              !!validationErrorPassword?.confirmNewPassword
            )}
            strengthClassName="bg-gray-200 dark:bg-gray-800"
          />

          {/* Show/Hide Password */}
          <section className="flex justify-start items-center">
            {/* <button
              onClick={toggleAllPasswords}
              className="dark:text-gray-400 dark:hover:text-white transition-colors duration-300 flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold py-2 px-4"
            >
              {allVisible ? <EyeOff size={18} /> : <Eye size={18} />}
              <span className="text-sm dark:text-gray-400">
                {allVisible ? "Hide" : "Show"} all passwords
              </span>
            </button> */}
            {/* <VisibilityToggleButton
              allVisible={allVisible}
              onToggle={() => toggleAllPasswords()}
            /> */}
            <button
              onClick={updatePassword}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
            >
              Save Password Changes
            </button>
          </section>
        </div>
      </section>
      <section className="bg-white dark:bg-gray-700 p-6 rounded-lg">
        {/* Email Settings */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium dark:text-gray-300">
            Email Settings
          </h4>
          <div className="flex flex-wrap items-center md:space-x-4">
            <span className="dark:text-gray-400">Current Email:</span>
            <span className="text-gray-700 font-semibold dark:text-white overflow-hidden text-ellipsis break-all">
              {user.email}
            </span>
          </div>
          {/* New Email Address */}
          <InputField
            label="New Email Address"
            id="newEmail"
            type="email"
            icon={<Mail size={18} />}
            value={newEmail.newEmail}
            onChange={(e) => setNewEmail({ newEmail: e.target.value })}
            required
            validationError={validationErrorEmail}
            placeholder="Enter new email address"
          />
          <section className="flex justify-start items-center">
            <button
              type="button"
              onClick={updateEmail}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-2 md:px-4 rounded-lg transition-colors duration-300"
            >
              Send Verification Code
            </button>
          </section>
        </div>
      </section>

      {/* Account Deletion Section */}
      {!isMasterAdmin && (
        <section className="bg-white dark:bg-gray-700 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-4 text-red-500">
            Account Deletion
          </h3>
          <p className="dark:text-gray-300 mb-4">
            Deleting your account is permanent. All your data will be
            permanently removed and cannot be recovered.
          </p>
          <ul className="list-disc list-inside dark:text-gray-300 mb-4">
            <li>Your profile and all associated data will be deleted</li>
            <li>You will lose access to all services and subscriptions</li>
            <li>Any outstanding balances or credits will be forfeited</li>
          </ul>
          <section className="flex justify-start items-center space-x-8">
            <DeactivateAccountButton />
            {isMasterAdmin && <HardDeleteUserButton />}
          </section>

          <DeleteUserAlert
            dangerModalOpen={dangerModalOpen}
            setDangerModalOpen={setDangerModalOpen}
          />
        </section>
      )}
    </div>
  );
}

interface VisibilityToggleButtonProps {
  allVisible: boolean;
  onToggle: () => void;
}

const VisibilityToggleButton = ({
  allVisible,
  onToggle,
}: VisibilityToggleButtonProps) => {
  // Use showAllPasswords instead of allVisible for the button state
  const buttonText = `${allVisible ? "Hide" : "Show"} All Passwords`;
  const Icon = allVisible ? EyeOff : Eye;
  return (
    <button
      type="button"
      onClick={onToggle}
      className="dark:text-gray-400 dark:hover:text-white transition-colors duration-300 flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold py-2 px-4"
    >
      <Icon size={18} />
      <span className="text-base">{buttonText}</span>
    </button>
  );
};

const InputField = ({
  label,
  id,
  icon,
  value,
  onChange,
  required,
  type = "text",
  validationError,
  placeholder,
}: {
  label: string;
  id: string;
  icon: React.ReactNode;
  value: string | undefined;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  type?: string;
  validationError?: string;
  placeholder?: string;
}) => (
  <div>
    <label
      className="block text-sm font-medium dark:text-gray-400 mb-1"
      htmlFor={id}
    >
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <div className="relative">
      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
        {icon}
      </span>
      <input
        id={id}
        type={type}
        className="bg-gray-100 dark:bg-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-2 w-full outline-none transition-all duration-300 focus:ring-2 focus:ring-red-500 focus:border-red-500 border-none"
        value={value || ""}
        onChange={onChange}
        required={required}
        placeholder={placeholder}
      />
    </div>
    {validationError && (
      <div className="text-sm mt-1 text-red-500">{validationError}</div>
    )}
  </div>
);

const PasswordStrengthIndicator = ({ strength }: { strength: number }) => {
  const getStrengthColor = () => {
    switch (strength) {
      case 1:
        return "bg-red-500";
      case 2:
        return "bg-yellow-500";
      case 3:
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStrengthText = () => {
    switch (strength) {
      case 1:
        return "Weak";
      case 2:
        return "Medium";
      case 3:
        return "Strong";
      default:
        return "No password";
    }
  };

  return (
    <div className="mt-2">
      <div className="h-2 w-full bg-gray-300 dark:bg-gray-800 rounded-full overflow-hidden">
        <div
          className={`h-full ${getStrengthColor()}`}
          style={{ width: `${strength * 33.33}%` }}
        ></div>
      </div>
      <p className="text-sm dark:text-gray-400 mt-1">
        Password strength: {getStrengthText()}
      </p>
    </div>
  );
};
