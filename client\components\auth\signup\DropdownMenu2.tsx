"use client";

import {
  <PERSON><PERSON><PERSON>D<PERSON>,
  ArrowDown,
  Check,
  ChevronDown,
  Info,
  Search,
  User,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { Menu, Transition } from "@headlessui/react";

export default function DropdownMenu2({
  label,
  id,
  icon,
  tooltip,
  options,
  searchValue,
  onSearchChange,
  isOpen,
  setIsOpen,
  renderError,
  ...props
}: {
  label: string;
  id: string;
  icon: React.ReactNode;
  tooltip?: string;
  options: { id: number; name: string }[];
  searchValue: string;
  onSearchChange: (value: string) => void;
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  [key: string]: any;
  renderError: (id: string) => JSX.Element;
}) {
  // on click outside of the field close the box

  const menuRef = useRef<HTMLDivElement>(null);
  const [selected, setSelected] = useState<number>(0);
  const handleClickOutside = (event: MouseEvent) => {
    if (
      !menuRef.current?.contains(event.target as Node) &&
      !(event.target instanceof HTMLInputElement)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative">
      <label
        className="block text-sm font-medium dark:text-gray-400 mb-1"
        htmlFor={id}
      >
        {label} {props.required && <span className="text-red-500">*</span>}
        {tooltip && <Tooltip text={tooltip} />}
      </label>
      {/* <div className="relative">
        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
          {icon}
        </span>
        <input
          id={id}
          className="bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-1.5 md:py-2.5 w-full outline-none transition-all duration-300 border-0 focus:ring-2 focus:ring-red-500 focus:border-red-500 placeholder:text-xs md:placeholder:text-sm selection:text-gray-800 dark:selection:text-white"
          placeholder={`Select ${label}`}
          value={searchValue || ""}
          onChange={(e) => onSearchChange(e.target.value)}
          onFocus={() => setIsOpen(true)}
          type="text"
          {...props}
        />
        <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500">
          <ArrowDown size={18} />
        </span>
      </div> */}
      {/* {renderError(id)} */}
      {/* {isOpen && (
        <div className="absolute text-sm z-10 w-full mt-1 bg-gray-300 dark:bg-gray-700 rounded-md shadow-lg max-h-60 overflow-auto border-0">
          {options.map((option, idx) => (
            <div
              key={idx}
              className={`px-4 py-2 cursor-pointer hover:bg-gray-400 dark:hover:bg-gray-600 capitalize ${
                searchValue.toLowerCase() === option.toLowerCase()
                  ? "text-red-500 font-bold"
                  : ""
              }`}
              onClick={() => {
                onSearchChange(option);
                setIsOpen(false);
              }}
            >
              {option}
            </div>
          ))}
          {options.length === 0 && (
            <div className="px-4 py-2 text-gray-400">No results found</div>
          )}
        </div>
      )} */}
      <Menu as="div" className="relative flex w-full">
        {({ open }) => (
          <>
            <Menu.Button
              className={`btn w-full justify-between min-w-[11rem] h-[45px] bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-1.5 md:py-2.5 outline-none transition-all duration-300 border-0 focus:ring-2 focus:ring-red-500 focus:border-red-500 placeholder:text-xs md:placeholder:text-sm selection:text-gray-800 dark:selection:text-white capitalize ${
                open ? "ring-1 ring-red-500 border-red-500" : ""
              }`}
              aria-label="Select gender"
            >
              <span className="absolute left-3 inset-y-0 flex items-center">
                <User size={18} className="text-gray-500 dark:text-gray-400" />
              </span>
              {selected !== 0
                ? options.find((opt) => opt.id === selected)?.name
                : "Select gender"}
              <ChevronDown
                className="text-gray-500 dark:text-gray-400 ml-3 shrink-0 mx-3 absolute top-3 right-0 pointer-events-none"
                size={20}
              />
            </Menu.Button>
            <Transition
              className="z-10 absolute top-full right-0 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 py-1.5 rounded shadow-lg overflow-hidden mt-1"
              enter="transition ease-out duration-100 transform"
              enterFrom="opacity-0 -translate-y-2"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-out duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Menu.Items className="font-medium text-sm text-gray-600 dark:text-gray-300 focus:outline-none divide-y-2 divide-gray-200/50 dark:divide-gray-700/50">
                {options
                  .filter((option) => option.id !== 0)
                  .map((option) => (
                    <Menu.Item key={option.id}>
                      {({ active }) => (
                        <button
                          className={`flex items-center w-full py-1 px-3 cursor-pointer capitalize ${
                            active ? "bg-gray-50 dark:bg-gray-700" : ""
                          } ${option.id === selected && "text-red-500"}`}
                          onClick={() => {
                            setSelected(option.id);
                            onSearchChange(option.name);
                          }}
                        >
                          <Check
                            className={`shrink-0 mr-2 text-red-500 ${
                              option.id !== selected && "invisible"
                            }`}
                            size={20}
                          />
                          <span>{option.name}</span>
                        </button>
                      )}
                    </Menu.Item>
                  ))}
              </Menu.Items>
            </Transition>
          </>
        )}
      </Menu>
      {renderError(id)}
    </div>
  );
}

const Tooltip = ({ text }: { text: string }) => (
  <div className="group relative inline-block ml-1">
    <Info size={16} className="text-gray-500 cursor-help" />
    <div className="opacity-0 bg-gray-800 text-white text-xs rounded-lg py-2 px-3 absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 group-hover:opacity-100 transition-opacity duration-300 w-48 pointer-events-none">
      {text}
      <svg
        className="absolute text-gray-800 h-2 w-full left-0 top-full"
        x="0px"
        y="0px"
        viewBox="0 0 255 255"
      >
        <polygon className="fill-current" points="0,0 127.5,127.5 255,0" />
      </svg>
    </div>
  </div>
);
