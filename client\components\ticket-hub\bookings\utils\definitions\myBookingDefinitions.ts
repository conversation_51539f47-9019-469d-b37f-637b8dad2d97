export interface Passenger {
  id?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  // Add any other passenger properties as needed
}

export interface Traveler {
  id: string;
  title?: string;
  firstName: string;
  lastName: string;
  nationality?: string;
  dateOfBirth?: string;
  gender?: string;
  documentType?: string;
  documentNumber?: string;
  issuingCountry?: string;
  expirationDate?: string;
  contactEmail?: string;
  contactPhone?: string;
  primaryContact?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface BookingTraveler {
  id: string;
  travelerId: string;
  bookingId: string;
  infoStatus: string;
  traveler: Traveler;
}

export interface Booking {
  reference: string;
  eTicketNumber: string;
  date: string;
  route: string;
  airline: string;
  passenger: string; // Kept for backward compatibility
  passengers?: Passenger[] | Passenger; // New property for multiple passengers
  travelers?: BookingTraveler[]; // Detailed travelers information
  agent: string; // This will be the full name of the agent who created the booking
  tripType: string;
  status: string;
  bookingAction: string;
  source: string;
  price: string;
  bookingSource: string;
  bookingStatus: string;
  createdAt: string;
  updatedAt: string;
  tripStatus?: string; // Added for trip status calculation
  referenceNumber?: string;
  user?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  };
}

export interface BookingResponse {
  data: Booking[];
  meta: {
    pagination: {
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
    };
  };
}

export interface BookingDetails {
  id: string;
  reference: string;
  eTicketNumber: string;
  date: string;
  route: string;
  airline: string;
  passenger: string;
  agent: string;
  tripType: string;
  status: string;
  bookingAction: string;
  source: string;
  bookingSource: string;
  bookingStatus: string;
  createdAt: string;
  updatedAt: string;
  referenceNumber?: string;
}

export interface BookingDetailsResponse {
  data: BookingDetails;
}
