import { PrismaClient, BookingEventType, BookingStatus, ActorType, Prisma } from '@prisma/client';
import { Request } from 'express';

import { prisma } from "../prisma";

type BookingEventData = {
  bookingId: string;
  eventType: BookingEventType;
  actorId?: string;
  actorType: ActorType;
  oldStatus?: BookingStatus;
  newStatus?: BookingStatus;
  reason?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
};

export class BookingEventLogger {
  /**
   * Logs a booking event
   */
  static async logEvent(data: BookingEventData, req?: Request) {
    const ipAddress = req?.ip || req?.connection.remoteAddress;
    const userAgent = req?.headers['user-agent'];

    // Create the base event data
    const eventData: Prisma.BookingHistoryLogCreateInput = {
      booking: { connect: { id: data.bookingId } },
      eventType: data.eventType,
      actorId: data.actorId || null,
      actorType: data.actorType,
      oldStatus: data.oldStatus || null,
      newStatus: data.newStatus || null,
      reason: data.reason || null,
      metadata: data.metadata as Prisma.InputJsonValue || {},
      ipAddress: ipAddress || null,
      userAgent: userAgent || null,
    };

    return prisma.bookingHistoryLog.create({
      data: eventData,
    });
  }

  /**
   * Logs a seat hold event
   */
  static async logSeatHeld(
    bookingId: string, 
    actorId: string, 
    actorType: ActorType, 
    holdType: 'QUICK_HOLD' | 'RESERVE', 
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'SEAT_HELD',
      actorId,
      actorType,
      newStatus: 'PENDING_APPROVAL',
      metadata: { 
        holdType, 
        holdDuration: holdType === 'QUICK_HOLD' ? 3600 : 900 
      },
    }, req);
  }

  /**
   * Logs a booking approval event
   */
  static async logBookingApproved(
    bookingId: string, 
    actorId: string, 
    actorType: ActorType, 
    notes?: string, 
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'BOOKING_APPROVED',
      actorId,
      actorType,
      newStatus: 'BOOKING_CONFIRMED',
      reason: notes,
    }, req);
  }

  /**
   * Logs a booking rejection event
   */
  static async logBookingRejected(
    bookingId: string, 
    actorId: string, 
    actorType: ActorType, 
    reason: string, 
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'BOOKING_REJECTED',
      actorId,
      actorType,
      newStatus: 'BOOKING_REJECTED',
      reason,
    }, req);
  }

  /**
   * Logs a booking cancellation event
   */
  static async logBookingCancelled(
    bookingId: string, 
    actorId: string, 
    actorType: ActorType, 
    reason?: string, 
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'BOOKING_CANCELLED',
      actorId,
      actorType,
      newStatus: 'CANCELLED_BY_USER',
      reason,
    }, req);
  }

  /**
   * Logs a booking timeout event
   */
  static async logBookingTimedOut(
    bookingId: string, 
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'BOOKING_TIMED_OUT',
      actorType: 'SYSTEM',
      newStatus: 'TIMED_OUT',
    }, req);
  }

  /**
   * Gets the latest event for a booking
   */
  static async getLatestEvent(bookingId: string) {
    return prisma.bookingHistoryLog.findFirst({
      where: { bookingId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Logs a new booking creation event
   */
  static async logBookingCreated(
    bookingId: string,
    actorId: string,
    actorType: ActorType,
    bookingType: 'QUICK_HOLD' | 'RESERVE',
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'BOOKING_CREATED',
      actorId,
      actorType,
      newStatus: bookingType === 'QUICK_HOLD' ? 'QUICK_HOLD' : 'PENDING_APPROVAL',
      metadata: { bookingType },
    }, req);
  }

  /**
   * Logs a payment completion event
   */
  static async logPaymentCompleted(
    bookingId: string,
    actorId: string,
    actorType: ActorType,
    paymentId: string,
    amount: number,
    currency: string,
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'PAYMENT_COMPLETED',
      actorId,
      actorType,
      metadata: {
        paymentId,
        amount,
        currency,
      },
    }, req);
  }

  /**
   * Logs a booking status change
   */
  static async logStatusChange(
    bookingId: string,
    actorId: string,
    actorType: ActorType,
    oldStatus: BookingStatus,
    newStatus: BookingStatus,
    reason?: string,
    req?: Request
  ) {
    return this.logEvent({
      bookingId,
      eventType: 'STATUS_CHANGED',
      actorId,
      actorType,
      oldStatus,
      newStatus,
      reason,
    }, req);
  }

  /**
   * Gets the complete timeline of events for a booking
   */
  static async getBookingTimeline(bookingId: string) {
    return prisma.bookingHistoryLog.findMany({
      where: { bookingId },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Gets extended booking timeline with related data
   */
  static async getExtendedBookingTimeline(bookingId: string) {
    return prisma.bookingHistoryLog.findMany({
      where: { bookingId },
      orderBy: { createdAt: 'asc' },
      include: {
        booking: {
          select: {
            id: true,
            ticketId: true,
            status: true
          }
        }
      },
    });
  }

  /**
   * Gets the current status of a booking
   */
  static async getCurrentStatus(bookingId: string): Promise<BookingStatus | null> {
    const latestEvent = await this.getLatestEvent(bookingId);
    return latestEvent?.newStatus || null;
  }
}
