// utils/association.ts
import { prisma } from '../prisma';

export async function getAssociatedAccountIds(userId: string, accountType: string) {
  if (accountType === 'masterOwner') {
    // Get all team members for this master
    const teamMembers = await prisma.teamMember.findMany({ where: { teamId: userId } });
    return [userId, ...teamMembers.map(tm => tm.id)];
  }
  if (accountType === 'masterUser') {
    // Find the master owner (creator) for this team member and all peers in the same team
    const teamMember = await prisma.teamMember.findUnique({
      where: { id: userId },
      select: { teamId: true, createdById: true },
    });
    if (!teamMember) return [userId];

    // Get all team members that belong to the same team (including the current user)
    const teamMembers = await prisma.teamMember.findMany({
      where: { teamId: teamMember.teamId },
      select: { id: true },
    });

    // The master owner who created the team members (acts as the owner for booking wallet)
    const masterOwnerId = teamMember.createdById;

    const ids: string[] = [userId];

    // Include master owner if present
    if (masterOwnerId) ids.push(masterOwnerId);

    // Include peer team members
    ids.push(...teamMembers.map((tm) => tm.id));

    // Return unique ids to avoid duplicates
    return Array.from(new Set(ids));
  }
  if (accountType === 'agencyOwner') {
    // Get all agents for this agency
    const agents = await prisma.agencyAgent.findMany({ where: { agencyId: userId } });
    return [userId, ...agents.map(a => a.id)];
  }
  if (accountType === 'agencyUser') {
    // Find the agency owner for this agent
    const agent = await prisma.agencyAgent.findUnique({ where: { id: userId } });
    if (!agent) return [userId];
    const agents = await prisma.agencyAgent.findMany({ where: { agencyId: agent.agencyId } });
    return [agent.agencyId, ...agents.map(a => a.id)];
  }
  // Affiliate or other: just this user
  return [userId];
}