import { AlertCircle } from "lucide-react";
import React, { useState } from "react";

export const Card = ({
  icon,
  title,
  children,
  newClass = "bg-gray-100 dark:bg-gray-700",
}: {
  icon: any;
  title: string;
  children: React.ReactNode;
  newClass?: string;
}) => (
  <div
    className={`${newClass} rounded-lg p-5 md:p-6 mb-6 shadow-md hover:shadow-lg`}
  >
    <h2 className="text-2xl mb-4 text-[#EE4544] flex items-center font-bold">
      {React.cloneElement(icon, { className: "mr-2" })}
      <span className="text-gray-700 dark:text-white">{title}</span>
    </h2>
    {children}
  </div>
);

export const InputField = ({
  id,
  label,
  placeholder,
  icon,
  tooltip,
  value,
  onChange,
  type,
  min,
  max,
  validationError,
  input,
  required = true,
  defaultValue = 0,
}: {
  id?: string;
  label: string;
  placeholder?: string;
  icon?: any;
  tooltip?: string;
  value?: string | number | undefined;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  min?: number;
  max?: number;
  validationError?: any;
  input?: any;
  required?: boolean;
  defaultValue?: string | number | undefined;
}) => {
  // If value is provided, we'll use controlled component pattern
  // Otherwise, we'll use defaultValue for uncontrolled components
  const inputProps = {
    placeholder,
    className: "w-full outline-none border-0 focus:border-0 border-none focus:border-none text-gray-700 dark:text-white dark:placeholder:text-gray-200 placeholder:text-opacity-70 disabled:text-gray-600 dark:disabled:text-gray-400 bg-gray-50 dark:bg-gray-600 rounded-lg py-2 px-3 shadow dark:shadow-inner focus:ring-2 focus:ring-red-500 focus:outline-none transition-all duration-300 no-arrows capitalize",
    id,
    min,
    max,
    type: type as 'text' | 'number' | 'email' | 'password' | 'search' | 'tel' | 'url' | 'date' | 'time' | 'datetime-local' | 'month' | 'week' | 'color' | 'range' | 'file' | 'hidden' | 'image' | 'radio' | 'checkbox' | 'submit' | 'reset' | 'button',
    inputMode: (type === "number" ? "numeric" : "text") as React.HTMLAttributes<HTMLInputElement>['inputMode'],
    pattern: type === "number" ? "[0-9]*" : undefined,
    onWheel: (e: React.WheelEvent<HTMLInputElement>) => (e.target as HTMLInputElement).blur(),
    ...(value !== undefined 
      ? { value: value ?? '' } // Controlled component
      : { defaultValue: defaultValue ?? '' } // Uncontrolled component
    ),
    onChange
  };

  return (
  <div className="flex-1">
    <label className="text-sm font-medium mb-2 flex items-center space-x-2">
      <span className="capitalize">{label}</span>
      {required && <span className="text-red-500">*</span>}
      {tooltip && <Tooltip text={tooltip} />}
    </label>
    {input ? (
      input
    ) : (
      <div className="relative">
        <input {...inputProps} />
        {icon && (
          <span className="absolute right-3 top-2.5 text-gray-400">{icon}</span>
        )}
      </div>
    )}
    <div className="text-sm mt-1 text-red-500">{validationError}</div>
  </div>
)};

export const Tooltip = ({
  text,
  className,
}: {
  text: string;
  className?: string;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative inline-block ml-2">
      <AlertCircle
        size={16}
        className={`cursor-help ${
          className ? className : "text-gray-800 dark:text-gray-200"
        }`}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
      />
      {isVisible && (
        <div className="absolute z-10 w-64 px-3 py-2 text-sm font-normal text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2">
          {text}
        </div>
      )}
    </div>
  );
};

export const EmptyState = ({
  icon,
  title,
  description,
}: {
  icon: any;
  title: string;
  description: string;
}) => (
  <div className="flex flex-col items-center justify-center py-8">
    <div className="bg-gray-100 dark:bg-gray-600 rounded-full p-4 mb-4">
      {icon}
    </div>
    <h3 className="text-xl font-semibold mb-2 text-gray-600 dark:text-white">
      {title}
    </h3>
    <p className="text-gray-400 text-center mb-4">{description}</p>
  </div>
);
