// server base url
import { API_VERSION } from '../constants/apiVersion';
const SERVER_URL = process.env.SERVER_URL;
// BASE URL
const BASE_URL = SERVER_URL + API_VERSION + "/check";

/**
 * Check endpoint URLs
 *
 * This object contains all the endpoint URLs related to checks.
 * Each endpoint is documented with a clear description of its purpose.
 */
const checkUrl = {
  check: BASE_URL,
  health: BASE_URL + "/health",
  ready: BASE_URL + "/ready",
  info: BASE_URL + "/info",
  status: BASE_URL + "/status",
  businessMetrics: BASE_URL + "/business-metrics",
  debugSentry: SERVER_URL + "/api/debug-sentry"
};

export default checkUrl;