/*
 * Time constants for both test and production modes
 * Test mode:
 * - 0-1 min: Can reactivate
 * - 1-4 min: Cannot login
 * - After 4 min: Auto delete
 *
 * Production mode:
 * - 0-30 days: Can reactivate
 * - 30-180 days: Cannot login
 * - After 180 days: Auto delete
 */
import moment from "moment";

/*
 * 1 second = 1000 milliseconds
 * 1 minute = 60 * 1000 = 60000 milliseconds
 * 1 hour = 60 * 60 * 1000 = 3,600,000 milliseconds
 * 1 day = 24 * 60 * 60 * 1000 = 86,400,000 milliseconds (this is 86400000)
 */
export const isTestMode = true;

export const oneMinute = 5; // default to 5 minutes
export const fourMinutes = 10; // default to 10 minutes
export const thirtyDays = 30; // default to 30 days
export const oneEightyDays = 180; // default to 180 days
export const oneDay = 24; // default to 1 day in hours

export const BOOKING_SESSION_TIMEOUT_MS = 10 * 60 * 1000; // 10 minutes

// Base time units
export const msInSecond = 1000;
export const msInMinute = msInSecond * 60;
export const msInHour = msInMinute * 60;
export const msInDay = msInHour * 24;

// Add booking timeout constant
export const BOOKING_TIMEOUT_MINUTES = 10;
export const BOOKING_TIMEOUT_MS = BOOKING_TIMEOUT_MINUTES * 60 * 1000;

// Time unit constants
export const MILLISECONDS = "milliseconds";
export const SECONDS = "seconds";
export const MINUTES = "minutes";
export const HOURS = "hours";
export const DAYS = "days";
export const WEEKS = "weeks";
export const MONTHS = "months";
export const YEARS = "years";

// Calculate unit based on mode : for test mode, 1 minute, for prod, 1 day
export const msPerUnit = isTestMode ? msInMinute : msInDay; //  per minute in test mode, per day in prod
export const unitOfTime = isTestMode ? MINUTES : DAYS; // unit of time in test mode is minutes, in prod is days
export const unitTimeForUnverified = HOURS; // unit of time in test mode is minutes, in prod is days

// Test mode timings (/1,4/ in minutes) and Production mode timings (/30,180/ in days)
export const DELETION_PERIOD_FOR_UNVERIFIED = oneDay;
export const REACTIVATION_PERIOD = isTestMode ? oneMinute : thirtyDays;
export const DELETION_PERIOD = isTestMode ? fourMinutes : oneEightyDays;

// Calculate the time thresholds based on mode
export const REACTIVATION = moment()
  .utc()
  .subtract(REACTIVATION_PERIOD, unitOfTime)
  .startOf("minute"); // Truncate to the start of the minute
export const REACTIVATION_THRESHOLD = REACTIVATION.toDate();

export const DELETION = moment()
  .utc()
  .subtract(DELETION_PERIOD, unitOfTime)
  .startOf("minute"); // Truncate to the start of the minute
export const DELETION_THRESHOLD = DELETION.toDate();

export const REACTIVATION_THRESHOLD_NUMBER = Number(REACTIVATION);
export const DELETION_THRESHOLD_NUMBER = Number(DELETION);

// Calculate how many days the user deactivated have passed since the user's deactivation date
export const calculateDeactivationDays = (date: Date): number => {
  const daysDeactivated = Math.floor((Date.now() - date.getTime()) / msPerUnit);
  return daysDeactivated;
};
