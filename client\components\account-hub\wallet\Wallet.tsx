"use client";
import React from "react";
import { DollarSign, Clock, Download, Search } from "lucide-react";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";

type CardProps = {
  title: string;
  icon: React.ReactNode;
  amount: string;
  children: React.ReactNode;
};

const Card: React.FC<CardProps> = ({ title, icon, amount, children }) => (
  <div className="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm dark:shadow-none">
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        {React.cloneElement(icon as React.ReactElement, {
          className: "w-5 h-5 text-gray-500 dark:text-gray-400",
        })}
        <span className="text-gray-500 dark:text-gray-400">{title}</span>
      </div>
    </div>
    <div className="mb-4">
      <h3 className="text-3xl font-bold text-gray-900 dark:text-white">
        {amount}
      </h3>
    </div>
    {children}
  </div>
);

const Button: React.FC<
  {
    variant?: "primary" | "secondary" | "outline";
    icon?: React.ReactNode;
    className?: string;
  } & React.ButtonHTMLAttributes<HTMLButtonElement>
> = ({ children, variant = "primary", icon, className = "", ...props }) => {
  const baseStyles =
    "px-4 py-2 rounded-lg transition-colors flex items-center space-x-2";
  const variantStyles = {
    primary: "bg-red-500 hover:bg-red-600 text-white",
    secondary:
      "bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white",
    outline:
      "bg-transparent border border-gray-300 hover:bg-gray-100 text-gray-700 dark:border-gray-500 dark:hover:bg-gray-700 dark:text-white",
  };

  return (
    <button
      className={`${baseStyles} ${variantStyles[variant]} ${className}`}
      {...props}
    >
      {icon && <span>{icon}</span>}
      <span>{children}</span>
    </button>
  );
};

const TableHeader: React.FC<{ columns: string[] }> = ({ columns }) => (
  <thead>
    <tr className="border-b border-gray-600">
      {columns.map((column) => (
        <th
          key={column}
          className="text-left px-4 py-3 text-sm font-medium text-gray-400"
        >
          {column}
        </th>
      ))}
    </tr>
  </thead>
);

const Wallet: React.FC = () => {
  const user = useAppSelector(selectUser);
  const balance =
    "creditBalance" in user && user.creditBalance
      ? parseFloat(user.creditBalance).toFixed(2)
      : "0.00";

  const tableColumns = [
    "Invoice ID",
    "Date",
    "Type",
    "Category",
    "Amount",
    "Details",
    "Status",
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-8 w-full">
        <h2 className="text-gray-900 dark:text-white text-xl md:text-2xl font-semibold capitalize">
          Credit Balance
        </h2>
      </div>
      <p className="text-gray-600 dark:text-gray-300 mb-8">
        View and manage your agency's credit balance for airline ticket
        bookings.
      </p>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card
          title="Current Balance"
          icon={<DollarSign className="w-5 h-5 text-gray-400" />}
          amount={`JOD ${balance}`}
        >
          <div className="flex space-x-3">
            <Button variant="primary">Add Credits</Button>
            <Button variant="secondary">Payout</Button>
          </div>
        </Card>

        <Card
          title="Monthly Usage"
          icon={<Clock className="w-5 h-5 text-gray-400" />}
          amount="JOD 0.000"
        >
          <div className="space-y-2">
            <p className="text-gray-400">Monthly Transactions: 0</p>
            <p className="text-green-500">Credits: JOD 0.000</p>
            <p className="text-red-500">Debits: JOD 0.000</p>
          </div>
        </Card>
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm dark:shadow-none">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Recent Transactions
          </h3>
          <Button variant="outline" icon={<Download className="w-4 h-4" />}>
            Export
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="relative w-full">
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search transactions..."
              className="w-full bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 text-gray-900 dark:text-white rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-red-500"
            />
          </div>
          <select className="bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 text-gray-900 dark:text-white rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 w-full sm:w-auto">
            <option value="all-time">All Time</option>
            <option value="this-month">This Month</option>
            <option value="last-month">Last Month</option>
            <option value="last-3-months">Last 3 Months</option>
          </select>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <TableHeader columns={tableColumns} />
            <tbody>{/* Transaction rows will be mapped here */}</tbody>
          </table>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center mt-4 text-sm text-gray-500 dark:text-gray-400 gap-4">
          <span>Showing 1 to 0 of 0 entries</span>
          <div className="flex items-center space-x-2">
            <Button variant="outline" className="px-2 py-1" disabled>
              Previous
            </Button>
            <span className="px-3 py-1 bg-red-500 text-white rounded dark:bg-red-600">
              1
            </span>
            <Button variant="outline" className="px-2 py-1" disabled>
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Wallet;
