export const flightClassOptions = [
  { id: 0, value: "economy" },
  { id: 1, value: "premium economy" },
  { id: 2, value: "business class" },
  { id: 3, value: "first class" },
];

export const carryOnAllowedOptions = [
  { id: 0, value: "0" },
  { id: 1, value: "1" },
  { id: 2, value: "2" },
  { id: 3, value: "3" },
  { id: 4, value: "4" },
  { id: 5, value: "5" },
  { id: 6, value: "6" },
  { id: 7, value: "7" },
  { id: 8, value: "8" },
  { id: 9, value: "9" },
  { id: 10, value: "10" },
];

export const carryOnWeightOptions = [
  { id: 1, value: "7" },
  { id: 2, value: "10" },
  { id: 3, value: "15" },
  { id: 4, value: "23" },
  { id: 5, value: "30" },
  { id: 6, value: "32" },
];

// Special option only used when Carry-On Allowed is 0
export const zeroCarryOnWeightOption = { id: 0, value: "0" };

export const checkedAllowedOptions = [
  { id: 0, value: "0" },
  { id: 1, value: "1" },
  { id: 2, value: "2" },
  { id: 3, value: "3" },
  { id: 4, value: "4" },
  { id: 5, value: "5" },
  { id: 6, value: "6" },
  { id: 7, value: "7" },
  { id: 8, value: "8" },
  { id: 9, value: "9" },
  { id: 10, value: "10" },
];

export const checkedWeightOptions = [
  { id: 1, value: "14" },
  { id: 2, value: "15" },
  { id: 3, value: "20" },
  { id: 4, value: "23" },
  { id: 5, value: "25" },
  { id: 6, value: "30" },
  { id: 7, value: "32" },
  { id: 8, value: "40" },
  { id: 9, value: "45" },
  { id: 10, value: "50" },
];

// Special option only used when Checked Allowed is 0
export const zeroCheckedWeightOption = { id: 0, value: "0" };

export const extraOffersNameOptions: { id: number; value: string }[] = [
  { id: 0, value: "Extra Checked Bag" },
  { id: 1, value: "Additional Carry-On Bag" },
  { id: 2, value: "Seat Selection" },
  { id: 3, value: "In-Flight Meals" },
  { id: 4, value: "Snacks and Beverages" },
  { id: 5, value: "Refundable Ticket" },
  { id: 6, value: "Changeable Ticket" },
  { id: 7, value: "Travel Insurance" },
  { id: 8, value: "Airport Lounge Access" },
  { id: 9, value: "In-Flight Wi-Fi" },
  { id: 10, value: "Priority Boarding" },
  { id: 11, value: "Priority Check-In" },
  { id: 12, value: "Fast Track Security" },
  { id: 13, value: "Airport Transfer Services" },
  { id: 14, value: "Excess Baggage" },
  { id: 15, value: "Sports Equipment Handling" },
  { id: 16, value: "Pet Travel" },
  { id: 17, value: "Special Assistance (SSR Needs)" },
];

export const extraOffersAvailableOptions = [
  { id: 0, value: "yes" },
  { id: 1, value: "no" },
  { id: 2, value: "charge" },
];

export const statusOptions = [
  {
    id: 0,
    value: "available",
  },
  {
    id: 1,
    value: "unavailable",
  },
];
