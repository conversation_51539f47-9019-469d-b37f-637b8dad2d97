{"name": "mosaic-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@headlessui/react": "^1.7.19", "@mui/joy": "^5.0.0-beta.36", "@mui/material": "^5.15.21", "@mui/material-nextjs": "^5.15.11", "@mui/x-date-pickers": "^7.8.0", "@reduxjs/toolkit": "^2.2.5", "@types/chart.js": "^2.9.41", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "antd": "^5.19.0", "axios": "^1.7.2", "chart.js": "^4.4.8", "chartjs-adapter-moment": "^1.0.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dayjs": "^1.11.11", "detectincognitojs": "^1.3.5", "dotenv": "^16.4.5", "html2pdf.js": "^0.10.3", "joi": "^17.13.3", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libphonenumber-js": "^1.12.6", "lodash": "^4.17.21", "lucide-react": "^0.411.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "next": "^14.0.3", "next-themes": "^0.2.1", "react": "18.2.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^7.2.0", "react-dom": "18.2.0", "react-flatpickr": "^3.10.13", "react-icons": "^5.2.1", "react-intersection-observer": "^9.13.0", "react-redux": "^9.1.2", "react-select": "^5.8.0", "react-window": "^1.8.10", "react-window-infinite-loader": "^1.0.9", "redux-persist": "^6.0.0", "sharp": "^0.33.4", "socket.io-client": "^4.7.5", "typescript": "^5.3.3", "use-debounce": "^10.0.1", "xlsx": "^0.18.5", "yarn": "1.22.22"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/lodash": "^4.17.6", "@types/react-flatpickr": "^3.8.11", "@types/react-window": "^1.8.8", "autoprefixer": "^10.4.16", "postcss": "^8.4.39", "tailwindcss": "^3.4.4"}, "packageManager": "yarn@1.22.22"}