import { prisma } from "../../prisma";
import { TeamMember } from '@prisma/client';
// import { TeamMember } from "../../types/prismaEnums";

/**
 * Returns all active master users (TeamMembers with master role).
 * Only returns users who are verified and accepted.
 */
export async function getActiveMasterUsers(): Promise<TeamMember[]> {
  return prisma.teamMember.findMany({
    where: {
      role: "master",
      roleType: "master_admin",
      verified: true,
      accountStatus: "accepted",
    },
    // Select all fields to match TeamMember type
  });
}
