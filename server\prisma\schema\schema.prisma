generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
  engineType      = "binary"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

/// MODELS ////
model User {
  id                    String                  @id @default(cuid())
  refId                 String?                 @unique @map("ref_id")
  firstName             String                  @map("first_name")
  lastName              String                  @map("last_name")
  username              String                  @unique
  agencyName            String?                 @unique @map("agency_name")
  email                 String                  @unique
  hashedPassword        String                  @map("hashed_password")
  nationality           String?
  dateOfBirth           String?
  gender                String?
  logo                  String?
  website               String?
  accountStatus         AccountStatus           @default(pending) @map("account_status")
  role                  Role                    @default(affiliate) @map("role")
  roleType              RoleType                @default(affiliate) @map("role_type")
  verified              Bo<PERSON>an                 @default(false)
  phoneNumber           String                  @map("phone_number")
  phoneNumberVerified   Boolean                 @default(true) @map("phone_number_verified")
  subscriptionStatus    SubscriptionStatus      @default(inactive) @map("subscription_status")
  lastLogin             DateTime?               @map("last_login")
  iataNo                String?                 @default("") @map("iata_no")
  commercialOperationNo String?                 @default("") @map("commercial_operation_no")
  createdAt             DateTime                @default(now()) @map("created_at")
  updatedAt             DateTime                @updatedAt @map("updated_at")
  deactivationDate      DateTime?               @map("deactivation_date")
  creditBalance         Decimal                 @default(10000.00)
  addressId             String?                 @unique @map("address_id")
  userAddressId         String?                 @unique
  address               UserAddress?            @relation(fields: [userAddressId], references: [id])
  creditTransactions    CreditTransaction[]
  myTickets             FlightTicket[]
  purchasedTickets      PurchasedFlightTicket[]
  agents                AgencyAgent[]
  ticketHistoryLogs     TicketHistoryLog[]
  Invitation            Invitation[]
  createdTeamMembers    TeamMember[]
  notifications         Notification[]
  webPushSubscriptions  WebPushSubscription[]
  bookings              Booking[]
  TicketAccess          TicketAccess[]
  payments              Payment[]

  @@index([email, username, createdAt], map: "user_main_idx")
  @@index([phoneNumber], map: "user_phone_idx")
  @@map("users")
}

model UserAddress {
  id          String       @id @default(cuid())
  country     String
  city        String
  street      String?
  user        User?
  TeamMember  TeamMember?
  AgencyAgent AgencyAgent?

  @@index([country, city], map: "address_location_idx")
  @@map("user_addresses")
}

model AgencyAgent {
  id                   String                @id @default(cuid())
  refId                String?               @unique @map("ref_id")
  firstName            String                @map("first_name")
  lastName             String                @map("last_name")
  username             String                @unique
  agencyName           String?               @map("agency_name")
  email                String                @unique
  hashedPassword       String                @map("hashed_password")
  nationality          String
  dateOfBirth          String?
  access               Boolean               @default(true)
  role                 Role                  @default(agency) @map("role")
  subRole              AgentRole?            @default(accountant) @map("sub_role")
  roleType             RoleType?             @default(agency_accountant) @map("role_type")
  department           Department
  status               TeamMemberStatus      @default(active)
  accountStatus        AccountStatus         @default(accepted) @map("account_status")
  verified             Boolean               @default(true)
  lastLogin            DateTime?             @map("last_login")
  createdAt            DateTime              @default(now()) @map("created_at")
  updatedAt            DateTime              @updatedAt @map("updated_at")
  deactivationDate     DateTime?             @map("deactivation_date")
  agencyId             String                @map("agency_id")
  addressId            String?               @unique @map("address_id")
  userAddressId        String?               @unique
  agency               User                  @relation(fields: [agencyId], references: [id], onDelete: Restrict)
  address              UserAddress?          @relation(fields: [userAddressId], references: [id])
  flightTicket         FlightTicket[]
  bookedSeats          BookedFlightSeat[]
  ticketHistoryLogs    TicketHistoryLog[]
  notifications        Notification[]
  webPushSubscriptions WebPushSubscription[]
  bookings             Booking[]

  @@index([agencyId, email], map: "agent_agency_idx")
  @@index([createdAt], map: "agent_created_idx")
  @@map("agency_agents")
}

model Team {
  id          String       @id @default(cuid())
  name        String
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")
  teamMembers TeamMember[]
  Invitation  Invitation[]

  @@index([name], map: "team_name_idx")
}

model TeamMember {
  id                    String                @id @default(cuid())
  refId                 String?               @unique @map("ref_id")
  username              String                @unique
  firstName             String                @map("first_name")
  lastName              String                @map("last_name")
  email                 String                @unique
  password              String
  agencyName            String?               @map("agency_name")
  nationality           String?
  dateOfBirth           String?
  gender                String?
  logo                  String?
  website               String?
  role                  Role                  @default(affiliate) @map("role")
  subRole               TeamMemberRole?       @default(accountant) @map("sub_role")
  roleType              RoleType?             @default(affiliate) @map("role_type")
  department            Department
  status                TeamMemberStatus      @default(active)
  accountStatus         AccountStatus         @default(accepted) @map("account_status")
  verified              Boolean               @default(true)
  lastLogin             DateTime?             @map("last_login")
  createdAt             DateTime              @default(now()) @map("created_at")
  updatedAt             DateTime              @updatedAt @map("updated_at")
  deactivationDate      DateTime?             @map("deactivation_date")
  addressId             String?               @unique @map("address_id")
  teamId                String                @map("team_id")
  createdById           String?               @map("created_by_id")
  createdByTeamMemberId String?               @map("created_by_team_member_id")
  userAddressId         String?               @unique
  address               UserAddress?          @relation(fields: [userAddressId], references: [id])
  team                  Team                  @relation(fields: [teamId], references: [id], onDelete: Restrict)
  invitation            Invitation[]
  createdBy             User?                 @relation(fields: [createdById], references: [id])
  createdByTeamMember   TeamMember?           @relation("TeamMemberCreatedBy", fields: [createdByTeamMemberId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  notifications         Notification[]
  webPushSubscriptions  WebPushSubscription[]
  bookings              Booking[]
  createdTeamMembers    TeamMember[]          @relation("TeamMemberCreatedBy")

  @@index([teamId], map: "team_idx")
  @@index([email, teamId], map: "member_team_idx")
  @@index([createdById], map: "member_creator_idx")
  @@index([createdByTeamMemberId], map: "created_by_team_member_idx")
}

model Invitation {
  id           String           @id @default(cuid())
  token        String           @unique
  email        String
  status       InvitationStatus @default(pending)
  role         RoleType
  department   Department
  expiresAt    DateTime         @map("expires_at")
  acceptedAt   DateTime?        @map("accepted_at")
  revokedAt    DateTime?        @map("revoked_at")
  createdAt    DateTime         @default(now()) @map("created_at")
  teamMemberId String           @map("team_member_id")
  teamId       String           @map("team_id")
  invitedById  String           @map("invited_by_id")
  teamMember   TeamMember       @relation(fields: [teamMemberId], references: [id], onDelete: Restrict)
  team         Team             @relation(fields: [teamId], references: [id], onDelete: Restrict)
  invitedBy    User             @relation(fields: [invitedById], references: [id], onDelete: Restrict)

  @@unique([email, teamId], name: "unique_invitation_per_team")
  @@index([teamMemberId], map: "team_member_idx")
  @@index([invitedById], map: "invited_by_idx")
  @@index([token, email], map: "invitation_token_idx")
  @@index([teamId, status], map: "invitation_team_idx")
}

model Notification {
  id            String        @id @default(cuid()) @map("notification_id")
  userId        String        @map("user_id")
  type          String        @db.VarChar(50)
  title         String        @db.VarChar(255)
  message       String
  read          Boolean       @default(false)
  createdAt     DateTime      @default(now()) @map("created_at")
  relatedId     String?       @map("related_id")
  link          String?       @map("link") @db.VarChar(500)
  priority      Int           @default(1) @map("priority")
  agencyAgentId String?
  teamMemberId  String?
  bookingId     String?
  user          User          @relation(fields: [userId], references: [id])
  ticket        FlightTicket? @relation("TicketNotifications", fields: [relatedId], references: [id])
  AgencyAgent   AgencyAgent?  @relation(fields: [agencyAgentId], references: [id])
  TeamMember    TeamMember?   @relation(fields: [teamMemberId], references: [id])
  booking       Booking?      @relation("BookingNotifications", fields: [bookingId], references: [id])

  @@index([userId], map: "notification_user_idx")
  @@index([relatedId], map: "notification_ticket_idx")
  @@index([agencyAgentId], map: "notification_agent_idx")
  @@index([teamMemberId], map: "notification_team_member_idx")
  @@index([createdAt])
  @@index([priority])
  @@index([bookingId])
}

model WebPushSubscription {
  id            String       @id @default(cuid())
  endpoint      String
  userId        String
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  teamMemberId  String?
  agencyAgentId String?
  bookingId     String?
  user          User         @relation(fields: [userId], references: [id], onDelete: Restrict)
  TeamMember    TeamMember?  @relation(fields: [teamMemberId], references: [id])
  AgencyAgent   AgencyAgent? @relation(fields: [agencyAgentId], references: [id])
  Booking       Booking?     @relation(fields: [bookingId], references: [id])

  @@index([userId])
  @@index([teamMemberId], map: "subscription_team_member_idx")
  @@index([agencyAgentId], map: "subscription_agent_idx")
  @@index([bookingId])
  @@map("web_push_subscriptions")
}

model RateLimit {
  key       String   @id
  count     Int
  expiresAt DateTime

  @@index([expiresAt])
}

model JobLock {
  jobName   String   @id
  lockedAt  DateTime
  expiresAt DateTime
}

model CreditTransaction {
  id           String   @id @default(cuid())
  userId       String
  amount       Decimal
  type         String
  referenceId  String?
  balanceAfter Decimal
  createdAt    DateTime @default(now())
  user         User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model PurchasedFlightTicket {
  id        String       @id @default(cuid())
  userId    String
  ticketId  String       @unique
  seats     Int
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  ticket    FlightTicket @relation(fields: [ticketId], references: [id], onDelete: Restrict)
  user      User         @relation(fields: [userId], references: [id], onDelete: Restrict)

  @@index([userId])
  @@map("purchasedFlightTickets")
}

model BookedFlightSeat {
  id             String                 @id @default(cuid())
  flightTicketId String
  seatStatus     BookedFlightSeatStatus @default(booked)
  totalPrice     Decimal                @db.Decimal(10, 2)
  travelerId     String?                @unique
  bookedByAgency String
  bookedByAgent  String?
  bookingId      String
  seatNumber     String?
  flightClass    String?
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt
  traveler       Traveler?              @relation(fields: [travelerId], references: [id])
  agencyAgent    AgencyAgent?           @relation(fields: [bookedByAgent], references: [id])
  booking        Booking                @relation(fields: [bookingId], references: [id])
  flightTicket   FlightTicket           @relation(fields: [flightTicketId], references: [id], onDelete: Restrict)

  @@index([flightTicketId, seatStatus])
  @@index([bookedByAgent])
  @@index([bookingId])
  @@index([travelerId])
  @@map("bookedFlightSeats")
}

model FlightTicket {
  id                String                 @id @default(cuid())
  refId             String                 @unique
  ticketStatus      String                 @default("pending")
  previousStatus    String?
  description       String?
  seats             Int
  remainingSeats    Int
  departureId       String?                @unique
  arrivalId         String?                @unique
  flightDate        String
  departureTime     String?
  arrivalTime       String?
  duration          String?
  stops             Int
  ownerId           String
  agencyAgentId     String?
  updated           Boolean                @default(false)
  createdBy         String?
  archived          Boolean                @default(false)
  archivedAt        DateTime?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  departure         FlightLocation?        @relation("TicketDepartures", fields: [departureId], references: [id])
  arrival           FlightLocation?        @relation("TicketArrivals", fields: [arrivalId], references: [id])
  owner             User                   @relation(fields: [ownerId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  agencyAgent       AgencyAgent?           @relation(fields: [agencyAgentId], references: [id], onUpdate: Restrict)
  bookedSeats       BookedFlightSeat[]
  flightClasses     FlightClass[]
  segments          FlightSegment[]
  purchasedSeats    PurchasedFlightTicket?
  ticketHistoryLogs TicketHistoryLog[]     @relation("TicketHistoryLogs")
  notifications     Notification[]         @relation("TicketNotifications")
  ticketAccess      TicketAccess[]
  Booking           Booking[]
  manifestId        String?                @unique @map("manifestId") // Must be unique for one-to-one
  manifest          Manifest?              @relation(fields: [manifestId], references: [manifestId], onDelete: SetNull, onUpdate: Cascade)

  @@index([ownerId], map: "flight_ticket_owner_idx")
  @@index([agencyAgentId], map: "flight_ticket_agency_agent_idx")
  @@index([flightDate], map: "ticket_flight_date_idx")
  @@index([ticketStatus, flightDate, seats], map: "ticket_availability_idx")
  @@map("flightTickets")
}

model Manifest {
  id             String        @id @default(cuid())
  manifestId     String        @unique
  manifestStatus String        @default("PENDING")
  submittedAt    DateTime?
  flightDate     String
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  flightTicket   FlightTicket? // One-to-one relation to FlightTicket

  @@index([manifestId], map: "manifest_manifest_idx")
  @@map("manifests")
}

model TicketHistoryLog {
  id            String       @id @default(cuid())
  ticketId      String
  changeType    String
  changeDetails String?
  oldValue      Json?
  newValue      Json?
  agencyId      String?
  agencyAgentId String?
  changedAt     DateTime     @default(now())
  ticket        FlightTicket @relation("TicketHistoryLogs", fields: [ticketId], references: [id], onDelete: Restrict)
  agency        User?        @relation(fields: [agencyId], references: [id])
  agencyAgent   AgencyAgent? @relation(fields: [agencyAgentId], references: [id])

  @@index([ticketId], map: "ticket_history_log_ticket_idx")
  @@index([agencyId], map: "ticket_history_log_agency_idx")
  @@index([agencyAgentId], map: "ticket_history_log_agency_agent_idx")
  @@map("ticketHistoryLogs")
}

model FlightClass {
  id             String             @id @default(cuid())
  type           String
  carryOnAllowed Int
  carryOnWeight  Float
  checkedAllowed Int
  checkedWeight  Float
  checkedFee     Float
  additionalFee  Float
  flightTicketId String
  extraOffers    FlightExtraOffer[]
  flightTicket   FlightTicket       @relation(fields: [flightTicketId], references: [id], onDelete: Restrict)
  price          FlightPrice?

  @@index([flightTicketId], map: "flight_class_ticket_idx")
  @@map("flightClasses")
}

model FlightExtraOffer {
  id            String      @id @default(cuid())
  flightClassId String
  name          String?
  available     String?
  flightClass   FlightClass @relation(fields: [flightClassId], references: [id], onDelete: Restrict)

  @@index([flightClassId], map: "flight_extra_offer_class_idx")
  @@map("flightExtraOffer")
}

model FlightPrice {
  id            String      @id @default(cuid())
  adult         Float
  child         Float
  infant        Float
  tax           Float?
  currency      String      @default("JOD")
  flightClassId String      @unique
  flightClass   FlightClass @relation(fields: [flightClassId], references: [id], onDelete: Restrict)

  @@map("flightPrices")
}

model FlightSegment {
  id             String         @id @default(cuid())
  flightTicketId String
  flightNumber   String
  carrier        String
  departureId    String         @unique
  arrivalId      String         @unique
  departureTime  String
  arrivalTime    String
  duration       String
  flightTicket   FlightTicket   @relation(fields: [flightTicketId], references: [id], onDelete: Restrict)
  departure      FlightLocation @relation("SegmentDepartures", fields: [departureId], references: [id], onDelete: Restrict)
  arrival        FlightLocation @relation("SegmentArrivals", fields: [arrivalId], references: [id])

  @@index([flightTicketId], map: "flight_segment_ticket_idx")
  @@index([departureTime, arrivalTime], map: "segment_time_idx")
  @@map("flightSegments")
}

model FlightLocation {
  id                String          @id @default(cuid())
  airportCode       String
  country           String
  city              String
  airport           String
  ticketDepartures  FlightTicket[]  @relation("TicketDepartures")
  ticketArrivals    FlightTicket[]  @relation("TicketArrivals")
  segmentDepartures FlightSegment[] @relation("SegmentDepartures")
  segmentArrivals   FlightSegment[] @relation("SegmentArrivals")

  @@index([airportCode], map: "location_airport_code_idx")
  @@map("flightLocations")
}

model TicketAccess {
  id           String       @id @default(cuid())
  ticketId     String
  teamMemberId String
  accessLevel  AccessLevel
  createdById  String
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  ticket       FlightTicket @relation(fields: [ticketId], references: [id])
  createdBy    User         @relation(fields: [createdById], references: [id])

  @@index([ticketId, teamMemberId], map: "ticket_access_composite_idx")
  @@index([createdById])
}

model Booking {
  id                   String                @id @default(cuid())
  ticketId             String
  userId               String?
  agencyAgentId        String?
  teamMemberId         String?
  sellerAgencyId       String?
  buyerAgencyId        String?
  source               BookingSource
  type                 BookingType
  status               BookingStatus
  tripType             TripType              @default(ONE_WAY)
  requestId            String                @unique
  referenceNumber      String?
  totalSeats           Int                   @default(1)
  totalAmount          Decimal               @default(0) @db.Decimal(10, 2)
  initialHoldType      BookingType           @default(QUICK_HOLD)
  timerDuration        Int
  timerStartedAt       DateTime              @default(now())
  expiresAt            DateTime
  timedOutAt           DateTime?
  cancellationReason   String?
  statusReason         String?
  transactionId        String?               @unique
  transactionDate      DateTime?
  paymentCompletedAt   DateTime?
  meta                 Json?
  createdAt            DateTime?             @default(now())
  updatedAt            DateTime?             @updatedAt
  ticket               FlightTicket          @relation(fields: [ticketId], references: [id])
  user                 User?                 @relation(fields: [userId], references: [id])
  agencyAgent          AgencyAgent?          @relation(fields: [agencyAgentId], references: [id])
  TeamMember           TeamMember?           @relation(fields: [teamMemberId], references: [id])
  eTickets             ETicket[]             @relation("BookingETickets")
  payment              Payment?
  notifications        Notification[]        @relation("BookingNotifications")
  travelers            BookingTraveler[]
  bookedSeats          BookedFlightSeat[]
  webPushSubscriptions WebPushSubscription[]
  bookingHistoryLogs   BookingHistoryLog[]
  Receipt              Receipt[]

  @@index([ticketId], map: "booking_ticket_idx")
  @@index([userId], map: "booking_user_idx")
  @@index([requestId], map: "booking_request_id_idx")
  @@index([agencyAgentId], map: "booking_agency_agent_idx")
  @@index([teamMemberId], map: "booking_team_member_idx")
  @@index([userId, status], map: "booking_user_status_idx")
  @@index([status, expiresAt], map: "booking_status_expiry_idx")
  @@index([sellerAgencyId], map: "booking_seller_agency_idx")
  @@index([buyerAgencyId], map: "booking_buyer_agency_idx")
  @@index([sellerAgencyId, status], map: "booking_seller_status_idx")
  @@index([buyerAgencyId, status], map: "booking_buyer_status_idx")
  @@index([status], map: "booking_status_idx")
  @@index([createdAt], map: "booking_created_at_idx")
  @@map("bookings")
}

model Payment {
  id               String        @id @default(cuid())
  bookingId        String        @unique
  amount           Decimal       @db.Decimal(10, 2)
  paymentMethod    String?
  paymentMeta      Json?
  paidByUserId     String?
  currency         String        @default("JOD") @db.VarChar(3)
  paymentStatus    PaymentStatus @default(PENDING)
  paymentReference String?
  idempotencyKey   String?       @unique
  refundedAmount   Decimal?      @default(0) @db.Decimal(10, 2)
  refundReason     String?
  retryCount       Int           @default(0)
  lastAttempt      DateTime?
  errorMessage     String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  booking          Booking       @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  paidByUser       User?         @relation(fields: [paidByUserId], references: [id], onDelete: SetNull)

  @@index([bookingId])
  @@index([paidByUserId])
  @@map("payments")
}

model BookingHistoryLog {
  id         String           @id @default(cuid())
  bookingId  String
  eventType  BookingEventType @default(STATUS_CHANGED)
  changeType String?
  oldStatus  BookingStatus?
  newStatus  BookingStatus?
  actorType  ActorType
  actorId    String?
  reason     String?
  metadata   Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime?        @updatedAt
  booking    Booking          @relation(fields: [bookingId], references: [id], onDelete: Restrict)

  @@index([bookingId])
  @@index([eventType])
  @@index([createdAt])
  @@index([actorId])
  @@map("booking_history_logs")
}

model Traveler {
  id             String                @id @default(cuid())
  title          CustomerInfoTitle?
  firstName      String?
  lastName       String?
  nationality    String?
  dateOfBirth    DateTime?
  gender         String?
  documentType   CustomerDocumentType?
  documentNumber String?
  issuingCountry String?
  expirationDate DateTime?
  contactEmail   String?
  contactPhone   String?
  primaryContact Boolean?              @default(false)
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @updatedAt
  type           TravelerType          @default(ADULT)
  bookings       BookingTraveler[]
  bookedSeats    BookedFlightSeat[]

  @@index([documentNumber, documentType])
  @@map("travelers")
}

model BookingTraveler {
  id         String              @id @default(cuid())
  bookingId  String
  travelerId String
  infoStatus TravelerInfoStatus? @default(PLACEHOLDER)
  booking    Booking             @relation(fields: [bookingId], references: [id])
  traveler   Traveler            @relation(fields: [travelerId], references: [id])

  @@unique([bookingId, travelerId])
  @@index([travelerId])
}

model ETicket {
  id             String        @id @default(cuid())
  bookingId      String
  eTicketNumber  String        @unique
  fileUrl        String?
  meta           Json?
  issuedAt       DateTime      @default(now())
  issuedBy       String?
  ticketType     TicketType
  status         ETicketStatus @default(ISSUED)
  voidedAt       DateTime?
  voidReason     String?
  reissuedFromId String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  Booking        Booking       @relation("BookingETickets", fields: [bookingId], references: [id])
  reissuedFrom   ETicket?      @relation("ReissueChain", fields: [reissuedFromId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  reissues       ETicket[]     @relation("ReissueChain")
  receipt        Receipt?

  @@index([reissuedFromId])
  @@index([bookingId])
}

model Receipt {
  id            String        @id @default(cuid())
  bookingId     String
  eTicketId     String?       @unique
  receiptNumber String        @unique
  fileUrl       String?
  meta          Json?
  issuedAt      DateTime      @default(now())
  issuedBy      String?
  receiptType   ReceiptType
  status        ReceiptStatus @default(ISSUED)
  voidedAt      DateTime?
  voidReason    String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  Booking       Booking       @relation(fields: [bookingId], references: [id])
  ETicket       ETicket?      @relation(fields: [eTicketId], references: [id])

  @@index([bookingId])
  @@index([eTicketId])
}

/// ENUMS ////
enum Role {
  master
  agency
  affiliate
}

enum TeamMemberRole {
  admin
  moderator
  accountant
}

enum AgentRole {
  admin
  accountant
  operation
  sales
}

enum RoleType {
  master_owner
  master_admin
  master_moderator
  master_accountant
  agency_owner
  agency_admin
  agency_accountant
  agency_operation
  agency_sales
  affiliate
}

enum AccountStatus {
  accepted
  pending
  rejected
  suspended
  deactivated
  disabled
}

enum SubscriptionStatus {
  active
  inactive
}

enum TeamMemberStatus {
  active
  inactive
}

enum Department {
  customer_support
  management
  finance
  marketing
  sales
  it
  operations
}

enum InvitationStatus {
  pending
  accepted
  expired
  rejected
  revoked
}

enum BookedFlightSeatStatus {
  booked
  onHold
  canceled
}

enum CustomerInfoTitle {
  MR
  MRS
  MS
}

enum CustomerDocumentType {
  passport
  id_card
}

enum AccessLevel {
  READ
  WRITE
  ADMIN
}

enum BookingType {
  QUICK_HOLD
  SUBMIT_BOOKING
}

enum TripType {
  ONE_WAY
  ROUND_TRIP
}

enum BookingSource {
  INTERNAL
  THIRD_PARTY
}

enum BookingStatus {
  QUICK_HOLD
  TIMED_OUT
  PENDING_APPROVAL
  BOOKING_CONFIRMED
  BOOKING_REJECTED
  CANCELLED_BY_USER
  CANCELLED_BY_SYSTEM
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum BookingEventType {
  SEAT_HELD
  BOOKING_CREATED
  PAYMENT_INITIATED
  PAYMENT_COMPLETED
  PAYMENT_FAILED
  BOOKING_APPROVED
  BOOKING_REJECTED
  BOOKING_CANCELLED
  BOOKING_TIMED_OUT
  SEAT_RELEASED
  STATUS_CHANGED
}

enum ActorType {
  AGENT
  ADMIN
  SYSTEM
  CUSTOMER
}

enum TravelerType {
  ADULT
  CHILD
  INFANT
}

enum TravelerInfoStatus {
  PLACEHOLDER
  COMPLETED
}

enum TicketType {
  INTERNAL
  THIRD_PARTY
}

enum ETicketStatus {
  ISSUED
  VOIDED
  REISSUED
  CANCELLED
}

enum ReceiptType {
  INTERNAL
  THIRD_PARTY
}

enum ReceiptStatus {
  ISSUED
  VOIDED
  CANCELLED
}
