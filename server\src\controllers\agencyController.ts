import { AuthRequest } from "../utils/definitions";
import { Response } from "express";
import { AgentRole, Department, Prisma, RoleType, Role } from "@prisma/client";
// import { AgentRole, Department, RoleType, Role } from "../types/prismaEnums";
// import { validateAgentMember } from "../utils/validators/teamValidator";
import bcrypt from "bcrypt";
import { PrismaClient } from "@prisma/client";
import { hasPermission, PERMISSIONS, UserRole } from "../utils/types/auth";
import { getCreatedTimeRange } from "../utils/functions";
import generateUserRefId from "../utils/generateUserRefId";
import { generateRoleType } from "../models/team.model";
import { validatePassword } from "../utils/validators/passwordValidator";
import { validateAgentMember } from "../utils/validators/teamValidation";
import { sendInvitationEmail } from "../utils/email/sendInvitation";
import { prisma } from "../prisma";

interface DateRange {
  from?: string;
  to?: string;
}

// create agent
export const createAgent = async (req: AuthRequest, res: Response) => {
  try {
    const {
      firstName,
      lastName,
      username,
      email,
      password,
      subRole,
      department,
      agencyId,
    } = req.body;

    // Validate required fields
    if (
      !firstName ||
      !lastName ||
      !email ||
      !password ||
      !department ||
      !subRole ||
      !agencyId
    ) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields",
        errors: [
          !firstName && {
            field: "firstName",
            message: "First name is required",
          },
          !lastName && { field: "lastName", message: "Last name is required" },
          !email && { field: "email", message: "Please enter your email" },
          !password && {
            field: "password",
            message: "Please enter your password",
          },
          !subRole && { field: "subRole", message: "Role is required" },
          !department && {
            field: "department",
            message: "Department is required",
          },
          !agencyId && {
            field: "agencyId",
            message: "Agency ID is required",
          },
        ].filter(Boolean),
      });
    }

    // Determine the actual agency ID to use
    const actualAgencyId =
      req.user?.role === "master"
        ? agencyId // Use provided agencyId for master users
        : req.user?.id || req?.agencyAgent?.agencyId; // Use existing logic for agency users

    // Check if agency id exists
    if (!actualAgencyId) {
      return res.status(400).json({
        success: false,
        message: "Agency ID is not found",
      });
    }

    // Check the current number of agents
    const currentAgentsCount = await prisma.agencyAgent.count({
      where: {
        agencyId: actualAgencyId,
        role: Role.agency,
      },
    });

    if (currentAgentsCount >= 20) {
      return res.status(400).json({
        success: false,
        message: "Maximum limit of 20 agents has been reached",
        errors: [
          {
            field: "agentLimit",
            message:
              "Cannot add more agents. Maximum limit of 20 agents has been reached",
          },
        ],
      });
    }

    // For agents, mainRole is always AGENCY
    const role = Role.agency;

    // Convert role to AgentRole and validate it
    const subRoleAgent = subRole as AgentRole;
    if (
      subRoleAgent &&
      !Object.values(AgentRole).includes(subRoleAgent as any)
    ) {
      return res.status(400).json({
        message: "Invalid role type mapping for the provided role",
        errors: [
          {
            field: "roleType",
            message: "Invalid role type mapping for the provided role",
          },
          {
            field: "subRole",
            message: `Invalid role type for subRole: ${subRole}`,
          },
        ],
      });
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: "Password validation failed",
        errors: passwordValidation.errors.map((error) => ({
          field: "password",
          message: error,
        })),
      });
    }

    // Validate and sanitize agent input
    const validatedData = validateAgentMember(
      {
        firstName,
        lastName,
        email,
        password,
        role: role,
        subRole: subRoleAgent,
        department,
      },
      req.user?.role as any
    );

    if (validatedData.errors.length > 0) {
      return res.status(400).json({
        message: validatedData.errors[0].message,
        errors: validatedData.errors,
      });
    }

    // let validationErrors = { ...validatedData.validationErrors };

    // // Return if there are any validation errors
    // if (Object.keys(validationErrors).length > 0) {
    //   return res.status(400).json({
    //     success: false,
    //     errors: validationErrors,
    //     message: validatedData.validationErrors,
    //   });
    // }

    const requiredPermission = PERMISSIONS.MANAGE_TEAM.name;
    const userRoleType =
      (req.user?.roleType as UserRole) ??
      (req?.agencyAgent?.roleType as RoleType);

    if (!hasPermission(userRoleType, requiredPermission)) {
      return res.status(403).json({
        message: "Forbidden: You don't have permission to perform this action.",
      });
    }
    const agencyData = await prisma.user.findFirst({
      where: { id: actualAgencyId },
      select: { agencyName: true },
    });
    const agencyName = agencyData?.agencyName;

    // Create username with fallback for undefined values
    const usernameParts = [
      validatedData.firstName?.toLowerCase() || "",
      validatedData.lastName?.toLowerCase() || "",
      agencyName?.toLowerCase(),
    ].filter(Boolean); // Remove empty strings

    const userName = usernameParts.join("_");
    if (!userName) {
      return res.status(400).json({
        success: false,
        message: "Unable to generate username - missing required information",
      });
    }

    // Check if username already exists within the current agency
    const existingUser = await prisma.agencyAgent.findFirst({
      where: {
        username: userName,
      },
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message:
          "Username already exists. Please use a different name combination.",
      });
    }

    // Check if user already exists with sanitized email
    const existingAgent = await prisma.agencyAgent.findUnique({
      where: { email: validatedData.email },
    });

    if (existingAgent) {
      return res.status(400).json({
        message: "An agent with this email already exists",
      });
    }

    // Create everything in a transaction
    const result = await prisma.$transaction(async (tx: any) => {
      // Hash password
      if (!validatedData.password) {
        throw new Error("Please enter your password");
      }

      const hashedPassword = await bcrypt.hash(validatedData.password, 10);

      // Determine the roleType based on the role and userRoles
      const roleType = generateRoleType(Role.agency, subRoleAgent as any);

      // Generate refId for the new agent
      const refId = await generateUserRefId();

      const agent = await prisma.agencyAgent.create({
        data: {
          refId,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          username: userName,
          email: validatedData.email,
          hashedPassword,
          role: role,
          subRole: subRoleAgent as any,
          roleType: roleType,
          department: validatedData.department as Department,
          agencyName: agencyName,
          nationality:
            req.user?.nationality ?? req?.agencyAgent?.nationality ?? "",
          dateOfBirth:
            req.user?.dateOfBirth ?? req?.agencyAgent?.dateOfBirth ?? "",
          status: "active",
          agency: {
            connect: {
              id: actualAgencyId,
            },
          },
        },
        include: {
          agency: true,
        },
      });

      return { agent };
    });

    // Still send invitation email to notify team member of their account creation
    await sendInvitationEmail(validatedData.email, req.body);

    res.status(201).json({
      success: true,
      message: "Agent added successfully",
      agent: result.agent,
    });
  } catch (error: any) {
    console.error("Error creating agent:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while creating the agent",
    });
  }
};

// Update agent
export const updateAgent = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const {
      refId,
      firstName,
      lastName,
      username,
      email,
      password,
      role,
      subRole,
      department,
      status,
      accountStatus,
    } = req.body;

    if (!id) {
      return res.status(400).json({ message: "Agent ID is required" });
    }

    // Before validation, fetch the agent
    const agent = await prisma.agencyAgent.findUnique({ where: { id } });
    if (!agent) {
      return res.status(404).json({ message: "Agent not found" });
    }

    if (agent.username === username) {
      return res.status(400).json({
        success: false,
        message:
          "Username already exists. Please use a different name combination.",
      });
    }
     // Check if email is being updated and if it already exists for another agent
     if (email && email !== agent.email) {
      const emailExists = await prisma.agencyAgent.findFirst({
        where: {
          email: email,
          id: { not: id } // Exclude current agent from the check
        }
      });

      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: "An agent with this email already exists"
        });
      }
    }

    // Use request value if present, otherwise fallback to existing
    const roleToUse = role ?? agent.role;
    const subRoleToUse = subRole ?? agent.subRole;  

    // Only validate fields that are being updated
    if (firstName || lastName || email || password || subRole || department) {
      const validatedData = validateAgentMember(
        {
          firstName,
          lastName,
          email,
          password,
          role: roleToUse,
          subRole: subRoleToUse as any,
          department,
        },
        req.user?.role as any
      );

      if (validatedData.errors.length > 0) {
        return res.status(400).json({
          message: validatedData.errors[0].message,
          errors: validatedData.errors,
        });
      }
    }

    // if (validatedData.errors.length > 0) {
    //   return res.status(400).json({
    //     message: validatedData.errors[0].message,
    //     errors: validatedData.errors,
    //     validationErrors: validatedData.errors,
    //     success: false,
    //   });
    // }

    // let validationErrors = { ...validatedData.validationErrors };

    // // Return if there are any validation errors
    // if (Object.keys(validationErrors).length > 0) {
    //   return res.status(400).json({
    //     success: false,
    //     errors: validationErrors,
    //     message: validatedData.validationErrors,
    //   });
    // }

    // Generate roleType based on role and subRole
    // const roleType = subRole
    //   ? generateRoleType(role, subRole as AgentRole)
    //   : undefined;
    // Determine the roleType based on the role and userRoles
    const roleType = generateRoleType(Role.agency, subRoleToUse as AgentRole);

    // Check if the agent exists and belongs to the same agency as the current user
    const currentUserAgencyId = req.user?.id || req.agencyAgent?.agencyId;
    
    const existingAgent = await prisma.agencyAgent.findUnique({
      where: { id },
      include: {
        agency: {
          select: { id: true }
        }
      },
    });

    const masterUser = await prisma.user.findUnique({
      where: { id: req.user?.id },
      select: { role: true },
    });

    const masterTeamMember = await prisma.teamMember.findUnique({
      where: { id: req.user?.id },
      select: { role: true },
    });

    if (!existingAgent) {
      return res.status(404).json({ 
        success: false,
        message: "Agent not found" 
      });
    }
    
    // Determine if the current user is a master user or master team member
    const isMaster =
      (masterUser && masterUser.role === "master") ||
      (masterTeamMember && masterTeamMember.role === "master");
    
    // Verify the agent belongs to the same agency as the current user
    if (!isMaster && existingAgent.agencyId !== currentUserAgencyId) {
      return res.status(403).json({
        success: false,
        message: "Forbidden: You can only manage agents in your own agency"
      });
    }

    // Hash the password if provided
    let hashedPassword;
    if (password) {
      // Validate password strength
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: "Password validation failed",
          errors: passwordValidation.errors.map((error) => ({
            field: "password",
            message: error,
          })),
        });
      }
      hashedPassword = await bcrypt.hash(password, 10);
    }

    // Update the agent with only the provided fields
    const updateData: any = {};
    if (refId) updateData.refId = refId;
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (username) updateData.username = username;
    if (email) updateData.email = email;
    if (hashedPassword) updateData.hashedPassword = hashedPassword;
    if (subRoleToUse) {
      updateData.subRole = subRoleToUse;
      updateData.roleType = roleType;
    }
    if (department) updateData.department = department;
    if (status) updateData.status = status;
    if (accountStatus) updateData.accountStatus = accountStatus;

    // Update the agent
    const updatedAgent = await prisma.agencyAgent.update({
      where: { id },
      data: updateData,
    });

    // Remove hashedPassword from response
    const { hashedPassword: _, ...agentWithoutPassword } = updatedAgent;

    res.status(200).json({
      success: true,
      message: "Agent updated successfully",
      agent: agentWithoutPassword,
    });
  } catch (error: any) {
    console.error("Error updating agent:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while updating the agent",
    });
  }
};

export const fetchAllAgents = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  try {
    const { userId } = req.params;
    // If userId is provided, we're in master user mode
    // If not, we're in agency user mode and should use the logged-in user's agency
    let agencyId = userId;

    if (!userId) {
      // Agency user mode - use their own agency ID
      agencyId = req.agencyAgent?.agencyId || req.user?.id;
      if (!agencyId) {
        return res
          .status(400)
          .json({ message: "Agency ID not found for current user" });
      }
    }

    const pageSize = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;

    // Get filter parameters from query
    const accountType = req.query.accountType as AgentRole | undefined;
    const department = req.query.department as Department | undefined;
    const registrationDateRange = req.query.registrationDate
      ? (JSON.parse(req.query.registrationDate as string) as DateRange)
      : undefined;
    const lastLoginRange = req.query.lastLogin
      ? (JSON.parse(req.query.lastLogin as string) as DateRange)
      : undefined;

    // Build filter conditions
    const filters: any = {
      NOT: [{ id: req.agencyAgent?.id }],
      AND: [{ access: true }],
    };

    // Add account type filter if valid role is provided
    if (accountType && Object.values(AgentRole).includes(accountType)) {
      filters.AND.push({ subRole: accountType });
    }

    // Add department filter if valid department is provided
    if (department && Object.values(Department).includes(department)) {
      filters.AND.push({ department });
    }

    // Add registration date range filter
    if (registrationDateRange) {
      const dateFilter: any = {};
      if (registrationDateRange.from) {
        dateFilter.gte = new Date(registrationDateRange.from);
      }
      if (registrationDateRange.to) {
        dateFilter.lte = new Date(registrationDateRange.to);
      }
      if (Object.keys(dateFilter).length > 0) {
        filters.AND.push({ createdAt: dateFilter });
      }
    }

    // Add last login range filter
    if (lastLoginRange) {
      const loginFilter: any = {};
      if (lastLoginRange.from) {
        loginFilter.gte = new Date(lastLoginRange.from);
      }
      if (lastLoginRange.to) {
        loginFilter.lte = new Date(lastLoginRange.to);
      }
      if (Object.keys(loginFilter).length > 0) {
        filters.AND.push({ lastLogin: loginFilter });
      }
    }

    // Add agency filter to restrict agents to current agency
    if (agencyId) {
      filters.AND.push({ agencyId });
    }

    // get all agents except master agent with filters
    const [agents, agentsTotal] = await Promise.all([
      prisma.agencyAgent.findMany({
        where: filters,
        take: pageSize,
        skip: cursor ? 1 : 0,
        cursor: cursor ? { id: cursor } : undefined,
        select: {
          id: true,
          refId: true,
          firstName: true,
          lastName: true,
          username: true,
          email: true,
          role: true,
          subRole: true,
          roleType: true,
          department: true,
          status: true,
          nationality: true,
          dateOfBirth: true,
          lastLogin: true,
          accountStatus: true,
          access: true,
          agency: true,
          agencyId: true,
          agencyName: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      }),
      prisma.agencyAgent.count({
        where: filters,
      }),
      req.agencyAgent?.id
        ? prisma.agencyAgent.update({
            where: { id: req.agencyAgent?.id },
            data: {
              lastLogin: new Date(),
            },
          })
        : Promise.resolve(null),
    ]);

    const nextCursor =
      agents.length === pageSize ? agents[agents.length - 1].id : null;

    // return agents
    return res.status(200).json({
      success: true,
      message: "Agents fetched successfully",
      results: {
        agents,
        agentsTotal,
        nextCursor,
      },
    });
  } catch (error: any) {
    console.error("Error fetching agents:", error);
    return res.status(400).json({
      success: false,
      message: error.message || "An error occurred while fetching agents",
    });
  }
};

// Search agents

export const searchAgents = async (req: AuthRequest, res: Response) => {
  try {
    const { input } = req.query;
    const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
    const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;

    let searchData = {
      searchQuery: "",
      accountType: "all",
      department: "all",
      registrationDateFilter: "all time",
      lastLoginFilter: "all time",
    };

    if (!input) {
      return res.status(400).json({
        success: false,
        message: "Search input is required",
      });
    }

    // let searchData;
    if (input) {
      try {
        searchData = JSON.parse(input as string);
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: "Invalid search input format",
        });
      }
    }

    const {
      searchQuery,
      accountType,
      department,
      registrationDateFilter,
      lastLoginFilter,
    } = searchData;

    const registrationDateRange = registrationDateFilter
      ? getCreatedTimeRange(registrationDateFilter)
      : {};

    const lastLoginDateRange = lastLoginFilter
      ? getCreatedTimeRange(lastLoginFilter)
      : {};

    // Build the where clause
    const whereClause: any = {
      AND: [{ access: true }],
    };

    // Add search query filter if provided
    if (searchQuery) {
      whereClause.AND.push({
        OR: [
          { firstName: { contains: searchQuery, mode: "insensitive" } },
          { lastName: { contains: searchQuery, mode: "insensitive" } },
          { email: { contains: searchQuery, mode: "insensitive" } },
        ],
      });
    }

    // Add account status filter if specified (using TeamMemberRole enum)
    if (accountType && accountType !== "all") {
      // whereClause.AND.push({ subRole: accountType as SubRole });
      whereClause.AND.push({ subRole: accountType as AgentRole });
    }

    // Add department filter if specified (using Department enum)
    if (department && department !== "all") {
      whereClause.AND.push({ department: department as Department });
    }

    // Add registration date range filter
    if (
      registrationDateRange &&
      Object.keys(registrationDateRange).length > 0
    ) {
      whereClause.AND.push({ createdAt: registrationDateRange });
    }

    // Add last login date range filter
    if (lastLoginDateRange && Object.keys(lastLoginDateRange).length > 0) {
      whereClause.AND.push({ lastLogin: lastLoginDateRange });
    }

    // Fetch agents
    const [agents, agentsTotal] = await Promise.all([
      prisma.agencyAgent.findMany({
        where: whereClause,
        take: pageSize,
        skip: cursor ? 1 : 0,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: {
          // id: Prisma.SortOrder.asc,
          id: "asc",
        },
        select: {
          id: true,
          refId: true,
          firstName: true,
          lastName: true,
          username: true,
          email: true,
          role: true,
          subRole: true,
          roleType: true,
          department: true,
          status: true,
          accountStatus: true,
          access: true,
          lastLogin: true,
          agency: true,
          agencyId: true,
          agencyName: true,
          createdAt: true,
          updatedAt: true,
        },
      }),
      prisma.agencyAgent.count({
        where: whereClause,
      }),
      req.agencyAgent?.id
        ? prisma.agencyAgent.update({
            where: { id: req.agencyAgent?.id },
            data: {
              lastLogin: new Date(),
            },
          })
        : Promise.resolve(null),
    ]);

    // Handle generating `refId` if required
    const refIdUpdates = agents
      .filter((agent: any) => !agent.refId)
      .map(async (agent: any) => {
        const refId = await generateUserRefId();
        return prisma.agencyAgent.update({
          where: { id: agent.id },
          data: { refId },
        });
      });

    await Promise.all(refIdUpdates);

    const nextCursor =
      agents.length === pageSize ? agents[agents.length - 1].id : null;

    return res.status(200).json({
      success: true,
      results: {
        agents,
        agentsTotal,
        nextCursor,
      },
    });
  } catch (error: any) {
    console.error("Error searching agents:", error);
    return res.status(400).json({
      success: false,
      message: error.message || "An error occurred while searching agents",
    });
  }
};

// Remove an agent
export const removeAgent = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const currentUserAgencyId = req.user?.id || req.agencyAgent?.agencyId;
    
    if (!id) {
      return res.status(400).json({ 
        success: false,
        message: "Agent ID is required" 
      });
    }

    // Check if the agent exists and get their agency info
    const agent = await prisma.agencyAgent.findUnique({
      where: { id },
      include: {
        agency: {
          select: { id: true }
        }
      },
    });

    if (!agent) {
      return res.status(404).json({ 
        success: false,
        message: "Agent not found" 
      });
    }

    // Verify the agent belongs to the same agency as the current user
    if (agent.agencyId !== currentUserAgencyId) {
      return res.status(403).json({
        success: false,
        message: "Forbidden: You can only remove agents from your own agency"
      });
    }

    // Prevent removing yourself
    if (req.user?.id === id || req.agencyAgent?.id === id) {
      return res.status(400).json({
        success: false,
        message: "You cannot remove your own account"
      });
    }

    // Check if the current user has permission to remove agents
    const isAgencyOwner = req.user?.roleType === 'agency_owner' || req.agencyAgent?.roleType === 'agency_owner';
    const isAgencyAdmin = req.user?.subRole === 'admin' || req.agencyAgent?.subRole === 'admin';
    
    if (!isAgencyOwner && !isAgencyAdmin) {
      return res.status(403).json({
        success: false,
        message: "Forbidden: You don't have permission to remove agents"
      });
    }

    // Delete the agent
    await prisma.agencyAgent.delete({
      where: { id },
    });

    return res.status(200).json({
      success: true,
      message: "Agent removed successfully"
    });
  } catch (error) {
    console.error("Error removing agent:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while removing the agent",
    });
  }
};
