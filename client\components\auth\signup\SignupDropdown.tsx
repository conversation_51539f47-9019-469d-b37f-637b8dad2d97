"use client";
import { useEffect, useRef, useState } from "react";
import { Check, ChevronDown, Info } from "lucide-react";

interface Option {
  code: string;
  name: string;
}

interface SignupDropdownProps {
  label: string;
  id: string;
  icon: React.ReactNode;
  tooltip?: string;
  options: Option[];
  searchValue: string;
  onSearchChange: (value: string) => void;
  required?: boolean;
  renderError: (id: string) => JSX.Element;
}

const SignupDropdown = ({
  label,
  id,
  icon,
  tooltip,
  options,
  searchValue,
  onSearchChange,
  required = false,
  renderError,
}: SignupDropdownProps) => {
  const [menuActive, setMenuActive] = useState(false);
  const [value, setValue] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  const handleClickOutside = (event: MouseEvent) => {
    if (
      !menuRef.current?.contains(event.target as Node) &&
      !(event.target instanceof HTMLInputElement)
    ) {
      setMenuActive(false);
      setValue("");
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Filter options based on search input
  const filteredOptions =
    value.trim() === ""
      ? options
      : options.filter(
          (option) =>
            option.name.toLowerCase().includes(value.toLowerCase()) ||
            (option.code &&
              option.code.toLowerCase().includes(value.toLowerCase()))
        );

  return (
    <div className="relative" ref={menuRef}>
      <label
        className="block text-sm font-medium dark:text-gray-400 mb-1"
        htmlFor={id}
      >
        {label} {required && <span className="text-red-500">*</span>}
        {tooltip && <Tooltip text={tooltip} />}
      </label>
      <div className="relative inline-flex w-full">
        <div
          onClick={() => {
            setMenuActive(!menuActive);
            setValue("");
          }}
          className={`btn py-0 pl-0 w-full justify-between min-w-[11rem] h-[45px] bg-gray-300 dark:bg-gray-600 hover:border hover:border-red-500 hover:ring-1 hover:ring-red-500 text-gray-800 hover:text-gray-900 dark:text-white dark:hover:text-gray-200 rounded-lg ${
            menuActive ? "border border-red-500 ring-1 ring-red-500" : ""
          }`}
        >
          <span className="flex-1 items-center">
            <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
              {icon}
            </span>
            <input
              id={id}
              className="absolute left-0 top-0 bg-transparent border-hidden focus:ring-0 focus:ring-offset-0 w-full dark:placeholder:text-gray-300 placeholder:text-gray-700 placeholder:text-sm rounded-lg pl-10 pr-4 py-1.5 md:py-2.5"
              value={value}
              onChange={(e) => {
                setValue(e.target.value);
                setMenuActive(true);
              }}
              placeholder={searchValue || `Search for a ${label.toLowerCase()}`}
            />
          </span>
          <ChevronDown
            className="text-gray-500 dark:text-gray-400 ml-3 mr-2"
            size={20}
          />
        </div>
        {menuActive && (
          <div className="z-20 absolute top-full left-0 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1">
            <div className="font-medium text-sm text-gray-600 dark:text-gray-300 divide-y divide-gray-200 dark:divide-gray-700 focus:outline-none max-h-60 overflow-auto custom-scrollbar">
              {filteredOptions.length === 0 && (
                <div className="text-center py-3">
                  <span>No Results</span>
                </div>
              )}
              {filteredOptions.length > 0 &&
                filteredOptions.map((option) => {
                  const isSelected =
                    searchValue.toLowerCase() === option.name.toLowerCase();

                  return (
                    <button
                      key={option.code || option.name}
                      type="button"
                      className={`flex items-center justify-between text-sm md:text-base w-full py-2 px-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 ${
                        isSelected ? "text-red-500" : ""
                      }`}
                      onClick={() => {
                        onSearchChange(option.name);
                        setMenuActive(false);
                        setValue("");
                      }}
                    >
                      <div className="text-start text-base">
                        <div className="font-bold">
                          {option.code && (
                            <span className="font-semibold">
                              {option.code} -{" "}
                            </span>
                          )}
                          {option.name}
                        </div>
                      </div>
                      <Check
                        className={`shrink-0 mr-2 text-red-500 ${
                          !isSelected && "invisible"
                        }`}
                        size={20}
                      />
                    </button>
                  );
                })}
            </div>
          </div>
        )}
      </div>
      {renderError(id)}
    </div>
  );
};

// Tooltip component
const Tooltip = ({ text }: { text: string }) => (
  <div className="group relative inline-block ml-1">
    <Info size={16} className="text-gray-500 cursor-help" />
    <div className="opacity-0 bg-white dark:bg-gray-800 dark:text-white text-xs rounded-lg py-2 px-3 absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 group-hover:opacity-100 transition-opacity duration-300 w-48 pointer-events-none">
      {text}
      <svg
        className="absolute text-white dark:text-gray-800 h-2 w-full left-0 top-full"
        x="0px"
        y="0px"
        viewBox="0 0 255 255"
      >
        <polygon className="fill-current" points="0,0 127.5,127.5 255,0" />
      </svg>
    </div>
  </div>
);

export default SignupDropdown;
