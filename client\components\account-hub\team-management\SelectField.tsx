"use client";
import { SelectFieldProps } from "@/utils/definitions/agentsDefinitions";
import { useEffect, useRef, useState } from "react";
import { Check, ChevronDown } from "lucide-react";

const SelectField = ({
  label,
  name,
  value,
  onChange,
  disabled,
  options,
  formatOption,
  required,
  formErrors,
}: SelectFieldProps) => {
  const [menuActive, setMenuActive] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);

  // Get the selected option's display text
  const getSelectedOptionText = () => {
    const selectedOption = options.find((opt) => {
      if (typeof opt === "string") {
        return opt === value;
      } else {
        return opt.value === value;
      }
    });

    if (!selectedOption) return "";

    if (typeof selectedOption === "string") {
      return formatOption ? formatOption(selectedOption) : selectedOption;
    } else {
      return selectedOption.label;
    }
  };

  // <PERSON>le click outside to close dropdown
  const handleClickOutside = (event: MouseEvent) => {
    // Only process if menu is active
    if (
      menuActive &&
      menuRef.current &&
      !menuRef.current.contains(event.target as Node)
    ) {
      setMenuActive(false);
      setSearchValue("");
    }
  };

  useEffect(() => {
    // Add event listener when component mounts
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up event listener when component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [menuActive]); // Re-attach listener when menuActive changes

  // Handle option selection
  const handleSelectOption = (
    option: string | { value: string; label: string }
  ) => {
    const newValue = typeof option === "string" ? option : option.value;
    const event = {
      target: { name, value: newValue },
    } as React.ChangeEvent<HTMLSelectElement>;

    onChange(event);
    setMenuActive(false);
    setSearchValue("");
  };

  // Filter options based on search input
  const filteredOptions =
    searchValue.trim() === ""
      ? options
      : options.filter((option) => {
          if (typeof option === "string") {
            const optionText = formatOption ? formatOption(option) : option;
            return optionText.toLowerCase().includes(searchValue.toLowerCase());
          } else {
            return option.label
              .toLowerCase()
              .includes(searchValue.toLowerCase());
          }
        });

  return (
    <div ref={menuRef}>
      <label className="block text-sm font-medium mb-1 text-gray-600 dark:text-gray-400">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative inline-flex w-full">
        <div
          onClick={() => {
            if (!disabled) {
              setMenuActive(!menuActive);
              setSearchValue("");
            }
          }}
          className={`btn py-0 pl-0 w-full justify-between min-w-[11rem] h-[45px] bg-gray-300 dark:bg-gray-800 hover:border hover:border-red-500 hover:ring-1 hover:ring-red-500 text-gray-500 hover:text-gray-600 dark:text-white dark:hover:text-gray-200 rounded-lg capitalize ${
            menuActive ? "border border-red-500 ring-1 ring-red-500" : ""
          } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
        >
          <span className="flex-1 items-center">
            <input
              className="absolute left-0 top-0 bg-transparent border-hidden focus:ring-0 focus:ring-offset-0 w-full dark:placeholder:text-gray-300 placeholder:text-gray-700 placeholder:text-sm capitalize"
              value={searchValue}
              onChange={(e) => {
                if (!disabled) {
                  setSearchValue(e.target.value);
                  setMenuActive(true);
                }
              }}
              placeholder={value ? getSelectedOptionText() : `Select ${label}`}
              disabled={disabled}
            />
          </span>
          <ChevronDown
            className="text-gray-500 dark:text-gray-400 ml-3"
            size={20}
          />
        </div>
        {menuActive && (
          <div className="z-20 absolute top-full left-0 w-full bg-white dark:bg-gray-800 border border-gray-500 dark:border-gray-700 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1">
            <div className="font-medium text-sm text-gray-600 dark:text-gray-300 divide-y divide-gray-200 dark:divide-gray-700 focus:outline-none max-h-40 overflow-auto custom-scrollbar">
              {filteredOptions.length === 0 && (
                <div className="text-center py-3">
                  <span>No Results</span>
                </div>
              )}
              {filteredOptions.length > 0 &&
                filteredOptions.map((option) => {
                  const optionValue =
                    typeof option === "string" ? option : option.value;
                  const optionLabel =
                    typeof option === "string"
                      ? formatOption
                        ? formatOption(option)
                        : option
                      : option.label;
                  const isSelected = optionValue === value;

                  return (
                    <button
                      key={optionValue}
                      type="button"
                      className={`flex items-center justify-between text-sm md:text-base w-full py-2 px-3 cursor-pointer capitalize ${
                        isSelected ? "text-red-500" : ""
                      }`}
                      onClick={() => handleSelectOption(option)}
                    >
                      <div className="text-start text-base">
                        <div className="font-bold">{optionLabel}</div>
                      </div>
                      <Check
                        className={`shrink-0 mr-2 text-red-500 ${
                          !isSelected && "invisible"
                        }`}
                        size={20}
                      />
                    </button>
                  );
                })}
            </div>
          </div>
        )}
      </div>
      {formErrors && (
        <p className="text-red-500 text-sm mt-1 flex items-center">
          {formErrors}
        </p>
      )}
    </div>
  );
};

export default SelectField;
