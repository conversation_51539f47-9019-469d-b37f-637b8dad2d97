"use client";
import React, { useState, useMemo, useEffect } from "react";
import { User, Edit } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { useInView } from "react-intersection-observer";
import ListLoading from "@/components/flight-tickets/myTickets/ListLoading";
import { decodeHtml, getFormatDateTable } from "@/utils/functions/functions";
import {
  deleteAgent,
  updateAgent,
  fetchAllAgents,
  createAgent,
} from "@/lib/data/agencyData";
import ProgressLoading from "@/components/utils/ProgressLoading";
import useAgencyUserAuth from "@/components/hooks/useAgencyUserAuth";
import { checkPasswordStrength } from "@/utils/passwordStrength";
import { PasswordStrength } from "@/utils/definitions/agentsDefinitions";
import AgentModal from "./AgentModal";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import { statusStyles, TABLE_HEADERS } from "@/utils/constants/agencyConstants";
import {
  Department,
  FormErrors,
  UserActionsProps,
} from "@/utils/types/agencyTypes";
import { SearchAndFilters } from "./SearchAndFilters";
import useDarkMode from "@/components/hooks/useDarkMode";

// Render Status with Dot
const renderStatus = (status: string) => {
  const statusKey = status?.toLowerCase();
  const { style, dot } = statusStyles[statusKey] || statusStyles.default;

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${style}`}
    >
      {dot ? (
        <span
          className={`inline-block w-1.5 h-1.5 rounded-full mr-1.5 ${dot}`}
        ></span>
      ) : (
        ""
      )}
      {status}
    </span>
  );
};

// Component for user actions
const UserActions = ({ member, onAction, canManageAgents }: UserActionsProps & { canManageAgents: boolean }) => (
  <div className="flex items-center space-x-2">
    <button
      className={`p-1 rounded-lg transition-colors duration-150 ${
        canManageAgents 
          ? ' text-blue-500 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer' 
          : ' text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-not-allowed'
      }`}
      onClick={() => canManageAgents && onAction("view", member)}
      title={canManageAgents ? "View details" : "No permission to view"}
      disabled={!canManageAgents}
    >
      <Edit size={18} />
    </button>
  </div>
);

// Main component
const TeamManagementSection = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser) as StoredUser | null;
  const maxUsers = 20;
  
  // Check if current user has permission to manage agents
  const canManageAgents = user?.role === 'agency' && 
    (user.roleType === 'agency_owner' || user.subRole === 'admin');
  const darkMode = useDarkMode();
  const isDarkMode = typeof darkMode === "boolean" ? darkMode : false;

  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [agents, setAgents] = useState<any[]>([]);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [error, setError] = useState("");
  const [selectedMember, setSelectedMember] = useState<
    UserActionsProps["member"] | null
  >({
    id: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "agency",
    subRole: "",
    department: "",
    status: "inactive",
    lastLogin: "",
  });
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<{
    type: string;
    member: UserActionsProps["member"];
  } | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSuspendConfirm, setShowSuspendConfirm] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "agency",
    subRole: "",
    department: "",
  });
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const getInitialPasswordStrength = (): PasswordStrength => ({
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  });
  const [passwordStrength, setPasswordStrength] = useState<
    PasswordStrength | undefined
  >(getInitialPasswordStrength());
  const [confirmPasswordStrength, setConfirmPasswordStrength] = useState<
    PasswordStrength | undefined
  >(getInitialPasswordStrength());

  // Add a separate state for the add modal form data
  const [addModalFormData, setAddModalFormData] = useState({
    id: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "agency",
    subRole: "",
    department: "",
    status: "inactive" as "active" | "inactive",
    lastLogin: "",
  });

  const handleAgentAdded = async (newAgent: any) => {
    if (!newAgent) return;

    // Format the agent data to match the table structure
    const formattedAgent = {
      id: newAgent.id,
      firstName: newAgent.firstName,
      lastName: newAgent.lastName,
      email: newAgent.email,
      role: newAgent.role,
      subRole: newAgent.subRole,
      department: newAgent.department,
      status: newAgent.status,
      lastLogin: newAgent.lastLogin || null,
    };

    setAgents((prevAgents) =>
      Array.isArray(prevAgents)
        ? [...prevAgents, formattedAgent]
        : [formattedAgent]
    );
    setTotalUsers(totalUsers + 1);
  };

  const { ref, inView } = useInView();

  // Fetch users function
  const loadMoreUsers = async () => {
    if (isLoadingMore || !nextCursor) return;

    setIsLoadingMore(true);
    try {
      const data = await fetchAllAgents(nextCursor);

      if (data.success && data.results?.agents) {
        setUsers((prev) => [...prev, ...data.results.agents]);
        setTotalUsers(data.results.agentsTotal || 0);
        setNextCursor(data.results.nextCursor);
        setHasMore(!!data.results.nextCursor);
      }
    } catch (error) {
      console.error("Error fetching agents:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    const fetchInitialUsers = async () => {
      setIsLoading(true);
      try {
        const data = await fetchAllAgents();

        if (data.success && data.results?.agents) {
          setUsers(data.results.agents);
          setTotalUsers(data.results.agentsTotal);
          setNextCursor(data.results.nextCursor);
          setHasMore(!!data.results.nextCursor);
        }
      } catch (error) {
        console.error("Error fetching initial agents:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchInitialUsers();
  }, []);

  useEffect(() => {
    const loadAgents = async () => {
      try {
        const response = await fetchAllAgents();

        if (response?.success && Array.isArray(response.data)) {
          // Format the agents data to match the table structure
          const formattedAgents = response.data.map((agent: any) => ({
            id: agent.id,
            firstName: agent.firstName,
            lastName: agent.lastName,
            email: agent.email,
            role: agent.role,
            subRole: agent.subRole,
            department: agent.department,
            status: agent.status,
            lastLogin: agent.lastLogin || null,
          }));
          setAgents(formattedAgents);
          setTotalUsers(response.results.agentsTotal || 0);
        } else {
          setAgents([]);
        }
      } catch (error) {
        console.error("Error loading agents:", error);
        setAgents([]);
      }
    };
    loadAgents();
  }, []);

  // Combine users and agents states
  const allUsers = useMemo(() => {
    return [...users, ...agents];
  }, [users, agents]);

  // Filter combined users
  const filteredUsers = useMemo(() => {
    return allUsers.filter((user) => {
      const matchesSearch =
        !searchQuery ||
        user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesRole =
        selectedRoles.length === 0 || selectedRoles.includes(user.subRole);

      return matchesSearch && matchesRole;
    });
  }, [allUsers, searchQuery, selectedRoles]);

  // Handle infinite scroll
  useEffect(() => {
    if (inView) {
      loadMoreUsers();
    }
  }, [inView]);

  // Fetch users on component mount
  useEffect(() => {
    loadMoreUsers();
  }, []);

  const handleRoleToggle = (role: string | null) => {
    if (role === null) {
      setSelectedRoles([]);
    } else {
      setSelectedRoles((prev) =>
        prev.includes(role) ? prev.filter((r) => r !== role) : [...prev, role]
      );
    }
  };

  const handleAction = async (
    action: "view" | "edit" | "suspend" | "delete",
    formData: UserActionsProps["member"]
  ) => {
    // Always set the selected member regardless of action type
    setSelectedMember(formData);

    switch (action) {
      case "view":
        setIsViewModalOpen(true);
        break;

      case "edit":
        setIsEditModalOpen(true);
        break;

      case "suspend":
        setConfirmAction({ type: "suspend", member: formData });
        setShowSuspendConfirm(true);
        break;

      case "delete":
        setConfirmAction({ type: "delete", member: formData });
        setShowDeleteConfirm(true);
        break;
    }
  };

  const handleConfirmAction = async () => {
    if (!confirmAction) {
      console.error("No confirmAction state found!");
      return;
    }

    try {
      const { type, member } = confirmAction;

      if (type === "delete") {
        try {
          // await removeTeamMember(member.id);
          const deleteResult = await deleteAgent(member.id);

          // Remove the member from the local state
          setUsers((prev) => prev.filter((m) => m.id !== member.id));
          // Update the total users count
          setTotalUsers(deleteResult.results.agentsTotal);
          dispatch(
            setMsg({ success: true, message: "Agent removed successfully" })
          );
        } catch (error) {
          console.error("Error deleting agent:", error);
          dispatch(
            setMsg({ success: false, message: "Failed to delete agent" })
          );
        }
      } else if (type === "suspend") {
        const newStatus = member.status === "inactive" ? "active" : "inactive";

        try {
          // await updateTeamMember(member.id, {
          //   status: newStatus,
          // });
          const response = await updateAgent(member.id, {
            status: newStatus,
            ...(member.password ? { password: member.password } : {}),
          });

          // Update the agent's status in the local state
          setUsers((prev) =>
            prev.map((m) =>
              m.id === member.id ? { ...m, status: newStatus } : m
            )
          );

          dispatch(
            setMsg({
              success: true,
              message: `Agent ${
                newStatus === "inactive" ? "deactivated" : "activated"
              } successfully`,
            })
          );
        } catch (error) {
          console.error("Error updating agent status:", error);
          dispatch(
            setMsg({ success: false, message: "Failed to update agent status" })
          );
        }
      }

      // Close the confirmation modal
      setShowSuspendConfirm(false);
      setShowDeleteConfirm(false);
      setConfirmAction(null);

      // Close any open edit/view modals
      setIsEditModalOpen(false);
      setIsViewModalOpen(false);

      // Show success message
      dispatch(
        setMsg({
          success: true,
          message:
            type === "delete"
              ? "Agent deleted successfully"
              : "Agent status updated successfully",
        })
      );
    } catch (error) {
      console.error("Error in handleConfirmAction:", error);
      dispatch(
        setMsg({
          success: false,
          message: "An error occurred while processing your request",
        })
      );
    }
  };

  const handleEditSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setFormErrors({
      ...formErrors,
    });
    setError("");

    try {
      const formData = new FormData(event.currentTarget);
      const password = formData.get("password") as string;
      const confirmPassword = formData.get("confirmPassword") as string;

      // Only validate passwords if both fields have actual values (not empty strings)
      if (password && confirmPassword) {
        // Both password fields are filled, so validate them
        if (password !== confirmPassword) {
          setFormErrors((prev) => ({
            ...prev,
            confirmPassword:
              "The passwords you entered don't match. Please try again",
          }));
          dispatch(
            setMsg({
              success: false,
              message:
                "The passwords you entered don't match. Please try again",
            })
          );
          setError("The passwords you entered don't match. Please try again");
          return;
        }

        // Check password strength
        const strength = checkPasswordStrength(password, isDarkMode);
        if (strength.score < 3) {
          setFormErrors((prev) => ({
            ...prev,
            password:
              "Your password is too weak. " + strength.feedback.join(" and "),
          }));
          dispatch(
            setMsg({
              success: false,
              message: "Your password is too weak. Please make it stronger.",
            })
          );
          setError(
            "Your password is too weak. " + strength.feedback.join(" and ")
          );
          return;
        }
      } else if (
        (password && !confirmPassword) ||
        (!password && confirmPassword)
      ) {
        // One password field is filled but the other is empty
        setFormErrors((prev) => ({
          ...prev,
          confirmPassword:
            "Both password fields must be filled or both must be empty",
        }));
        dispatch(
          setMsg({
            success: false,
            message:
              "Both password fields must be filled or both must be empty",
          })
        );
        setError("Both password fields must be filled or both must be empty");
        return;
      }

      console.log("formData:", formData);

      // Get form values
      const firstName = formData.get("firstName") as string;
      const lastName = formData.get("lastName") as string;
      const email = formData.get("email") as string;
      const role = formData.get("role") as string;
      const subRole = formData.get("subRole") as string;
      const department = formData.get("department") as string;

      // Create update object with only the fields we want to update
      // Don't include fields that are empty strings or null
      const updatedAgent: Record<string, any> = {
        id: selectedMember?.id,
      };

      // Only add fields that have actual values
      if (firstName) updatedAgent.firstName = firstName;
      if (lastName) updatedAgent.lastName = lastName;
      if (email) updatedAgent.email = email;

      // Always include subRole and department in the update request
      // These are required fields in the database schema
      updatedAgent.subRole = subRole || selectedMember?.subRole || "";
      updatedAgent.department = department || selectedMember?.department || "";

      // Only include password if both fields are filled with non-empty values
      if (password && confirmPassword) {
        updatedAgent.password = password;
      }

      // Ensure agent has an id before updating
      if (!updatedAgent.id) {
        throw new Error("Updated agent must have an ID");
      }

      const response = await updateAgent(updatedAgent.id, updatedAgent);

      if (response.success) {
        // Update local state with the updated agent data
        setUsers((prev) =>
          prev.map((user) =>
            user.id === updatedAgent.id ? { ...user, ...updatedAgent } : user
          )
        );
        // Only update total users if the value exists in the response
        if (response.results?.agentsTotal !== undefined) {
          setTotalUsers(response.results.agentsTotal);
        }

        // Reset password strength state
        setPasswordStrength({
          score: 0,
          feedback: [],
          // color: isDarkMode ? "#ff4444" : "#cc0000",
          color: "#ff4444",
          label: "",
          message: "",
        });

        // Close edit modal and show success message
        setIsEditModalOpen(false);
        dispatch(
          setMsg({
            success: true,
            message: "Agent updated successfully",
          })
        );
      } else {
        //       dispatch(
        //         setMsg({
        //           success: false,
        //           message: response.message || "Failed to update agent",
        //         })
        //       );
        //     }
        //   } catch (error: any) {
        //     const errorMessages =
        //       error.errors?.[0]?.message ||
        //       error.message ||
        //       "An error occurred while processing your request";
        //     setError(errorMessages);
        //     dispatch(
        //       setMsg({
        //         success: false,
        //         message: errorMessages,
        //       })
        //     );
        //   }
        // };
        // dispatch(
        //   setMsg({
        //     success: false,
        //     message: response.message || "Failed to update agent",
        //   })
        // );
        // Handle validation errors from the response
        if (response.validationErrors) {
          setFormErrors(response.validationErrors);
          setError(Object.values(response.validationErrors)[0] as string); // Show first error in header
          dispatch(
            setMsg({
              success: false,
              message: Object.values(response.validationErrors)[0] as string,
            })
          );
        } else {
          const errorMsg = response.message || "Failed to update agent";
          setError(errorMsg);
          dispatch(
            setMsg({
              success: false,
              message: errorMsg,
            })
          );
        }
      }
      //   } catch (err: any) {
      //     const errorMessages =
      //       err.errors?.[0]?.message ||
      //       err.message ||
      //       "An error occurred while processing your request";
      //     setError(errorMessages);
      //     dispatch(
      //       setMsg({
      //         success: false,
      //         message: errorMessages,
      //       })
      //     );
      //   }
      // };
    } catch (err: any) {
      // Handle different error formats
      if (err.response?.data?.validationErrors) {
        setFormErrors(err.response.data.validationErrors);
        const firstError = Object.values(err.response.data.validationErrors)[0];
        setError(firstError as string);
        dispatch(
          setMsg({
            success: false,
            message: firstError as string,
          })
        );
      } else {
        const errorMsg =
          err.response?.data?.message ||
          err.message ||
          "An error occurred while processing your request";
        setError(errorMsg);
        setFormErrors((prev) => ({
          ...prev,
          general: errorMsg,
        }));
        dispatch(
          setMsg({
            success: false,
            message: errorMsg,
          })
        );
      }
    }
  };

  const validateForm = (data: typeof addModalFormData): FormErrors => {
    const errors: FormErrors = {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "",
      subRole: "",
      department: "",
    };

    if (!data?.firstName) {
      errors.firstName = "First name is required";
    }
    if (!data?.lastName) {
      errors.lastName = "Last name is required";
    }
    if (!data?.email) {
      errors.email = "Please enter your email";
    } else if (!/\S+@\S+\.\S+/.test(data?.email)) {
      errors.email = "Please use a valid email format";
    }
    if (!data?.password) {
      errors.password = "Please enter your password";
    } else {
      // Check password strength
      const strength = checkPasswordStrength(data?.password || "", isDarkMode);
      if (strength.score < 3) {
        errors.password = strength.feedback.join(", ");
      }
    }
    if (!data?.confirmPassword) {
      errors.confirmPassword = "Please confirm password";
    } else if (data?.password !== data?.confirmPassword) {
      errors.confirmPassword =
        "The passwords you entered don’t match. Please try again";
    }
    if (!data?.subRole) {
      errors.subRole = "Role is required";
    }
    if (!data?.department) {
      errors.department = "Department is required";
    }

    return errors;
  };

  const handleAddAgent = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setSuccessMessage(null);

    // Initialize form errors to empty
    setFormErrors({
      firstName: "",
      lastName: "",
      email: "",
      role: "agency",
      subRole: "",
      department: "",
      password: "",
      confirmPassword: "",
    });

    // Validate the form data
    const errors = validateForm(addModalFormData);
    const hasErrors = Object.values(errors).some((error) => error !== "");

    if (hasErrors) {
      setFormErrors(errors);
      // Set the first error message to display at the top
      const firstError = Object.values(errors).find((error) => error !== "");
      if (firstError) {
        setError(firstError);
      }
      return;
    }

    setIsLoading(true);
    try {
      if (!user?.agencyName) {
        setError("Agency name is required to add an agent");
        throw new Error("Agency name is required to add an agent");
      }

      const data = {
        ...addModalFormData,
        agencyId: user.id,
      };

      const response = await createAgent(data);

      if (!response) {
        const errorMessage = response?.message || "Failed to add agent";
        setError(errorMessage);
        dispatch(
          setMsg({
            message: errorMessage,
            success: false,
          })
        );
        return;
      }

      if (response.errors?.some((error: any) => error.field === "agentLimit")) {
        setError(response.message);
        dispatch(
          setMsg({
            message: response.message,
            success: false,
          })
        );
        return;
      }
      setSuccessMessage("Agent added successfully!");
      dispatch(
        setMsg({
          message: response?.message || "Agent added successfully",
          success: true,
        })
      );

      // Call the callback with the new agent data
      if (handleAgentAdded && response.agent) {
        handleAgentAdded(response.agent);
      }

      // Reset form and close modal
      setAddModalFormData({
        id: "",
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: "agency",
        subRole: "",
        department: "",
        status: "inactive" as "active" | "inactive",
        lastLogin: "",
      });

      // Reset password strength indicators
      setPasswordStrength(getInitialPasswordStrength());
      setConfirmPasswordStrength(getInitialPasswordStrength());

      // Close modal after successful addition
      setTimeout(() => {
        setIsAddModalOpen(false);
        setSuccessMessage(null);
        setError("");
      }, 2000);
    } catch (err: any) {
      const errorMessages =
        err.errors?.[0]?.message ||
        err.message ||
        "An error occurred while processing your request";
      setError(errorMessages);
      dispatch(
        setMsg({
          message: errorMessages,
          success: false,
        })
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddModalInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setAddModalFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear errors for this field if there were any
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleAddModalPasswordChange =
    (
      field: "password" | "confirmPassword",
      strengthSetter: (strength: PasswordStrength) => void
    ) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;

      setAddModalFormData((prev) => ({
        ...prev,
        [field]: value,
      }));

      strengthSetter(checkPasswordStrength(value, isDarkMode));

      // Clear errors for this field if there were any
      if (formErrors[field]) {
        setFormErrors((prev) => ({
          ...prev,
          [field]: "",
        }));
      }
    };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setSelectedMember((prev) => {
      if (!prev) {
        return {
          id: "",
          firstName: "",
          lastName: "",
          email: "",
          role: "agency",
          password: "",
          confirmPassword: "",
          subRole: "",
          department: "",
          status: "inactive",
          lastLogin: "",
          [name]: value,
        };
      }
      return {
        ...prev,
        [name]: value,
      };
    });

    // Clear error when user starts typing
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev };
        newErrors[name] = "";
        return newErrors;
      });
    }
  };

  const handlePasswordChange =
    (
      field: "password" | "confirmPassword",
      strengthSetter: (strength: PasswordStrength) => void
    ) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSelectedMember((prev) =>
        prev
          ? {
              ...prev,
              [field]: value,
            }
          : {
              id: "",
              firstName: "",
              lastName: "",
              email: "",
              role: "agency",
              password: "",
              confirmPassword: "",
              subRole: "",
              department: "",
              status: "inactive",
              lastLogin: "",
              [field]: value,
            }
      );
      strengthSetter(checkPasswordStrength(value, isDarkMode));
    };

  // const currentUsers = totalUsers;

  const customScrollbarStyles = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #9ca3a0;
      border-radius: 5px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #d1d5db;
      border-radius: 5px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #f3f4f6;
    }

    .dark .custom-scrollbar-logs::-webkit-scrollbar-track {
      background: #1f2937;
      border-radius: 5px;
    }
    .dark .custom-scrollbar-logs::-webkit-scrollbar-thumb {
      background: #374151;
      border-radius: 5px;
    }
    .dark .custom-scrollbar-logs::-webkit-scrollbar-thumb:hover {
      background: #6b7280;
    }
  `;

  return (
    <div className="flex-grow">
      <style>{customScrollbarStyles}</style>
      {/* Header Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">Employee Management</h2>
        <p className="text-gray-500 dark:text-gray-400">
          Manage your employees and their account permissions here.
        </p>
      </div>

      {/* Info Card */}
      <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-6 mb-6">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="w-full flex items-center sm:items-start flex-row flex-wrap md:flex-col gap-2 md:gap-0 md:w-auto">
            <h3 className="text-lg sm:text-xl font-bold">{totalUsers}</h3>
            <div className="flex flex-wrap text-xs sm:text-sm text-gray-500 dark:text-gray-400">
              <span className="font-medium">Active users</span>
              <span className="mx-1 hidden sm:inline">•</span>
              <span className="w-full sm:w-auto">
                {maxUsers - totalUsers} available
              </span>
            </div>
          </div>

          <SearchAndFilters
            selectedRoles={selectedRoles}
            onRoleToggle={handleRoleToggle}
            onAddUser={() => {
              // Reset addModalFormData to empty default values before opening add modal
              setAddModalFormData({
                id: "",
                firstName: "",
                lastName: "",
                email: "",
                password: "",
                confirmPassword: "",
                role: "agency",
                subRole: "",
                department: "",
                status: "inactive" as "active" | "inactive",
                lastLogin: "",
              });
              setIsAddModalOpen(true);
            }}
            canAddAgents={canManageAgents}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            setIsAddModalOpen={setIsAddModalOpen}
          />
        </div>
      </div>

      {/* User Table */}
      <div className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
        {isLoading && users.length === 0 ? (
          <div className="text-center py-4">Loading...</div>
        ) : (
          <>
            <div className="overflow-x-auto custom-scrollbar max-h-[calc(100vh-400px)]">
              <table className="table-auto w-full">
                <thead className="text-xs font-semibold capitalize text-gray-800 dark:text-white bg-gray-50 dark:bg-gray-600 text-left sticky -top-0.5">
                  <tr className="pr-4 pl-2 py-4 whitespace-nowrap font-semibold text-sm z-10">
                    {TABLE_HEADERS.map((header) => (
                      <th
                        key={header.key}
                        className="px-2 py-4 text-left text-sm font-semibold tracking-wider text-gray-800 dark:text-white border-b border-gray-400/50 dark:border-gray-600"
                      >
                        {header.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="text-sm divide-y divide-gray-300 dark:divide-gray-600">
                  {filteredUsers.map((user, index) => (
                    <tr
                      key={user.id}
                      className={
                        index !== filteredUsers.length - 1
                          ? "border-b border-gray-300 dark:border-gray-600 hover:bg-gray-200/50 dark:hover:bg-gray-800/50"
                          : ""
                      }
                    >
                      {/* Username */}
                      <td className="px-2 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          {/* firstName & lastName */}
                          <div>
                            {user.firstName.length + user.lastName.length <
                            15 ? (
                              <div className="font-medium">
                                {decodeHtml(user.firstName)}{" "}
                                {decodeHtml(user.lastName)}
                              </div>
                            ) : (
                              <div className="group">
                                {/* Truncated agency name */}
                                <div className="font-medium truncate max-w-[150px]">
                                  {decodeHtml(user.firstName)}{" "}
                                  {decodeHtml(user.lastName)}
                                </div>

                                {/* Tooltip with Full agency name */}
                                <div className="relative">
                                  <div className="absolute left-0 top-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
                                    {decodeHtml(user.firstName)}{" "}
                                    {decodeHtml(user.lastName)}
                                  </div>
                                </div>
                              </div>
                            )}
                            <div className="text-sm dark:text-gray-400 text-gray-500">
                              {user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      {/* Role / Department */}
                      <td className="px-2 py-4 whitespace-nowrap">
                        <span className="p-1 text-sm rounded-full capitalize">
                          {renderStatus(user.subRole)}
                        </span>
                        <span
                          className="p-1 text-sm rounded-full capitalize"
                          key={user.department}
                        >
                          {renderStatus(
                            user.department === Department.IT
                              ? user.department.toUpperCase()
                              : user.department.split("_").join(" ")
                          )}
                        </span>
                      </td>
                      {/* Status */}
                      <td className="px-2 py-4">{renderStatus(user.status)}</td>
                      {/* Last Login */}
                      <td className="px-2 py-4 text-gray-700 dark:text-gray-300">
                        {user.lastLogin
                          ? getFormatDateTable(user.lastLogin)
                          : "Never signed in"}
                      </td>
                      {/* Created At */}
                      <td className="px-2 py-4 text-gray-700 dark:text-gray-300">
                        {user.createdAt
                          ? getFormatDateTable(user.createdAt)
                          : "N/A"}
                      </td>
                      {/* Actions */}
                      <td className="px-6 py-4">
                        <UserActions
                          member={{
                            id: user.id,
                            firstName: user.firstName,
                            lastName: user.lastName,
                            email: user.email,
                            role: user.role,
                            password: user.password,
                            confirmPassword: user.confirmPassword,
                            subRole: user.subRole,
                            department: user.department,
                            status: user.status,
                            lastLogin: user.lastLogin,
                          }}
                          onAction={handleAction}
                          canManageAgents={canManageAgents}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredUsers.length === 0 && isLoading === false && (
                <p className="text-gray-500 text-center mt-10">
                  No users found
                </p>
              )}

              {/* Loading indicator and observer target */}
              <div ref={ref} className="w-full py-4">
                {isLoading && (
                  <div className="py-3 w-full">
                    <div className="flex justify-center">
                      <ListLoading />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
      {/* // Add Modal */}
      <AgentModal
        mode="add"
        isOpen={isAddModalOpen}
        setIsOpen={setIsAddModalOpen}
        setIsEditModalOpen={setIsEditModalOpen}
        setIsViewModalOpen={setIsViewModalOpen}
        passwordStrength={passwordStrength}
        confirmPasswordStrength={confirmPasswordStrength}
        setPasswordStrength={setPasswordStrength}
        setConfirmPasswordStrength={setConfirmPasswordStrength}
        showPassword={showPassword}
        showConfirmPassword={showConfirmPassword}
        isLoading={isLoading}
        error={error}
        successMessage={successMessage}
        onInputChange={handleAddModalInputChange}
        onPasswordChange={handleAddModalPasswordChange}
        onTogglePassword={() => setShowPassword(!showPassword)}
        onToggleConfirmPassword={() =>
          setShowConfirmPassword(!showConfirmPassword)
        }
        onSubmit={handleAddAgent}
        formData={addModalFormData}
        formErrors={formErrors}
        onAction={(action, formData) =>
          handleAction(
            action as "view" | "edit" | "suspend" | "delete",
            formData
          )
        }
        handleConfirmAction={handleConfirmAction}
      />

      {/* // View Modal */}
      <AgentModal
        mode="view"
        isOpen={isViewModalOpen}
        setIsOpen={setIsViewModalOpen}
        setIsEditModalOpen={setIsEditModalOpen}
        setIsViewModalOpen={setIsViewModalOpen}
        formData={
          selectedMember
            ? {
                ...selectedMember,
                role: selectedMember.subRole,
                status: selectedMember.status as "active" | "inactive",
              }
            : {
                id: "",
                firstName: "",
                lastName: "",
                email: "",
                role: "agency",
                status: "inactive" as "active" | "inactive",
                department: "",
                subRole: "",
              }
        }
        formErrors={{}}
        isLoading={isLoading}
        successMessage={successMessage}
        passwordStrength={passwordStrength}
        confirmPasswordStrength={confirmPasswordStrength}
        setPasswordStrength={setPasswordStrength}
        setConfirmPasswordStrength={setConfirmPasswordStrength}
        showPassword={false}
        showConfirmPassword={false}
        onInputChange={handleInputChange}
        onPasswordChange={handlePasswordChange}
        onTogglePassword={() => setShowPassword(!showPassword)}
        onToggleConfirmPassword={() =>
          setShowConfirmPassword(!showConfirmPassword)
        }
        onSubmit={handleAddAgent}
        onAction={(action, formData) =>
          handleAction(
            action as "view" | "edit" | "suspend" | "delete",
            formData
          )
        }
        handleConfirmAction={handleConfirmAction}
      />

      {/* // Edit Modal */}
      <AgentModal
        mode="edit"
        isOpen={isEditModalOpen}
        setIsOpen={setIsEditModalOpen}
        setIsEditModalOpen={setIsEditModalOpen}
        setIsViewModalOpen={setIsViewModalOpen}
        formData={
          selectedMember
            ? {
                ...selectedMember,
                role: selectedMember.subRole,
                status: selectedMember.status as "active" | "inactive",
              }
            : {
                id: "",
                firstName: "",
                lastName: "",
                email: "",
                role: "agency",
                status: "inactive" as "active" | "inactive",
                department: "",
                subRole: "",
              }
        }
        error={error}
        formErrors={formErrors}
        passwordStrength={passwordStrength}
        confirmPasswordStrength={confirmPasswordStrength}
        setPasswordStrength={setPasswordStrength}
        setConfirmPasswordStrength={setConfirmPasswordStrength}
        showPassword={showPassword}
        showConfirmPassword={showConfirmPassword}
        isLoading={isLoading}
        onInputChange={handleInputChange}
        onPasswordChange={handlePasswordChange}
        onTogglePassword={() => setShowPassword(!showPassword)}
        onToggleConfirmPassword={() =>
          setShowConfirmPassword(!showConfirmPassword)
        }
        onSubmit={handleEditSubmit}
        onAction={(action, formData) =>
          handleAction(
            action as "view" | "edit" | "suspend" | "delete",
            formData
          )
        }
        handleConfirmAction={handleConfirmAction}
      />
    </div>
  );
};

export default function TeamManagement() {
  const loading = useAgencyUserAuth();

  if (loading) {
    return <ProgressLoading />;
  }

  return <TeamManagementSection />;
}
