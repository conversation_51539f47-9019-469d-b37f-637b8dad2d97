export type AgencyTier = "standard" | "elite";

export type UserType = "master" | "agency" | "affiliate";

export interface Permission {
  name: string;
  description: string;
  category: PermissionCategory;
  requiredTier?: AgencyTier;
  masterOnly?: boolean;
}

export type PermissionCategory =
  | "booking"
  | "financial"
  | "team"
  | "reporting"
  | "support"
  | "inventory"
  | "client"
  | "master"
  | "user_management"
  | "administration"
  | "moderation";

export enum UserRole {
  MASTER_OWNER = "master_owner",
  MASTER_ADMIN = "master_admin",
  MASTER_MODERATOR = "master_moderator",
  MASTER_ACCOUNTANT = "master_accountant",
  AGENCY_OWNER = "agency_owner",
  AGENCY_ADMIN = "agency_admin",
  AGENCY_ACCOUNTANT = "agency_accountant",
  AGENCY_OPERATION = "agency_operation",
  // AGENCY_TICKETMASTER = 'agency_ticketmaster',
  AGENCY_SALES = "agency_sales",
  AFFILIATE = "affiliate",
}

export const PERMISSIONS = {
  // Booking Management
  SEARCH_TICKETS: {
    name: "search_tickets",
    description: "Ability to search for available tickets",
    category: "booking" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  BUY_TICKETS: {
    name: "buy_tickets",
    description: "Purchase tickets with complete travel details",
    category: "booking" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  BUY_TICKETS_NO_DETAILS: {
    name: "buy_tickets_no_details",
    description: "Purchase tickets without travel details",
    category: "booking" as PermissionCategory,
    masterOnly: false,
    requiredTier: "elite" as AgencyTier,
  },
  REVIEW_TICKETS: {
    name: "review_tickets",
    description: "Ability to review and moderate tickets",
    category: "moderation" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  MANAGE_MODERATOR_ACCOUNTANT: {
    name: "manage_moderator_accountant",
    description: "Ability to manage moderators and accountants",
    category: "administration" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  EDIT_ADMIN: {
    name: "edit_admin",
    description: "Ability to edit admin accounts",
    category: "administration" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  SUSPEND_ADMIN: {
    name: "suspend_admin",
    description: "Ability to suspend admin accounts",
    category: "administration" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  DELETE_ADMIN: {
    name: "delete_admin",
    description: "Ability to delete admin accounts",
    category: "administration" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  BLOCK_TICKETS: {
    name: "block_tickets",
    description: "Ability to block tickets",
    category: "moderation" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  DELETE_TICKETS: {
    name: "delete_tickets",
    description: "Ability to delete tickets",
    category: "moderation" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  UPDATE_TICKETS: {
    name: "update_tickets",
    description: "Ability to update ticket details",
    category: "booking" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  CHANGE_TICKET_STATUS: {
    name: "change_ticket_status",
    description: "Ability to change ticket status",
    category: "moderation" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  ACCEPT_REJECT_AGENCIES: {
    name: "accept_reject_agencies",
    description: "Ability to accept or reject agencies",
    category: "moderation" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  SUSPEND_ACCOUNTS: {
    name: "suspend_accounts",
    description: "Ability to suspend accounts",
    category: "moderation" as PermissionCategory,
    masterOnly: true,
    requiredTier: undefined,
  },
  MANAGE_TEAM: {
    name: "manage_team",
    description: "Ability to manage team members",
    category: "team" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  VIEW_TEAM: {
    name: "view_team",
    description: "Ability to view team members",
    category: "team" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  MANAGE_CREDITS: {
    name: "manage_credits",
    description: "Ability to manage credits",
    category: "financial" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  VIEW_ANALYTICS: {
    name: "view_analytics",
    description: "Ability to view analytics",
    category: "reporting" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  MODIFY_USER_SETTINGS: {
    name: "modify_user_settings",
    description: "Ability to modify user settings",
    category: "user_management" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  HELP_SUPPORT: {
    name: "help_support",
    description: "Ability to access help and support",
    category: "support" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
  MESSAGE_CENTER: {
    name: "message_center",
    description: "Ability to access message center",
    category: "support" as PermissionCategory,
    masterOnly: false,
    requiredTier: undefined,
  },
} as const;

export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  [UserRole.MASTER_OWNER]: Object.values(PERMISSIONS).map((p) => p.name),
  [UserRole.MASTER_ADMIN]: Object.values(PERMISSIONS).map((p) => p.name),
  [UserRole.MASTER_MODERATOR]: [
    PERMISSIONS.MANAGE_MODERATOR_ACCOUNTANT.name,
    PERMISSIONS.REVIEW_TICKETS.name,
    PERMISSIONS.BLOCK_TICKETS.name,
    PERMISSIONS.DELETE_TICKETS.name,
    PERMISSIONS.CHANGE_TICKET_STATUS.name,
    PERMISSIONS.ACCEPT_REJECT_AGENCIES.name,
    PERMISSIONS.SUSPEND_ACCOUNTS.name,
    PERMISSIONS.MANAGE_TEAM.name,
    PERMISSIONS.VIEW_TEAM.name,
    PERMISSIONS.VIEW_ANALYTICS.name,
  ],
  [UserRole.MASTER_ACCOUNTANT]: [
    PERMISSIONS.VIEW_TEAM.name,
    PERMISSIONS.VIEW_ANALYTICS.name,
    PERMISSIONS.MANAGE_CREDITS.name,
  ],
  [UserRole.AGENCY_OWNER]: Object.values(PERMISSIONS).map((p) => p.name),
  [UserRole.AGENCY_ADMIN]: [
    PERMISSIONS.SEARCH_TICKETS.name,
    PERMISSIONS.BUY_TICKETS.name,
    PERMISSIONS.MANAGE_TEAM.name,
    PERMISSIONS.UPDATE_TICKETS.name,
  ],
  [UserRole.AGENCY_ACCOUNTANT]: [
    PERMISSIONS.SEARCH_TICKETS.name,
    PERMISSIONS.MANAGE_CREDITS.name,
  ],
  [UserRole.AGENCY_OPERATION]: [
    PERMISSIONS.SEARCH_TICKETS.name,
    PERMISSIONS.BUY_TICKETS.name,
  ],
  [UserRole.AGENCY_SALES]: [
    PERMISSIONS.SEARCH_TICKETS.name,
    PERMISSIONS.BUY_TICKETS.name,
  ],
  [UserRole.AFFILIATE]: [
    PERMISSIONS.SEARCH_TICKETS.name,
    PERMISSIONS.BUY_TICKETS_NO_DETAILS.name,
  ],
};

export function hasPermission(
  userRole: UserRole,
  permission: string,
  agencyTier?: AgencyTier
): boolean {
  const hasPermission =
    ROLE_PERMISSIONS[userRole]?.includes(permission) ?? false;
  if (!hasPermission) return false;

  const permissionObj = Object.values(PERMISSIONS).find(
    (p) => p.name === permission
  );
  if (!permissionObj) return false;

  if (permissionObj.masterOnly && !userRole.startsWith("master_")) {
    return false;
  }

  if (
    permissionObj.requiredTier &&
    (!agencyTier || agencyTier !== permissionObj.requiredTier)
  ) {
    return false;
  }

  return true;
}

export function getUserType(role: UserRole): UserType {
  if (role.startsWith("master_")) return "master";
  if (role.startsWith("agency_")) return "agency";
  return "affiliate";
}

export function isAgencyRole(role: UserRole): boolean {
  return role.startsWith("agency_");
}

export function getMaxTeamMembers(
  role: UserRole,
  agencyTier?: AgencyTier
): number {
  if (role === UserRole.AGENCY_ADMIN) {
    return agencyTier === "elite" ? 20 : 5;
  }
  return 0;
}

export function getPermissionsByCategory(
  role: UserRole,
  category: PermissionCategory,
  agencyTier?: AgencyTier
): string[] {
  return (
    ROLE_PERMISSIONS[role]?.filter((permName) => {
      const permission = Object.values(PERMISSIONS).find(
        (p) => p.name === permName
      );
      if (!permission) return false;

      if (permission.category !== category) return false;

      if (
        permission.requiredTier &&
        (!agencyTier || agencyTier !== permission.requiredTier)
      ) {
        return false;
      }

      return true;
    }) ?? []
  );
}

export function hasAnyPermissionInCategory(
  role: UserRole,
  category: PermissionCategory,
  agencyTier?: AgencyTier
): boolean {
  return getPermissionsByCategory(role, category, agencyTier).length > 0;
}

export function isEliteAgency(agencyTier?: AgencyTier): boolean {
  return agencyTier === "elite";
}

export function isMasterRole(role: UserRole): boolean {
  return role.startsWith("master_");
}

export function canManageMasterTeam(role: UserRole): boolean {
  return role === UserRole.MASTER_ADMIN;
}

export function canApproveCredits(role: UserRole): boolean {
  return role === UserRole.MASTER_ADMIN;
}

export function getMasterPermissions(role: UserRole): string[] {
  return getPermissionsByCategory(role, "master");
}

export function canManageUserType(
  managerRole: UserRole,
  targetUserType: UserType
): boolean {
  if (managerRole === UserRole.MASTER_ADMIN) return true;
  if (managerRole === UserRole.MASTER_MODERATOR)
    return targetUserType !== "master";
  return false;
}
