import { ValidationErrorItem } from "joi";
import { TeamMemberValidationError } from "../types/team-member";
import { commonPatterns } from "../validators/baseUserValidation";

export class ValidationService {
  static formatJoiErrors(
    errors: ValidationErrorItem[]
  ): TeamMemberValidationError[] {
    return errors.map((error) => ({
      field: error.path.join("."),
      message: error.message.replace(/"/g, "").replace(/'/g, ""),
    }));
  }

  static validateName(value: string, fieldName: string): string {
    const decoded = this.decodeHtmlEntities(value);

    // Prevent leading or trailing hyphens/apostrophes first
    if (/^['-]|['-]$/.test(decoded)) {
      throw new Error(
        `${fieldName} must not begin or end with an apostrophe or hyphen`
      );
    }

    // Prevent excessive repetition of any character
    if (/(.)\1{2,}/.test(decoded)) {
      throw new Error(`${fieldName} contains excessive repetition`);
    }

    // Ensure only valid characters: letters, spaces, apostrophes, hyphens
    if (!/^[A-Za-z\s'-]+$/.test(decoded)) {
      throw new Error(
        `${fieldName} can only contain letters, apostrophes, hyphens, and spaces`
      );
    }

    // Prevent consecutive hyphens or apostrophes
    if (/[-']{2,}/.test(decoded)) {
      throw new Error(
        `${fieldName} cannot contain consecutive hyphens or apostrophes`
      );
    }
    return decoded;
  }

  static decodeHtmlEntities(str: string): string {
    return str.replace(/&([^;]+);/g, (entity, entityCode) => {
      const entities: { [key: string]: string } = {
        apos: "'",
        "#x27": "'",
        "#39": "'",
        nbsp: " ",
        "#x2D": "-",
        "#45": "-",
        minus: "-",
        hyphen: "-",
      };
      return entities[entityCode] || entity;
    });
  }

  static validateEmail(value: string, fieldName: string): string {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(value)) {
      throw new Error(`${fieldName} is not a valid email address`);
    }
    return value;
  }

  static validateEmailWithDomain(value: string): boolean {
    return value.endsWith("@airvilla-charters.travel");
  }

  static validateTeamMemberEmail(value: string): string {
    if (!this.validateEmail(value, "Email")) {
      throw new Error(`Email must be a valid email`);
    }
    if (!this.validateEmailWithDomain(value)) {
      throw new Error(
        "Please use an @airvilla-charters.travel email for team member accounts"
      );
    }
    return value;
  }

  static validatePassword(value: string, fieldName: string): string {
    if (value.length < 8) {
      throw new Error(
        `Please make sure your ${fieldName} is at least 8 characters long`
      );
    }
    if (value.length > 128) {
      throw new Error(
        `Please make sure your ${fieldName} is at most 128 characters long`
      );
    }
    if (!/[A-Z]/.test(value)) {
      throw new Error(
        `${fieldName} must contain at least one uppercase letter`
      );
    }
    if (!/[a-z]/.test(value)) {
      throw new Error(
        `${fieldName} must contain at least one lowercase letter`
      );
    }
    if (!/[0-9]/.test(value)) {
      throw new Error(`${fieldName} must contain at least one number`);
    }
    if (!/[!@#$%^&*()_+|~=`{}\[\]:";'<>?,./]/.test(value)) {
      throw new Error(
        `Please include at least one special character in your ${fieldName} (e.g., !, @, #, $)`
      );
    }
    if (
      commonPatterns.some((pattern) => value.toLowerCase().includes(pattern))
    ) {
      throw new Error(
        `${fieldName} contains common patterns that are easily guessable`
      );
    }
    return value;
  }
  static validatePhoneNumber(value: string, fieldName: string): string {
    if (!/^\+[1-9]\d{1,3}[0-9]{6,15}$/.test(value)) {
      throw new Error(`${fieldName} is not a valid phone number`);
    }
    return value;
  }
  static validateNationality(value: string, fieldName: string): string {
    if (!/^[A-Z]{2}$/.test(value)) {
      throw new Error(`${fieldName} is not a valid nationality code`);
    }
    return value;
  }
  static validateDate(value: string, fieldName: string): string {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
      throw new Error(`${fieldName} is not a valid date`);
    }
    return value;
  }
  static validateTime(value: string, fieldName: string): string {
    if (!/^\d{2}:\d{2}$/.test(value)) {
      throw new Error(`${fieldName} is not a valid time`);
    }
    return value;
  }
  static validateDateTime(value: string, fieldName: string): string {
    if (!/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(value)) {
      throw new Error(`${fieldName} is not a valid date and time`);
    }
    return value;
  }
  static validateDateRange(value: string, fieldName: string): string {
    if (!/^\d{4}-\d{2}-\d{2}\s-\s\d{4}-\d{2}-\d{2}$/.test(value)) {
      throw new Error(`${fieldName} is not a valid date range`);
    }
    return value;
  }

  static validateDateTimeRange(value: string, fieldName: string): string {
    if (
      !/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}\s-\s\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(
        value
      )
    ) {
      throw new Error(`${fieldName} is not a valid date and time range`);
    }
    return value;
  }

  static validateUrl(value: string, fieldName: string): string {
    const urlRegex =
      /^(https?:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,4})(\/[^\s]*)?$/;
    if (!urlRegex.test(value)) {
      throw new Error(`${fieldName} is not a valid URL`);
    }
    return value;
  }

  static hasLeadingOrTrailingSpaces(str: string, fieldName: string): string {
    if (str.trim() !== str && str.length > 0) {
      throw new Error(`${fieldName} cannot have leading or trailing spaces`);
    }
    return str;
  }
}
