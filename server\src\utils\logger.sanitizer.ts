// logger.sanitizer.ts
// Utility to sanitize log meta objects and redact sensitive information

const SENSITIVE_KEYS = [
  'password', 'token', 'accessToken', 'refreshToken', 'secret', 'apiKey', 'authorization', 'auth', 'creditCard', 'ssn', 'email', 'phone'
];

function redactValue(value: any): any {
  if (typeof value === 'string') {
    return '[REDACTED]';
  }
  if (Array.isArray(value)) {
    return value.map(redactValue);
  }
  if (typeof value === 'object' && value !== null) {
    return sanitizeLogObject(value);
  }
  return value;
}

export function sanitizeLogObject(obj: any): any {
  if (!obj || typeof obj !== 'object') return obj;
  const sanitized: Record<string, any> = {};
  for (const key of Object.keys(obj)) {
    if (SENSITIVE_KEYS.some(sensitive => key.toLowerCase().includes(sensitive))) {
      sanitized[key] = redactValue(obj[key]);
    } else {
      sanitized[key] = obj[key];
    }
  }
  return sanitized;
}
