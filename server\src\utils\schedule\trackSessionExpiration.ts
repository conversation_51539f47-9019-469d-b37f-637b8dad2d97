/**
 * @module trackSessionExpiration
 * Robust, production-grade session expiration tracker for user sessions.
 * - Uses in-memory timeouts to track session expiration per user.
 * - Emits socket events on expiration.
 * - Ensures atomicity, clear error handling, and best practices.
 * - Uses Winston logger for structured logging.
 */

import { getIO } from "../../socket";
import logger from "../logger";

/**
 * In-memory map to track session expiration timeouts for each user.
 * Key: userId, Value: Timeout object
 */
const sessionTimeouts: Map<string, NodeJS.Timeout> = new Map();

/**
 * Starts (or resets) a session expiration timer for a user.
 * Emits a "sessionExpiration" event to the user's socket room upon expiration.
 * All time calculations use UTC. The delay should be computed from UTC timestamps (e.g., Date.now()).
 *
 * @param userId - Unique identifier for the user/session
 * @param delay - Expiration delay in milliseconds (UTC-based)
 * @example
 *   // Expires in 1 hour from now (UTC)
 *   trackSessionExpiration("user123", 3600000);
 */
export const trackSessionExpiration = (userId: string, delay: number): void => {
  clearSessionExpiration(userId); // Ensure no duplicate timers

  try {
    /**
     * Attempt to emit the session expiration event with retry logic.
     * Retries up to 3 times with exponential backoff (100ms, 200ms, 400ms).
     */
    const emitWithRetry = async (attempt: number = 1): Promise<void> => {
      try {
        const io = getIO();
        io.to(userId).emit("sessionExpiration");
        logger.info("Session expired for user", { userId, attempt, expiredAtUTC: new Date().toISOString() });
        incrementExpirationMetric();
      } catch (emitErr) {
        logger.error("Failed to emit session expiration event", { userId, attempt, error: emitErr });
        if (attempt < 3) {
          // Exponential backoff: 100ms, 200ms, 400ms
          const backoff = 100 * Math.pow(2, attempt - 1);
          setTimeout(() => emitWithRetry(attempt + 1), backoff);
        } else {
          logger.error("Session expiration event failed after 3 attempts", { userId });
          logger.log({
            level: 'crit',
            message: 'CRITICAL: Session expiration event failed after all retries',
            userId,
          });
          // TODO: Notify admins (email, Slack, etc.)
          // Example: notifyAdmins(`Session expiration event failed for user ${userId}`);
        }
      }
    };
    const timeoutId = setTimeout(() => {
      // Start the retrying emission
      emitWithRetry();
    }, delay);
    sessionTimeouts.set(userId, timeoutId);
    logger.debug("Session expiration timer started", { userId, delay, startedAtUTC: new Date().toISOString() });
  } catch (err) {
    logger.error("Error starting session expiration timer", { userId, delay, error: err });
  }
};

/**
 * Cancels and removes the session expiration timer for a user.
 *
 * @param userId - Unique identifier for the user/session
 * @example
 *   clearSessionExpiration("user123");
 */
/**
 * Increments the session expiration counter for metrics.
 */
let expirationsThisMinute = 0;
function incrementExpirationMetric() {
  expirationsThisMinute++;
}

/**
 * Returns current session metrics for monitoring.
 * @returns {object} metrics
 */
export function getSessionExpirationMetrics() {
  return {
    activeTimers: sessionTimeouts.size,
    expirationsPerMinute: expirationsThisMinute
  };
}

// Periodically log metrics for observability
setInterval(() => {
  logger.info("Session expiration metrics", getSessionExpirationMetrics());
  expirationsThisMinute = 0; // Reset counter every minute
}, 60 * 1000);

export const clearSessionExpiration = (userId: string): void => {
  try {
    const timeout = sessionTimeouts.get(userId);
    if (timeout) {
      clearTimeout(timeout);
      sessionTimeouts.delete(userId);
      logger.debug("Session expiration timer cleared", { userId });
    }
  } catch (err) {
    logger.error("Error clearing session expiration timer", { userId, error: err });
  }
};
