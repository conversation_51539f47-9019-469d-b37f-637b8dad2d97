"use client";

import { useEffect, useState } from "react";
import { Menu, Transition } from "@headlessui/react";
import { Check, ChevronDown } from "lucide-react";

interface FilterDropdownProps {
  label: string;
  options: string[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  labelClassName?: string;
  buttonClassName?: string;
  menuClassName?: string;
}

export default function FilterDropdown({
  label,
  options,
  value,
  onChange,
  className = "flex-1",
  labelClassName = "block text-sm font-medium mb-1 text-gray-700 dark:text-white",
  buttonClassName =
    "btn w-full justify-between rounded-lg min-w-[11rem] h-[45px] bg-gray-100 dark:bg-gray-700 dark:border-gray-700 text-gray-500 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-200",
  menuClassName =
    "z-10 absolute top-full right-0 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 py-1.5 rounded shadow-lg overflow-hidden mt-1",
}: FilterDropdownProps) {
  const [selected, setSelected] = useState(0);

  useEffect(() => {
    const idx = options.findIndex(
      (opt) => (opt?.toLowerCase().trim() ?? "") === (value?.toLowerCase().trim() ?? "")
    );
    setSelected(idx === -1 ? 0 : idx);
  }, [value, options]);

  return (
    <div className={className}>
      <label className={labelClassName}>{label}</label>
      <Menu as="div" className="relative w-full">
        {({ open }) => (
          <>
            <Menu.Button
              className={`${buttonClassName} ${open ? "ring-2 ring-red-500" : ""}`}
              aria-label={`Select ${label}`}
            >
              {options[selected]}
              <ChevronDown
                className="text-gray-500 dark:text-gray-400 ml-3 shrink-0 mx-3 absolute top-3 right-0 pointer-events-none"
                size={20}
              />
            </Menu.Button>
            <Transition
              className={menuClassName}
              enter="transition ease-out duration-100 transform"
              enterFrom="opacity-0 -translate-y-2"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-out duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Menu.Items className="font-medium text-sm text-gray-600 dark:text-gray-300 focus:outline-none divide-y-2 divide-gray-200/50 dark:divide-gray-700/50">
                {options.map((option, idx) => (
                  <Menu.Item key={option}>
                    {({ active }) => (
                      <button
                        className={`flex items-center w-full py-1 px-3 cursor-pointer ${
                          active ? "bg-gray-50 dark:bg-gray-700" : ""
                        } ${idx === selected && "text-red-500"}`}
                        onClick={() => {
                          setSelected(idx);
                          onChange(option);
                        }}
                      >
                        <Check
                          className={`shrink-0 mr-2 text-red-500 ${
                            idx !== selected && "invisible"
                          }`}
                          size={20}
                        />
                        <span>{option}</span>
                      </button>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </>
        )}
      </Menu>
    </div>
  );
}
