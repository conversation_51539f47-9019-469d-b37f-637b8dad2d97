// server/src/utils/memoryMonitor.ts
import { writeFile, mkdir } from 'fs/promises';
import { join, dirname } from 'path';
import * as heapdump from 'heapdump';
import * as fs from 'fs';

const LOG_FILE = join(__dirname, '../../memory-usage.log');

interface MemorySnapshot {
  timestamp: string;
  rss: string;
  heapTotal: string;
  heapUsed: string;
  external: string;
  arrayBuffers: string;
}

export class MemoryMonitor {
  private static HEAP_SNAPSHOT_DIR = join(__dirname, '../../heap-snapshots');
  private static ensureSnapshotDir = async (): Promise<void> => {
    if (!fs.existsSync(this.HEAP_SNAPSHOT_DIR)) {
      await mkdir(this.HEAP_SNAPSHOT_DIR, { recursive: true });
    }
  };

  /**
   * Takes a heap snapshot and saves it to the heap-snapshots directory
   * @returns Promise<string> Path to the saved heap snapshot
   */
  public async takeHeapSnapshot(prefix: string = 'snapshot'): Promise<string> {
    await MemoryMonitor.ensureSnapshotDir();
    
    return new Promise((resolve, reject) => {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filePath = join(MemoryMonitor.HEAP_SNAPSHOT_DIR, `${prefix}-${timestamp}.heapsnapshot`);
      
      heapdump.writeSnapshot(filePath, (err, filename) => {
        if (err) {
          console.error('Failed to take heap snapshot:', err);
          reject(err);
        } else {
        // Use filePath as a fallback if filename is undefined
        const resultPath = filename || filePath;
        console.log(`Heap snapshot saved to: ${resultPath}`);
        resolve(resultPath);
        }
      });
    });
  }
  private static instance: MemoryMonitor;
  private snapshots: MemorySnapshot[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  private formatMemory(bytes: number): string {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  }

  public takeSnapshot(): MemorySnapshot {
    const memoryUsage = process.memoryUsage();
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    const snapshot: MemorySnapshot = {
      timestamp: now.toISOString(),
      rss: this.formatMemory(memoryUsage.rss),
      heapTotal: this.formatMemory(memoryUsage.heapTotal),
      heapUsed: this.formatMemory(memoryUsage.heapUsed),
      external: this.formatMemory(memoryUsage.external || 0),
      arrayBuffers: this.formatMemory((memoryUsage as any).arrayBuffers || 0),
    };
    
    this.snapshots.push(snapshot);
    return snapshot;
  }

  public startMonitoring(intervalMs = 60000): void {
    if (this.monitoringInterval) return;
    
    this.monitoringInterval = setInterval(async () => {
      const snapshot = this.takeSnapshot();
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      console.log('Memory Snapshot:', {
        time: timeString,
        ...snapshot
      });
      
      try {
        await this.saveToFile();
      } catch (error) {
        console.error('Error saving memory log:', error);
      }
    }, intervalMs);
  }

  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  private async saveToFile(): Promise<void> {
    const logEntry = JSON.stringify(this.snapshots, null, 2);
    await writeFile(LOG_FILE, logEntry, 'utf-8');
  }

  public static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }
}

// Initialize and start monitoring
const memoryMonitor = MemoryMonitor.getInstance();
memoryMonitor.startMonitoring(60000); // Log every minute

// Example usage for taking heap snapshots
memoryMonitor.takeHeapSnapshot('before-operation')
  .then(path => console.log(`Heap snapshot saved to: ${path}`))
  .catch(console.error);

export default memoryMonitor;