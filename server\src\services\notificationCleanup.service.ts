import { prisma } from "../prisma";
import logger from '../utils/logger';

/**
 * Deletes read notifications older than the specified number of days
 * @param daysOlderThan - Delete notifications older than this many days (default: 7)
 * @returns Object containing delete count and status
 */
export const cleanupOldNotifications = async (daysOlderThan: number = 7) => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOlderThan);

    // Delete read notifications older than the cutoff date
    const result = await prisma.notification.deleteMany({
      where: {
        read: true,
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    logger.info(`Successfully deleted ${result.count} old notifications`);
    return {
      success: true,
      deletedCount: result.count,
      message: `Successfully deleted ${result.count} old notifications`,
    };
  } catch (error) {
    const errorMessage = `Error cleaning up old notifications: ${error instanceof Error ? error.message : 'Unknown error'}`;
    logger.error(errorMessage, { error });
    return {
      success: false,
      deletedCount: 0,
      error: errorMessage,
    };
  }
};

// For testing purposes
if (require.main === module) {
  cleanupOldNotifications(7)
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}
