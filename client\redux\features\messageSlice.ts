import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface MessageState {
  message: string;
  type: "success" | "error" | "info" | "warning" | null;
}

const initialState: MessageState = {
  message: "",
  type: null,
};

const messageSlice = createSlice({
  name: "message",
  initialState,
  reducers: {
    setMsg: (
      state,
      action: PayloadAction<{ message: string; type: MessageState["type"] }>
    ) => {
      state.message = action.payload.message;
      state.type = action.payload.type;
    },
    clearMsg: (state) => {
      state.message = "";
      state.type = null;
    },
  },
});

export const { setMsg, clearMsg } = messageSlice.actions;
export default messageSlice.reducer;
