import React, { useState, useEffect, useCallback } from "react";
import { useNotifications } from "@/context/NotificationContext";
import { formatDistanceToNow } from "date-fns";
import { Bell, X, CheckCircle } from "lucide-react";

export default function NotificationDropdown() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    deleteNotification,
    markAllAsRead,
    loading,
  } = useNotifications();

  const [isOpen, setIsOpen] = useState(false);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleMarkAsRead = useCallback(async (id: string) => {
    if (processingIds.has(id)) return;
    try {
      setProcessingIds(prev => new Set(prev).add(id));
      await markAsRead(id);
    } catch (error) {
      console.error("Error marking notification as read:", error);
    } finally {
      setProcessingIds(prev => { const newSet = new Set(prev); newSet.delete(id); return newSet; });
    }
  }, [markAsRead, processingIds]);

  const handleDelete = useCallback(async (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    if (deletingIds.has(id)) return;
    try {
      setDeletingIds(prev => new Set(prev).add(id));
      await deleteNotification(id);
    } catch (error) {
      console.error("Error deleting notification:", error);
    } finally {
      setDeletingIds(prev => { const newSet = new Set(prev); newSet.delete(id); return newSet; });
    }
  }, [deleteNotification, deletingIds]);

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  return (
    <div className="relative">
      <button
        className="relative p-2 text-gray-400 hover:text-gray-500"
        onClick={toggleDropdown}
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-50">
          <div className="p-2 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-sm font-semibold">Notifications</h3>
            {notifications.length > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="text-xs text-blue-600 hover:text-blue-800"
                disabled={loading}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="px-4 py-2 text-sm text-gray-500">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`px-4 py-2 hover:bg-gray-50 cursor-pointer ${
                    !notification.read ? "bg-blue-50" : ""
                  }`}
                  onClick={() => handleMarkAsRead(notification.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </p>
                      <p className="text-sm text-gray-500">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatDistanceToNow(new Date(notification.createdAt), {
                          addSuffix: true,
                        })}
                      </p>
                    </div>
                    <div className="ml-2 flex items-center space-x-2">
                      {!notification.read && (
                        <button
                          onClick={e => { e.stopPropagation(); handleMarkAsRead(notification.id); }}
                          className="text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-300"
                          title="Mark as Read"
                        >
                          <CheckCircle size={16} />
                        </button>
                      )}
                      <button
                        onClick={e => handleDelete(e, notification.id)}
                        className="text-red-400 hover:text-red-500 dark:hover:text-red-300 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-300"
                        title="Delete"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
