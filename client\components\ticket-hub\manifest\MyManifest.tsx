"use client";
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  Search,
  Edit,
  Download,
  X,
  Mail,
  Check,
  ChevronDown,
} from "lucide-react";
import Image from "next/image";
import logo from "@/public/images/logo/airvilla_logo_symbol_red.png";
import useAgencyUserAuth from "@/components/hooks/useAgencyUserAuth";
import ProgressLoading from "@/components/utils/ProgressLoading";
import {
  ManifestTicket,
  getManifestTickets,
  sendManifestEmail,
  ManifestFilter,
} from "@/lib/data/manifestData";
import {
  capitalizeFirst,
  getFormatDateTable,
  getFormatTime,
} from "@/utils/functions/functions";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import {
  exportManifestToCSV,
  exportToCSV,
  formatManifestForExport,
} from "@/utils/functions/exportUtils";
import autoTable from "jspdf-autotable";
import "jspdf-autotable";
import html2pdf from "html2pdf.js";
import { Menu, Transition } from "@headlessui/react";
import { useInView } from "react-intersection-observer";
import { useDispatch } from "react-redux";
import type { HookData, PageHook } from "jspdf-autotable";
import { useDebounce } from "@/hooks/useDebounce";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

const MANIFEST_STATUS = {
  SUBMITTED: "Submitted",
  PENDING: "Pending",
  NOT_SUBMITTED: "Not Submitted",
} as const;

const TRIP_TYPES = {
  ROUND_TRIP: "Round Trip",
  ONE_WAY: "One Way",
} as const;

const STATUS_STYLES = {
  [MANIFEST_STATUS.SUBMITTED]: "bg-green-100 text-green-800",
  [MANIFEST_STATUS.PENDING]: "bg-yellow-200 text-yellow-800",
  [MANIFEST_STATUS.NOT_SUBMITTED]: "bg-red-200 text-red-800",
  // Add these for backend compatibility
  SUBMITTED: "bg-green-100 text-green-800",
  PENDING: "bg-yellow-200 text-yellow-800",
  NOT_SUBMITTED: "bg-red-200 text-red-800",
} as const;

// Add this type to ensure type safety
type StatusKey = keyof typeof STATUS_STYLES;

const FILTER_OPTIONS = {
  ManifestStatus: ["All", ...Object.values(MANIFEST_STATUS)],
  FlightDate: [
    "Today",
    "Next 7 Days",
    "Next 30 Days",
    "Next 90 Days",
    "All Time",
  ],
  TimeUntilFlight: [
    "Today",
    "Next 7 Days",
    "Next 30 Days",
    "Next 90 Days",
    "All Time",
  ],
} as const;

const TABLE_HEADERS = [
  "Manifest ID",
  "Flight NO.",
  "Flight Date",
  "Route",
  "Departure Time",
  "Arrival Time",
  "Carrier",
  "Status",
  "Time Until Flight",
  "Passengers",
  "Actions",
] as const;

const NAVIGATION_TABS = [
  { name: "All" },
  { name: "Submitted" },
  { name: "Pending" },
  { name: "Not Submitted" },
] as const;

// Grace period in minutes after departure before marking as "Not Submitted"
const GRACE_PERIOD_MINUTES = 30;

// Update interval for status checks (1 minute)
const STATUS_UPDATE_INTERVAL = 60000;

// ===========================
// TYPE DEFINITIONS
// ===========================

interface ManifestData {
  reference: string;
  flightDate: string;
  passengerCount: number;
  route: string;
  tripType: string;
  carrier: string;
  flightNumber: string;
  status: string;
}

interface PassengerData {
  id: number;
  title: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  nationality: string;
  issuingCountry: string;
  documentNumber: string;
  documentExpiryDate: string;
  ticketNumber: string;
}

interface ManifestCounts {
  totalManifests: number;
}

// ===========================
// UTILITY FUNCTIONS
// ===========================

/**
 * Calculates the total number of passengers across all bookings in a manifest
 * @param manifest - The manifest ticket object
 * @returns Total number of passengers
 */
const getActualPassengerCount = (manifest: ManifestTicket): number => {
  if (!manifest.Booking || manifest.Booking.length === 0) {
    return 0;
  }
  return manifest.Booking.reduce((total, booking) => {
    return total + (booking.travelers?.length || 0);
  }, 0);
};

/**
 * Determines if a flight has passed the departure time plus grace period
 * @param flightDate - Date string in DD/MM/YYYY format
 * @param departureTime - Time string in HH:MM format (default: "14:30")
 * @returns boolean indicating if flight has passed
 */
const hasFlightPassed = (
  flightDate: string,
  departureTime: string = "14:30"
): boolean => {
  const now = new Date();
  const [day, month, year] = flightDate.split("/");
  const [hours, minutes] = departureTime.split(":");

  const flightDateTime = new Date(
    parseInt(year),
    parseInt(month) - 1,
    parseInt(day),
    parseInt(hours),
    parseInt(minutes)
  );

  // Add grace period
  flightDateTime.setMinutes(flightDateTime.getMinutes() + GRACE_PERIOD_MINUTES);

  return now > flightDateTime;
};

const formatStatusForDisplay = (status: string): string => {
  const statusMap: Record<string, string> = {
    SUBMITTED: "Submitted",
    PENDING: "Pending",
    NOT_SUBMITTED: "Not Submitted",
    // Add any other status mappings if needed
  };

  return statusMap[status] || status;
};

const formatAddress = (address: any) => {
  if (!address) return "N/A";
  if (typeof address === "string") return address;
  return [address.street, address.city, address.country]
    .filter(Boolean)
    .join(", ");
};

/**
 * Auto-updates manifest status based on flight timing rules
 * @param manifest - The manifest object to check
 * @returns Updated manifest with corrected status
 */
const updateManifestStatus = (manifest: ManifestTicket): ManifestTicket => {
  // If status is already SUBMITTED, no need to change it
  if (manifest.status === MANIFEST_STATUS.SUBMITTED) {
    return manifest;
  }

  const flightPassed = hasFlightPassed(
    manifest.flightDate,
    manifest.departureTime
  );

  // Auto-convert Pending to Not Submitted if flight has passed
  if (manifest.status === "PENDING" && flightPassed) {
    return {
      ...manifest,
      status: "NOT_SUBMITTED",
      manifestStatus: "NOT_SUBMITTED",
    };
  }

  return manifest;
};

/**
 * Calculates time remaining until flight departure
 * @param flightDate - Date string in DD/MM/YYYY format
 * @returns Formatted time string (e.g., "2d 5h" or "3h 45m")
 */
const calculateTimeUntilFlight = (flightDate: string): string => {
  const now = new Date();
  const flight = new Date(flightDate.split("/").reverse().join("-"));
  const hoursUntilFlight = Math.max(
    0,
    (flight.getTime() - now.getTime()) / (1000 * 60 * 60)
  );

  if (hoursUntilFlight < 24) {
    return `${Math.floor(hoursUntilFlight)}h ${Math.floor(
      (hoursUntilFlight % 1) * 60
    )}m`;
  } else {
    const days = Math.floor(hoursUntilFlight / 24);
    const hours = Math.floor(hoursUntilFlight % 24);
    return `${days}d ${hours}h`;
  }
};

/**
 * Generates unique manifest ID based on flight date
 * @param flightDate - Date string in DD/MM/YYYY format
 * @returns Formatted manifest ID (e.g., "MAN-2025-05-25-001")
 */
const generateManifestId = (flightDate: string): string => {
  const randomSuffix = String(Math.floor(Math.random() * 999) + 1).padStart(
    3,
    "0"
  );
  const formattedDate = flightDate.split("/").reverse().join("-");
  return `MAN-${formattedDate}-${randomSuffix}`;
};

// ===========================
// MOCK DATA GENERATOR
// ===========================

/**
 * Generates mock passenger data for demonstration
 * @param count - Number of passengers to generate
 * @returns Array of passenger objects
 */
const generateMockPassengers = (count: number): PassengerData[] => {
  return [];
};

// ===========================
// REUSABLE COMPONENTS
// ===========================

/**
 * Email Manifest Popup Component
 * Handles email sending functionality with validation and status feedback
 */
interface EmailManifestPopupProps {
  isOpen: boolean;
  onClose: () => void;
  booking: ManifestTicket | null;
  onEmailSent: (recipientEmail: string) => void;
  handleGenerateAndSendPdf: (
    booking: ManifestTicket,
    recipientEmail: string
  ) => Promise<{ success: boolean; message?: string } | undefined>;
}

const EmailManifestPopup: React.FC<EmailManifestPopupProps> = ({
  isOpen,
  onClose,
  booking,
  onEmailSent,
  handleGenerateAndSendPdf,
}) => {
  const [recipientEmail, setRecipientEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const dispatch = useDispatch();

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!recipientEmail.trim()) return;
      if (!booking) return;

      setIsSubmitting(true);

      try {
        console.log("Calling handleGenerateAndSendPdf...");
        const response = await handleGenerateAndSendPdf(
          booking,
          recipientEmail
        );
        console.log("response", response);

        if (!response || !response.success) {
          throw new Error("Failed to send email. Please try again.");
        }
        console.log("Email sent, calling onEmailSent...");
        await onEmailSent(recipientEmail);
        setEmailSent(true);

        // Simulate email sending process
        setTimeout(() => {
          setIsSubmitting(false);

          // Show success for 2 seconds, then close all popups
          setTimeout(() => {
            setEmailSent(false);
            setRecipientEmail("");
            onClose();
          }, 2000);
        }, 1500);
      } catch (error) {
        console.error("Error sending email:", error);
        // Handle error (show error message, etc.)
        dispatch(
          setMsg({
            success: false,
            message:
              (error as Error).message ||
              "Failed to send email. Please try again.",
          })
        );
      } finally {
        setIsSubmitting(false);
      }
    },
    [recipientEmail, onClose, onEmailSent]
  );

  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      setRecipientEmail("");
      setEmailSent(false);
      onClose();
    }
  }, [isSubmitting, onClose]);

  if (!isOpen || !booking) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="dark:bg-gray-800 bg-gray-50 rounded-lg w-full max-w-md">
        {/* Header */}
        <div className="dark:bg-gray-800 bg-gray-50 px-6 py-4 flex items-center justify-between border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold dark:text-white text-gray-800">
              Email Manifest
            </h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="bg-red-500 text-white hover:opacity-80 transition duration-300 p-2 rounded disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {emailSent ? (
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold dark:text-white text-gray-800 mb-2">
                Email Sent Successfully!
              </h3>
              <p className="dark:text-gray-400 text-gray-600">
                The manifest has been sent to {recipientEmail}
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2 dark:text-gray-300 text-gray-600">
                  Recipient Email Address
                </label>
                <input
                  type="email"
                  value={recipientEmail}
                  onChange={(e) => setRecipientEmail(e.target.value)}
                  placeholder="Enter email address..."
                  className="w-full dark:bg-gray-700 dark:text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 border-none"
                  required
                  disabled={isSubmitting || emailSent}
                />
                <p className="text-xs dark:text-gray-400 text-gray-500 mt-2">
                  The system will send the manifest PDF on your behalf
                </p>
              </div>

              {/* Flight Info Summary */}
              <div className="dark:bg-gray-700 bg-gray-200 rounded-lg p-4 mb-6">
                <h4 className="text-sm font-medium dark:text-gray-300 text-gray-600 mb-3">
                  Manifest Details
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="dark:text-gray-400 text-gray-600">
                      Flight:
                    </span>
                    <span className="dark:text-white text-gray-700 ml-2 font-mono">
                      {booking.flightNumber}
                    </span>
                  </div>
                  <div>
                    <span className="dark:text-gray-400 text-gray-600">
                      Date:
                    </span>
                    <span className="dark:text-white text-gray-700 ml-2">
                      {getFormatDateTable(booking.flightDate)}
                    </span>
                  </div>
                  <div>
                    <span className="dark:text-gray-400 text-gray-600">
                      Route:
                    </span>
                    <span className="dark:text-white text-gray-700 ml-2">
                      {booking.route}
                    </span>
                  </div>
                  <div>
                    <span className="dark:text-gray-400 text-gray-600">
                      Passengers:
                    </span>
                    <span className="dark:text-white text-gray-700 ml-2">
                      {getActualPassengerCount(booking)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg transition duration-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || !recipientEmail.trim()}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition duration-300 disabled:opacity-50 flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Mail size={16} />
                      <span>Send Email</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Flight Manifest Detail Popup Component
 * Displays comprehensive flight and passenger information
 */
interface FlightManifestPopupProps {
  isOpen: boolean;
  onClose: () => void;
  booking: ManifestTicket | null;
  onEmailClick: (booking: ManifestTicket) => void;
}

const FlightManifestPopup: React.FC<FlightManifestPopupProps> = ({
  isOpen,
  onClose,
  booking,
  onEmailClick,
}) => {
  const dispatch = useAppDispatch();
  // const passengers = useMemo(() => {
  //   if (!booking) return [];
  //   return generateMockPassengers(booking.passengerCount);
  // }, [booking?.passengerCount]);

  const passengersCount = useMemo(() => {
    if (!booking?.Booking?.length) return [];

    // Flatten all travelers from all bookings and add sequential numbers
    let passengerCounter = 1;
    return booking.Booking.flatMap((bookingItem) =>
      bookingItem.travelers.map((traveler) => ({
        id: traveler.id,
        number: passengerCounter++, // Increment counter for each passenger
        title: traveler.traveler.title,
        firstName: traveler.traveler.firstName,
        lastName: traveler.traveler.lastName,
        dateOfBirth: traveler.traveler.dateOfBirth,
        nationality: traveler.traveler.nationality,
        issuingCountry: traveler.traveler.issuingCountry || "",
        documentNumber: traveler.traveler.documentNumber || "",
        documentExpiryDate: traveler.traveler.documentExpiryDate || "",
        ticketNumber: bookingItem.id,
      }))
    );
  }, [booking]);

  const [fromAirport = "", toAirport = ""] = useMemo(() => {
    if (!booking?.route) return ["", ""];
    const parts = booking.route.split(/\s*[→↔]\s*/);
    return [parts[0] || "", parts[1] || ""];
  }, [booking?.route]);

  const [isLoading, setIsLoading] = useState(false);

  // Mock flight times - in production, these would come from booking data
  //   const departureTime = "14:30";
  //   const arrivalTime = "18:45";

  if (!isOpen || !booking) return null;

  console.log("booking", booking);

  // const handleDownloadPdf = async (manifest: ManifestTicket) => {
  //   try {
  //     const manifestElement = document.getElementById(
  //       "flight-manifest-content"
  //     );
  //     if (!manifestElement) return;

  //     const canvas = await html2canvas(manifestElement, {
  //       scale: 2,
  //       useCORS: true,
  //       logging: false,
  //     });

  //     const pdf = new jsPDF("p", "mm", "a4");
  //     const imgData = canvas.toDataURL("image/png");
  //     const imgWidth = 210;
  //     const pageHeight = 295;
  //     const imgHeight = (canvas.height * imgWidth) / canvas.width;
  //     let heightLeft = imgHeight;
  //     let position = 0;

  //     pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
  //     heightLeft -= pageHeight;

  //     while (heightLeft >= 0) {
  //       position = heightLeft - imgHeight;
  //       pdf.addPage();
  //       pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
  //       heightLeft -= pageHeight;
  //     }

  //     // Save the PDF
  //     pdf.save(`manifest-${manifest.manifestId}.pdf`);
  //   } catch (error) {
  //     console.error("Error generating PDF:", error);
  //   }
  // };

  // const handleDownloadPdf = (manifest: ManifestTicket) => {
  //   const doc = new jsPDF();

  //   doc.setFontSize(14);
  //   doc.text("Flight Manifest", 14, 15);
  //   doc.setFontSize(11);
  //   doc.text(`Generated: ${new Date().toLocaleString()}`, 14, 22);

  //   // Example flight details
  //   const flightDetails = [
  //     ["Flight Number", manifest.flightNumber || ""],
  //     ["Carrier", manifest.carrier || ""],
  //     ["Flight Date", manifest.flightDate || ""],
  //     ["Travel Class", manifest.travelClass || ""],
  //   ];

  //   autoTable(doc, {
  //     startY: 30,
  //     head: [["Flight Detail", "Value"]],
  //     body: flightDetails,
  //     theme: "grid",
  //     styles: { fontSize: 10 },
  //   });

  //   // Passenger manifest
  //   const passengerRows = manifest.Booking?.flatMap((booking: any, index: number) =>
  //     booking.travelers?.map((passenger: any) => [
  //     index + 1,
  //     passenger.traveler.title,
  //     passenger.traveler.lastName,
  //     passenger.traveler.firstName,
  //     getFormatDateTable(passenger.traveler.dateOfBirth),
  //     passenger.traveler.nationality,
  //     passenger.traveler.issuingCountry,
  //     passenger.traveler.documentNumber,
  //     getFormatDateTable(passenger.traveler.documentExpiryDate),
  //     passenger.bookingId,
  //   ])
  //   );

  //   autoTable(doc, {
  //     startY: (doc as any).lastAutoTable.finalY + 10,
  //     head: [
  //       [
  //         "#",
  //         "Title",
  //         "Last Name",
  //         "First Name",
  //         "Date of Birth",
  //         "Nationality",
  //         "Issuing Country",
  //         "Passport Number",
  //         "Passport Expiry",
  //         "Booking ID",
  //       ],
  //     ],
  //     body: passengerRows,
  //     styles: { fontSize: 8 },
  //   });

  //   doc.save(`manifest-${manifest.manifestId}.pdf`);
  // };

  // const handleDownloadPdf = (manifest: ManifestTicket) => {
  //   const doc = new jsPDF();

  //   // Set document properties
  //   doc.setProperties({
  //     title: `Manifest - ${manifest.manifestId}`,
  //     subject: 'Flight Manifest',
  //     author: 'AirVilla',
  //   });

  //   // Add logo
  //   const logoUrl = '/images/logo/airvilla_logo_symbol_red.png';
  //   doc.addImage(logoUrl, 'PNG', 15, 10, 20, 20);

  //   // Add title and flight info
  //   doc.setFontSize(18);
  //   doc.setTextColor(40, 40, 40);
  //   doc.text(manifest.owner?.agencyName || '', 40, 20);

  //   doc.setFontSize(10);
  //   doc.setTextColor(100, 100, 100);
  //   doc.text(`Flight Number:`, 40, 30);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${manifest.flightNumber}`, 70, 30);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`Flight Date:` , 150, 30);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${getFormatDateTable(manifest.flightDate)}` , 175, 30);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`Carrier:`, 40, 36);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${manifest.carrier}`, 70, 36);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`Travel Class:`, 150, 36);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${manifest.travelClass}`, 175, 36);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`From:`, 40, 42);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${manifest.departure.city}`, 70, 42);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`To:`, 150, 42);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${manifest.arrival.city}`, 175, 42);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`Departure Time:`, 40, 48);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${getFormatTime(manifest.departureTime)}`, 70, 48);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);
  //   doc.text(`Arrival Time:`, 150, 48);
  //   doc.setFont('helvetica', 'bold');
  //   doc.setFontSize(11);
  //   doc.text(`${getFormatTime(manifest.arrivalTime)}`, 175, 48);
  //   doc.setFont('helvetica', 'normal');
  //   doc.setFontSize(10);

  //   // Add current date and time
  //   const now = new Date();
  //   doc.text(`Generated: ${now.toLocaleString()}`, 198, 10, { align: 'right' });

  //   // Add passenger table
  //   const passengerRows = manifest.Booking?.flatMap((booking, index) =>
  //     booking.travelers?.map((passenger) => [
  //       (index + 1).toString(),
  //       `${capitalizeFirst(passenger.traveler.title)} ${passenger.traveler.firstName} ${passenger.traveler.lastName}`,
  //       getFormatDateTable(passenger.traveler.dateOfBirth),
  //       `${passenger.traveler.nationality} / ${passenger.traveler.issuingCountry}`,
  //       passenger.traveler.documentNumber,
  //       passenger?.traveler?.documentExpiryDate ? getFormatDateTable(passenger.traveler.documentExpiryDate) : 'N/A',
  //       passenger.bookingId,
  //     ])
  //   ) || [];

  //   autoTable(doc, {
  //     startY: 60,
  //     head: [['#', 'Passenger Name', 'Date of Birth', 'Nationality / Issuing Country', 'Document #', 'Expiry', 'Booking ID']],
  //     body:  passengerRows as (string | number)[][],
  //     headStyles: {
  //       fillColor: [41, 128, 185],
  //       textColor: 255,
  //       fontStyle: 'bold'
  //     },
  //     alternateRowStyles: {
  //       fillColor: [245, 245, 245]
  //     },
  //     margin: { top: 10 },
  //     styles: {
  //       fontSize: 8,
  //       cellPadding: 3,
  //       overflow: 'linebreak',
  //       lineColor: [200, 200, 200],
  //       lineWidth: 0.1
  //     },
  //     columnStyles: {
  //       0: { cellWidth: 10 },  // #
  //       1: { cellWidth: 40 },  // Name
  //       2: { cellWidth: 25 },  // DOB
  //       3: { cellWidth: 25 },  // Nationality
  //       4: { cellWidth: 30 },  // Document #
  //       5: { cellWidth: 25 },  // Expiry
  //       6: { cellWidth: 30 }   // Booking ID
  //     }
  //   });

  //   // Add page numbers
  //   const pageCount = doc.getNumberOfPages();
  //   for (let i = 1; i <= pageCount; i++) {
  //     doc.setPage(i);
  //     doc.setFontSize(10);
  //     doc.setTextColor(150);
  //     doc.text(
  //       `Page ${i} of ${pageCount}`,
  //       doc.internal.pageSize.width / 2,
  //       doc.internal.pageSize.height - 10,
  //       { align: 'center' }
  //     );
  //   }

  //   doc.save(`manifest-${manifest.manifestId}.pdf`);
  // };

  // +++++++++++++++++++++ WORKING CODE AHMED VERSION +++++++++++++++++++++
  // const handleDownloadPdf = async (manifest: ManifestTicket) => {
  //   try {
  //     // Create a temporary element to hold our HTML
  //     const element = document.createElement("div");
  //     element.id = "pdf-export-container";
  //     // element.style.position = "absolute";
  //     // element.style.left = "-9999px";
  //     element.style.width = "210mm";
  //     element.style.padding = "20px";
  //     // element.style.backgroundColor = "#ffffff";

  //     // Generate passenger rows HTML
  //     const passengerRows =
  //       manifest.Booking?.flatMap(
  //         (booking, bookingIndex) =>
  //           booking.travelers?.map((traveler, travelerIndex) => {
  //             const dob = traveler.traveler?.dateOfBirth
  //               ? new Date(traveler.traveler.dateOfBirth).toLocaleDateString()
  //               : "N/A";

  //             // Add page-break class every 15 rows
  //             const pageBreakClass =
  //               (bookingIndex * booking.travelers?.length + travelerIndex) %
  //                 15 ===
  //                 0 &&
  //               (bookingIndex > 0 || travelerIndex > 0)
  //                 ? "page-break-before"
  //                 : "";

  //             return `
  //         <tr>
  //           <td class="border p-2 ${pageBreakClass}">${
  //               bookingIndex * booking.travelers?.length + travelerIndex + 1
  //             }</td>
  //           <td class="border p-2">${traveler.traveler?.title || "N/A"}</td>
  //           <td class="border p-2">${traveler.traveler?.lastName || "N/A"}</td>
  //           <td class="border p-2">${traveler.traveler?.firstName || "N/A"}</td>
  //           <td class="border p-2">${dob}</td>
  //           <td class="border p-2">${
  //             traveler.traveler?.nationality || "N/A"
  //           }</td>
  //           <td class="border p-2">${
  //             traveler.traveler?.issuingCountry || "N/A"
  //           }</td>
  //           <td class="border p-2">${
  //             traveler.traveler?.documentNumber || "N/A"
  //           }</td>
  //           <td class="border p-2">${
  //             traveler.traveler?.documentExpiryDate
  //               ? new Date(
  //                   traveler.traveler.documentExpiryDate
  //                 ).toLocaleDateString()
  //               : "N/A"
  //           }</td>
  //           <td class="border p-2">${booking.id || "N/A"}</td>
  //         </tr>
  //       `;
  //           }) || []
  //       ).join("") ||
  //       '<tr><td colspan="10" class="border p-2 text-center">No passenger data available</td></tr>';

  //     // Set the HTML content
  //     element.innerHTML = `<!DOCTYPE html>
  //     <html lang="en">
  //     <head>
  //         <meta charset="UTF-8">
  //         <meta name="viewport" content="width=device-width, initial-scale=1.0">
  //         <title>Flight Manifest - RJ263</title>
  //         <style>
  //             * {
  //                 margin: 0;
  //                 padding: 0;
  //                 box-sizing: border-box;
  //             }

  //             body {
  //                 font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  //                 line-height: 1.6;
  //                 color: #333;
  //                 background: #ffffff;
  //                 padding: 20px;
  //                 max-width: 1200px;
  //                 margin: 0 auto;
  //             }

  //             .manifest-header {
  //                 border-bottom: 3px solid #e74c3c;
  //                 padding-bottom: 20px;
  //                 margin-bottom: 30px;
  //             }

  //             .agency-section {
  //                 background: #f8f9fa;
  //                 border: 1px solid #dee2e6;
  //                 border-radius: 8px;
  //                 padding: 25px;
  //                 margin-bottom: 30px;
  //             }

  //             .agency-header {
  //                 display: flex;
  //                 justify-content: space-between;
  //                 align-items: flex-start;
  //                 margin-bottom: 20px;
  //             }

  //             .agency-info {
  //                 flex: 1;
  //             }

  //             .agency-logo {
  //                 width: 80px;
  //                 height: 80px;
  //                 background: #e9ecef;
  //                 border: 2px solid #dee2e6;
  //                 border-radius: 8px;
  //                 display: flex;
  //                 align-items: center;
  //                 justify-content: center;
  //                 margin-right: 25px;
  //                 font-weight: bold;
  //                 color: #6c757d;
  //                 font-size: 12px;
  //             }

  //             .agency-name {
  //                 font-size: 28px;
  //                 font-weight: bold;
  //                 color: #2c3e50;
  //                 margin-bottom: 8px;
  //             }

  //             .agency-address {
  //                 color: #6c757d;
  //                 font-size: 16px;
  //             }

  //             .manifest-id-badge {
  //                 background: #e74c3c;
  //                 color: white;
  //                 padding: 15px 20px;
  //                 border-radius: 8px;
  //                 text-align: center;
  //                 min-width: 180px;
  //             }

  //             .manifest-id-label {
  //                 font-size: 12px;
  //                 text-transform: uppercase;
  //                 letter-spacing: 1px;
  //                 opacity: 0.9;
  //             }

  //             .manifest-id-number {
  //                 font-size: 18px;
  //                 font-weight: bold;
  //                 font-family: 'Courier New', monospace;
  //                 margin-top: 5px;
  //             }

  //             .contact-grid {
  //                 background: #ffffff;
  //                 border: 1px solid #dee2e6;
  //                 border-radius: 6px;
  //                 padding: 20px;
  //                 display: grid;
  //                 grid-template-columns: repeat(5, 1fr);
  //                 gap: 20px;
  //                 margin-bottom: 15px;
  //             }

  //             .contact-item {
  //                 text-align: left;
  //             }

  //             .contact-label {
  //                 font-size: 11px;
  //                 text-transform: uppercase;
  //                 letter-spacing: 0.5px;
  //                 color: #6c757d;
  //                 margin-bottom: 5px;
  //                 font-weight: 600;
  //             }

  //             .contact-value {
  //                 font-weight: 600;
  //                 color: #2c3e50;
  //                 font-size: 14px;
  //             }

  //             .authorization-status {
  //                 display: flex;
  //                 justify-content: space-between;
  //                 align-items: center;
  //                 font-size: 14px;
  //                 color: #6c757d;
  //             }

  //             .status-indicator {
  //                 display: flex;
  //                 align-items: center;
  //                 gap: 8px;
  //             }

  //             .status-dot {
  //                 width: 8px;
  //                 height: 8px;
  //                 background: #28a745;
  //                 border-radius: 50%;
  //             }

  //             .flight-details {
  //                 background: #f8f9fa;
  //                 border: 1px solid #dee2e6;
  //                 border-radius: 8px;
  //                 padding: 25px;
  //                 margin-bottom: 30px;
  //             }

  //             .flight-header {
  //                 display: flex;
  //                 align-items: center;
  //                 gap: 15px;
  //                 margin-bottom: 25px;
  //             }

  //             .flight-title {
  //                 font-size: 20px;
  //                 font-weight: bold;
  //                 color: #2c3e50;
  //             }

  //             .flight-type-badge {
  //                 background: #dc3545;
  //                 color: white;
  //                 padding: 6px 12px;
  //                 border-radius: 4px;
  //                 font-size: 12px;
  //                 font-weight: 600;
  //                 text-transform: uppercase;
  //             }

  //             .flight-info-grid {
  //                 display: grid;
  //                 grid-template-columns: repeat(4, 1fr);
  //                 gap: 20px;
  //                 margin-bottom: 25px;
  //             }

  //             .flight-info-card {
  //                 background: #ffffff;
  //                 border: 1px solid #dee2e6;
  //                 border-radius: 6px;
  //                 padding: 20px;
  //                 text-align: center;
  //             }

  //             .flight-info-label {
  //                 font-size: 11px;
  //                 text-transform: uppercase;
  //                 letter-spacing: 0.5px;
  //                 color: #6c757d;
  //                 margin-bottom: 8px;
  //                 font-weight: 600;
  //             }

  //             .flight-info-value {
  //                 font-size: 20px;
  //                 font-weight: bold;
  //                 color: #2c3e50;
  //                 font-family: 'Courier New', monospace;
  //             }

  //             .route-section {
  //                 display: grid;
  //                 grid-template-columns: 1fr 1fr;
  //                 gap: 20px;
  //             }

  //             .route-card {
  //                 background: #ffffff;
  //                 border: 1px solid #dee2e6;
  //                 border-radius: 6px;
  //                 padding: 20px;
  //             }

  //             .route-display {
  //                 display: flex;
  //                 align-items: center;
  //                 justify-content: space-between;
  //                 margin-top: 15px;
  //             }

  //             .airport-info {
  //                 text-align: center;
  //             }

  //             .airport-code {
  //                 font-size: 24px;
  //                 font-weight: bold;
  //                 color: #2c3e50;
  //                 font-family: 'Courier New', monospace;
  //             }

  //             .airport-label {
  //                 font-size: 12px;
  //                 color: #6c757d;
  //                 margin-top: 5px;
  //             }

  //             .route-line {
  //                 flex: 1;
  //                 display: flex;
  //                 align-items: center;
  //                 justify-content: center;
  //                 margin: 0 20px;
  //             }

  //             .route-arrow {
  //                 width: 40px;
  //                 height: 40px;
  //                 background: #e74c3c;
  //                 border-radius: 50%;
  //                 display: flex;
  //                 align-items: center;
  //                 justify-content: center;
  //                 color: white;
  //             }

  //             .times-grid {
  //                 display: grid;
  //                 grid-template-columns: 1fr 1fr;
  //                 gap: 20px;
  //                 margin-top: 15px;
  //             }

  //             .time-info {
  //                 text-align: center;
  //             }

  //             .time-label {
  //                 font-size: 12px;
  //                 color: #6c757d;
  //                 margin-bottom: 5px;
  //             }

  //             .time-value {
  //                 font-size: 24px;
  //                 font-weight: bold;
  //                 color: #2c3e50;
  //                 font-family: 'Courier New', monospace;
  //             }

  //             .passenger-section {
  //                 background: #f8f9fa;
  //                 border: 1px solid #dee2e6;
  //                 border-radius: 8px;
  //                 padding: 25px;
  //                 margin-bottom: 30px;
  //             }

  //             .passenger-header {
  //                 display: flex;
  //                 justify-content: space-between;
  //                 align-items: center;
  //                 margin-bottom: 20px;
  //             }

  //             .passenger-title {
  //                 font-size: 20px;
  //                 font-weight: bold;
  //                 color: #2c3e50;
  //             }

  //             .passenger-count {
  //                 background: #007bff;
  //                 color: white;
  //                 padding: 8px 16px;
  //                 border-radius: 6px;
  //                 font-weight: 600;
  //             }

  //             .passenger-table {
  //                 width: 100%;
  //                 border-collapse: collapse;
  //                 background: white;
  //                 border-radius: 6px;
  //                 overflow: hidden;
  //                 box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  //             }

  //             .passenger-table th {
  //                 background: #f8f9fa;
  //                 color: #495057;
  //                 font-weight: 600;
  //                 padding: 15px 12px;
  //                 text-align: left;
  //                 font-size: 12px;
  //                 text-transform: uppercase;
  //                 letter-spacing: 0.5px;
  //                 border-bottom: 2px solid #dee2e6;
  //             }

  //             .passenger-table td {
  //                 padding: 15px 12px;
  //                 border-bottom: 1px solid #dee2e6;
  //                 font-size: 14px;
  //             }

  //             .passenger-table tr:hover {
  //                 background: #f8f9fa;
  //             }

  //             .passenger-number {
  //                 font-weight: bold;
  //                 color: #e74c3c;
  //             }
  //             .passenger-table {
  //                 table-layout: fixed;
  //                 width: 100%;
  //                 font-size: 11px;
  //             }
  //             .passenger-table th:nth-child(1) { width: 3%; }  /* # */
  //             .passenger-table th:nth-child(2) { width: 5%; }  /* Title */
  //             .passenger-table th:nth-child(3) { width: 12%; } /* Last Name */
  //             .passenger-table th:nth-child(4) { width: 12%; } /* First Name */
  //             .passenger-table th:nth-child(5) { width: 12%; } /* DOB */
  //             .passenger-table th:nth-child(6) { width: 12%; } /* Nationality/Issuing */
  //             .passenger-table th:nth-child(7) { width: 12%; } /* Passport */
  //             .passenger-table th:nth-child(8) { width: 12%; } /* Expiry */
  //             .passenger-table th:nth-child(9) { width: 12%; } /* Booking ID */

  //             .passenger-table tr {
  //                 page-break-inside: avoid;
  //             }

  //             .passport-number {
  //                 font-family: 'Courier New', monospace;
  //                 font-weight: 600;
  //             }

  //             .booking-id {
  //                 font-family: 'Courier New', monospace;
  //                 font-size: 12px;
  //                 color: #6c757d;
  //             }

  //             .footer-section {
  //                 margin-top: 40px;
  //                 padding-top: 20px;
  //                 border-top: 2px solid #dee2e6;
  //                 display: flex;
  //                 justify-content: space-between;
  //                 align-items: center;
  //             }

  //             .generation-info {
  //                 color: #6c757d;
  //                 font-size: 14px;
  //             }

  //             .page-break {
  //                 page-break-before: always;
  //             }

  //             @media print {
  //                 body {
  //                     padding: 10px;
  //                 }

  //                 .manifest-header {
  //                     border-bottom: 2px solid #333;
  //                 }

  //                 .agency-section, .flight-details, .passenger-section {
  //                     border: 1px solid #333;
  //                     box-shadow: none;
  //                 }

  //                 .passenger-table {
  //                     box-shadow: none;
  //                 }
  //             }

  //             @media (max-width: 768px) {
  //                 .flight-info-grid {
  //                     grid-template-columns: repeat(2, 1fr);
  //                 }

  //                 .contact-grid {
  //                     grid-template-columns: repeat(2, 1fr);
  //                 }

  //                 .route-section {
  //                     grid-template-columns: 1fr;
  //                 }
  //             }
  //         </style>
  //     </head>
  //     <body>
  //         <!-- Manifest Header -->
  //         <div class="manifest-header relative">
  //             <h1 style="font-size: 32px; font-weight: bold; color: #2c3e50;">Flight Manifest</h1>
  //         </div>

  //         <!-- Agency Information Section -->
  //         <div class="agency-section">
  //             <div class="agency-header">
  //                 <div style="display: flex; align-items: flex-start;">
  //                     <img class="agency-logo" src='/images/logo/airvilla_logo_symbol_red.png'/>
  //                     <div class="agency-info">
  //                         <div class="agency-name">${
  //                           manifest.owner?.agencyName
  //                         }</div>
  //                         <div class="agency-address">${
  //                           manifest.owner?.address
  //                         }</div>
  //                     </div>
  //                 </div>
  //                 <div class="manifest-id-badge">
  //                     <div class="manifest-id-label">Manifest ID</div>
  //                     <div class="manifest-id-number">${
  //                       manifest?.manifestId
  //                     }</div>
  //                 </div>
  //             </div>

  //             <div class="contact-grid">
  //                 <div class="contact-item">
  //                     <div class="contact-label">Email</div>
  //                     <div class="contact-value">${manifest.owner?.email}</div>
  //                 </div>
  //                 <div class="contact-item">
  //                     <div class="contact-label">Website</div>
  //                     <div class="contact-value">${
  //                       manifest.owner?.website
  //                     }</div>
  //                 </div>
  //                 <div class="contact-item">
  //                     <div class="contact-label">CON</div>
  //                     <div class="contact-value">${
  //                       manifest.owner?.commercialOperationNo
  //                     }</div>
  //                 </div>
  //                 <div class="contact-item">
  //                     <div class="contact-label">IATA Number</div>
  //                     <div class="contact-value">${manifest.owner?.iataNo}</div>
  //                 </div>
  //                 <div class="contact-item">
  //                     <div class="contact-label">Phone</div>
  //                     <div class="contact-value">${
  //                       manifest.owner?.phoneNumber
  //                     }</div>
  //                 </div>
  //             </div>
  //         </div>

  //         <!-- Flight Details Section -->
  //         <div class="flight-details">
  //             <div class="flight-header">
  //                 <div class="flight-title">Flight Details</div>
  //             </div>

  //             <div class="flight-info-grid">
  //                 <div class="flight-info-card">
  //                     <div class="flight-info-label">Flight Number</div>
  //                     <div class="flight-info-value">${
  //                       manifest.flightNumber
  //                     }</div>
  //                 </div>
  //                 <div class="flight-info-card">
  //                     <div class="flight-info-label">Carrier</div>
  //                     <div class="flight-info-value">${manifest?.carrier}</div>
  //                 </div>
  //                 <div class="flight-info-card">
  //                     <div class="flight-info-label">Flight Date</div>
  //                     <div class="flight-info-value">${getFormatDateTable(
  //                       manifest.flightDate
  //                     )}</div>
  //                 </div>
  //                 <div class="flight-info-card">
  //                     <div class="flight-info-label">Travel Class</div>
  //                     <div class="flight-info-value">${
  //                       manifest?.travelClass
  //                     }</div>
  //                 </div>
  //             </div>

  //             <div class="route-section">
  //                 <div class="route-card">
  //                     <div class="flight-info-label">Route</div>
  //                     <div class="route-display">
  //                         <div class="airport-info">
  //                             <div class="airport-code">${
  //                               manifest?.departure.airportCode
  //                             }</div>
  //                             <div class="airport-label">Departure</div>
  //                         </div>
  //                         <div class="route-line">
  //                             <div style="height: 2px; background: #dee2e6; flex: 1;"></div>
  //                             <div class="route-arrow">
  //                                 <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
  //                                     <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
  //                                 </svg>
  //                             </div>
  //                             <div style="height: 2px; background: #dee2e6; flex: 1;"></div>
  //                         </div>
  //                         <div class="airport-info">
  //                             <div class="airport-code">${
  //                               manifest?.arrival.airportCode
  //                             }</div>
  //                             <div class="airport-label">Arrival</div>
  //                         </div>
  //                     </div>
  //                 </div>

  //                 <div class="route-card">
  //                     <div class="flight-info-label">Flight Times</div>
  //                     <div class="times-grid">
  //                         <div class="time-info">
  //                             <div class="time-label">Departure</div>
  //                             <div class="time-value">${getFormatTime(
  //                               manifest?.departureTime
  //                             )}</div>
  //                         </div>
  //                         <div class="time-info">
  //                             <div class="time-label">Arrival</div>
  //                             <div class="time-value">${getFormatTime(
  //                               manifest?.arrivalTime
  //                             )}</div>
  //                         </div>
  //                     </div>
  //                 </div>
  //             </div>
  //         </div>

  //         <!-- Passenger Manifest Section -->
  //         <div class="passenger-section">
  //             <div class="passenger-header">
  //                 <div class="passenger-title">Passenger Manifest</div>
  //                 <div class="passenger-count">${
  //                   manifest?.passengerCount
  //                 } Passengers</div>
  //             </div>

  //             <table class="passenger-table">
  //                 <thead>
  //                     <tr>
  //                         <th>#</th>
  //                         <th>Title</th>
  //                         <th>Last Name</th>
  //                         <th>First Name</th>
  //                         <th>Date of Birth</th>
  //                         <th style="width: 10px;">Nationality / Issuing Country</th>
  //                         <th>Passport Number</th>
  //                         <th>Passport Expiry</th>
  //                     </tr>
  //                 </thead>
  //                 <tbody>
  //                   ${passengerRows}
  //                 </tbody>
  //             </table>
  //         </div>

  //         <!-- Footer Section -->
  //         <div class="footer-section">
  //             <div class="generation-info">
  //                 <strong>Document Status:</strong> Official Flight Manifest | <strong>Generated:</strong> June 03, 2025
  //             </div>
  //             <div style="color: #6c757d; font-size: 12px; max-width: 600px; line-height: 1.4;">
  //                 <strong>CONFIDENTIALITY NOTICE:</strong> This transmission contains restricted passenger data. Unauthorized access, use, or disclosure is prohibited. If received in error, delete immediately and notify sender.
  //             </div>
  //         </div>
  //     </body>
  //     </html>`;

  //     // Append to body to ensure styles are computed
  //     document.body.appendChild(element);
  //     console.log(element);

  //     // Create a print-specific stylesheet
  //     const style = document.createElement("style");
  //     style.textContent = `
  //     @media print {
  //       @page { size: A4; margin: 1cm; }
  //       body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
  //       .page-break { page-break-before: always; }
  //     }
  //   `;
  //     element.prepend(style);

  //     // Use html2pdf
  //     const opt = {
  //       margin: [10, 0, 10, 0], // top, right, bottom, left
  //       filename: `manifest-${manifest.manifestId}.pdf`,
  //       image: { type: "jpeg", quality: 0.98 },
  //       html2canvas: {
  //         scale: 2,
  //         useCORS: true,
  //         logging: true,
  //         letterRendering: true,
  //         allowTaint: true,
  //         scrollX: 0,
  //         scrollY: 0,
  //       },
  //       jsPDF: {
  //         unit: "mm",
  //         format: "a4",
  //         orientation: "portrait",
  //         putOnlyUsedFonts: true,
  //         floatPrecision: 16,
  //       },
  //       pagebreak: {
  //         mode: ["avoid-all", "css", "legacy"],
  //         before: ".page-break-before",
  //         after: ".page-break-after",
  //         avoid: "tr",
  //       },
  //     };
  //     // Define table headers
  //     const headers = [
  //       "#",
  //       "Title",
  //       "Last Name",
  //       "First Name",
  //       "DOB",
  //       "Nationality",
  //       "Issuing Country",
  //       "Document #",
  //       "Expiry",
  //       "Booking ID",
  //     ];
  //     // Add the table using autoTable
  //     (doc as any).autoTable({
  //       head: [headers],
  //       body: passengerRows,
  //       startY: 50, // Start below the header
  //       headStyles: {
  //         fillColor: [41, 128, 185], // Dark blue header
  //         textColor: 255,
  //         fontStyle: "bold",
  //         fontSize: 8,
  //       },
  //       styles: {
  //         fontSize: 7,
  //         cellPadding: 2,
  //         overflow: "linebreak",
  //         valign: "middle",
  //       },
  //       columnStyles: {
  //         0: { cellWidth: 10 }, // #
  //         1: { cellWidth: 15 }, // Title
  //         2: { cellWidth: 25 }, // Last Name
  //         3: { cellWidth: 25 }, // First Name
  //         4: { cellWidth: 20 }, // DOB
  //         5: { cellWidth: 20 }, // Nationality
  //         6: { cellWidth: 25 }, // Issuing Country
  //         7: { cellWidth: 30 }, // Document #
  //         8: { cellWidth: 25 }, // Expiry
  //         9: { cellWidth: 25 }, // Booking ID
  //       },
  //       margin: { top: 10 },
  //       didDrawPage: function (data: any) {
  //         // Add page number
  //         const pageSize = doc.internal.pageSize;
  //         const pageHeight = pageSize.height
  //           ? pageSize.height
  //           : pageSize.getHeight();
  //         doc.setFontSize(10);
  //         doc.text(
  //           `Page ${data.pageCount}`,
  //           pageSize.width / 2,
  //           pageHeight - 10,
  //           { align: "center" }
  //         );
  //       },
  //     });

  //     // Generate and download PDF
  //     const pdf = await html2pdf().set(opt).from(element).toPdf().get("pdf");
  //     const blob = pdf.output("blob");
  //     const url = URL.createObjectURL(blob);
  //     const a = document.createElement("a");
  //     a.href = url;
  //     a.download = `manifest-${manifest.flightNumber || "flight"}-${
  //       manifest.flightDate || "date"
  //     }.pdf`;
  //     document.body.appendChild(a);
  //     a.click();

  //     // Clean up
  //     document.body.removeChild(a);
  //     URL.revokeObjectURL(url);
  //     document.body.removeChild(element);
  //   } catch (error) {
  //     console.error("Error generating PDF:", error);
  //     // Make sure to clean up even if there's an error
  //     const element = document.getElementById("pdf-export-container");
  //     if (element) {
  //       document.body.removeChild(element);
  //     }
  //   }
  // };

  const handleDownloadPdf = async (manifest: ManifestTicket) => {
    try {
      // Create temporary container for HTML content
      const container = document.createElement("div");
      container.id = "pdf-container";
      container.style.position = "absolute";
      container.style.left = "-9999px";
      container.style.width = "210mm";
      container.style.padding = "20px";
      container.style.backgroundColor = "#ffffff";
      container.innerHTML = `
<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Flight Manifest - ${manifest.flightNumber}</title>
          <style>
              * {
                  margin: 0;
                  padding: 0;
                  box-sizing: border-box;
              }

              body {
                  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                  line-height: 1.6;
                  color: #333;
                  background: #ffffff;
                  padding: 20px;
                  max-width: 1200px;
                  margin: 0 auto;
              }

              .manifest-header {
                  border-bottom: 3px solid #e74c3c;
                  padding-bottom: 20px;
                  margin-bottom: 30px;
              }

              .agency-section {
                  background: #f8f9fa;
                  border: 1px solid #dee2e6;
                  border-radius: 8px;
                  padding: 25px;
                  margin-bottom: 30px;
              }

              .agency-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  margin-bottom: 20px;
              }

              .agency-info {
                  flex: 1;
              }

              .agency-logo {
                  width: 80px;
                  height: 80px;
                  background: #e9ecef;
                  border: 2px solid #dee2e6;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 25px;
                  font-weight: bold;
                  color: #6c757d;
                  font-size: 12px;
              }

              .agency-name {
                  font-size: 28px;
                  font-weight: bold;
                  color: #2c3e50;
                  margin-bottom: 8px;
              }

              .agency-address {
                  color: #6c757d;
                  font-size: 16px;
              }

              .manifest-id-badge {
                  background: #e74c3c;
                  color: white;
                  padding: 15px 20px;
                  border-radius: 8px;
                  text-align: center;
                  min-width: 180px;
              }

              .manifest-id-label {
                  font-size: 12px;
                  text-transform: uppercase;
                  letter-spacing: 1px;
                  opacity: 0.9;
              }

              .manifest-id-number {
                  font-size: 18px;
                  font-weight: bold;
                  font-family: 'Courier New', monospace;
                  margin-top: 5px;
              }

              .contact-grid {
                  background: #ffffff;
                  border: 1px solid #dee2e6;
                  border-radius: 6px;
                  padding: 20px;
                  display: grid;
                  grid-template-columns: repeat(5, 1fr);
                  gap: 20px;
                  margin-bottom: 15px;
              }

              .contact-item {
                  text-align: left;
              }

              .contact-label {
                  font-size: 11px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  color: #6c757d;
                  margin-bottom: 5px;
                  font-weight: 600;
              }

              .contact-value {
                  font-weight: 600;
                  color: #2c3e50;
                  font-size: 14px;
              }

              .authorization-status {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  font-size: 14px;
                  color: #6c757d;
              }

              .status-indicator {
                  display: flex;
                  align-items: center;
                  gap: 8px;
              }

              .status-dot {
                  width: 8px;
                  height: 8px;
                  background: #28a745;
                  border-radius: 50%;
              }

              .flight-details {
                  background: #f8f9fa;
                  border: 1px solid #dee2e6;
                  border-radius: 8px;
                  padding: 25px;
                  margin-bottom: 30px;
              }

              .flight-header {
                  display: flex;
                  align-items: center;
                  gap: 15px;
                  margin-bottom: 25px;
              }

              .flight-title {
                  font-size: 20px;
                  font-weight: bold;
                  color: #2c3e50;
              }

              .flight-type-badge {
                  background: #dc3545;
                  color: white;
                  padding: 6px 12px;
                  border-radius: 4px;
                  font-size: 12px;
                  font-weight: 600;
                  text-transform: uppercase;
              }

              .flight-info-grid {
                  display: grid;
                  grid-template-columns: repeat(4, 1fr);
                  gap: 20px;
                  margin-bottom: 25px;
              }

              .flight-info-card {
                  background: #ffffff;
                  border: 1px solid #dee2e6;
                  border-radius: 6px;
                  padding: 20px;
                  text-align: center;
              }

              .flight-info-label {
                  font-size: 11px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  color: #6c757d;
                  margin-bottom: 8px;
                  font-weight: 600;
              }

              .flight-info-value {
                  font-size: 20px;
                  font-weight: bold;
                  color: #2c3e50;
                  font-family: 'Courier New', monospace;
              }

              .route-section {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 20px;
              }

              .route-card {
                  background: #ffffff;
                  border: 1px solid #dee2e6;
                  border-radius: 6px;
                  padding: 20px;
              }

              .route-display {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-top: 15px;
              }

              .airport-info {
                  text-align: center;
              }

              .airport-code {
                  font-size: 24px;
                  font-weight: bold;
                  color: #2c3e50;
                  font-family: 'Courier New', monospace;
              }

              .airport-label {
                  font-size: 12px;
                  color: #6c757d;
                  margin-top: 5px;
              }

              .route-line {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 20px;
              }

              .route-arrow {
                  width: 40px;
                  height: 40px;
                  background: #e74c3c;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
              }

              .times-grid {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 20px;
                  margin-top: 15px;
              }

              .time-info {
                  text-align: center;
              }

              .time-label {
                  font-size: 12px;
                  color: #6c757d;
                  margin-bottom: 5px;
              }

              .time-value {
                  font-size: 24px;
                  font-weight: bold;
                  color: #2c3e50;
                  font-family: 'Courier New', monospace;
              }

              .passenger-section {
                  background: #f8f9fa;
                  border: 1px solid #dee2e6;
                  border-radius: 8px;
                  padding: 25px;
                  margin-bottom: 30px;
              }

              .passenger-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px;
                  margin-bottom: 30px;
              }

              .passenger-title {
                  font-size: 20px;
                  font-weight: bold;
                  color: #2c3e50;
              }

              .passenger-count {
                  background: #007bff;
                  color: white;
                  padding: 8px 16px;
                  border-radius: 6px;
                  font-weight: 600;
              }

              .passenger-table {
                  width: 100%;
                  border-collapse: collapse;
                  background: white;
                  border-radius: 6px;
                  overflow: hidden;
                  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              }

              .passenger-table th {
                  background: #f8f9fa;
                  color: #495057;
                  font-weight: 600;
                  padding: 15px 12px;
                  text-align: left;
                  font-size: 12px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  border-bottom: 2px solid #dee2e6;
              }

              .passenger-table td {
                  padding: 15px 12px;
                  border-bottom: 1px solid #dee2e6;
                  font-size: 14px;
              }

              .passenger-table tr:hover {
                  background: #f8f9fa;
              }

              .passenger-number {
                  font-weight: bold;
                  color: #e74c3c;
              }
              .passenger-table {
                  table-layout: fixed;
                  width: 100%;
                  font-size: 11px;
              }
              .passenger-table th:nth-child(1) { width: 3%; }  /* # */
              .passenger-table th:nth-child(2) { width: 5%; }  /* Title */
              .passenger-table th:nth-child(3) { width: 12%; } /* Last Name */
              .passenger-table th:nth-child(4) { width: 12%; } /* First Name */
              .passenger-table th:nth-child(5) { width: 12%; } /* DOB */
              .passenger-table th:nth-child(6) { width: 12%; } /* Nationality/Issuing */
              .passenger-table th:nth-child(7) { width: 12%; } /* Passport */
              .passenger-table th:nth-child(8) { width: 12%; } /* Expiry */
              .passenger-table th:nth-child(9) { width: 12%; } /* Booking ID */

              .passenger-table tr {
                  page-break-inside: avoid;
              }

              .passport-number {
                  font-family: 'Courier New', monospace;
                  font-weight: 600;
              }

              .booking-id {
                  font-family: 'Courier New', monospace;
                  font-size: 12px;
                  color: #6c757d;
              }

              .footer-section {
                  margin-top: 40px;
                  padding-top: 20px;
                  border-top: 2px solid #dee2e6;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
              }

              .generation-info {
                  color: #6c757d;
                  font-size: 14px;
              }

              .page-break {
                  page-break-before: always;
              }

              @media print {
                  body {
                      padding: 10px;
                  }

                  .manifest-header {
                      border-bottom: 2px solid #333;
                  }

                  .agency-section, .flight-details, .passenger-section {
                      border: 1px solid #333;
                      box-shadow: none;
                  }

                  .passenger-table {
                      box-shadow: none;
                  }
              }

              @media (max-width: 768px) {
                  .flight-info-grid {
                      grid-template-columns: repeat(2, 1fr);
                  }

                  .contact-grid {
                      grid-template-columns: repeat(2, 1fr);
                  }

                  .route-section {
                      grid-template-columns: 1fr;
                  }
              }
          </style>
      </head>
      <body>
          <!-- Manifest Header -->
          <div class="manifest-header relative">
              <h1 style="font-size: 32px; font-weight: bold; color: #2c3e50;">Flight Manifest</h1>
          </div>

          <!-- Agency Information Section -->
          <div class="agency-section">
              <div class="agency-header">
                  <div style="display: flex; align-items: flex-start;">
                      <img class="agency-logo" src='/images/logo/airvilla_logo_symbol_red.png'/>
                      <div class="agency-info">
                          <div class="agency-name">${
                            manifest.owner?.agencyName
                          }</div>
                          <div class="agency-address">${formatAddress(
                            manifest.owner?.address
                          )}</div>
                      </div>
                  </div>
                  <div class="manifest-id-badge">
                      <div class="manifest-id-label">Manifest ID</div>
                      <div class="manifest-id-number">${
                        manifest?.manifestId
                      }</div>
                  </div>
              </div>

              <div class="contact-grid">
                  <div class="contact-item">
                      <div class="contact-label">Email</div>
                      <div class="contact-value">${manifest.owner?.email}</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">Website</div>
                      <div class="contact-value">${
                        manifest.owner?.website
                      }</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">CON</div>
                      <div class="contact-value">${
                        manifest.owner?.commercialOperationNo
                      }</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">IATA Number</div>
                      <div class="contact-value">${manifest.owner?.iataNo}</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">Phone</div>
                      <div class="contact-value">${
                        manifest.owner?.phoneNumber
                      }</div>
                  </div>
              </div>
          </div>

          <!-- Flight Details Section -->
          <div class="flight-details">
              <div class="flight-header">
                  <div class="flight-title">Flight Details</div>
              </div>

              <div class="flight-info-grid">
                  <div class="flight-info-card">
                      <div class="flight-info-label">Flight Number</div>
                      <div class="flight-info-value">${
                        manifest.flightNumber
                      }</div>
                  </div>
                  <div class="flight-info-card">
                      <div class="flight-info-label">Carrier</div>
                      <div class="flight-info-value">${manifest?.carrier}</div>
                  </div>
                  <div class="flight-info-card">
                      <div class="flight-info-label">Flight Date</div>
                      <div class="flight-info-value">${getFormatDateTable(
                        manifest.flightDate
                      )}</div>
                  </div>
                  <div class="flight-info-card">
                      <div class="flight-info-label">Travel Class</div>
                      <div class="flight-info-value">${capitalizeFirst(
                        manifest?.travelClass
                      )}</div>
                  </div>
              </div>

              <div class="route-section">
                  <div class="route-card">
                      <div class="flight-info-label">Route</div>
                      <div class="route-display">
                          <div class="airport-info">
                              <div class="airport-code">${
                                manifest?.departure.airportCode
                              }</div>
                              <div class="airport-label">Departure</div>
                          </div>
                          <div class="route-line">
                              <div style="height: 2px; background: #dee2e6; flex: 1;"></div>
                              <div class="route-arrow">
                                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                      <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                  </svg>
                              </div>
                              <div style="height: 2px; background: #dee2e6; flex: 1;"></div>
                          </div>
                          <div class="airport-info">
                              <div class="airport-code">${
                                manifest?.arrival.airportCode
                              }</div>
                              <div class="airport-label">Arrival</div>
                          </div>
                      </div>
                  </div>

                  <div class="route-card">
                      <div class="flight-info-label">Flight Times</div>
                      <div class="times-grid">
                          <div class="time-info">
                              <div class="time-label">Departure</div>
                              <div class="time-value">${getFormatTime(
                                manifest?.departureTime
                              )}</div>
                          </div>
                          <div class="time-info">
                              <div class="time-label">Arrival</div>
                              <div class="time-value">${getFormatTime(
                                manifest?.arrivalTime
                              )}</div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <!-- Passenger Manifest Section -->
          <div class="passenger-section">
              <div class="passenger-header">
                  <div class="passenger-title">Passenger Manifest</div>
                  <div class="passenger-count">${getActualPassengerCount(
                    manifest
                  )} Passengers</div>
              </div>
          </div>
      </body>
      </html>
      `;

      document.body.appendChild(container);

      // Convert HTML to canvas
      const canvas = await html2canvas(container, {
        scale: 2,
        useCORS: true,
        logging: false,
      });

      // Create PDF
      const doc = new jsPDF("p", "mm", "a4");

      // Get image data
      const imgData = canvas.toDataURL("image/png");
      const imgProps = doc.getImageProperties(imgData);
      const pdfWidth = doc.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Add header image
      doc.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight, "header");

      // Calculate start position for table (after header content)
      const startY = pdfHeight + 10;

      // Generate passenger data
      let passengerNumber = 1;
      const passengerRows =
        manifest.Booking?.flatMap((booking, index) =>
          booking.travelers?.map((passenger) => [
            passengerNumber++,
            `${
              passenger.traveler?.title
                ? capitalizeFirst(passenger.traveler.title)
                : ""
            } ${passenger.traveler?.firstName || ""} ${
              passenger.traveler?.lastName || ""
            }`.trim(),
            passenger.traveler?.dateOfBirth
              ? getFormatDateTable(passenger.traveler.dateOfBirth)
              : "N/A",
            `${passenger.traveler?.nationality || "N/A"} / ${
              passenger.traveler?.issuingCountry || "N/A"
            }`,
            passenger.traveler?.documentNumber || "N/A",
            passenger.traveler?.documentExpiryDate
              ? getFormatDateTable(passenger.traveler.documentExpiryDate)
              : "N/A",
            passenger.bookingId || "N/A",
          ])
        ) || [];

      // Add autoTable with pagination
      autoTable(doc, {
        startY: startY,
        head: [
          [
            "#",
            "Passenger Name",
            "Date of Birth",
            "Nationality / Issuing Country",
            "Document #",
            "Expiry",
            "Booking ID",
          ],
        ],
        body: passengerRows,
        headStyles: {
          fillColor: [231, 76, 60],
          textColor: 255,
          fontStyle: "bold",
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245],
        },
        styles: {
          fontSize: 9,
          cellPadding: 3,
          overflow: "linebreak",
        },
        didDrawPage: (data: Parameters<PageHook>[0]) => {
          const pageCount = data.doc.internal.getNumberOfPages();
          const pageNumber = data.pageNumber;
          // Add footer to each page
          if (pageCount > 1) {
            doc.setFontSize(10);
            doc.setTextColor(100);
            doc.text(
              `Page ${pageNumber} of ${pageCount}`,
              pdfWidth / 2,
              doc.internal.pageSize.height - 10,
              { align: "center" }
            );

            // Add simple header for subsequent pages
            // if (data.pageNumber > 1) {
            //   doc.setFontSize(12);
            //   doc.setTextColor(40);
            //   doc.text(
            //     `Flight ${manifest.flightNumber} - ${getFormatDateTable(
            //       manifest.flightDate
            //     )}`,
            //     14,
            //     15
            //   );
            // }
          }
        },
      });

      // Add main footer to last page
      doc.setPage(doc.getNumberOfPages());
      doc.setFontSize(10);
      doc.setTextColor(150);
      doc.text(
        `Manifest generated: ${new Date().toLocaleString()}`,
        pdfWidth - 14,
        doc.internal.pageSize.height - 10,
        { align: "right" }
      );

      // Clean up
      document.body.removeChild(container);
      doc.save(`manifest-${manifest.manifestId}.pdf`);
    } catch (error) {
      console.error("Error generating PDF:", error);
      const container = document.getElementById("pdf-container");
      if (container) document.body.removeChild(container);
    }
  };

  return (
    <div
      id="pdf-export-container"
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 custom-scrollbar"
    >
      <div className="dark:bg-gray-800 bg-white rounded-lg w-full max-w-6xl max-h-screen overflow-auto">
        {/* Header */}
        <div className="dark:bg-gray-800 bg-white px-6 py-4 flex items-center justify-between">
          <div>
            <h2 className="font-semibold dark:text-white text-gray-800 text-xl leading-7">
              Flight Manifest
            </h2>
          </div>
          <button
            onClick={onClose}
            className="bg-red-500 text-white hover:opacity-80 transition duration-300 p-2 rounded"
          >
            <X size={20} />
          </button>
        </div>

        <div id="flight-manifest-content">
          {/* Agency Information Header */}
          <div className="px-6 pt-6 pb-4">
            <div className="dark:bg-gray-700 bg-gray-100 rounded-lg p-6">
              {/* Agency Header Row */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-6">
                  {/* Agency Logo Placeholder */}
                  <div className="w-16 h-16 dark:bg-gray-600 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="dark:text-gray-500 text-gray-800 text-xs font-medium">
                      <Image src={logo} alt="Logo" width={50} height={50} />
                    </span>
                  </div>

                  {/* Agency Name and Primary Info */}
                  <div>
                    <h2 className="text-2xl font-bold dark:text-white text-gray-800 mb-1">
                      {booking?.owner?.agencyName}
                    </h2>
                    <div className="flex items-center space-x-4 dark:text-gray-300 text-gray-700">
                      <span>{formatAddress(booking?.owner?.address)}</span>
                    </div>
                  </div>
                </div>

                {/* Manifest ID Badge */}
                <div className="text-center">
                  <div className="dark:bg-gray-600 bg-gray-200 rounded-lg px-4 py-2">
                    <div className="text-left text-xs font-medium dark:text-gray-200 text-gray-600 uppercase tracking-wide">
                      Manifest ID
                    </div>
                    <div className="text-lg font-bold dark:text-white text-gray-800">
                      {booking?.manifestId}
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information Grid */}
              <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-5 gap-4 text-sm">
                  <div>
                    <div className="dark:text-gray-300 text-gray-600 text-xs uppercase tracking-wide mb-1">
                      Email
                    </div>
                    <div className="dark:text-white text-gray-800 font-medium">
                      {booking?.owner?.email}
                    </div>
                  </div>
                  <div>
                    <div className="dark:text-gray-300 text-gray-600 text-xs uppercase tracking-wide mb-1">
                      Website
                    </div>
                    <div className="dark:text-white text-gray-800 font-medium">
                      {booking?.owner?.website}
                    </div>
                  </div>
                  <div>
                    <div className="dark:text-gray-300 text-gray-600 text-xs uppercase tracking-wide mb-1">
                      CON
                    </div>
                    <div className="dark:text-white text-gray-800 font-medium">
                      {booking?.owner?.commercialOperationNo}
                    </div>
                  </div>
                  <div>
                    <div className="dark:text-gray-300 text-gray-600 text-xs uppercase tracking-wide mb-1">
                      IATA Number
                    </div>
                    <div className="dark:text-white text-gray-800 font-medium">
                      {booking?.owner?.iataNo}
                    </div>
                  </div>
                  <div>
                    <div className="dark:text-gray-300 text-gray-600 text-xs uppercase tracking-wide mb-1">
                      Phone
                    </div>
                    <div className="dark:text-white text-gray-800 font-medium">
                      {booking?.owner?.phoneNumber}
                    </div>
                  </div>
                </div>
              </div>

              {/* Manifest Status Indicator */}
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="dark:text-gray-300 text-gray-600 text-sm">
                    Authorized to submit passenger manifests
                  </span>
                </div>
                <div className="dark:text-gray-300 text-gray-600 text-sm">
                  Generated: {new Date().toLocaleDateString()} at{" "}
                  {new Date().toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>

          {/* Flight Details Card */}
          <div className="px-6 py-4">
            <div className="dark:bg-gray-700 bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold dark:text-white text-gray-800 mb-6 flex items-center">
                Flight Details
              </h3>

              {/* Primary Flight Info Row */}
              <div className="grid grid-cols-4 gap-8 mb-6">
                <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                  <div className="text-xs font-medium dark:text-gray-300 text-gray-600 uppercase tracking-wide mb-2">
                    Flight Number
                  </div>
                  <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                    {booking?.flightNumber}
                  </div>
                </div>

                <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                  <div className="text-xs font-medium dark:text-gray-300 text-gray-600 uppercase tracking-wide mb-2">
                    Carrier
                  </div>
                  <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                    {booking?.carrier}
                  </div>
                </div>

                <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                  <div className="text-xs font-medium dark:text-gray-300 text-gray-600 uppercase tracking-wide mb-2">
                    Flight Date
                  </div>
                  <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                    {getFormatDateTable(booking?.flightDate)}
                  </div>
                </div>

                <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                  <div className="text-xs font-medium dark:text-gray-300 text-gray-600 uppercase tracking-wide mb-2">
                    Travel Class
                  </div>
                  <div className="text-xl font-bold dark:text-white text-gray-800 font-mono capitalize">
                    {booking?.travelClass}
                  </div>
                </div>
              </div>

              {/* Route and Times Row */}
              <div className="grid grid-cols-2 gap-8">
                {/* Route Section */}
                <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                  <div className="text-xs font-medium dark:text-gray-300 text-gray-600 uppercase tracking-wide mb-3">
                    Route
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="text-center">
                      <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                        {fromAirport}
                      </div>
                      <div className="text-xs dark:text-gray-300 text-gray-600 mt-1">
                        Departure
                      </div>
                    </div>
                    <div className="flex-1 flex items-center justify-center">
                      <div className="h-px dark:bg-gray-400 bg-gray-500 flex-1"></div>
                      <div className="mx-4 dark:text-gray-400 text-gray-600">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z" />
                        </svg>
                      </div>
                      <div className="h-px dark:bg-gray-400 bg-gray-500 flex-1"></div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                        {toAirport}
                      </div>
                      <div className="text-xs dark:text-gray-300 text-gray-600 mt-1">
                        Arrival
                      </div>
                    </div>
                  </div>
                </div>

                {/* Times Section */}
                <div className="dark:bg-gray-600 bg-gray-200 rounded-lg p-4">
                  <div className="text-xs font-medium dark:text-gray-300 text-gray-600 uppercase tracking-wide mb-3">
                    Flight Times
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-xs dark:text-gray-300 text-gray-600 mb-1">
                        Departure
                      </div>
                      <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                        {getFormatTime(booking?.departureTime)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs dark:text-gray-300 text-gray-600 mb-1">
                        Arrival
                      </div>
                      <div className="text-xl font-bold dark:text-white text-gray-800 font-mono">
                        {getFormatTime(booking?.arrivalTime)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Passenger List */}
          <div className="px-6 py-4 max-h-96 overflow-y-auto">
            <div className="dark:bg-gray-700 bg-gray-50 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold dark:text-white text-gray-800">
                  Passenger Manifest
                </h3>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="dark:bg-gray-700 bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        #
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Title
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Last Name
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        First Name
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Date of Birth
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Nationality
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Issuing Country
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Passport Number
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Passport Expiry
                      </th>
                      <th className="text-left p-3 text-sm font-bold dark:text-gray-300 text-gray-600">
                        Booking ID
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {(() => {
                      let passengerNumber = 1;
                      return booking?.Booking?.flatMap(
                        (booking: any, index: number) =>
                          booking.travelers?.map((passenger: any) => (
                            <tr
                              key={passenger.id}
                              className="border-b border-gray-600"
                            >
                              <td className="p-3 dark:text-white text-gray-600">
                                {passengerNumber++}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {capitalizeFirst(passenger.traveler.title)}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {passenger.traveler.lastName}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {passenger.traveler.firstName}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {getFormatDateTable(
                                  passenger.traveler.dateOfBirth
                                )}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {passenger.traveler.nationality}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {passenger.traveler.issuingCountry}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600 font-mono">
                                {passenger.traveler.documentNumber}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600">
                                {getFormatDateTable(
                                  passenger.traveler.documentExpiryDate
                                )}
                              </td>
                              <td className="p-3 dark:text-white text-gray-600 font-mono">
                                {passenger.bookingId}
                              </td>
                            </tr>
                          ))
                      );
                    })()}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => handleDownloadPdf(booking)}
              className="bg-gray-500 hover:opacity-80 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition duration-300"
            >
              <Download size={16} />
              <span>Download Manifest</span>
            </button>
            <button
              className="bg-red-500 hover:opacity-80 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition duration-300"
              onClick={() => onEmailClick(booking)}
            >
              <Mail size={16} />
              <span>Email Manifest</span>
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white bg-gray-300 hover:bg-gray-200 text-gray-600 px-4 py-2 rounded-lg transition duration-300"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Filter Controls Component
 * Handles all filtering options for manifest data
 */
interface FilterControlsProps {
  onFiltersChange?: (filters: any) => void;
  filters: ManifestFilter;
  setFilters: React.Dispatch<React.SetStateAction<ManifestFilter>>;
}

const FilterControls: React.FC<FilterControlsProps> = ({
  onFiltersChange,
  filters,
  setFilters,
}) => {
  const handleResetFilters = useCallback(() => {
    const resetFilters = {
      status: "All",
      flightDate: "All Time",
      timeUntilFlight: "All Time",
    };
    setFilters(resetFilters);
  }, []);

  // Notify parent of filter changes
  useEffect(() => {
    onFiltersChange?.({
      status: filters.status,
      flightDate: filters.flightDate,
      timeUntilFlight: filters.timeUntilFlight,
    });
  }, [
    filters.status,
    filters.flightDate,
    filters.timeUntilFlight,
    // onFiltersChange,
  ]);

  const selectStyle = {
    backgroundColor: "#374151",
    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
    backgroundPosition: "right 0.5rem center",
    backgroundRepeat: "no-repeat",
    backgroundSize: "1.5em 1.5em",
    paddingRight: "2.5rem",
  };

  const renderDropdown = ({
    label,
    value,
    options,
    onChange,
  }: {
    label: string;
    value: string;
    options: readonly string[];
    onChange: (value: string) => void;
  }) => (
    <div className="w-full">
      <label className="block text-sm font-medium mb-1 capitalize text-gray-700 dark:text-white">
        {label}
      </label>
      <Menu as="div" className="relative flex w-full">
        {({ open }) => (
          <>
            <Menu.Button
              className={`btn w-full justify-between rounded-lg min-w-[11rem] h-[45px] bg-gray-100 dark:bg-gray-700 dark:border-gray-700 text-gray-500 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-200 ${
                open ? "ring-2 ring-red-500" : ""
              }`}
              aria-label={`Select ${label}`}
            >
              {value}
              <ChevronDown
                className="text-gray-500 dark:text-gray-400 ml-3 shrink-0 mx-3 absolute top-3 right-0 pointer-events-none"
                size={20}
              />
            </Menu.Button>
            <Transition
              className="z-10 absolute top-full right-0 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 py-1.5 rounded shadow-lg overflow-hidden mt-1"
              enter="transition ease-out duration-100 transform"
              enterFrom="opacity-0 -translate-y-2"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-out duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Menu.Items className="font-medium text-sm text-gray-600 dark:text-gray-300 focus:outline-none divide-y-2 divide-gray-200/50 dark:divide-gray-700/50">
                {options.map((option, optionIndex) => (
                  <Menu.Item key={optionIndex}>
                    {({ active }) => (
                      <button
                        className={`flex items-center w-full py-1 px-3 cursor-pointer ${
                          active ? "bg-gray-50 dark:bg-gray-700" : ""
                        } ${option === value ? "text-red-500" : ""}`}
                        onClick={() => {
                          onChange(option);
                        }}
                      >
                        <Check
                          className={`shrink-0 mr-2 text-red-500 ${
                            option !== value && "invisible"
                          }`}
                          size={20}
                        />
                        {option}
                      </button>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </>
        )}
      </Menu>
    </div>
  );

  return (
    <div className="dark:bg-gray-800 bg-white rounded-lg p-5 mb-5 flex items-end space-x-4">
      <div className="flex-1">
        {renderDropdown({
          label: "Status",
          value: filters.status ?? "All",
          options: FILTER_OPTIONS.ManifestStatus,
          onChange: (value) =>
            setFilters({ ...filters, status: value ?? "All" }),
        })}
      </div>
      <div className="flex-1">
        {renderDropdown({
          label: "Flight Date",
          value: filters.flightDate ?? "All Time",
          options: FILTER_OPTIONS.FlightDate,
          onChange: (value) =>
            setFilters({ ...filters, flightDate: value ?? "All Time" }),
        })}
      </div>
      <div className="flex-1">
        {renderDropdown({
          label: "Time Until Flight",
          value: filters.timeUntilFlight ?? "All Time",
          options: FILTER_OPTIONS.TimeUntilFlight,
          onChange: (value) =>
            setFilters({ ...filters, timeUntilFlight: value ?? "All Time" }),
        })}
      </div>
      <button
        className="h-[45px] bg-blue-500 text-white text-sm hover:bg-blue-600 transition duration-300 py-2 px-4 rounded-lg"
        onClick={handleResetFilters}
      >
        Reset Filters
      </button>
    </div>
  );
};

/**
 * Navigation Tabs Component
 * Manages tab-based filtering and displays manifest counts
 */
interface NavigationTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  manifestCounts: ManifestCounts;
  totalPassengers: number;
}

const NavigationTabs: React.FC<NavigationTabsProps> = ({
  activeTab,
  onTabChange,
  manifestCounts,
  totalPassengers,
}) => {
  return (
    <div className="flex items-center space-x-6">
      <div className="flex flex-wrap items-center justify-between">
      <h2 className="font-semibold text-gray-800 dark:text-gray-100 mb-4 sm:mb-0">

        <span className="font-bold text-red-500 mr-2 text-2xl leading-7">
          {manifestCounts.totalManifests}
        </span>
        <span className="font-semibold text-lg leading-7">Total Bookings</span>
        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {totalPassengers} passengers
        </div>
      </h2>
      </div>
      <div className="h-8 w-px dark:bg-gray-700 bg-gray-300"></div>
      <div className="flex items-center space-x-2">
        {NAVIGATION_TABS.map((tab) => (
          <span
            key={tab.name}
            className={`cursor-pointer py-1 px-2 rounded font-normal text-sm leading-5 ${
              activeTab === tab.name
                ? "bg-red-500 text-white"
                : "dark:text-gray-700text-gray-300 dark:hover:bg-gray-700 hover:bg-gray-300"
            }`}
            onClick={() => onTabChange(tab.name)}
          >
            {tab.name}
          </span>
        ))}
      </div>
    </div>
  );
};

/**
 * Manifest Table Row Component
 * Renders individual manifest data rows with proper formatting
 */
interface ManifestTableRowProps {
  manifest: ManifestTicket;
  onEditClick: (manifest: ManifestTicket) => void;
}

const ManifestTableRow: React.FC<ManifestTableRowProps> = ({
  manifest,
  onEditClick,
}) => {
  const {
    flightDate,
    departureTime,
    arrivalTime,
    passengerCount,
    route,
    carrier,
    flightNumber,
    manifestStatus,
  } = manifest;

  const timeUntilFlight = useMemo(
    () => calculateTimeUntilFlight(flightDate),
    [flightDate]
  );

  // Calculate actual passenger count
  const passengerCountFormatted = useMemo(
    () => getActualPassengerCount(manifest),
    [manifest]
  );

  return (
    <tr className="border-b dark:border-gray-700 border-gray-200">
      <td className="p-4 whitespace-nowrap font-mono font-semibold text-blue-400 text-sm leading-5">
        {manifest.manifestId}
      </td>
      <td className="p-4 whitespace-nowrap font-medium text-sm leading-5">
        {flightNumber}
      </td>
      <td className="p-4 whitespace-nowrap font-medium text-sm leading-5">
        {getFormatDateTable(flightDate)}
      </td>
      <td className="p-4 whitespace-nowrap font-medium text-sm leading-5">
        {route}
      </td>
      <td className="p-4 whitespace-nowrap font-medium text-sm leading-5">
        {getFormatTime(departureTime)}
      </td>
      <td className="p-4 whitespace-nowrap font-medium text-sm leading-5">
        {getFormatTime(arrivalTime)}
      </td>
      <td className="p-4 whitespace-nowrap font-medium text-sm leading-5">
        {carrier}
      </td>
      <td className="p-4 whitespace-nowrap text-xs leading-[18px]">
        <span
          className={`px-3 py-1 rounded-md font-medium ${
            STATUS_STYLES[manifestStatus as StatusKey] ||
            "bg-gray-100 text-gray-800"
          }`}
        >
          {formatStatusForDisplay(manifestStatus)}
        </span>
      </td>
      <td className="p-4 whitespace-nowrap text-xs leading-[18px]">
        <span className="dark:text-white text-gray-700 font-medium">
          {timeUntilFlight}
        </span>
      </td>
      <td className="p-4 whitespace-nowrap text-xs leading-[18px]">
        <span className="bg-blue-200 text-blue-800 px-2 py-1 rounded font-medium text-xs leading-[18px]">
          {passengerCountFormatted}
        </span>
      </td>
      <td className="p-4 whitespace-nowrap">
        <div className="flex space-x-2">
          <button
            className="dark:text-gray-400 text-gray-500 dark:hover:text-gray-300 hover:text-gray-600 rounded-full p-1 dark:hover:bg-gray-700 hover:bg-gray-400 transition duration-300"
            title="Manage Passenger Information"
            onClick={() => onEditClick(manifest)}
          >
            <Edit size={18} />
          </button>
        </div>
      </td>
    </tr>
  );
};

/**
 * Manifest Table Component
 * Displays the main data table with all manifest entries
 */
interface ManifestTableProps {
  manifests: ManifestTicket[];
  onEditClick: (manifest: ManifestTicket) => void;
  hasMore: boolean;
  loadingMore: boolean;
  observerRef: any;
}

const ManifestTable: React.FC<ManifestTableProps> = ({
  manifests,
  onEditClick,
  hasMore,
  loadingMore,
  observerRef,
}) => {
  return (
    <div className="overflow-x-auto custom-scrollbar max-h-[calc(100vh-400px)]">
      <table className="w-full table-auto">
        <thead className="text-xs font-semibold capitalize text-gray-800 dark:text-gray-50 bg-gray-50 dark:bg-gray-900/20 border-t border-b border-gray-200 dark:border-gray-700 text-left sticky -top-0.5">
          <tr>
            {TABLE_HEADERS.map((header) => (
              <th
                key={header}
                className="p-4 whitespace-nowrap font-semibold text-sm  bg-gray-300 dark:bg-gray-700 z-10"
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {manifests.map((manifest) => (
            <ManifestTableRow
              key={manifest.id}
              manifest={manifest}
              onEditClick={onEditClick}
            />
          ))}
        </tbody>
      </table>
      {manifests?.length === 0 && (
        <div className="text-lg my-10 flex justify-center items-center">
          <h1>No Bookings Found</h1>
        </div>
      )}

      <div className="w-full">
        {loadingMore && (
          <div className="py-4">
            <div className="flex justify-center">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse delay-75"></div>
                <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse delay-150"></div>
              </div>
            </div>
          </div>
        )}
        {!loadingMore && hasMore && manifests.length > 0 && (
          <div ref={observerRef} className="w-full h-10" />
        )}
      </div>
    </div>
  );
};

/**
 * Search and Actions Bar Component
 * Contains search functionality and action buttons
 */
interface SearchAndActionsProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onExportClick: () => void;
}

const SearchAndActions: React.FC<SearchAndActionsProps> = ({
  searchQuery,
  onSearchChange,
  onExportClick,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [rawValue, setRawValue] = useState(searchQuery);
  const debouncedValue = useDebounce(rawValue, 300);

  useEffect(() => {
    if (debouncedValue !== searchQuery) onSearchChange(debouncedValue);
  }, [debouncedValue]);

  useEffect(() => {
    if (document.activeElement !== inputRef.current) {
      inputRef.current?.focus();
    }
  }, [rawValue]);

  return (
    <div className="flex items-center space-x-4">
      <button
        className="flex items-center space-x-2 bg-red-500 text-white text-sm hover:bg-red-600 transition duration-300 px-3 py-2 rounded-lg"
        onClick={onExportClick}
      >
        <Download size={16} />
        <span>Export</span>
      </button>
      <div className="relative">
        <input
          ref={inputRef}
          autoFocus
          type="text"
          placeholder="Search bookings..."
          className="dark:bg-gray-700 dark:text-white bg-gray-100 text-gray-700 rounded-lg py-2 pl-10 pr-4 w-64 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-none border-none"
          value={rawValue}
          onChange={(e) => {
            setRawValue(e.target.value);
          }}
        />
        <Search
          className="absolute left-3 top-2.5 dark:text-gray-400 text-gray-500"
          size={20}
        />
      </div>
    </div>
  );
};

// ===========================
// MAIN COMPONENT
// ===========================

/**
 * My Manifest Main Component
 * Central component managing the entire manifest management interface
 */
const MyManifestList: React.FC = () => {
  const dispatch = useAppDispatch();
  // State management
  const [manifests, setManifests] = useState<ManifestTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [manifestCounts, setManifestCounts] = useState<ManifestCounts>({
    totalManifests: 0,
  });
  const [isManifestPopupOpen, setIsManifestPopupOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<ManifestTicket | null>(
    null
  );
  const [isEmailPopupOpen, setIsEmailPopupOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [cursor, setCursor] = useState<string | null>(null);
  const [pageSize, setPageSize] = useState(20);
  const [filters, setFilters] = useState<ManifestFilter>({
    status: "All",
    flightDate: "All Time",
    timeUntilFlight: "All Time",
  });
  const debouncedFilters = useDebounce(filters, 300);
  const requestCtlRef = useRef<AbortController | null>(null);

  // Update filters coming from FilterControls and trigger data refresh
  const handleFiltersChange = useCallback((newFilters: ManifestFilter) => {
    setFilters((prev) => {
      // Avoid unnecessary re-renders if nothing changed
      if (JSON.stringify(prev) === JSON.stringify(newFilters)) return prev;
      return newFilters;
    });
  }, []);

  // Use IntersectionObserver via useInView hook
  const { ref, inView } = useInView({
    threshold: 0.5, // Trigger when 50% of the element is visible
    triggerOnce: false,
    rootMargin: "0px 0px 0px 0px", // No extra margin
    initialInView: false,
  });

  // Ref to track if we're currently loading more sales
  const isLoadingMoreRef = useRef(false);

  // Fetch manifest data from API
  useEffect(() => {
    // don’t run while debounce is “in-flight”
    if (!debouncedFilters) return;

    // cancel the previous request
    requestCtlRef.current?.abort();
    const controller = new AbortController();
    requestCtlRef.current = controller;

    const fetchManifestData = async () => {
      try {
        setLoading(true);
        const response = await getManifestTickets(
          page,
          cursor,
          pageSize,
          {
            ...debouncedFilters,
            search: searchQuery,
          },
          controller.signal
        );
        console.log("Response:", response);
        const updatedManifests = response.results.manifestTickets.map(
          (manifest) => updateManifestStatus(manifest)
        );
        console.log("Updated Manifests:", updatedManifests);
        setManifests(
          updatedManifests.map((manifest) => ({
            ...manifest,
            reference: manifest.refId,
            route: `${manifest.departure.airportCode} → ${manifest.arrival.airportCode}`,
            tripType: manifest?.Booking?.[0]?.tripType || "",
          }))
        );
        setManifestCounts({
          totalManifests: updatedManifests.length,
        });
        // Update hasMore based on whether we received any manifests
        setHasMore(response.results.manifestTickets.length > 0);

        // If we're using cursor-based pagination, check for nextCursor
        if (response.results.nextCursor !== undefined) {
          setHasMore(!!response.results.nextCursor);
          setNextCursor(response.results.nextCursor || null);
        }
      } catch (err) {
        setError("Failed to fetch manifest data");
        console.error("Error fetching manifest data:", err);
        setHasMore(false);
      } finally {
        setLoading(false);
      }
    };
    fetchManifestData();
  }, [page, cursor, pageSize, debouncedFilters, searchQuery]);

  // Reset pagination & data when filters or search query change
  useEffect(() => {
    setPage(1);
    setCursor(null);
    setNextCursor(null);
    setManifests([]);
  }, [debouncedFilters, searchQuery]);

  // Load more manifests when scrolling to the bottom
  const loadMore = useCallback(async () => {
    if (loadingMore || !nextCursor || !hasMore) return;

    // cancel the previous request
    requestCtlRef.current?.abort();
    const controller = new AbortController();
    requestCtlRef.current = controller;

    try {
      setLoadingMore(true);
      const response = await getManifestTickets(
        page + 1,
        nextCursor,
        pageSize,
        { ...debouncedFilters, search: searchQuery },
        controller.signal
      ); // Load 10 more items
      console.log("Response:", response);
      const newManifests = response.results?.manifestTickets || [];
      const updatedNewManifests = newManifests.map((manifest) =>
        updateManifestStatus(manifest)
      );
      console.log("Updated New Manifests:", updatedNewManifests);
      setManifests((prevManifests) => [
        ...prevManifests,
        ...updatedNewManifests.map((manifest) => ({
          ...manifest,
          reference: manifest.refId,
          route: `${manifest.departure.airportCode} → ${manifest.arrival.airportCode}`,
          tripType: manifest?.Booking?.[0]?.tripType || "",
        })),
      ]);

      // Update hasMore based on whether there's a next cursor
      const hasMoreItems = !!response.results?.nextCursor;
      setHasMore(hasMoreItems);

      // Update the nextCursor for the next fetch
      setNextCursor(response.results?.nextCursor || null);
    } catch (err) {
      console.error("Error loading more manifests:", err);
      // Don't show error to user, just stop loading more
      setHasMore(false);
    } finally {
      setLoadingMore(false);
    }
  }, [
    loadingMore,
    nextCursor,
    hasMore,
    debouncedFilters,
    searchQuery,
    page,
    pageSize,
  ]);

  // Handle infinite scroll when observer comes into view
  useEffect(() => {
    if (inView && !loadingMore && hasMore) {
      loadMore();
    }
  }, [inView, loadMore, loadingMore, hasMore]);

  const generatePdfBlob = async (manifest: ManifestTicket) => {
    try {
      // Create temporary container for HTML content
      const container = document.createElement("div");
      container.id = "pdf-container";
      container.style.position = "absolute";
      container.style.left = "-9999px";
      container.style.width = "210mm";
      container.style.padding = "20px";
      container.style.backgroundColor = "#ffffff";
      container.innerHTML = `
<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Flight Manifest - ${manifest.flightNumber}</title>
          <style>
              * {
                  margin: 0;
                  padding: 0;
                  box-sizing: border-box;
              }

              body {
                  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                  line-height: 1.6;
                  color: #333;
                  background: #ffffff;
                  padding: 20px;
                  max-width: 1200px;
                  margin: 0 auto;
              }

              .manifest-header {
                  border-bottom: 3px solid #e74c3c;
                  padding-bottom: 20px;
                  margin-bottom: 30px;
              }

              .agency-section {
                  background: #f8f9fa;
                  border: 1px solid #dee2e6;
                  border-radius: 8px;
                  padding: 25px;
                  margin-bottom: 30px;
              }

              .agency-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  margin-bottom: 20px;
              }

              .agency-info {
                  flex: 1;
              }

              .agency-logo {
                  width: 80px;
                  height: 80px;
                  background: #e9ecef;
                  border: 2px solid #dee2e6;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 25px;
                  font-weight: bold;
                  color: #6c757d;
                  font-size: 12px;
              }

              .agency-name {
                  font-size: 28px;
                  font-weight: bold;
                  color: #2c3e50;
                  margin-bottom: 8px;
              }

              .agency-address {
                  color: #6c757d;
                  font-size: 16px;
              }

              .manifest-id-badge {
                  background: #e74c3c;
                  color: white;
                  padding: 15px 20px;
                  border-radius: 8px;
                  text-align: center;
                  min-width: 180px;
              }

              .manifest-id-label {
                  font-size: 12px;
                  text-transform: uppercase;
                  letter-spacing: 1px;
                  opacity: 0.9;
              }

              .manifest-id-number {
                  font-size: 18px;
                  font-weight: bold;
                  font-family: 'Courier New', monospace;
                  margin-top: 5px;
              }

              .contact-grid {
                  background: #ffffff;
                  border: 1px solid #dee2e6;
                  border-radius: 6px;
                  padding: 20px;
                  display: grid;
                  grid-template-columns: repeat(5, 1fr);
                  gap: 20px;
                  margin-bottom: 15px;
              }

              .contact-item {
                  text-align: left;
              }

              .contact-label {
                  font-size: 11px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  color: #6c757d;
                  margin-bottom: 5px;
                  font-weight: 600;
              }

              .contact-value {
                  font-weight: 600;
                  color: #2c3e50;
                  font-size: 14px;
              }

              .authorization-status {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  font-size: 14px;
                  color: #6c757d;
              }

              .status-indicator {
                  display: flex;
                  align-items: center;
                  gap: 8px;
              }

              .status-dot {
                  width: 8px;
                  height: 8px;
                  background: #28a745;
                  border-radius: 50%;
              }

              .flight-details {
                  background: #f8f9fa;
                  border: 1px solid #dee2e6;
                  border-radius: 8px;
                  padding: 25px;
                  margin-bottom: 30px;
              }

              .flight-header {
                  display: flex;
                  align-items: center;
                  gap: 15px;
                  margin-bottom: 25px;
              }

              .flight-title {
                  font-size: 20px;
                  font-weight: bold;
                  color: #2c3e50;
              }

              .flight-type-badge {
                  background: #dc3545;
                  color: white;
                  padding: 6px 12px;
                  border-radius: 4px;
                  font-size: 12px;
                  font-weight: 600;
                  text-transform: uppercase;
              }

              .flight-info-grid {
                  display: grid;
                  grid-template-columns: repeat(4, 1fr);
                  gap: 20px;
                  margin-bottom: 25px;
              }

              .flight-info-card {
                  background: #ffffff;
                  border: 1px solid #dee2e6;
                  border-radius: 6px;
                  padding: 20px;
                  text-align: center;
              }

              .flight-info-label {
                  font-size: 11px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  color: #6c757d;
                  margin-bottom: 8px;
                  font-weight: 600;
              }

              .flight-info-value {
                  font-size: 20px;
                  font-weight: bold;
                  color: #2c3e50;
                  font-family: 'Courier New', monospace;
              }

              .route-section {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 20px;
              }

              .route-card {
                  background: #ffffff;
                  border: 1px solid #dee2e6;
                  border-radius: 6px;
                  padding: 20px;
              }

              .route-display {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-top: 15px;
              }

              .airport-info {
                  text-align: center;
              }

              .airport-code {
                  font-size: 24px;
                  font-weight: bold;
                  color: #2c3e50;
                  font-family: 'Courier New', monospace;
              }

              .airport-label {
                  font-size: 12px;
                  color: #6c757d;
                  margin-top: 5px;
              }

              .route-line {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 20px;
              }

              .route-arrow {
                  width: 40px;
                  height: 40px;
                  background: #e74c3c;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
              }

              .times-grid {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 20px;
                  margin-top: 15px;
              }

              .time-info {
                  text-align: center;
              }

              .time-label {
                  font-size: 12px;
                  color: #6c757d;
                  margin-bottom: 5px;
              }

              .time-value {
                  font-size: 24px;
                  font-weight: bold;
                  color: #2c3e50;
                  font-family: 'Courier New', monospace;
              }

              .passenger-section {
                  background: #f8f9fa;
                  border: 1px solid #dee2e6;
                  border-radius: 8px;
                  padding: 25px;
                  margin-bottom: 30px;
              }

              .passenger-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 10px;
                  margin-bottom: 30px;
              }

              .passenger-title {
                  font-size: 20px;
                  font-weight: bold;
                  color: #2c3e50;
              }

              .passenger-count {
                  background: #007bff;
                  color: white;
                  padding: 8px 16px;
                  border-radius: 6px;
                  font-weight: 600;
              }

              .passenger-table {
                  width: 100%;
                  border-collapse: collapse;
                  background: white;
                  border-radius: 6px;
                  overflow: hidden;
                  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              }

              .passenger-table th {
                  background: #f8f9fa;
                  color: #495057;
                  font-weight: 600;
                  padding: 15px 12px;
                  text-align: left;
                  font-size: 12px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  border-bottom: 2px solid #dee2e6;
              }

              .passenger-table td {
                  padding: 15px 12px;
                  border-bottom: 1px solid #dee2e6;
                  font-size: 14px;
              }

              .passenger-table tr:hover {
                  background: #f8f9fa;
              }

              .passenger-number {
                  font-weight: bold;
                  color: #e74c3c;
              }
              .passenger-table {
                  table-layout: fixed;
                  width: 100%;
                  font-size: 11px;
              }
              .passenger-table th:nth-child(1) { width: 3%; }  /* # */
              .passenger-table th:nth-child(2) { width: 5%; }  /* Title */
              .passenger-table th:nth-child(3) { width: 12%; } /* Last Name */
              .passenger-table th:nth-child(4) { width: 12%; } /* First Name */
              .passenger-table th:nth-child(5) { width: 12%; } /* DOB */
              .passenger-table th:nth-child(6) { width: 12%; } /* Nationality/Issuing */
              .passenger-table th:nth-child(7) { width: 12%; } /* Passport */
              .passenger-table th:nth-child(8) { width: 12%; } /* Expiry */
              .passenger-table th:nth-child(9) { width: 12%; } /* Booking ID */

              .passenger-table tr {
                  page-break-inside: avoid;
              }

              .passport-number {
                  font-family: 'Courier New', monospace;
                  font-weight: 600;
              }

              .booking-id {
                  font-family: 'Courier New', monospace;
                  font-size: 12px;
                  color: #6c757d;
              }

              .footer-section {
                  margin-top: 40px;
                  padding-top: 20px;
                  border-top: 2px solid #dee2e6;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
              }

              .generation-info {
                  color: #6c757d;
                  font-size: 14px;
              }

              .page-break {
                  page-break-before: always;
              }

              @media print {
                  body {
                      padding: 10px;
                  }

                  .manifest-header {
                      border-bottom: 2px solid #333;
                  }

                  .agency-section, .flight-details, .passenger-section {
                      border: 1px solid #333;
                      box-shadow: none;
                  }

                  .passenger-table {
                      box-shadow: none;
                  }
              }

              @media (max-width: 768px) {
                  .flight-info-grid {
                      grid-template-columns: repeat(2, 1fr);
                  }

                  .contact-grid {
                      grid-template-columns: repeat(2, 1fr);
                  }

                  .route-section {
                      grid-template-columns: 1fr;
                  }
              }
          </style>
      </head>
      <body>
          <!-- Manifest Header -->
          <div class="manifest-header relative">
              <h1 style="font-size: 32px; font-weight: bold; color: #2c3e50;">Flight Manifest</h1>
          </div>

          <!-- Agency Information Section -->
          <div class="agency-section">
              <div class="agency-header">
                  <div style="display: flex; align-items: flex-start;">
                      <img class="agency-logo" src='/images/logo/airvilla_logo_symbol_red.png'/>
                      <div class="agency-info">
                          <div class="agency-name">${
                            manifest.owner?.agencyName
                          }</div>
                          <div class="agency-address">${formatAddress(
                            manifest.owner?.address
                          )}</div>
                      </div>
                  </div>
                  <div class="manifest-id-badge">
                      <div class="manifest-id-label">Manifest ID</div>
                      <div class="manifest-id-number">${
                        manifest?.manifestId
                      }</div>
                  </div>
              </div>

              <div class="contact-grid">
                  <div class="contact-item">
                      <div class="contact-label">Email</div>
                      <div class="contact-value">${manifest.owner?.email}</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">Website</div>
                      <div class="contact-value">${
                        manifest.owner?.website
                      }</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">CON</div>
                      <div class="contact-value">${
                        manifest.owner?.commercialOperationNo
                      }</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">IATA Number</div>
                      <div class="contact-value">${manifest.owner?.iataNo}</div>
                  </div>
                  <div class="contact-item">
                      <div class="contact-label">Phone</div>
                      <div class="contact-value">${
                        manifest.owner?.phoneNumber
                      }</div>
                  </div>
              </div>
          </div>

          <!-- Flight Details Section -->
          <div class="flight-details">
              <div class="flight-header">
                  <div class="flight-title">Flight Details</div>
              </div>

              <div class="flight-info-grid">
                  <div class="flight-info-card">
                      <div class="flight-info-label">Flight Number</div>
                      <div class="flight-info-value">${
                        manifest.flightNumber
                      }</div>
                  </div>
                  <div class="flight-info-card">
                      <div class="flight-info-label">Carrier</div>
                      <div class="flight-info-value">${manifest?.carrier}</div>
                  </div>
                  <div class="flight-info-card">
                      <div class="flight-info-label">Flight Date</div>
                      <div class="flight-info-value">${getFormatDateTable(
                        manifest.flightDate
                      )}</div>
                  </div>
                  <div class="flight-info-card">
                      <div class="flight-info-label">Travel Class</div>
                      <div class="flight-info-value">${capitalizeFirst(
                        manifest?.travelClass
                      )}</div>
                  </div>
              </div>

              <div class="route-section">
                  <div class="route-card">
                      <div class="flight-info-label">Route</div>
                      <div class="route-display">
                          <div class="airport-info">
                              <div class="airport-code">${
                                manifest?.departure.airportCode
                              }</div>
                              <div class="airport-label">Departure</div>
                          </div>
                          <div class="route-line">
                              <div style="height: 2px; background: #dee2e6; flex: 1;"></div>
                              <div class="route-arrow">
                                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                      <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                                  </svg>
                              </div>
                              <div style="height: 2px; background: #dee2e6; flex: 1;"></div>
                          </div>
                          <div class="airport-info">
                              <div class="airport-code">${
                                manifest?.arrival.airportCode
                              }</div>
                              <div class="airport-label">Arrival</div>
                          </div>
                      </div>
                  </div>

                  <div class="route-card">
                      <div class="flight-info-label">Flight Times</div>
                      <div class="times-grid">
                          <div class="time-info">
                              <div class="time-label">Departure</div>
                              <div class="time-value">${getFormatTime(
                                manifest?.departureTime
                              )}</div>
                          </div>
                          <div class="time-info">
                              <div class="time-label">Arrival</div>
                              <div class="time-value">${getFormatTime(
                                manifest?.arrivalTime
                              )}</div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <!-- Passenger Manifest Section -->
          <div class="passenger-section">
              <div class="passenger-header">
                  <div class="passenger-title">Passenger Manifest</div>
                  <div class="passenger-count">${getActualPassengerCount(
                    manifest
                  )} Passengers</div>
              </div>
          </div>
      </body>
      </html>
      `;

      document.body.appendChild(container);

      // Convert HTML to canvas
      const canvas = await html2canvas(container, {
        scale: 2,
        useCORS: true,
        logging: false,
      });

      // Create PDF
      const doc = new jsPDF("p", "mm", "a4");

      // Get image data
      const imgData = canvas.toDataURL("image/png");
      const imgProps = doc.getImageProperties(imgData);
      const pdfWidth = doc.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Add header image
      doc.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight, "header");

      // Calculate start position for table (after header content)
      const startY = pdfHeight + 10;

      // Generate passenger data
      let passengerNumber = 1;
      const passengerRows =
        manifest.Booking?.flatMap((booking, index) =>
          booking.travelers?.map((passenger) => [
            passengerNumber++,
            `${
              passenger.traveler?.title
                ? capitalizeFirst(passenger.traveler.title)
                : ""
            } ${passenger.traveler?.firstName || ""} ${
              passenger.traveler?.lastName || ""
            }`.trim(),
            passenger.traveler?.dateOfBirth
              ? getFormatDateTable(passenger.traveler.dateOfBirth)
              : "N/A",
            `${passenger.traveler?.nationality || "N/A"} / ${
              passenger.traveler?.issuingCountry || "N/A"
            }`,
            passenger.traveler?.documentNumber || "N/A",
            passenger.traveler?.documentExpiryDate
              ? getFormatDateTable(passenger.traveler.documentExpiryDate)
              : "N/A",
            passenger.bookingId || "N/A",
          ])
        ) || [];

      // Add autoTable with pagination
      autoTable(doc, {
        startY: startY,
        head: [
          [
            "#",
            "Passenger Name",
            "Date of Birth",
            "Nationality / Issuing Country",
            "Document #",
            "Expiry",
            "Booking ID",
          ],
        ],
        body: passengerRows,
        headStyles: {
          fillColor: [231, 76, 60],
          textColor: 255,
          fontStyle: "bold",
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245],
        },
        styles: {
          fontSize: 9,
          cellPadding: 3,
          overflow: "linebreak",
        },
        didDrawPage: (data: Parameters<PageHook>[0]) => {
          const pageCount = data.doc.internal.getNumberOfPages();
          const pageNumber = data.pageNumber;
          // Add footer to each page
          if (pageCount > 1) {
            doc.setFontSize(10);
            doc.setTextColor(100);
            doc.text(
              `Page ${pageNumber} of ${pageCount}`,
              pdfWidth / 2,
              doc.internal.pageSize.height - 10,
              { align: "center" }
            );

            // Add simple header for subsequent pages
            // if (data.pageNumber > 1) {
            //   doc.setFontSize(12);
            //   doc.setTextColor(40);
            //   doc.text(
            //     `Flight ${manifest.flightNumber} - ${getFormatDateTable(
            //       manifest.flightDate
            //     )}`,
            //     14,
            //     15
            //   );
            // }
          }
        },
      });

      // Add main footer to last page
      doc.setPage(doc.getNumberOfPages());
      doc.setFontSize(10);
      doc.setTextColor(150);
      doc.text(
        `Manifest generated: ${new Date().toLocaleString()}`,
        pdfWidth - 14,
        doc.internal.pageSize.height - 10,
        { align: "right" }
      );

      // Clean up
      document.body.removeChild(container);
      // return the PDF as a blob
      return new Promise((resolve) => {
        const pdfBlob = doc.output("blob");
        resolve(pdfBlob);
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      const container = document.getElementById("pdf-container");
      if (container) document.body.removeChild(container);
    }
  };

  // Event handlers
  const handleGenerateAndSendPdf = useCallback(
    async (manifest: ManifestTicket, recipientEmail: string) => {
      try {
        setIsLoading(true);

        const pdfBlob = await generatePdfBlob(manifest);

        // Convert blob to base64 for sending
        const reader = new FileReader();
        const base64Pdf = await new Promise<string>((resolve, reject) => {
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(pdfBlob as Blob);
        });

        // Extract just the base64 data
        const base64Data = base64Pdf.split(",")[1];

        // Send to server
        const response = await sendManifestEmail(
          manifest.id,
          base64Data,
          recipientEmail
        );

        if (!response.success) {
          throw new Error(response.message || "Failed to send email");
        }

        dispatch(
          setMsg({
            message: "Email sent successfully",
            success: true,
          })
        );

        return response;
      } catch (error) {
        console.error("Error generating/sending PDF:", error);
        throw error; // Re-throw to be caught by the caller
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  const handleEditClick = useCallback((booking: ManifestTicket) => {
    setSelectedBooking(booking);
    setIsManifestPopupOpen(true);
  }, []);

  const handleCloseManifestPopup = useCallback(() => {
    setIsManifestPopupOpen(false);
    setSelectedBooking(null);
  }, []);

  const handleEmailClick = useCallback((booking: ManifestTicket) => {
    setSelectedBooking(booking);
    setIsEmailPopupOpen(true);
  }, []);

  const handleCloseEmailPopup = useCallback(() => {
    setIsEmailPopupOpen(false);
    setSelectedBooking(null);
  }, []);

  const handleEmailSent = useCallback(
    async (recipientEmail: string) => {
      if (!selectedBooking) return;

      try {
        // Generate PDF and send email
        await handleGenerateAndSendPdf(selectedBooking, recipientEmail);

        // Update the manifest status to Submitted after successful email
        setManifests((prevManifests) =>
          prevManifests.map((manifest) =>
            manifest.manifestId === selectedBooking?.manifestId
              ? {
                  ...manifest,
                  manifestStatus: "SUBMITTED",
                  status: "SUBMITTED",
                  submittedAt: new Date().toISOString(),
                }
              : manifest
          )
        );

        // Close all popups after email is sent
        setIsEmailPopupOpen(false);
        setIsManifestPopupOpen(false);
        setSelectedBooking(null);
      } catch (error) {
        console.error("Error sending email:", error);
      }
    },
    [selectedBooking?.manifestId]
  );

  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleExportClick = useCallback(() => {
    try {
      // Format the sales data for export
      const formattedData = formatManifestForExport(manifests);

      // Generate a filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split("T")[0]; // YYYY-MM-DD format
      const filename = `airvilla-manifest-${dateStr}`;

      // Export the data to CSV
      exportManifestToCSV(formattedData, filename);
    } catch (error) {
      console.error("Error exporting sales:", error);
    }
  }, []);

  // Auto-update manifest statuses on component mount and periodically
  useEffect(() => {
    const updateStatuses = () => {
      setManifests((prevManifests) => prevManifests.map(updateManifestStatus));
    };

    // Update statuses immediately
    updateStatuses();

    // Set up periodic updates every minute to catch status changes
    const interval = setInterval(updateStatuses, STATUS_UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, []);

  // Filter manifests based on search query and active tab
  const filteredManifests = useMemo(() => {
    return manifests.filter((manifest) => {
      // Apply search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          manifest.manifestId?.toLowerCase().includes(query) ||
          manifest.carrier?.toLowerCase().includes(query) ||
          manifest.route?.toLowerCase().includes(query) ||
          manifest.flightNumber?.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Apply tab filter
      if (activeTab !== "All") {
        const rawStatus = (manifest as any).manifestStatus || manifest.status;
        return formatStatusForDisplay(rawStatus) === activeTab;
      }

      return true;
    });
  }, [manifests, searchQuery, activeTab]);

   // Total passengers for current tab
  const totalPassengersForTab = useMemo(
    () =>
      filteredManifests.reduce(
        (sum, m) => sum + getActualPassengerCount(m),
        0
      ),
    [filteredManifests]
  );

  // Render loading state
  if (loading && manifests.length === 0) {
    return (
      <div className="dark:text-white text-gray-700 min-h-screen">
        <div className="w-full max-w-7xl mx-auto">
          <div className="dark:bg-gray-800 bg-gray-50 rounded-lg p-8 text-center">
            <p className="text-lg">Loading manifest data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Main render
  return (
    <div className="dark:text-white text-gray-700 min-h-screen">
      <div className="w-full max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-5">
          <h1 className="font-bold dark:text-white text-gray-700 text-3xl leading-10">
            My Manifest
          </h1>
        </div>

        {/* Filter Controls */}
        <FilterControls
          onFiltersChange={() => {}}
          filters={filters}
          setFilters={setFilters}
        />

        {/* Main Content Card */}
        <div className="dark:bg-gray-800 bg-white rounded-lg overflow-hidden shadow-lg">
          {/* Navigation and Actions Header */}
          <div className="dark:bg-gray-800 bg-white py-4 px-6 flex items-center justify-between border-b dark:border-gray-700 border-gray-200">
            <NavigationTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              manifestCounts={{ totalManifests: filteredManifests.length }}
              totalPassengers={totalPassengersForTab}
            />
            <SearchAndActions
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              onExportClick={handleExportClick}
            />
          </div>

          {/* Data Table */}
          <ManifestTable
            manifests={filteredManifests}
            onEditClick={handleEditClick}
            hasMore={hasMore}
            loadingMore={loadingMore}
            observerRef={ref}
          />
        </div>

        {/* Popup Components */}
        <FlightManifestPopup
          isOpen={isManifestPopupOpen}
          onClose={handleCloseManifestPopup}
          booking={selectedBooking}
          onEmailClick={handleEmailClick}
        />

        <EmailManifestPopup
          isOpen={isEmailPopupOpen}
          onClose={handleCloseEmailPopup}
          booking={selectedBooking}
          onEmailSent={handleEmailSent}
          handleGenerateAndSendPdf={handleGenerateAndSendPdf}
        />
      </div>
    </div>
  );
};

export default function MyManifest() {
  // check user's access
  const loading = useAgencyUserAuth();

  if (loading) {
    return <ProgressLoading />;
  }
  return (
    <div className="pt-8 w-full max-w-[96rem] mx-auto">
      <MyManifestList />
    </div>
  );
}
