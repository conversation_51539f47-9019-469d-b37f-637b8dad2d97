/**
 * Maps client-side payment data to server-side Prisma Payment model fields
 * This handles the transformation between the client's field names and the database schema
 */
export function mapClientPaymentToDbPayment(clientPayment: any) {
  // Extract values from client payment data
  const {
    paymentMethod,
    referenceNumber,
    transactionDate,
    formattedDate,
    amount,
    currency,
    remarks,
    bookingPrice,
    systemCurrency,
    ...rest
  } = clientPayment;

  // Create a payment meta object to store additional client fields
  const paymentMeta: any = {
    formattedDate,
    bookingPrice,
    systemCurrency,
    remarks,
    ...rest
  };

  // Map to Prisma Payment model fields
  return {
    amount: parseFloat(amount),
    currency,
    paymentMethod,
    paymentReference: referenceNumber,
    paymentMeta,
    // Convert string date to Date object if needed
    lastAttempt: transactionDate ? new Date(transactionDate) : new Date(),
  };
}
