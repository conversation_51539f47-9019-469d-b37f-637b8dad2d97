import rateLimit, { MemoryStore } from "express-rate-limit";
import <PERSON>is from "ioredis";
import RedisStore, { RedisReply } from "rate-limit-redis";
import logger from "../utils/logger";

// Memory store fallback
const memoryStore = new MemoryStore();
const REDIS_ENABLED = process.env.REDIS_ENABLED === "true";
const REDIS_HOST = process.env.REDIS_HOST || "localhost";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379");

// Create Redis client with retry strategy
const redisClient = new Redis({
  host: REDIS_HOST,
  port: REDIS_PORT,
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
});

// Add connection promise to track initial connection
const redisConnection = new Promise((resolve, reject) => {
  redisClient.on("error", (err) => {
    logger.error({
      message: "Redis connection error",
      error: err.message,
      host: REDIS_HOST,
      port: REDIS_PORT,
    });
    reject(err);
  });

  redisClient.on("connect", () => {
    logger.info("Successfully connected to Redis", {
      host: REDIS_HOST,
      port: REDIS_PORT,
    });
    resolve(true);
  });
});

// Initialize Redis connection
(async () => {
  try {
    await redisConnection;
  } catch (error) {
    logger.error("Failed to establish Redis connection, using memory store", {
      host: REDIS_HOST,
      port: REDIS_PORT,
    });
  }
})();

// Create store with fallback
export const getRateLimitStore = () => {
  if (!REDIS_ENABLED) {
    logger.info("Redis disabled, using memory store");
    return memoryStore;
  }
  return new RedisStore({
    sendCommand: (command: string, ...args: string[]): Promise<RedisReply> =>
      redisClient.call(command, ...args) as Promise<RedisReply>,
  });
};
