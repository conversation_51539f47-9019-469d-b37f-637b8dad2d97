import { Router } from "express";
import { requirePermission } from "../middlewares/authMiddleware";
import { PERMISSIONS } from "../utils/types/auth";
import {
  getAllUsersTickets,
  getSingleUsersTicket,
  deleteUsersTicket,
  updateTicketStatus,
  updateValidTicket,
  withdrawUpdateReqValidTicket,
} from "../controllers/userTicketController";

const router = Router();

// Ticket management routes with permission checks
router.get(
  "/",
  requirePermission(PERMISSIONS.SEARCH_TICKETS.name),
  getAllUsersTickets
);
router.get(
  "/:refId",
  requirePermission(PERMISSIONS.SEARCH_TICKETS.name),
  getSingleUsersTicket
);
router.delete(
  "/:refId",
  requirePermission(PERMISSIONS.DELETE_TICKETS.name),
  deleteUsersTicket
);
router.put(
  "/status/:refId",
  requirePermission(PERMISSIONS.CHANGE_TICKET_STATUS.name),
  updateTicketStatus
);
router.put(
  "/:refId",
  requirePermission(PERMISSIONS.UPDATE_TICKETS.name),
  updateValidTicket
);
router.put(
  "/withdraw/:refId",
  requirePermission(PERMISSIONS.UPDATE_TICKETS.name),
  withdrawUpdateReqValidTicket
);

export default router;
