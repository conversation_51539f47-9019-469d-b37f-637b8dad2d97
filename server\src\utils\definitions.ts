import { Request, Express } from "express";
import {
  TeamMember,
  FlightTicket,
  TicketAccess,
  AgencyAgent,
  User,
} from "@prisma/client";
// Define a new interface that extends Request to include both userId and user
export interface AuthRequest extends Request {
  userId?: string;
  accountType?:
    | "masterUser"
    | "masterOwner"
    | "agencyOwner"
    | "agencyUser"
    | "affiliate";
  teamMember?: TeamMember;
  agencyAgent?: AgencyAgent;
  masterUser?: TeamMember;
  masterOwner?: User;
  agencyOwner?: User;
  agencyUser?: AgencyAgent;
  affiliate?: User;
  user?: any; // TODO: Replace 'any' with proper User type from Prisma
  ticket?: FlightTicket & {
    ticketAccess?: TicketAccess[];
  };
  files?:
    | Express.Multer.File[]
    | { [fieldname: string]: Express.Multer.File[] }; // Add the files property from <PERSON>lter
}
