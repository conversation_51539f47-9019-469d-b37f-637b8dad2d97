import { <PERSON>r, Team<PERSON><PERSON>ber, AgencyAgent } from "@prisma/client";
// import { User, TeamMember, AgencyAgent } from "../../types/prismaEnums";
import { Response } from "express";
import checkUserAuth from "../authorization/checkUserAuth";
import { AuthRequest } from "../definitions";
import { prisma } from "../../prisma";

// Function to check the status of a user or team member
const checkAccountStatus = (
  account: User | TeamMember | AgencyAgent
): {
  success: boolean;
  message?: string;
  status?: number;
} => {
  // Check if the account object is null or undefined
  if (!account) {
    return { success: false, message: "Account not found2", status: 404 };
  }

  // Check if the account is verified
  if ("verified" in account && !account.verified) {
    return { success: false, message: "Account not verified", status: 401 };
  }

  // Check if the account status is accepted
  // if (account.accountStatus !== "accepted") {
  if ("accountStatus" in account && account.accountStatus !== "accepted") {
    return { success: false, message: "Account not approved", status: 403 };
  }

  // If it's a user or agency agent, check for appropriate role and subRole
  if ("role" in account) {
    // Affiliate users are not allowed
    if (account.role === "affiliate") {
      return {
        success: false,
        message: "Affiliate access not allowed",
        status: 403,
      };
    }

    // Allow moderators and master roles
    // if (["moderator", "master"].includes(account.role)) {
    if (account.role && ["moderator", "master"].includes(account.role)) {
      return { success: true };
    }

    // For agency role, check subRole restrictions
    if (account.role === "agency" && "subRole" in account && account.subRole) {
      const subRole = account.subRole.toString();
      if (["sales", "accountant"].includes(subRole)) {
        return {
          success: false,
          message: `Agency ${subRole} cannot create tickets`,
          status: 403,
        };
      }
    }
  }

  return { success: true };
};

interface AccessResponse {
  success: boolean;
  message?: string;
  status?: number;
  data?: {
    type: string;
    user?: User | null;
    teamMember?: TeamMember | null;
    agencyAgent?: AgencyAgent | null;
  };
}

// Function to handle ticket creator access (both agency users and team members)
const getTicketCreatorAccess = async (
  req: AuthRequest,
  res: Response
): Promise<AccessResponse> => {
  try {
    const userId = req.userId;
    if (!userId) {
      return {
        success: false,
        message: "User ID not found",
        status: 404,
      };
    }

    let user: User | null = null;
    let teamMember: TeamMember | null = null;
    let agencyAgent: AgencyAgent | null = null;

    /**
     * masterOwner, agencyOwner are all user accounts (master, agency roles) (roleType is master_owner or agency_owner)
     * masterUser is team member account (master role and roleType is not master_owner)
     * agencyAgent is agency agent account (agency role and roleType is not agency_owner)
     */

    if (
      req.accountType === "masterOwner" ||
      req.accountType === "agencyOwner"
    ) {
      user = await prisma.user.findUnique({
        where: { id: userId },
      });
      if (!user) {
        return {
          success: false,
          message: "User not found",
          status: 404,
        };
      }
      const statusCheck = checkAccountStatus(user);
      if (!statusCheck.success) {
        return {
          success: false,
          message: statusCheck.message || "Access denied",
          status: statusCheck.status || 403,
        };
      }
      return {
        success: true,
        message: "Access granted",
        data: {
          type: "user",
          user: user as any,
        },
        status: 200,
      };
    } else if (req.accountType === "masterUser") {
      teamMember = await prisma.teamMember.findUnique({
        where: { id: userId },
      });
      if (!teamMember) {
        return {
          success: false,
          message: "Team member not found",
          status: 404,
        };
      }
      // Convert null to undefined for type compatibility
      if (teamMember?.refId === null) teamMember.refId = undefined as any;
      if (teamMember?.agencyName === null)
        teamMember.agencyName = undefined as any;
      if (teamMember?.dateOfBirth === null)
        teamMember.dateOfBirth = undefined as any;
      const statusCheck = checkAccountStatus(teamMember as any);
      if (!statusCheck.success) {
        return {
          success: false,
          message: statusCheck.message || "Access denied",
          status: statusCheck.status || 403,
        };
      }
      return {
        success: true,
        message: "Access granted",
        data: {
          type: "teamMember",
          teamMember: teamMember as TeamMember,
        },
        status: 200,
      };
    } else {
      agencyAgent = await prisma.agencyAgent.findUnique({
        where: { id: userId },
        include: {
          agency: {
            select: {
              id: true,
              agencyName: true,
              accountStatus: true,
            },
          },
        },
      });
      if (!agencyAgent) {
        // If not found as AgencyAgent, try finding in User table
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });

        // Check if the user has role 'agency'
        if (user && user.role === "agency") {
          const statusCheck = checkAccountStatus(user);
          if (!statusCheck.success) {
            return {
              success: false,
              message: statusCheck.message || "Access denied",
              status: statusCheck.status || 403,
            };
          }
          return {
            success: true,
            message: "Access granted",
            data: {
              type: "user",
              user: user,
            },
            status: 200,
          };
        } else {
          return {
            success: false,
            message: "Agency agent not found",
            status: 404,
          };
        }
      }
      // Convert null to undefined for type compatibility
      // if (agencyAgent?.agencyName === null) agencyAgent.agencyName = undefined as any;
      // if (agencyAgent?.dateOfBirth === null) agencyAgent.dateOfBirth = undefined as any;
      // if (agencyAgent?.agency.agencyName === null) agencyAgent.agency.agencyName = undefined as any;
      const statusCheck = checkAccountStatus(agencyAgent as any);
      if (!statusCheck.success) {
        return {
          success: false,
          message: statusCheck.message || "Access denied",
          status: statusCheck.status || 403,
        };
      }
      return {
        success: true,
        message: "Access granted",
        data: {
          type: "agencyAgent",
          agencyAgent: agencyAgent as AgencyAgent,
        },
        status: 200,
      };
    }
  } catch (error: any) {
    console.error("Access check error:", error);
    return {
      success: false,
      message: "An error occurred while checking ticket creation access",
      status: 500,
    };
  }
};

export default getTicketCreatorAccess;
