import { User, TeamMember, AgencyAgent } from "@prisma/client";
import logger from "../logger";
import moment from "moment";
import { acquireLock, releaseLock } from "../locks/jobLock";
import {
  DELETION_PERIOD_FOR_UNVERIFIED,
  isTestMode,
  unitTimeForUnverified,
} from "../constants/timeVariables";
import { prisma } from "../../prisma";
const JOB_NAME = "unverifiedUserCleanup";
const BATCH_SIZE = parseInt(
  process.env.UNVERIFIED_CLEANUP_BATCH_SIZE || "100",
  10
);
const MAX_BATCH_RETRIES = 3;

/**
 * Removes unverified users (and related data) that were created before the cutoff date.
 * - Test mode: more than 10 minutes ago
 * - Production: more than 24 hours ago
 * Uses batching, robust error handling, structured logging, and locking.
 * @returns Promise<void>
 */
export const cleanupUnverifiedUsers = async (): Promise<void> => {
  let lockAcquired = false;
  try {
    logger.info(
      `[${JOB_NAME}] Starting cleanup of unverified users. isTestMode: ${isTestMode}`
    );

    // Acquire job lock to prevent concurrent runs
    lockAcquired = await acquireLock(JOB_NAME);
    if (!lockAcquired) {
      logger.info(`[${JOB_NAME}] Lock not acquired, skipping this run.`);
      return;
    }

    const cutoffDate = moment()
      .subtract(DELETION_PERIOD_FOR_UNVERIFIED, unitTimeForUnverified)
      .toDate();
    logger.info(
      `[${JOB_NAME}] Deleting unverified users older than ${DELETION_PERIOD_FOR_UNVERIFIED} ${unitTimeForUnverified} (before ${cutoffDate})`
    );

    // Common query parameters for all user types
    const queryParams = {
      where: {
        verified: false,
        createdAt: {
          lt: cutoffDate,
        },
      },
    } as const;

    // Batch processing for each user type
    let skip = 0;
    let hasMore = true;
    let users: User[] = [];
    let teamMembers: TeamMember[] = [];
    let agencyAgents: AgencyAgent[] = [];
    let batchNumber = 0;
    const batchErrors: Array<{ batch: number; error: any }> = [];

    while (hasMore) {
      batchNumber++;
      try {
        // Fetch batches for all user types in parallel
        const [userBatch, teamMemberBatch, agencyAgentBatch] =
          await Promise.all([
            prisma.user.findMany({ ...queryParams, skip, take: BATCH_SIZE }),
            prisma.teamMember.findMany({
              ...queryParams,
              skip,
              take: BATCH_SIZE,
            }),
            prisma.agencyAgent.findMany({
              ...queryParams,
              skip,
              take: BATCH_SIZE,
            }),
          ]);

        // If all batches are empty, we're done
        if (
          userBatch.length === 0 &&
          teamMemberBatch.length === 0 &&
          agencyAgentBatch.length === 0
        ) {
          hasMore = false;
          break;
        }

        users = [...users, ...userBatch];
        teamMembers = [...teamMembers, ...teamMemberBatch];
        agencyAgents = [...agencyAgents, ...agencyAgentBatch];
        logger.info(
          `[${JOB_NAME}] Batch #${batchNumber} fetched: users=${userBatch.length}, teamMembers=${teamMemberBatch.length}, agencyAgents=${agencyAgentBatch.length}`
        );
      } catch (batchError) {
        logger.error(
          `[${JOB_NAME}] Batch #${batchNumber} fetch failed`,
          batchError
        );
        batchErrors.push({ batch: batchNumber, error: batchError });
        // Continue to next batch
      }
      skip += BATCH_SIZE;
    }

    // Combine all unverified users for reporting
    const allUnverifiedUsers = [...users, ...teamMembers, ...agencyAgents];
    logger.info(
      `[${JOB_NAME}] Found ${allUnverifiedUsers.length} unverified users to delete.`
    );

    if (allUnverifiedUsers.length === 0) {
      logger.info(`[${JOB_NAME}] No unverified users to cleanup.`);
      return;
    }

    // Extract IDs for deletion
    const userIds = users.map((user) => user.id);
    const teamMemberIds = teamMembers.map((tm) => tm.id);
    const agencyAgentIds = agencyAgents.map((aa) => aa.id);
    const addressIds = [
      ...users.map(user => user.addressId).filter((id): id is string => id !== null),
      ...teamMembers.map(tm => tm.addressId).filter((id): id is string => id !== null),
      ...agencyAgents.map(aa => aa.userAddressId).filter((id): id is string => id !== null),
    ];

    // Retry logic for deletion batches
    let deletionSuccess = false;
    let deletionAttempts = 0;
    let lastDeletionError: any = null;
    while (!deletionSuccess && deletionAttempts < MAX_BATCH_RETRIES) {
      deletionAttempts++;
      try {
        await prisma.$transaction(async (tx: any) => {
          // Delete user addresses if any
          if (addressIds.length > 0) {
            logger.info(`[${JOB_NAME}] Deleting user addresses:`, addressIds);
            await tx.userAddress.deleteMany({
              where: { id: { in: addressIds } },
            });
          }
          // Delete users from each table in parallel
          await Promise.all(
            [
              userIds.length > 0 &&
                (logger.info(`[${JOB_NAME}] Deleting users:`, userIds),
                tx.user.deleteMany({ where: { id: { in: userIds } } })),
              teamMemberIds.length > 0 &&
                (logger.info(
                  `[${JOB_NAME}] Deleting team members:`,
                  teamMemberIds
                ),
                tx.teamMember.deleteMany({
                  where: { id: { in: teamMemberIds } },
                })),
              agencyAgentIds.length > 0 &&
                (logger.info(
                  `[${JOB_NAME}] Deleting agency agents:`,
                  agencyAgentIds
                ),
                tx.agencyAgent.deleteMany({
                  where: { id: { in: agencyAgentIds } },
                })),
            ].filter(Boolean)
          );
        });
        deletionSuccess = true;
        logger.info(
          `[${JOB_NAME}] Successfully removed ${allUnverifiedUsers.length} unverified users.`
        );
      } catch (deletionError) {
        lastDeletionError = deletionError;
        logger.error(
          `[${JOB_NAME}] Deletion batch attempt #${deletionAttempts} failed`,
          deletionError
        );
        // Exponential backoff before retrying
        await new Promise((res) => setTimeout(res, 500 * deletionAttempts));
      }
    }
    if (!deletionSuccess) {
      logger.error(
        `[${JOB_NAME}] Failed to delete unverified users after ${MAX_BATCH_RETRIES} attempts`,
        lastDeletionError
      );
      throw lastDeletionError;
    }

    if (batchErrors.length > 0) {
      logger.warn(`[${JOB_NAME}] Some fetch batches failed:`, batchErrors);
    }
  } catch (error) {
    logger.error(`[${JOB_NAME}] Error cleaning up unverified users:`, error);
    throw error;
  } finally {
    try {
      await releaseLock(JOB_NAME);
    } catch (lockReleaseError) {
      logger.error(`[${JOB_NAME}] Error releasing lock:`, lockReleaseError);
    }
    // Prisma disconnection is handled centrally; do not disconnect here.
  }
};
