import { Response } from "express";
import { AuthRequest } from "../utils/definitions";
import { prisma } from "../prisma";
import { searchMasterTicketInputValidation } from "../utils/validators/masterValidation";
import getMasterAccess from "../utils/access-check/getMasterAccess";
import { formatFlightDate, getCreatedTimeRange } from "../utils/functions";
import moment from "moment";
import { stringify } from "querystring";
import { FlightTicket, Prisma } from "@prisma/client";
// import { FlightTicket } from "../types/prismaEnums";
import notificationService from "../utils/services/notification.service";
import { sendRealTimeNotification } from "../socket";
import { createId } from "@paralleldrive/cuid2";
import { nanoid as cuidNanoId } from "nanoid";

interface TicketFilterOptions {
  [key: string]: any;
}

// Basic selector for list views
const basicTicketSelectors = {
  departure: true,
  arrival: true,
  agencyAgent: {
    select: {
      firstName: true,
      lastName: true,
    },
  },
  flightClasses: {
    include: {
      price: true,
    },
  },
  segments: {
    include: {
      departure: true,
      arrival: true,
    },
    orderBy: {
      // departureTime: Prisma.SortOrder.asc,
      departureTime: "asc",
    },
  },
  purchasedSeats: true,
  owner: true,
};

// Detailed selector including history for single ticket view
const detailedTicketSelectors = {
  ...basicTicketSelectors,
  ticketHistoryLogs: {
    select: {
      oldValue: true,
      newValue: true,
      changeType: true,
      changeDetails: true,
      changedAt: true,
      agency: {
        select: {
          id: true,
          agencyName: true,
          firstName: true,
          lastName: true,
          logo: true,
        },
      },
      agencyAgent: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
    },
    orderBy: {
      changedAt: Prisma.SortOrder.asc,
    },
  },
  agencyAgent: { select: { firstName: true, lastName: true } },
  flightClasses: {
    include: {
      extraOffers: true,
      price: true,
    },
  },
  segments: {
    include: {
      departure: true,
      arrival: true,
    },
    orderBy: {
      departureTime: Prisma.SortOrder.asc,
    },
  },
  purchasedSeats: true,
};

/**
 * Get all flight tickets for a master user.
 *
 * @param {AuthRequest} req - The request object containing the user's authorization token.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A JSON response with the success status and
 * the retrieved flight tickets. The response object will have the following
 * structure:
 * {
 *   success: boolean,
 *   results: {
 *     tickets: FlightTicket[],
 *     totalTickets: number,
 *     nextCursor: string | null,
 *   }
 * }
 * where `FlightTicket` is the shape of a flight ticket object returned by Prisma.
 */
export const getAllMasterTickets = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  try {
    // Get pagination parameters
    const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
    const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;

    // Get ticket filter parameters from request body
    const {
      agencyName,
      airportCode,
      arrivalAirportCode,
      startDate,
      endDate,
      flightClassType,
      createdTimeFilter,
    } = req.body as {
      updated: boolean;
      ticketStatus: string;
      agencyName: string;
      airportCode: string;
      arrivalAirportCode: string;
      startDate: string;
      endDate: string;
      flightClassType: string;
      createdTimeFilter: string;
    };

    const { ticketStatus, updated } = req.query;

    // Authorize the user and check their role
    const user = await getMasterAccess(req, res);
    if (!user) {
      return res.status(403).json({
        success: false,
        message: "Access denied. Invalid user or insufficient permissions.",
      });
    }
    // Validate the ticketStatus query parameter
    const ticketStatusValues = [
      "all",
      "pending",
      "available",
      "unavailable",
      "rejected",
      "blocked",
      "expired",
      "hold",
    ];
    if (ticketStatus && !ticketStatusValues.includes(ticketStatus as string)) {
      return res.status(400).json({
        success: false,
        message: "Invalid ticketStatus query parameter",
      });
    }

    // Apply filters to the query
    // Convert ticketStatus to the appropriate type
    const ticketStatusFiltered = ticketStatus as "all" &
      "pending" &
      "available" &
      "unavailable" &
      "rejected" &
      "blocked" &
      "expired" &
      "hold";

    // Get created time range based on filter
    const createdTimeRange = createdTimeFilter
      ? getCreatedTimeRange(createdTimeFilter)
      : {};

    // Create an array of filter options based on the provided parameters
    const filterOptions: any = [
      { updated: updated ? true : undefined },
      ticketStatusFiltered && ticketStatusFiltered !== "all"
        ? { ticketStatus: ticketStatusFiltered }
        : {},
      agencyName && agencyName !== "all"
        ? {
            owner: {
              NOT: { agencyName: null }, // Ensure owner is always not null
              agencyName: agencyName,
            },
          }
        : {},

      // Filter by departure city if provided
      airportCode
        ? {
            departure: {
              airportCode: {
                contains: airportCode,
                mode: "insensitive",
              },
            },
          }
        : {},

      // Filter by departure city if provided
      arrivalAirportCode
        ? {
            arrival: {
              airportCode: {
                contains: arrivalAirportCode,
                mode: "insensitive",
              },
            },
          }
        : {},

      // Filter by flight date range if provided
      startDate && endDate
        ? {
            flightDate: {
              gte: startDate,
              lte: endDate,
            },
          }
        : {},

      // Filter by flight class type if provided
      flightClassType
        ? {
            flightClasses: {
              some: {
                type: flightClassType,
              },
            },
          }
        : {},
      // Apply created time range filter
      createdTimeRange
        ? {
            createdAt: createdTimeRange,
          }
        : { createdAt: {} },
    ];

    // Get the flight tickets and total count based on filters
    // Remove empty filter objects to prevent invalid AND conditions
    const cleanedFilterOptions = filterOptions.filter(
      (option: TicketFilterOptions) => Object.keys(option).length > 0
    );
    const [tickets, totalTickets] = await Promise.all([
      prisma.flightTicket.findMany({
        where: {
          AND: cleanedFilterOptions,
        },
        include: {
          departure: true,
          arrival: true,
          agencyAgent: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
          flightClasses: {
            include: {
              price: true,
            },
          },
          segments: {
            include: {
              departure: true,
              arrival: true,
            },
            orderBy: {
              // departureTime: Prisma.SortOrder.asc,
              departureTime: "asc",
            },
          },
          purchasedSeats: true,
          owner: true,
        },
        orderBy: {
          // flightDate: Prisma.SortOrder.asc,
          flightDate: "asc",
        },
        take: pageSize,
        skip: cursor ? 1 : 0,
        cursor: cursor ? { id: cursor } : undefined,
      }),
      prisma.flightTicket.count({
        where: {
          AND: cleanedFilterOptions,
        },
      }),
    ]);

    // Filter out tickets with null owner
    const validTickets = tickets.filter((ticket: any) => ticket.owner !== null);

    // Calculate the next cursor based on valid tickets
    const nextCursor =
      validTickets.length === pageSize
        ? validTickets[validTickets.length - 1].id
        : null;

    // Return the valid flight tickets and total count
    return res.status(200).json({
      success: true,
      results: { tickets: validTickets, totalTickets, nextCursor },
    });
  } catch (error) {
    // Log the error and return a 500 error response
    const err = error as Error;
    console.error("Fetch All Master User Tickets error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to fetch flight tickets. Please try again later.",
    });
  }
};

/**
 * Retrieves a single master flight ticket by its refId.
 *
 * @param req - The request object containing the ticket refId.
 * @param res - The response object to send the result.
 * @returns The response with the retrieved ticket or an error message. The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: flightTicketRes,
 * }
 */
export const getSingleMasterTicket = async (
  req: AuthRequest,
  res: Response
) => {
  const { refId } = req.params;

  try {
    // Authorize the user and check their role
    const user = await getMasterAccess(req, res);

    // Query the database for the ticket with all related data
    const ticket = await prisma.flightTicket.findFirst({
      where: {
        refId: refId,
        // OR: [{ ticketStatus: "pending" }, { ticketStatus: "updated" }],
      },
      include: detailedTicketSelectors as any,
    });

    // Handle case where ticket is not found
    if (!ticket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    // Successfully return the found ticket
    return res.status(200).json({ success: true, results: ticket });
  } catch (error) {
    // Log the error and return a 500 error response
    const err = error as Error;
    console.error("Fetch Single Master User Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message:
        "Failed to fetch master single flight ticket. Please try again later.",
    });
  }
};

/**
 * Updates a single master flight ticket's status.
 *
 * @param {AuthRequest} req - The request object containing the user's authorization token and the ticket's refId and new status.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A JSON response with the success status and the
 * updated ticket. The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: FlightTicket,
 *   message: string,
 * }
 * where `FlightTicket` is the shape of a flight ticket object returned by Prisma.
 */
export const updateTicketStatus = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  // Extract the ticket's refId and new status from the request
  const { refId } = req.params;
  const { ticketStatus, comment } = req.body;

  try {
    // Authorize the user
    const user = await getMasterAccess(req, res);

    // Validate the ticketStatus query param
    const ticketStatusValues = [
      "pending",
      "available",
      "unavailable",
      "rejected",
      "updated",
      "blocked",
      "hold",
    ];

    // If the ticketStatus is invalid, return a 400 error
    if (!ticketStatus || !ticketStatusValues.includes(ticketStatus as string)) {
      return res.status(400).json({
        success: false,
        message: "Invalid Ticket Status",
      });
    }

    // Find the flight ticket along with its relations
    const flightTicket = await prisma.flightTicket.findUnique({
      where: {
        refId: refId,
      },
      select: {
        owner: {
          select: {
            id: true,
          },
        },
        flightDate: true,
        ticketStatus: true,
        refId: true,
        id: true,
      },
    });

    // if ticket not found
    if (!flightTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    const date = new Date();
    const currentDate = moment(date).format("YYYY-MM-DDTHH:mm:ss.SSS");

    // Check if the flight date has passed
    if (flightTicket.flightDate < currentDate) {
      return res
        .status(400)
        .json({ success: false, message: "Flight date has passed" });
    }

    // Check if the ticket's previous status was "pending"
    const wasPending = flightTicket.ticketStatus === "pending";

    // Determine the status to be shown in the response
    const getResponseStatus = (): string => {
      if (ticketStatus === "available" && wasPending) {
        return "accepted";
      }
      return ticketStatus;
    };

    const isApproval = ticketStatus === "available" && wasPending;

    const formattedDate = formatFlightDate(flightTicket.flightDate);

    // Create a date object and add one day
    const flightDate = new Date(flightTicket.flightDate);
    flightDate.setDate(flightDate.getDate());

    // Format the date as YY-MM-DD
    const year = flightDate.getFullYear().toString().slice(-2);
    const month = String(flightDate.getMonth() + 1).padStart(2, "0");
    const day = String(flightDate.getDate()).padStart(2, "0");

    // Generate a random character (a-z, A-Z, 0-9)
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const randomChar = chars.charAt(Math.floor(Math.random() * chars.length));

    const manifestId = isApproval
      ? `${year}-${month}-${day}-${randomChar}`
      : undefined;

    // Update the ticket update data to include manifestId when approving
    const updateData: any = {
      ticketStatus: ticketStatus,
      // Add manifestId only when approving the ticket
      ...(isApproval && { manifestId }),
      // Add a new ticket history log depending on the ticket's new status
      ticketHistoryLogs: {
        create: {
          changeType: getResponseStatus(),
          changeDetails: JSON.stringify({
            comment: comment,
            ...(isApproval && { manifestId }), // Include manifestId in history if this is an approval
          }),
        },
      },
    };

    if (isApproval && manifestId) {
      await prisma.manifest.create({
        data: {
          manifestId: manifestId,
          flightDate: formattedDate,
        },
      });
    }

    // Update the ticket's status
    const updatedTicket = await prisma.flightTicket.update({
      where: {
        refId: refId,
      },
      data: updateData,
      include: detailedTicketSelectors as any,
    });

    // Send notification to ticket owner
    if (flightTicket?.owner?.id) {
      const notificationData = {
        id: createId(),
        createdAt: new Date(),
        userId: flightTicket.owner.id,
        type: "TICKET_STATUS_UPDATE",
        title: "Ticket Status Updated",
        message: `Your ticket (${flightTicket.refId}) has been ${getResponseStatus()}${comment ? `: ${comment}` : ""}`,
        relatedId: flightTicket.id,
        link: `/tickets/${flightTicket.refId}`,
        priority: 1,
        agencyAgentId: null,
        read: false,
        teamMemberId: null,
        bookingId: null,
      };
      await notificationService.createNotification(notificationData);
      await notificationService.getUserNotifications(flightTicket?.owner?.id);
      sendRealTimeNotification(flightTicket.owner.id, notificationData as any);
    }

    // Return the updated ticket
    return res.status(200).json({
      success: true,
      results: updatedTicket,
      message: "Ticket updated successfully",
    });
  } catch (error) {
    // Log the error and return a 500 error response
    const err = error as Error;
    console.error("Update Single Master User Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message:
        "Failed to update master single flight ticket status. Please try again later.",
    });
  }
};
/**
 * Updates a single master flight ticket with the given input.
 *
 * req.body = {
 *   refId: string,
 *   updateRespond: boolean,
 *   comment: string
 * }
 * @param {AuthRequest} req - The request object containing the user's authorization token and the ticket's refId and new status.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A JSON response with the success status and
 * the retrieved flight tickets. The response object will have the following
 * structure:
 * {
 *   success: boolean,
 *   results: FlightTicketRes,
 *   message: string,
 * }
 * where `FlightTicketRes` is the shape of a flight ticket object returned by Prisma.
 */
export const updateValidTicket = async (req: AuthRequest, res: Response) => {
  // Extract the ticket's refId and new status from the request
  const { refId } = req.params;
  const { updateRespond, comment } = req.body;

  try {
    // Authorize the user
    const user = await getMasterAccess(req, res);

    // Find the flight ticket along with its relations
    const flightTicket = await prisma.flightTicket.findUnique({
      where: {
        refId: refId,
      },
      include: {
        ...detailedTicketSelectors,
        owner: {
          select: {
            id: true,
            firstName: true,
          },
        },
        ticketHistoryLogs: {
          orderBy: {
            changedAt: Prisma.SortOrder.desc,
          },
        },
      },
    });

    // if ticket not found
    if (!flightTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    // Validate the ticketStatus query param
    const ticketStatusValues = ["available", "unavailable"];

    // If the ticketStatus is invalid, return a 400 error
    if (!ticketStatusValues.includes(flightTicket.ticketStatus)) {
      return res.status(400).json({
        success: false,
        message: "Invalid Ticket Status",
      });
    }

    const date = new Date();
    const currentDate = moment(date).format("YYYY-MM-DDTHH:mm:ss.SSS");

    // Check if the flight date has passed
    if (
      flightTicket.departureTime &&
      flightTicket.departureTime < currentDate
    ) {
      return res
        .status(400)
        .json({ success: false, message: "Flight date has passed" });
    }

    // Check if the ticket has been updated
    if (flightTicket.updated === false) {
      return res.status(400).json({
        success: false,
        message: "Ticket cannot be updated",
      });
    }

    // Check if the respond is valid
    if (updateRespond !== "accepted" && updateRespond !== "rejected") {
      return res.status(400).json({
        success: false,
        message: "Invalid update respond",
      });
    }

    // ###### REJECTED ##########
    // If the user wants to reject the ticket
    if (updateRespond === "rejected") {
      const updatedTicket = await prisma.$transaction(
        async (transaction: any) => {
          // Update the ticket details
          const flightTicket = await transaction.flightTicket.update({
            where: { refId: refId },
            data: {
              updated: false,
            },
          });

          // Log detailed changes to the ticket
          await transaction.ticketHistoryLog.create({
            data: {
              ticketId: flightTicket.id,
              changeType: "rejected",
              changeDetails: JSON.stringify({ comment: comment }),
              agencyId: req.user?.id || "",
            },
          });
        }
      );

      // Send notification to ticket owner
      if (flightTicket?.owner?.id) {
        const notificationData = {
          id: createId(),
          createdAt: new Date(),
          userId: flightTicket.owner.id,
          type: "TICKET_STATUS_UPDATE",
          title: "Ticket Status Updated",
          message: `Your ticket (${flightTicket.refId}) has been ${updateRespond}${comment ? `: ${comment}` : ""}`,
          relatedId: flightTicket.id,
          link: `/tickets/${flightTicket.refId}`,
          priority: 1,
          agencyAgentId: null,
          read: false,
          teamMemberId: null,
          bookingId: null,
        };
        await notificationService.createNotification(notificationData);
        await notificationService.getUserNotifications(flightTicket?.owner?.id);
        sendRealTimeNotification(
          flightTicket.owner.id,
          notificationData as any
        );
      }

      return res.status(200).json({
        success: true,
        message: "Ticket rejected successfully",
      });
    }

    // ########## ACCEPTED ##########
    // Find the latest "update request" log (ignoring other logs like seat holds)
    if (updateRespond === "accepted") {
      // Since logs are ordered DESC, the first match will be the most recent update request
      const updateRequestLog = flightTicket.ticketHistoryLogs.find(
        (log: any) =>
          log.changeType === "update request" && log.newValue !== null
      );

      if (!updateRequestLog) {
        return res.status(400).json({
          success: false,
          message: "No update request found to apply",
        });
      }

      let newValue: any = {};
      try {
        newValue = JSON.parse(updateRequestLog.newValue as unknown as string);
      } catch (err) {
        console.error("Failed to parse newValue from update request log", err);
        return res.status(500).json({
          success: false,
          message: "Failed to process update request details",
        });
      }

      if (!newValue || Object.keys(newValue).length === 0) {
        return res.status(400).json({
          success: false,
          message: "Update request contains no changes",
        });
      }

      const ticketChanges: FlightTicket | any = newValue;

      const updatedTicket = await prisma.$transaction(
        async (transaction: any) => {
          // First fetch the existing flight ticket
          const existingTicket = await transaction.flightTicket.findUnique({
            where: { refId: refId },
          });

          if (!existingTicket) {
            throw new Error("Flight ticket not found");
          }

          // Update the ticket details
          const updateData: any = {
            updated: false,
          };

          // Add seats update if present
          if (ticketChanges.remainingSeats) {
            updateData.remainingSeats =
              parseInt(ticketChanges.remainingSeats) ||
              existingTicket.remainingSeats;
          }

          // Add total seats update if present
          if (ticketChanges.seats) {
            updateData.seats =
              parseInt(ticketChanges.seats) || existingTicket.seats;
          }

          // Add status update if present
          if (ticketChanges.status) {
            updateData.ticketStatus = ticketChanges.status;
          }

          // Add flight date update if present
          if (ticketChanges.flightDate) {
            updateData.flightDate = ticketChanges.flightDate;
          }

          // Add departure time update if present
          if (ticketChanges.departureTime) {
            updateData.departureTime = ticketChanges.departureTime;
          }

          // Add arrival time update if present
          if (ticketChanges.arrivalTime) {
            updateData.arrivalTime = ticketChanges.arrivalTime;
          }

          // Add duration update if present
          if (ticketChanges.duration) {
            updateData.duration = ticketChanges.duration;
          }

          // Update the ticket with the collected changes
          const updatedFlightTicket = await transaction.flightTicket.update({
            where: { refId: refId },
            data: updateData,
          });

          // ===== Handle price-only updates when flightClasses structure is not provided =====
          if (!Array.isArray(ticketChanges?.flightClasses)) {
            const priceUpdateData: any = {};
            if (ticketChanges["flightClasses.0.price.adult"] !== undefined) {
              priceUpdateData.adult = parseFloat(
                ticketChanges["flightClasses.0.price.adult"]
              );
            }
            if (ticketChanges["flightClasses.0.price.child"] !== undefined) {
              priceUpdateData.child = parseFloat(
                ticketChanges["flightClasses.0.price.child"]
              );
            }
            if (ticketChanges["flightClasses.0.price.infant"] !== undefined) {
              priceUpdateData.infant = parseFloat(
                ticketChanges["flightClasses.0.price.infant"]
              );
            }
            if (ticketChanges["flightClasses.0.price.tax"] !== undefined) {
              priceUpdateData.tax = parseFloat(
                ticketChanges["flightClasses.0.price.tax"]
              );
            }

            if (Object.keys(priceUpdateData).length > 0) {
              // find first flight class (index 0) of this ticket and update its price
              const firstClass = await transaction.flightClass.findFirst({
                where: { flightTicketId: updatedFlightTicket.id },
                select: { price: { select: { id: true } }, id: true },
                orderBy: { id: "asc" },
              });
              if (firstClass?.price?.id) {
                await transaction.flightPrice.update({
                  where: { id: firstClass.price.id },
                  data: priceUpdateData,
                });
              }
            }
          }

          // Only update flight classes if they exist and we're not just updating status
          if (Array.isArray(ticketChanges?.flightClasses)) {
            // delete all flight classes
            await transaction.flightClass.deleteMany({
              where: { flightTicketId: updatedFlightTicket.id },
            });

            // Update flight classes
            for (const flightClass of ticketChanges.flightClasses) {
              // Create new flight class
              await transaction.flightClass.create({
                data: {
                  flightTicket: { connect: { id: updatedFlightTicket.id } },
                  type: flightClass.type.trim().toLowerCase(),
                  carryOnAllowed: parseInt(flightClass.carryOnAllowed),
                  carryOnWeight: parseFloat(flightClass.carryOnWeight),
                  checkedAllowed: parseInt(flightClass.checkedAllowed),
                  checkedWeight: parseFloat(flightClass.checkedWeight),
                  checkedFee: parseFloat(flightClass.checkedFee),
                  additionalFee: parseFloat(flightClass.additionalFee),
                  price: {
                    create: {
                      adult: parseFloat(flightClass.price.adult),
                      child: parseFloat(flightClass.price.child),
                      infant: parseFloat(flightClass.price.infant),
                      tax: parseFloat(flightClass.price.tax),
                    },
                  },
                  extraOffers: {
                    create: Array.isArray(flightClass.extraOffers)
                      ? flightClass.extraOffers.map(
                          (offer: { name: string; available: string }) => ({
                            name: offer.name.trim().toLowerCase(),
                            available: offer.available.trim().toLowerCase(),
                          })
                        )
                      : [],
                  },
                },
              });
            }
          }

          // ===== Handle segment updates =====
          if (Array.isArray(ticketChanges?.segments)) {
            for (const segmentChange of ticketChanges.segments) {
              // Assuming segmentChange includes the segment's ID
              if (segmentChange.id) {
                await transaction.flightSegment.update({
                  where: { id: segmentChange.id },
                  data: {
                    departureTime: segmentChange.departureTime,
                    arrivalTime: segmentChange.arrivalTime,
                    duration: segmentChange.duration, // Ensure duration is also updated
                  },
                });
              }
            }
          }

          // Log detailed changes to the ticket
          await transaction.ticketHistoryLog.create({
            data: {
              ticketId: flightTicket.id,
              changeType: "accepted",
              changeDetails: JSON.stringify({ comment: "Accept changes" }),
              agencyId: req.user?.id || "",
            },
          });

          return updatedFlightTicket;
        }
      );

      // 🔄  Refetch the full ticket with relations to ensure frontend gets latest prices/classes
      const refreshedTicket = await prisma.flightTicket.findUnique({
        where: { refId },
        include: {
          ...detailedTicketSelectors,
          flightClasses: {
            include: {
              price: true,
              extraOffers: true,
            },
          },
        } as any,
      });

      // Return the updated ticket
      return res.status(200).json({
        success: true,
        results: refreshedTicket,
        message: "Ticket updated successfully",
      });
    }
  } catch (error) {
    // Log the error and return a 500 error response
    const err = error as Error;
    console.error("Update Single Master User Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message:
        "Failed to update master single flight ticket. Please try again later.",
    });
  }
};

/**
 * Deletes a single master flight ticket.
 *
 * @param {AuthRequest} req - The authenticated request object.
 * @param {Response} res - The response object.
 * @returns {Promise<Response>} The response with the deleted ticket or an error message.
 */
export const deleteSingleMasterTicket = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  // Parse the refId from the request parameters
  const { refId } = req.params;

  try {
    // Authorize the user
    const user = await getMasterAccess(req, res);

    // Delete the ticket
    const deletedTicket = await prisma.flightTicket.delete({
      where: {
        refId: refId,
      },
      include: {
        departure: true,
        arrival: true,
        agencyAgent: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        flightClasses: {
          include: {
            price: true,
          },
        },
        segments: {
          include: {
            departure: true,
            arrival: true,
          },
          orderBy: {
            // departureTime: Prisma.SortOrder.asc,
            departureTime: "asc",
          },
        },
        purchasedSeats: true,
        owner: true,
      },
    });

    // If the ticket is not found, return a 404 error
    if (!deletedTicket) {
      return res
        .status(404)
        .json({ success: false, message: "Ticket not found" });
    }

    // Send notification to ticket owner
    if (deletedTicket?.owner?.id) {
      const notificationData = {
        id: createId(),
        createdAt: new Date(),
        userId: deletedTicket.owner.id,
        type: "TICKET_STATUS_UPDATE",
        title: "Ticket Status Updated",
        message: `Your ticket (${deletedTicket.refId}) has been ${deletedTicket.ticketStatus}`,
        relatedId: deletedTicket.id,
        link: `/tickets/${deletedTicket.refId}`,
        priority: 1,
        agencyAgentId: null,
        read: false,
        teamMemberId: null,
        bookingId: null,
      };
      await notificationService.createNotification(notificationData);
      await notificationService.getUserNotifications(deletedTicket?.owner?.id);
      sendRealTimeNotification(deletedTicket.owner.id, notificationData as any);
    }

    // Return the deleted ticket
    return res.status(200).json({
      success: true,
      results: deletedTicket,
      message: "Ticket deleted successfully",
    });
  } catch (error) {
    // Log the error and return a 500 error response
    const err = error as Error;
    console.error("Delete Single Master User Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message:
        "Failed to delete master single flight ticket. Please try again later.",
    });
  }
};

export const rescheduleTicket = async (req: AuthRequest, res: Response) => {
  const { refId } = req.params;
  const {
    flightDate,
    departureTime,
    arrivalTime,
    duration,
    comment,
    segmentTimes,
  } = req.body;

  try {
    // Authorize the user
    await getMasterAccess(req, res);

    // Check if ticket exists
    const existingTicket = await prisma.flightTicket.findUnique({
      where: { refId },
    });

    if (!existingTicket) {
      return res.status(404).json({
        success: false,
        message: "Ticket not found",
      });
    }

    // Update the ticket with new schedule info
    const updatedTicket = await prisma.flightTicket.update({
      where: { refId },
      data: {
        flightDate,
        departureTime,
        arrivalTime,
        duration,
      },
      include: detailedTicketSelectors as any,
    });

    // Create a history log entry separately to avoid relation issues
    if (updatedTicket) {
      await prisma.ticketHistoryLog.create({
        data: {
          ticket: {
            connect: {
              id: updatedTicket.id,
            },
          },
          changeType: "rescheduled",
          changeDetails: JSON.stringify({ comment }),
        },
      });
    }

    // Update segments if provided
    if (Array.isArray(segmentTimes)) {
      for (const seg of segmentTimes) {
        await prisma.flightSegment.update({
          where: { id: seg.segmentId },
          data: {
            departureTime: seg.departureTime,
            arrivalTime: seg.arrivalTime,
            duration: seg.duration,
          },
        });
      }
    }

    // Refetch the ticket with updated segments
    const refreshedTicket = await prisma.flightTicket.findUnique({
      where: { refId },
      include: detailedTicketSelectors as any,
    });

    return res.status(200).json({
      success: true,
      results: refreshedTicket,
      message: "Ticket rescheduled successfully",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Reschedule Ticket error", {
      message: err.message,
      stack: err.stack,
    });
    return res.status(500).json({
      success: false,
      message: "Failed to reschedule ticket. Please try again later.",
    });
  }
};
