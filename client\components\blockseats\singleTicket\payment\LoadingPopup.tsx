import React from "react";
import { Loader } from "lucide-react";

interface LoadingPopupProps {
  message?: string;
}

const LoadingPopup: React.FC<LoadingPopupProps> = ({ message = "Processing your payment…" }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 flex flex-col items-center gap-4 min-w-[320px]">
        <Loader className="animate-spin text-red-500" size={48} />
        <span className="text-lg font-semibold text-gray-800 dark:text-gray-100 text-center">
          {message}
        </span>
      </div>
    </div>
  );
};

export default LoadingPopup;
