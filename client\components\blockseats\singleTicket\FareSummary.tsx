import React, { useState, useEffect } from "react";
import {
  FlightTicketRes,
  Traveler,
} from "@/utils/definitions/blockSeatsDefinitions";
import { formattedPrice, getFormatDate } from "@/utils/functions/functions";
import { useRouter, useSearchParams } from "next/navigation";
import { FaArrowRight } from "react-icons/fa";
import { ChevronDown, CircleAlert, Loader } from "lucide-react";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";
import { useCallback } from "react";

// Extend the user type to include agency and organization properties
interface AuthUser {
  id?: string;
  name?: string;
  email?: string;
  roleType?: string;
  role?: string;
  agency?:
    | string
    | {
        id?: string;
        name?: string;
        [key: string]: any;
      };
  agencyName?: string;
  organization?: string;
  [key: string]: any; // For any additional properties
}
import CancelModal from "./CancelModal";
import { AgentOperations } from "./SingleTicketDetails";
import { useBookingTimer } from "@/hooks/useBookingTimer";
import FareBreakdownRow from "@/components/common/FareBreakdownRow";

/**
 * FareSummary component displays the fare summary for a flight booking.
 * It takes in departure and return ticket data, calculates the fare, and displays it in a formatted manner.
 *
 * @param departureTicket - The departure flight ticket data.
 * @param returnTicket - The return flight ticket data (optional).
 *
 * @returns A React component displaying the fare summary.
 */
export default function FareSummary({
  departureTicket,
  returnTicket,
  isFormValid = false,
  onProceedToPayment,
  travelers = [],
}: {
  departureTicket: FlightTicketRes;
  returnTicket?: FlightTicketRes;
  isFormValid?: boolean;
  onProceedToPayment?: (travelers: Traveler[]) => void;
  travelers?: Traveler[];
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryParams = useUrlParams();
  // Helper to get expiry timestamp from ticket or query params
  function getExpiresAt(
    ticket: FlightTicketRes,
    queryParams: any
  ): number | undefined {
    if (
      ticket &&
      typeof ticket === "object" &&
      "expiresAt" in ticket &&
      typeof ticket.expiresAt === "number"
    ) {
      return ticket.expiresAt;
    }
    if (queryParams && queryParams.expiresAt) {
      const ts = parseInt(queryParams.expiresAt);
      return isNaN(ts) ? undefined : ts;
    }
    return undefined;
  }
  const expiresAt = getExpiresAt(departureTicket, queryParams);
  const { formattedTime, isExpired } = useBookingTimer({
    expiresAt,
    redirectOnExpire: true, // Enable automatic redirection to /blockseats/list
  });

  const [agentOperations, setAgentOperations] =
    useState<AgentOperations | null>(null);
  const [feedbackModalOpen, setFeedbackModalOpen] = useState<boolean>(false);
  const user = useAppSelector(selectUser) as AuthUser | null;
  const [isLoading, setIsLoading] = useState(false);

  // Generate a unique booking reference
  const generateBookingReference = () => {
    return "BK-" + Math.random().toString(36).substring(2, 15).toUpperCase();
  };

  // Get agent operations data from user context
  const getAgentOperations = useCallback((): AgentOperations | null => {
    // If no user is logged in, return null
    if (!user) return null;

    // Get the agency name from the user object
    const getAgencyName = () => {
      try {
        // Check if agency is an object with a name property
        if (
          user.agency &&
          typeof user.agency === "object" &&
          user.agency.name
        ) {
          return user.agency.name;
        }
        // If agency is a string, use it directly
        if (typeof user.agency === "string") {
          return user.agency;
        }
        // Fallback to other possible properties
        return user.agencyName || user.organization || "N/A";
      } catch (error) {
        console.error("Error getting agency name:", error);
        return "N/A";
      }
    };

    // Get user's full name from firstName and lastName
    const getUserFullName = () => {
      try {
        // Combine first and last name if both exist
        if (user.firstName && user.lastName) {
          return `${user.firstName} ${user.lastName}`.trim();
        }
        // Fallback to other name properties if available
        return user.name || user.username || user.email?.split("@")[0] || "N/A";
      } catch (error) {
        console.error("Error getting user full name:", error);
        return "N/A";
      }
    };

    const agencyName = getAgencyName();
    const userFullName = getUserFullName();
    const userRole = user.roleType || user.role || "Agent"; // Default to 'Agent' if no role is specified

    return {
      bookingReference: generateBookingReference(),
      ticketId: "",
      issuingBookingSource: agencyName,
      agent: {
        name: userFullName,
        role: userRole,
        agency: agencyName,
      },
      formFiller: {
        id: user.id || "",
        name: userFullName,
        email: user.email || "",
        role: userRole,
        agency: agencyName,
      },
    };
  }, [user]);

  // Update agent operations when user data changes
  useEffect(() => {
    if (user) {
      const operations = getAgentOperations();
      if (operations) {
        setAgentOperations(operations);
      }
    }
  }, [user, getAgentOperations]);

  // Show loading state while fetching user data
  if (!user || !agentOperations) {
    return (
      <div className="p-4 text-center">Loading booking information...</div>
    );
  }

  // calculate Fare
  const departureFare = calculateFare(departureTicket, queryParams);
  const returnFare = returnTicket
    ? calculateFare(returnTicket, queryParams)
    : null;

  // Check if it's a round trip
  const isRoundTrip = !!returnFare;

  // --- Timer/Expired Banner ---
  const timerBanner = (
    <section className="rounded-xl p-4 mb-4 bg-gray-100 dark:bg-gray-700">
      <div className="flex items-start space-x-2">
        <CircleAlert
          className={`w-4 md:w-8 flex-shrink-0 mt-0.5 ${
            isExpired
              ? "text-gray-400 dark:text-gray-500"
              : "text-red-500 dark:text-red-400"
          }`}
        />
        {isExpired ? (
          <span className="text-sm text-gray-500 dark:text-gray-300 leading-relaxed font-medium">
            Booking session expired. Please start again.
          </span>
        ) : (
          <p className="text-sm text-red-500 dark:text-red-400 leading-relaxed font-medium">
            Please complete booking within{" "}
            <span className="font-bold">{formattedTime}</span> minutes
          </p>
        )}
      </div>
    </section>
  );

  const constructPaymentURLParams = (
    travelers: Traveler[],
    departureTicket: FlightTicketRes | undefined,
    returnTicket: FlightTicketRes | undefined,
    agentOperationsData: AgentOperations | undefined,
    searchParams: ReturnType<typeof useSearchParams>
  ) => {
    // Get all current URL parameters
    const urlParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      // Skip any existing traveler data parameters
      if (!key.startsWith("traveler_")) {
        urlParams.append(key, value);
      }
    });

    // Add carrier and flight number with explicit console logging
    const departureCarrier = departureTicket?.segments?.[0]?.carrier || "";
    const departureFlightNumber =
      departureTicket?.segments?.[0]?.flightNumber || "";
    const departureFlightDate =
      departureTicket?.segments?.[0]?.flightDate || "";
    const arrivalFlightDate = departureTicket?.segments?.[0]?.flightDate || "";
    const departureTime = departureTicket?.departureTime || "";
    const arrivalTime = departureTicket?.arrivalTime || "";
    const duration = departureTicket?.duration || "";

    // Safely access return ticket data with proper null checks
    const returnCarrier = returnTicket?.segments?.[0]?.carrier || "";
    const returnFlightNumber = returnTicket?.segments?.[0]?.flightNumber || "";

    urlParams.append("departureCarrier", departureCarrier);
    urlParams.append("departureFlightNumber", departureFlightNumber);
    urlParams.append("returnCarrier", returnCarrier);
    urlParams.append("returnFlightNumber", returnFlightNumber);
    urlParams.append("departureTime", departureTime);
    urlParams.append("arrivalTime", arrivalTime);
    urlParams.append("duration", duration);
    // // Always include stops
    urlParams.append("stops", (departureTicket?.stops ?? 0).toString());

    const price = {
      departureAdultPrice:
        departureTicket?.flightClasses?.[0]?.price?.adult || 0,
      departureChildPrice:
        departureTicket?.flightClasses?.[0]?.price?.child || 0,
      departureInfantPrice:
        departureTicket?.flightClasses?.[0]?.price?.infant || 0,
      departureTaxPrice: departureTicket?.flightClasses?.[0]?.price?.tax || 0,
      departureCurrency:
        departureTicket?.flightClasses?.[0]?.price?.currency || "",

      returnAdultPrice: returnTicket?.flightClasses?.[0]?.price?.adult || 0,
      returnChildPrice: returnTicket?.flightClasses?.[0]?.price?.child || 0,
      returnInfantPrice: returnTicket?.flightClasses?.[0]?.price?.infant || 0,
      returnTaxPrice: returnTicket?.flightClasses?.[0]?.price?.tax || 0,
      returnCurrency: returnTicket?.flightClasses?.[0]?.price?.currency || "",
    };

    urlParams.append("price", JSON.stringify(price));

    // urlParams.append("price", JSON.stringify(price));

    // Stringify the agentOperations data
    const agentOperationsJson = agentOperationsData || {};

    // Append the stringified JSON directly; URLSearchParams will handle encoding
    urlParams.append("agentOperations", JSON.stringify(agentOperationsJson));

    // Add simplified individual parameters for agent operations
    urlParams.set("bookRef", agentOperationsData?.bookingReference || "");
    urlParams.set("tktId", agentOperationsData?.ticketId || "");
    urlParams.set("source", agentOperationsData?.issuingBookingSource || "");

    // Agent details
    urlParams.set("agentName", agentOperationsData?.agent?.name || "");
    urlParams.set("agentRole", agentOperationsData?.agent?.role || "");
    urlParams.set("agentAgency", agentOperationsData?.agent?.agency || "");

    // Form filler details
    if (agentOperationsData?.formFiller) {
      urlParams.set("fillerName", agentOperationsData.formFiller.name || "");
      urlParams.set("fillerEmail", agentOperationsData.formFiller.email || "");
      urlParams.set("fillerRole", agentOperationsData.formFiller.role || "");

      // Add the form filler's agency if it exists
      if ("agency" in agentOperationsData.formFiller) {
        urlParams.set(
          "fillerAgency",
          agentOperationsData.formFiller.agency || ""
        );
      }
    }

    // Add each traveler's data as individual parameters
    travelers.forEach((traveler, index) => {
      // Skip the errors field
      Object.entries(traveler).forEach(([field, value]) => {
        if (field !== "errors" && value) {
          urlParams.append(`traveler_${index}_${field}`, value.toString());
        }
      });
    });
    // Construct the full URL
    // const fullUrl = `/blockseats/list/${
    //   departureTicket?.id
    // }/checkout?${urlParams.toString()}`;
    let fullUrl;
    if (returnTicket?.id) {
      // For round trips, include both ticket IDs in the path
      fullUrl = `/blockseats/list/${departureTicket?.id}_${
        returnTicket.id
      }/checkout?${urlParams.toString()}`;
    } else {
      // For one-way trips, just include the departure ticket ID
      fullUrl = `/blockseats/list/${
        departureTicket?.id
      }/checkout?${urlParams.toString()}`;
    }

    return fullUrl;
  };

  const handleProceedToPayment = (travelers: Traveler[]) => {
    if (departureTicket) {
      // // Save traveler data to state
      // setTravelerData(travelers);

      const fullUrl = constructPaymentURLParams(
        travelers,
        departureTicket,
        returnTicket,
        agentOperations,
        searchParams
      );

      // Navigate to the checkout page
      router.push(fullUrl);
    }
  };

  return (
    <section className="border-0 shadow-lg mb-6 bg-gray-200 dark:bg-gray-800 rounded-lg p-4 md:p-6 space-y-4">
      {/* Header */}
      <div className="w-full">
        <h2 className="text-2xl font-bold text-gray-700 dark:text-white">
          Fare Summary
        </h2>
      </div>
      <div className="text-base mb-6 space-y-3">
        {/* Combined Flight Details for Round Trip */}
        {isRoundTrip ? (
          <CombinedFlightDetails
            departureTicket={departureTicket}
            returnTicket={returnTicket!}
            travelClass={queryParams.travelClass!}
            queryParams={queryParams}
          />
        ) : (
          /* Flight Details for One Way */
          <FlightDetailsSection
            ticket={departureTicket}
            travelClass={queryParams.travelClass!}
            queryParams={queryParams}
          />
        )}

        {/* Price Details */}
        {isRoundTrip && returnFare ? (
          <CombinedPriceDetails
            departureFare={departureFare}
            returnFare={returnFare}
          />
        ) : (
          <FareSection fare={departureFare} showPreTotal={false} />
        )}

        {/* Offer & Discount */}
        <OfferDiscount />

        {/* Banner */}
        <section className="rounded-xl p-4 mb-4 bg-gray-100 dark:bg-gray-700">
          <div className="flex items-start space-x-2">
            <CircleAlert className="text-red-500 dark:text-red-400 w-4 md:w-8 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-500 dark:text-red-400 leading-relaxed font-medium">
              Please complete booking within{" "}
              <span className="font-bold">{formattedTime}</span> minutes
            </p>
          </div>
        </section>

        {/*  Payment Button */}
        <div className="relative z-10 mt-10 pt-4 border-t border-gray-300 dark:border-gray-700">
          <button
            type="button"
            onClick={() => {
              // Force validation before proceeding
              const isValid = travelers.every((traveler) => {
                const requiredFields = [
                  "title",
                  "firstName",
                  "lastName",
                  // "contactEmail",
                  // "contactPhone",
                  "dateOfBirth",
                  "nationality",
                  "passportNumber",
                  "issuingCountry",
                  "passportExpiry",
                ];

                return requiredFields.every(
                  (field) =>
                    traveler[field as keyof Traveler] &&
                    String(traveler[field as keyof Traveler]).trim() !== ""
                );
              });

              if (isValid && travelers.length > 0) {
                // Encode traveler data as URL parameters
                // Call onProceedToPayment if it exists (for backward compatibility)
                if (onProceedToPayment) {
                  onProceedToPayment(travelers);
                }
                handleProceedToPayment(travelers);
                setIsLoading(true);
              }
            }}
            disabled={!isFormValid}
            className={`w-full bg-red-500 text-white py-3 px-4 rounded-lg hover:bg-red-600 transition font-semibold duration-300 flex items-center justify-center ${
              !isFormValid ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isLoading ? (
              <Loader className="animate-spin mr-2 h-4 w-4" />
            ) : (
              "Proceed to Payment"
            )}
          </button>
        </div>

        <div className="text-xs text-gray-600 dark:text-gray-300 text-center">
          By confirming, you agree to our{" "}
          <button
            className="text-red-500 hover:underline"
            // aria-controls="feedback-modal"
            // onClick={() => setFeedbackModalOpen(true)}
          >
            cancellation policy
            <CancelModal
              feedbackModalOpen={feedbackModalOpen}
              setFeedbackModalOpen={setFeedbackModalOpen}
              ticket={departureTicket as unknown as FlightTicketRes}
            />
          </button>
        </div>
      </div>
    </section>
  );
}

// OfferDiscount Component
function OfferDiscount() {
  return (
    <section className="text-lg text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 rounded-lg shadow p-4 h-fit">
      <h3 className="font-semibold mb-4">Offer & Discount</h3>
      <div className="flex gap-2">
        <label htmlFor="coupon_code" className="sr-only">
          Coupon Code
        </label>
        <input
          id="coupon_code"
          type="text"
          placeholder="Coupon code"
          className="w-full flex-grow border-none focus:border rounded-l-lg px-4 py-2 bg-gray-300 dark:bg-gray-600 dark:text-gray-100 flex-1 text-gray-600 p-2 rounded-md border-0 focus:ring-2 focus:ring-red-500 focus:outline-none placeholder:text-gray-600 dark:placeholder:text-gray-400"
          aria-label="Coupon code"
          title="Enter your coupon code here"
          autoComplete="off"
        />
        <button
          className="text-lg py-2 px-4 bg-red-500 text-white font-semibold rounded-md hover:bg-red-600 transition-colors duration-300"
          aria-label="Apply coupon code"
          title="Apply the entered coupon code"
        >
          Apply
        </button>
      </div>
    </section>
  );
}

/**
 * CombinedFlightDetails component displays both departure and return flight details in a single card.
 *
 * @param departureTicket - The departure flight ticket data.
 * @param returnTicket - The return flight ticket data.
 * @param travelClass - The travel class (e.g., economy, business, first class).
 * @param queryParams - The URL query parameters.
 *
 * @returns A React component displaying combined flight details.
 */
function CombinedFlightDetails({
  departureTicket,
  returnTicket,
  travelClass,
  queryParams,
}: {
  departureTicket: FlightTicketRes;
  returnTicket: FlightTicketRes;
  travelClass: string;
  queryParams: any;
}) {
  const { adults, children, infants } = queryParams;

  return (
    <section className="space-y-2 p-4 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-white">
      <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
        Flight Details
      </h3>

      {/* Departure Flight */}
      <div className="mb-4">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
          OUTBOUND
        </h4>
        <h3 className="font-bold flex flex-wrap flex-row justify-between text-sm">
          <p className="flex items-center">
            {departureTicket.departure.airportCode}
            <FaArrowRight className="md:w-5 md:h-5 md:mx-4 text-gray-500" />{" "}
            {departureTicket.arrival.airportCode}{" "}
          </p>
        </h3>
        <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
          <p>{getFormatDate(departureTicket.departureTime)}</p>
          <p> • </p>
          <p>
            {departureTicket.segments[0].carrier} ({" "}
            {departureTicket.segments[0].flightNumber} )
          </p>
        </span>
      </div>

      {/* Return Flight */}
      <div className="mt-2">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-blue-500/20 px-2 py-0.5 rounded w-fit">
          RETURN
        </h4>
        <h3 className="font-bold flex flex-wrap flex-row justify-between text-sm">
          <p className="flex items-center">
            {returnTicket.departure.airportCode}
            <FaArrowRight className="md:w-5 md:h-5 md:mx-4 text-gray-400" />{" "}
            {returnTicket.arrival.airportCode}{" "}
          </p>
        </h3>
        <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
          <p>{getFormatDate(returnTicket.departureTime)}</p>
          <p> • </p>
          <p>
            {returnTicket.segments[0].carrier} ({" "}
            {returnTicket.segments[0].flightNumber} )
          </p>
        </span>
      </div>

      {/* Passenger Information */}
      <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300 mt-3 pt-2 border-t border-gray-300 dark:border-gray-600">
        {adults > 0 && <p>{adults} Adults</p>}
        {children > 0 && (
          <>
            <p> • </p>
            <p>{children} Children</p>
          </>
        )}
        {infants > 0 && (
          <>
            <p> • </p>
            <p>{infants} Infants</p>
          </>
        )}

        <p> • </p>
        <p className="capitalize">{travelClass}</p>
      </span>
    </section>
  );
}

/**
 * FlightDetailsSection component displays flight details for a single flight.
 *
 * @param ticket - The flight ticket data.
 * @param travelClass - The travel class (e.g., economy, business, first class).
 * @param queryParams - The URL query parameters.
 *
 * @returns A React component displaying flight details for a single flight.
 */
function FlightDetailsSection({
  ticket,
  travelClass,
  queryParams,
}: {
  ticket: FlightTicketRes;
  travelClass: string;
  queryParams: any;
}) {
  const { adults, children, infants } = queryParams;

  return (
    <section className="space-y-2 p-4 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-white">
      <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
        Flight Details
      </h3>
      <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
        OUTBOUND
      </h4>
      <h3 className="font-bold flex flex-wrap flex-row justify-between text-sm">
        <p className="flex items-center">
          {ticket.departure.airportCode}
          <FaArrowRight className="md:w-5 md:h-5 md:mx-4 text-gray-500" />{" "}
          {ticket.arrival.airportCode}{" "}
        </p>
      </h3>
      <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
        <p>{getFormatDate(ticket.departureTime)}</p>
        <p> • </p>
        <p>
          {ticket.segments[0].carrier} ( {ticket.segments[0].flightNumber} )
        </p>
      </span>
      <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
        {adults > 0 && <p>{adults} Adults</p>}
        {children > 0 && <p> • {children} Children</p>}
        {infants > 0 && <p> • {infants} Infants</p>}
        <p> • </p> <p className="capitalize">{travelClass}</p>
      </span>
    </section>
  );
}

/**
 * CombinedPriceDetails component displays price details for both departure and return flights in a single card.
 *
 * @param departureFare - The fare details for the departure flight.
 * @param returnFare - The fare details for the return flight.
 *
 * @returns A React component displaying combined price details.
 */
function CombinedPriceDetails({
  departureFare,
  returnFare,
}: {
  departureFare: any;
  returnFare: any;
}) {
  const totalFare = departureFare.total + returnFare.total;
  const currency = departureFare.currency;

  return (
    <section className="space-y-2 p-4 rounded-lg bg-gray-100 dark:bg-gray-700">
      <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-500 pb-2">
        Price Details
      </h3>

      {/* Departure Fare */}
      <div className="mb-4">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
          OUTBOUND
        </h4>
        {/* {departureFare.fareBreakdown
          .filter((item: any) => item.count > 0)
          .map((item: any) => (
            <TravelerFareItem
              key={`departure-${item.label}`}
              label={item.label}
              value={formattedPrice(item.value, departureFare.currency)}
              perPersonValue={formattedPrice(
                item.perPersonValue,
                departureFare.currency
              )}
              count={item.count}
            />
          ))} */}
        {/* Outbound Base Fare */}
        <div className="block mb-3">
          {/* <span className="dark:text-gray-300 text-gray-700">Base Fare</span>
          <span className="dark:text-white text-gray-700">
            {departureFare.fareBreakdown
              ? `${departureFare.fareBreakdown
                  .filter((item: any) => item.count > 0)
                  .reduce((total: number, item: any) => total + item.value, 0)
                  .toFixed(2)} ${departureFare.currency}`
              : "N/A"}
          </span> */}
          {departureFare.fareBreakdown
            .filter((item: any) => item.count > 0)
            .map((item: any) => (
              <FareBreakdownRow
                key={`departure-${item.label}`}
                item={item}
                currency={departureFare.currency}
              />
            ))}
        </div>
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full mb-3"></div>
        <FareItem
          label={`Taxes (${departureFare.taxRate.toFixed(2)}%)`}
          value={`${formattedPrice(departureFare.tax, departureFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <FareItem
          label="Transaction Fee"
          value={`${formattedPrice(0.0, departureFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full my-3"></div>
        <FareItem
          label="Subtotal"
          value={`${formattedPrice(
            departureFare.total,
            departureFare.currency
          )}`}
          className="font-semibold text-gray-700 dark:text-gray-300"
        />
      </div>

      {/* Return Fare */}
      <div className="mb-4 border-t border-gray-500 pt-3">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-blue-500/20 px-2 py-0.5 rounded w-fit">
          RETURN
        </h4>
        {/* {returnFare.fareBreakdown
          .filter((item: any) => item.count > 0)
          .map((item: any) => (
            <TravelerFareItem
              key={`return-${item.label}`}
              label={item.label}
              value={formattedPrice(item.value, returnFare.currency)}
              perPersonValue={formattedPrice(
                item.perPersonValue,
                returnFare.currency
              )}
              count={item.count}
            />
          ))} */}
        {/* Return Base Fare */}
        <div className="block mb-3">
          {/* <span className="dark:text-gray-300 text-gray-700">Base Fare</span>
          <span className="dark:text-white text-gray-700">
            {returnFare.fareBreakdown
              ? `${returnFare.fareBreakdown
                  .filter((item: any) => item.count > 0)
                  .reduce((total: number, item: any) => total + item.value, 0)
                  .toFixed(2)} ${returnFare.currency}`
              : "N/A"}
          </span> */}
          {returnFare.fareBreakdown
            .filter((item: any) => item.count > 0)
            .map((item: any) => (
              <FareBreakdownRow
                key={`departure-${item.label}`}
                item={item}
                currency={returnFare.currency}
              />
            ))}
        </div>
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full mb-3"></div>
        <FareItem
          label={`Taxes (${returnFare.taxRate.toFixed(2)}%)`}
          value={`${formattedPrice(returnFare.tax, returnFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <FareItem
          label="Transaction Fee"
          value={`${formattedPrice(0.0, returnFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full my-3"></div>
        <FareItem
          label="Subtotal"
          value={`${formattedPrice(returnFare.total, returnFare.currency)}`}
          className="font-semibold text-gray-700 dark:text-gray-300"
        />
      </div>

      {/* Total Fare */}
      <div className="border-t border-gray-500 pt-3">
        <FareItem
          label="Total"
          value={`${formattedPrice(totalFare, currency)}`}
          className="font-semibold text-lg leading-8 tracking-wide text-gray-700 dark:text-white"
        />
      </div>
    </section>
  );
}

/**
 * FareSection component displays the fare details for a specific flight segment.
 * It takes in departure and arrival airports, travel class, fare details, and a flag to show pre-total fare.
 * It renders the fare breakdown, tax, and pre-total fare if applicable.
 *
 * @param departure - The departure airport code.
 * @param arrival - The arrival airport code.
 * @param travelClass - The travel class (e.g., economy, business, first class).
 * @param fare - The fare details for the flight segment.
 * @param showPreTotal - A flag indicating whether to show the pre-total fare.
 *
 * @returns A React component displaying the fare details for a specific flight segment.
 */
function FareSection({
  fare,
  showPreTotal,
}: {
  fare: any;
  showPreTotal: boolean;
}) {
  const totalFare = fare.total;

  return (
    <div className="space-y-2">
      {/* Price Details */}

      <section className="space-y-2 p-4 rounded-lg bg-gray-100 dark:bg-gray-700">
        <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
          Price Details
        </h3>

        {/* Departure Fare */}
        {/* <div className=""> */}
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
          OUTBOUND
        </h4>
        {/* Outbound Base Fare */}
        <div className="block mb-3">
          {fare.fareBreakdown
            .filter((item: any) => item.count > 0)
            .map((item: any) => (
              <FareBreakdownRow
                key={`departure-${item.label}`}
                item={item}
                currency={fare.currency}
              />
            ))}
        </div>
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full mb-3"></div>
        <FareItem
          label={`Taxes ( ${fare.taxRate.toFixed(2)} % )`}
          value={`${formattedPrice(fare.tax, fare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <FareItem
          label="Transaction Fee"
          value={`${formattedPrice(0.0, fare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        {showPreTotal && (
          <FareItem
            label="PreTotal"
            value={`${formattedPrice(fare.total, fare.currency)}`}
            className="font-semibold text-gray-700 dark:text-gray-300"
          />
        )}

        {/* Total Fare */}
        <div className="border-t border-gray-300 dark:border-gray-500 pt-3">
          <FareItem
            label="Total"
            value={`${formattedPrice(totalFare, fare.currency)}`}
            className="font-semibold text-lg leading-8 tracking-wide text-gray-700 dark:text-white"
          />
        </div>
        {/* </div> */}
      </section>
    </div>
  );
}

/**
 * TravelerFareItem component displays a single fare item with a label, value, and per person value.
 * It also includes a collapsible section to show the count and per person value.
 *
 * @param label - The label for the fare item.
 * @param value - The total value for the fare item.
 * @param perPersonValue - The per person value for the fare item.
 * @param count - The count of the fare item.
 *
 * @returns A React component displaying a single fare item with collapsible details.
 */
function TravelerFareItem({
  label,
  value,
  perPersonValue,
  count,
}: {
  label: string;
  value: string;
  perPersonValue: string;
  count: number;
}) {
  const [showDetails, setShowDetails] = useState(false);
  return (
    <div>
      <div
        className="flex justify-between items-center font-extrabold text-gray-700 dark:text-white"
        onClick={() => setShowDetails(!showDetails)}
      >
        <span className="font-medium flex items-center justify-between">
          <span className="font-medium w-full">{label}</span>
          <ChevronDown
            size={16}
            className={`transform transition-transform duration-300 ${
              showDetails ? "rotate-180" : "rotate-0"
            }`}
          />
        </span>
        <span className="font-medium">{value}</span>
      </div>
      {showDetails && (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          {count} X {perPersonValue}
        </div>
      )}
    </div>
  );
}

/**
 * FareItem component displays a single fare item with a label and value.
 *
 * @param label - The label for the fare item.
 * @param value - The value for the fare item.
 * @param info - Indicates whether to show an info icon (optional, default is false).
 * @param className - Additional CSS classes for the fare item (optional).
 *
 * @returns A React component displaying a single fare item.
 */
function FareItem({
  label,
  value,
  info = false,
  className = "",
}: {
  label: string;
  value: string;
  info?: boolean;
  className?: string;
}) {
  return (
    <div className={`flex justify-between items-center`}>
      <div className="flex items-center">
        <span className={`${className}`}>{label}</span>
        {info && (
          <svg
            className="w-4 h-4 ml-1 text-gray-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </div>
      <span className={`font-medium ${className}`}>{value}</span>
    </div>
  );
}

/**
 * calculateFare function calculates the fare for a given flight ticket and query parameters.
 *
 * @param ticket - The flight ticket data.
 * @param queryParams - The query parameters for the flight booking (optional).
 *
 * @returns An object containing the calculated fare details.
 */
export const calculateFare = (ticket: FlightTicketRes, queryParams?: any) => {
  const { adults, children, infants, travelClass } = queryParams;

  let adultPrice = 0;
  let childPrice = 0;
  let infantPrice = 0;
  let taxRate = 0;
  let currency = "JOD";

  const selectedFlightClass = ticket?.flightClasses?.find(
    (flightClass) => flightClass.type === travelClass?.toLowerCase()
  );

  if (selectedFlightClass) {
    adultPrice = parseFloat(selectedFlightClass.price.adult!.toFixed(2));
    childPrice = parseFloat(selectedFlightClass.price.child!.toFixed(2));
    infantPrice = parseFloat(selectedFlightClass.price.infant!.toFixed(2));
    taxRate = parseFloat(selectedFlightClass.price.tax!.toFixed(2));
    currency = selectedFlightClass.price.currency;
  }

  const adultsPrice = adults * adultPrice;
  const childrenPrice = children * childPrice;
  const infantsPrice = infants * infantPrice;
  const subtotal = adultsPrice + childrenPrice + infantsPrice;

  const tax = subtotal * (taxRate / 100);
  const total = subtotal + tax;

  const fareBreakdown = [
    {
      label: `${adults} Adult`,
      value: adultsPrice,
      perPersonValue: adultPrice,
      count: adults,
      className: "text-gray-600 dark:text-gray-100",
    },
    {
      label: `${children} Child`,
      value: childrenPrice,
      perPersonValue: childPrice,
      count: children,
      className: "text-gray-600 dark:text-gray-100",
    },
    {
      label: `${infants} Infant`,
      value: infantsPrice,
      perPersonValue: infantPrice,
      count: infants,
      className: "text-gray-600 dark:text-gray-100",
    },
  ];

  return {
    subtotal,
    tax,
    total,
    currency,
    adultsPrice,
    childrenPrice,
    infantsPrice,
    taxRate,
    fareBreakdown,
  };
};

/**
 * useUrlParams function retrieves the query parameters from the URL.
 *
 * @returns An object containing the retrieved query parameters.
 */
const useUrlParams = () => {
  const searchParams = useSearchParams();

  const itinerary = searchParams.get("itinerary");
  const travelClass = searchParams.get("travelClass");
  const adults = parseInt(searchParams.get("adults") || "0");
  const children = parseInt(searchParams.get("children") || "0");
  const infants = parseInt(searchParams.get("infants") || "0");

  return {
    itinerary,
    travelClass,
    adults,
    children,
    infants,
  };
};
