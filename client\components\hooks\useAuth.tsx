"use client";

import { usePathname } from "next/navigation";
import useMasterUserAuth from "./useMasterUserAuth";

// Define paths that require master-only access
const MASTER_ONLY_PATHS = [
  "/master-control/users",
  "/master-control/team-management",
  "/master-control/ticket-requests",
  "/master-control/tickets-overview",
  "/master-control/global-bookings",
  "/ticket-hub/myBookings",
  "/ticket-hub/mySales",
];

// Define paths that allow team member access
const TEAM_MEMBER_PATHS = [
  "/master-control/users",
  "/master-control/team-management",
  "/master-control/ticket-requests",
  "/master-control/tickets-overview",
  "/master-control/global-bookings",
  "/ticket-hub/myBookings",
  "/ticket-hub/mySales",
];

const useAuth = () => {
  const pathname = usePathname();

  // Check if current path requires master-only access
  const isMasterOnlyPath = MASTER_ONLY_PATHS.some((path) =>
    pathname.startsWith(path)
  );

  // Check if current path allows team member access
  const isTeamMemberPath = TEAM_MEMBER_PATHS.some((path) =>
    pathname.startsWith(path)
  );

  // Use master-only auth for master paths, team member auth for team paths
  const { loading } = useMasterUserAuth(isTeamMemberPath);

  return { loading };
};

export default useAuth;
