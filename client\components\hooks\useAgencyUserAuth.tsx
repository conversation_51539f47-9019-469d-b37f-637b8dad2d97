"use client";

import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import { AccountStatusEnum } from "@/utils/definitions/masterDefinitions";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const useAgencyUserAuth = () => {
  const user = useAppSelector(selectUser);
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  const VALID_STATUSES: AccountStatusEnum[] = [
    AccountStatusEnum.accepted,
    AccountStatusEnum.suspended,
    AccountStatusEnum.disabled,
  ];
  const LOGOUT_STATUSES: AccountStatusEnum[] = [
    AccountStatusEnum.suspended,
    AccountStatusEnum.disabled,
  ];

  useEffect(() => {
    // redirect the user to signin page if not signed in
    if (
      !user.isLogin &&
      LOGOUT_STATUSES.includes(
        (user as StoredUser).accountStatus as AccountStatusEnum
      )
    ) {
      router.push("/signin");
      console.log(
        "User is not logged in or is suspended, disabled, or deactivated in agency auth. Redirecting to signin page."
      );
      return;
    }

    // redirect the user to not verified page if not verified yet
    if (!(user as StoredUser).verified) {
      router.push("/signup-process/not-verified");
      return;
    }

    // redirect the user to not approved page if not approved yet
    if (
      !VALID_STATUSES.includes(
        (user as StoredUser).accountStatus as AccountStatusEnum
      )
    ) {
      router.push("/signup-process/not-accepted");
      return;
    }

    // redirect the user to home page if not master
    if ((user as StoredUser).role === "affiliate") {
      router.push("/blockseats");
      return;
    }

    setLoading(false);
  }, [user, router]);

  return loading;
};

export default useAgencyUserAuth;
