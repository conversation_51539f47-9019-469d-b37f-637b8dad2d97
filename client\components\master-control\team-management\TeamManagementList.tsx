import React, { useEffect, useState } from "react";
import {
  formatDepartmentNameTeamMember,
  getFormatDateTable,
  getFormatTime,
} from "@/utils/functions/functions";
import { MasterUserResultType } from "@/utils/definitions/masterDefinitions";
import { Edit, Trash2 } from "lucide-react";
import {
  fetchDeleteUsersForMaster,
  fetchUserRequestForMaster,
} from "@/lib/data/masterUsersData";
import AlertWindow from "@/components/alert/AlertWindow";
import { setLoading } from "@/redux/features/LoadingSlice";
import { useAppDispatch } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import AddTeamMember from "./AddTeamMember";
import { getTeamId } from "@/lib/data/teamData";
import { TooltipTruncate } from "@/components/common/TooltipTruncate";
import { AccountStatus } from "@/components/common/AccountStatus";
import {
  ActionCellsProps,
  TeamManagementTableListProps,
  TeamMember,
} from "@/utils/definitions/teamDefinitions";

type StatusColor = "yellow" | "red" | "green" | "blue" | "purple" | "gray";

type StatusConfig = {
  [key: string]: StatusColor;
};

// Define status aliases
type StatusAliases = {
  [key: string]: string[];
};

const STATUS_COLORS: StatusConfig = {
  suspended: "red",
  accepted: "green",
  pending: "blue",
  rejected: "gray",
} as const;

const STATUS_ALIASES: StatusAliases = {
  suspended: ["inactive"],
  accepted: ["active"],
} as const;

const DEFAULT_STATUS = "rejected";

const getStatusConfig = (status: string) => {
  const normalizedStatus = status?.toLowerCase();

  // Check direct status
  if (normalizedStatus in STATUS_COLORS) {
    return STATUS_COLORS[normalizedStatus as keyof typeof STATUS_COLORS];
  }

  // Check aliases
  for (const [mainStatus, aliases] of Object.entries(STATUS_ALIASES)) {
    if (aliases.includes(normalizedStatus)) {
      return STATUS_COLORS[mainStatus as keyof typeof STATUS_COLORS];
    }
  }

  // Return default
  return STATUS_COLORS[DEFAULT_STATUS];
};

const getStatusStyle = (status: string) => {
  const color = getStatusConfig(status);
  const baseStyle =
    "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium";
  return `${baseStyle} bg-${color}-100 text-${color}-800`;
};

const getStatusDot = (status: string) => {
  const color = getStatusConfig(status);
  return `bg-${color}-400`;
};

export default function TeamManagementTableList({
  user,
  setUserStatus,
  onUpdate,
}: TeamManagementTableListProps) {
  // hash value url
  const hash = window.location.hash.replace("#", "");
  // loading
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userInfo, setUserInfo] = useState<MasterUserResultType | {}>([]);
  const [editMode, setEditMode] = useState<boolean>(() =>
    hash === "edit" ? true : false
  );
  const [selectedRequest, setSelectedRequest] = useState<string>("");
  const [dangerModalOpen, setDangerModalOpen] = useState<boolean>(false);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<TeamMember | null>(null);
  const [teamId, setTeamId] = useState<string>("");

  const dispatch = useAppDispatch();

  // handle user request
  const handleUserRequest = async () => {
    const status = () => {
      if (selectedRequest === "accept") {
        return "accepted";
      } else if (selectedRequest === "reject") {
        return "rejected";
      } else if (selectedRequest === "suspend") {
        return "suspended";
      }
    };

    dispatch(setLoading(true));
    try {
      if (selectedRequest === "reject") {
        // Handle delete/reject operation
        const deleteData = await fetchDeleteUsersForMaster(user.id);

        if (deleteData.success) {
          // Signal deletion to parent component
          onUpdate({ ...user, deleted: true } as TeamMember);
          // Immediately update the table
          onUpdate(user);
          // Update the user info in the parent component
          setUserInfo(deleteData.results);
          dispatch(
            setMsg({
              success: true,
              message: "Team member deleted successfully",
            })
          );
        } else {
          dispatch(
            setMsg({ success: false, message: "Failed to delete team member" })
          );
        }
      } else {
        const updateData = await fetchUserRequestForMaster(user.id, {
          accountStatus: status(),
        });

        // update user info with client if success
        if (updateData.success) {
          // setUserInfo(updateData.results);
          // Create a complete updated user object
          const updatedUserData: TeamMember = {
            ...user, // Keep existing user data as base
            ...updateData.data, // Override with new data
            accountStatus: status() || user.accountStatus,
            status: status() || user.status,
            updatedAt: new Date().toISOString(),
          };

          // Update local state
          setUserInfo(updatedUserData);

          // Update parent component state immediately
          setUserStatus({
            userId: user.id,
            status: status() || "",
            value: updatedUserData.accountStatus || "",
          });
          // Immediately update the table
          onUpdate(updatedUserData);
        }

        // display message
        dispatch(
          setMsg({ success: updateData.success, message: updateData.message })
        );
      }
    } catch (error) {
      dispatch(setMsg({ success: false, message: "Failed to update status" }));
    } finally {
      dispatch(setLoading(false));
      setModalOpen(false); // Close modal after operation
    }
  };

  // Reset selectedUser when modal closes
  useEffect(() => {
    if (!modalOpen) {
      setSelectedUser(null);
    }
  }, [modalOpen]);

  // Fetch team ID
  useEffect(() => {
    const fetchTeamId = async () => {
      try {
        const response = await getTeamId();
        setTeamId(response.teamId);
      } catch (error) {
        console.error("Error fetching team ID:", error);
      }
    };
    fetchTeamId();
  }, []);

  // Handle edit success
  const handleEditSuccess = (updatedUser: TeamMember) => {
    // Handle success
    setModalOpen(false);

    // If we receive an empty update, ignore it
    if (!updatedUser || !Object.keys(updatedUser).length) {
      return;
    }

    // Create complete updated user with all required fields
    const completeUpdatedUser: TeamMember = {
      ...user, // Keep all existing user data as base
      // ...updatedUser, // Override with new updates
      ...(Object.keys(updatedUser).length > 0 ? updatedUser : {}),
      // Explicitly set critical fields to ensure they're not undefined
      id: user.id,
      refId: user.refId,
      firstName: updatedUser.firstName ?? user.firstName,
      lastName: updatedUser.lastName ?? user.lastName,
      email: updatedUser.email ?? user.email,
      role: user.role, // Preserve original role
      subRole: updatedUser.subRole ?? user.subRole,
      department: updatedUser.department ?? user.department,
      status: updatedUser.status ?? user.status,
      accountStatus: updatedUser.accountStatus ?? user.accountStatus,
      username: user.username,
      verified: user.verified,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: new Date().toISOString(),
      team: user.team,
      invitation: user.invitation,
    };

    // Only proceed with the update if there are actual changes
    if (JSON.stringify(completeUpdatedUser) !== JSON.stringify(user)) {
      // Update local state
      setUserInfo(completeUpdatedUser);
      // Update the table immediately with complete data
      onUpdate(completeUpdatedUser);
    }

    // Close modal
    setModalOpen(false);
  };

  return (
    <tr>
      <td className="hidden">
        <AlertWindow
          title={`${selectedRequest} User Request`}
          content={`Are you sure you want to ${selectedRequest} this user?`}
          yesBtnText={selectedRequest}
          noBtnText="Cancel"
          handleYesBtnClick={handleUserRequest}
          dangerModalOpen={dangerModalOpen}
          setDangerModalOpen={setDangerModalOpen}
        />
      </td>
      {/* User ID */}
      <TeamMemberIdCell teamMemberId={user.refId} />
      {/* Full Name */}
      <TeamMemberNameCell teamMember={user} />
      {/* Email */}
      <EmailCell email={user.email} />
      {/* Role */}
      <RoleCell role={user.subRole} />
      {/* Department */}
      <DepartmentCell department={user.department} />
      {/* login status */}
      <StatusCell status={user.status} />
      {/* date added */}
      <DateAddedCell date={user.createdAt} />
      {/* last login */}
      <LastLoginCell lastLogin={user.lastLogin} />
      {/* Actions */}
      <ActionCells
        teamMember={user}
        onDelete={() => {
          setDangerModalOpen(true);
          setSelectedRequest("reject");
        }}
        onEdit={() => setEditMode(true)}
        setSelectedUser={() => {
          setSelectedUser(user);
          setModalOpen(true);
        }}
      />
      {/* Add Team Member Modal */}
      <td className="hidden">
        <AddTeamMember
          dangerModalOpen={modalOpen}
          setDangerModalOpen={setModalOpen}
          teamId={teamId}
          onSuccess={handleEditSuccess}
          selectedUser={selectedUser}
        />
      </td>
    </tr>
  );
}

// Helper function to check if the ticket is editable
const isEditable = (status: string) => {
  return ["rejected"].includes(status?.toLowerCase());
};

// Helper function to check if the ticket is view-only
const isDeletable = (status: string) => {
  return ["pending", "blocked", "rejected"].includes(status?.toLowerCase());
};

// Sub-component for Team Member ID cell
const TeamMemberIdCell = ({ teamMemberId }: { teamMemberId: number }) => (
  <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
    <div className="font-medium text-blue-400">#{teamMemberId}</div>
  </td>
);

// Sub-component for Team Member Name cell
const TeamMemberNameCell = ({ teamMember }: { teamMember: TeamMember }) => (
  <td className="table-list-field">
    <div className="font-medium text-gray-800 dark:text-gray-100 capitalize">
      <TooltipTruncate
        text={`${teamMember?.firstName} ${teamMember?.lastName}`}
        maxLength={15}
      />
    </div>
  </td>
);

// Sub-component for Email cell
const EmailCell = ({ email }: { email: string }) => (
  <td className="table-list-field">
    <TooltipTruncate text={email} maxLength={15} />
  </td>
);

// Sub-component for Role cell
const RoleCell = ({ role }: { role: string }) => (
  <td className="table-list-field">
    <div className="font-medium text-gray-800 dark:text-gray-100 capitalize">
      <AccountStatus status={role} />
    </div>
  </td>
);

// Sub-component for Department cell
const DepartmentCell = ({ department }: { department: string | undefined }) => (
  <td className="table-list-field">
    <div className="font-medium text-gray-800 dark:text-gray-100 capitalize">
      <AccountStatus status={formatDepartmentNameTeamMember(department)} />
    </div>
  </td>
);

// Sub-component for Status cell
const StatusCell = ({ status }: { status: string }) => (
  <td className="table-list-field">
    <AccountStatus status={status} />
  </td>
);

// Sub-component for Date Added cell
const DateAddedCell = ({ date }: { date: string }) => (
  <td className="table-list-field">
    <div className="font-medium text-gray-800 dark:text-gray-100">
      {date ? getFormatDateTable(date) : "N/A"}
    </div>
  </td>
);

// Sub-component for Last Login cell
const LastLoginCell = ({ lastLogin }: { lastLogin: string }) => (
  <td className="table-list-field">
    <div className="font-medium text-gray-800 dark:text-gray-100">
      {lastLogin ? getFormatDateTable(lastLogin) : "Never signed in"}{" "}
      {lastLogin ? "|" : ""} {lastLogin ? getFormatTime(lastLogin) : ""}
    </div>
  </td>
);

// Sub-component for Action buttons
const ActionCells = ({
  teamMember,
  onEdit,
  onDelete,
  setSelectedUser,
}: ActionCellsProps) => (
  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
    <div className="flex space-x-2">
      {isDeletable(teamMember.accountStatus) && (
        <ActionButton icon={Trash2} color="red" onClick={onDelete} />
      )}

      {isEditable(teamMember.accountStatus) && (
        <ActionButton icon={Edit} color="blue" onClick={onEdit} />
      )}

      <ActionButton
        icon={Edit}
        color="blue"
        onClick={() => {
          setSelectedUser(teamMember);
          onEdit();
        }}
      />
    </div>
  </td>
);

// Reusable Action Button component
const ActionButton = ({
  icon: Icon,
  color,
  onClick,
}: {
  icon: React.ComponentType<any>;
  color: string;
  onClick: () => void;
}) => (
  <button
    className={`text-${color}-400 hover:text-${color}-500 dark:hover:text-${color}-300 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-700 transition duration-300`}
    onClick={onClick}
  >
    <Icon size={18} />
  </button>
);
