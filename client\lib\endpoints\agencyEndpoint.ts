// server base url
import { API_VERSION } from '../constants/apiVersion';
const SERVER_URL = process.env.SERVER_URL;
// BASE URL
const BASE_URL = SERVER_URL + API_VERSION + "/agency";

// AGENCY ENDPOINT
const agencyUrl = {
  createAgent: BASE_URL + "/agent",
  updateAgent: (agentId: string) => BASE_URL + `/agent/${agentId}`,
  deleteAgent: (agentId: string) => BASE_URL + `/agent/${agentId}`,
  fetchAllAgents: BASE_URL + "/agents", // For agency users
  searchAgents: BASE_URL + "/agents/search",
};

export default agencyUrl;
