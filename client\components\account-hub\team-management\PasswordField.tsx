import { PasswordFieldProps } from "@/utils/definitions/agentsDefinitions";
import { EyeIcon, EyeOffIcon } from "lucide-react";

const PasswordField = ({
  label,
  name,
  value,
  placeholder,
  onChange,
  showPassword,
  onToggleVisibility,
  strength,
  formErrors,
  required,
}: PasswordFieldProps) => {
  return (
    <div>
      <label className="block text-sm text-gray-600 dark:text-gray-300 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative">
        <input
          type={showPassword ? "text" : "password"}
          name={name}
          value={value}
          placeholder={placeholder}
          onChange={onChange}
          className="w-full bg-gray-300 dark:bg-gray-800 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 border-0 relative"
          required={required}
        />
        <button
          type="button"
          onClick={onToggleVisibility}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        >
          {showPassword ? <EyeOffIcon size={18} /> : <EyeIcon size={18} />}
        </button>
      </div>
      {strength && (
        <div className="mt-2">
          <div className="flex justify-between items-center mb-1">
            <span
              className="text-sm font-medium"
              style={{ color: strength.color }}
            >
              {strength.label}
            </span>
          </div>
          <div className="flex gap-1">
            {Array.from({ length: 5 }).map((_, index) => {
              // Only fill blocks if password has content and score is sufficient
              const isFilled = value && index < strength.score;
              return (
                <div
                  key={index}
                  className={`h-2 rounded-sm flex-1 transition-colors duration-300 ${
                    !isFilled ? "bg-[#d1d5db] dark:bg-[#1f2937]" : ""
                  }`}
                  style={{
                    backgroundColor: isFilled ? strength.color : undefined,
                    transition: "all 0.3s ease",
                  }}
                />
              );
            })}
          </div>
          <div className="mt-1 text-sm" style={{ color: strength.color }}>
            {strength.message}
          </div>
        </div>
      )}
      {formErrors && (
        <p className="text-red-500 text-sm mt-1 flex items-center">
          {formErrors}
        </p>
      )}
    </div>
  );
};

export default PasswordField;
