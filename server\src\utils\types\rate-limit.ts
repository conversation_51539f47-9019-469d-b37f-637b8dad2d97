import { Store } from "express-rate-limit";

export type RateLimitTier = "free" | "premium" | "enterprise";

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  burstInterval?: number;
  burstMax?: number;
  store: Store;
}

export interface RateLimitResponse {
  error: {
    code: string;
    message: string;
    details: {
      retryAfter: number;
      rateLimitReset: string;
      currentLimit: string;
      remaining: string;
    };
  };
}
