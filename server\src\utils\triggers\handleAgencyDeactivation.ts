import { prisma } from "../../prisma";
import { getIO } from "../../socket";
import { updateAgencyTicketsToHold } from "./handleAgencyTickets";

/**
 * Updates the access status of all agency agents when an agency owner's account is deactivated
 * @param agencyId - The ID of the agency owner whose account is deactivated
 */
export const handleAgencyDeactivation = async (agencyId: string) => {
  try {
    // Update all agency agents' access status
    await prisma.agencyAgent.updateMany({
      where: {
        agencyId: agencyId,
      },
      data: {
        accountStatus: "deactivated",
        status: "inactive",
      },
    });
    // Update tickets to HOLD
    await updateAgencyTicketsToHold(agencyId, "deactivated");

    // Send session expiration event to all agency agents
    const agents = await prisma.agencyAgent.findMany({
      where: { agencyId: agencyId },
    });
    agents.forEach((agent: any) => {
      const io = getIO();
      io.to(agent.id).emit("sessionExpiration", {
        message: "Your agency account has been deactivated.",
      });
    });
  } catch (error) {
    console.error("Error in handleAgencyDeactivation:", error);
    throw error;
  }
};
