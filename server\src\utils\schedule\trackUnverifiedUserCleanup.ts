/**
 * @module trackUnverifiedUserCleanup
 * Schedules and executes robust, production-grade cleanup jobs for unverified and deactivated users.
 * - Uses UTC and logs all times as ISO strings.
 * - Employs centralized, structured logging (Winston).
 * - Ready for atomicity/locking and error aggregation.
 * - Ensures Prisma or DB connections are always closed.
 * - Designed for maintainability, DRY, and operational clarity.
 */

import cron from "node-cron";
import { cleanupUnverifiedUsers } from "../cleanup/unverifiedUserCleanup";
import {
  cleanupDeactivatedUsers,
  handle180DayDeletion,
  handle30DayMark,
} from "../cleanup/deactivationUserCleanup";
import {
  DELETION_PERIOD,
  REACTIVATION_PERIOD,
  unitOfTime,
} from "../constants/timeVariables";
import logger from "../logger";
import { retryBatch } from "../retryBatch";
// Optional: Import or implement acquireLock for distributed/atomic job runs
// import { acquireLock, releaseLock } from "../utils/lock";

/**
 * Performs all user cleanup tasks with robust error aggregation and logging.
 * Ensures all DB connections are closed, and supports future locking/atomicity.
 *
 * @param forceDeactivatedCleanup - If true, forces deactivated user cleanup regardless of schedule.
 * @returns Promise<void>
 */
export const performCleanupTasks = async (
  forceDeactivatedCleanup = false
): Promise<void> => {
  // Placeholder for lock acquisition (for future concurrency control)
  // const lock = await acquireLock("userCleanupJob");
  const errors: Error[] = [];
  logger.info("⚙️ [CLEANUP] Starting user cleanup tasks", { startedAtUTC: new Date().toISOString(), forceDeactivatedCleanup });

  try {
    // --- Unverified User Cleanup ---
    try {
      await retryBatch(async () => await cleanupUnverifiedUsers(), [], 3, 100);
      logger.info("✅ [CLEANUP] Unverified user cleanup completed (with retries)");
    } catch (err) {
      logger.error("❌ [CLEANUP] Unverified user cleanup failed after retries", { error: err });
      errors.push(err as Error);
    }

    // --- 30-Day Deactivation Mark Handling ---
    try {
      await retryBatch(async () => await handle30DayMark(), [], 3, 100);
      logger.info("✅ [CLEANUP] 30-day deactivation mark handling completed (with retries)", { reactivationPeriod: REACTIVATION_PERIOD, unitOfTime });
    } catch (err) {
      logger.error("❌ [CLEANUP] 30-day deactivation mark handling failed after retries", { error: err });
      errors.push(err as Error);
    }

    // --- 180-Day Deletion Handling ---
    try {
      if (forceDeactivatedCleanup) {
        await retryBatch(async () => await handle180DayDeletion(), [], 3, 100);
        logger.info("✅ [CLEANUP] 180-day deletion handling completed (with retries)", { deletionPeriod: DELETION_PERIOD, unitOfTime });
      }
    } catch (err) {
      logger.error("❌ [CLEANUP] 180-day deletion handling failed after retries", { error: err });
      errors.push(err as Error);
    }

    // --- Deactivated User Cleanup ---
    try {
      if (forceDeactivatedCleanup) {
        await retryBatch(async () => await cleanupDeactivatedUsers(), [], 3, 100);
        logger.info("✅ [CLEANUP] Deactivated user cleanup completed (with retries)");
      }
    } catch (err) {
      logger.error("❌ [CLEANUP] Deactivated user cleanup failed after retries", { error: err });
      errors.push(err as Error);
    }

    if (errors.length > 0) {
      logger.warn("[CLEANUP] Some cleanup tasks failed", { errorCount: errors.length, errors });
    } else {
      logger.info("[CLEANUP] All cleanup tasks completed successfully");
    }
  } catch (fatalErr) {
    logger.log({ level: 'crit', message: "[CLEANUP] Fatal error in cleanup job", error: fatalErr });
    // TODO: Notify admins (email, Slack, etc.)
  } finally {
    // Placeholder: Always release lock and close DB connections here
    // if (lock) await releaseLock(lock);
    logger.info("[CLEANUP] Cleanup job finished", { finishedAtUTC: new Date().toISOString() });
  }
};

/**
 * Force cleanup of all deactivated users regardless of their deactivation date
 * Use this to clean up all deactivated users immediately
 */
export const forceDeactivatedUserCleanup = async (): Promise<void> => {
  console.log(
    "🔥 [CLEANUP] FORCE cleanup of ALL deactivated users triggered at:",
    new Date().toISOString()
  );
  await performCleanupTasks(true);
  console.log("🔥 [CLEANUP] FORCE cleanup completed");
};

// Initialize the cron job to run the cleanup tasks at the specified interval and timezone (UTC)
// in test mode run every minute, prod run every day at 2am
const schedulerInterval = "0 2 * * *";
const scheduledTask = cron.schedule(
  schedulerInterval,
  async () => {
    console.log(
      "⏰ [CLEANUP] Running scheduled cleanup at:",
      new Date().toISOString()
    );
    await performCleanupTasks();
  },
  {
    scheduled: true,
    timezone: "UTC",
  }
);

// Make sure the schedule is running
scheduledTask.start();

// Execute cleanup tasks immediately when this module is imported
console.log("🔄 [CLEANUP] Initializing cleanup scheduler");
performCleanupTasks()
  .then(() => {
    console.log(
      "✅ [CLEANUP] Initial cleanup tasks executed on server startup"
    );
  })
  .catch((err) => {
    console.error("❌ [CLEANUP STARTUP ERROR]", err);
  });
