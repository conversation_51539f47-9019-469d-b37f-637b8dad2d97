openapi: 3.0.3
info:
  title: AirVilla Charter API
  description: API documentation for AirVilla Charter backend
  version: 1.0.0
servers:
  - url: /api/v1
    description: Local server
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        name:
          type: string
    Ticket:
      type: object
      properties:
        ticketId:
          type: string
        status:
          type: string
        userId:
          type: string
    Booking:
      type: object
      properties:
        id:
          type: string
        status:
          type: string
        userId:
          type: string
        type:
          type: string
        createdAt:
          type: string
          format: date-time
    AuthResponse:
      type: object
      properties:
        token:
          type: string
        user:
          $ref: '#/components/schemas/User'
    ErrorResponse:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
security:
  - bearerAuth: []
paths:
  /auth/signup:
    post:
      tags: [Auth]
      summary: Register a new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /auth/login:
    post:
      tags: [Auth]
      summary: Login with credentials
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user/profile:
    get:
      tags: [User]
      summary: Get current user profile
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags: [User]
      summary: Update user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: Updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user/tickets:
    post:
      tags: [User]
      summary: Get all tickets for current user
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Ticket'
  /ticket/search:
    post:
      tags: [Ticket]
      summary: Search tickets
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: string
      responses:
        '200':
          description: Search results
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Ticket'
  /ticket:
    get:
      tags: [Ticket]
      summary: Get all tickets
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Ticket'
  /booking:
    post:
      tags: [Booking]
      summary: Create a new internal booking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /booking/{id}:
    get:
      tags: [Booking]
      summary: Get booking by ID
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /booking/{id}/approve:
    post:
      tags: [Booking]
      summary: Approve booking (admin)
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Booking approved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
# Master Endpoints
  /master/users:
    get:
      tags: [Master]
      summary: Get all users (admin)
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
  /master/users/{userId}:
    get:
      tags: [Master]
      summary: Get user by ID (admin)
      parameters:
        - in: path
          name: userId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
  /master/tickets:
    post:
      tags: [Master]
      summary: Get all master tickets
      responses:
        '200':
          description: List of tickets
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Ticket'

# Team Endpoints
  /team/members:
    get:
      tags: [Team]
      summary: Get all team members
      responses:
        '200':
          description: List of team members
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
    post:
      tags: [Team]
      summary: Create a new team member
      responses:
        '201':
          description: Team member created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
  /team/members/{id}:
    put:
      tags: [Team]
      summary: Update a team member
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Updated team member
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
    delete:
      tags: [Team]
      summary: Remove a team member
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '204':
          description: Team member removed

# Agency Endpoints
  /agency/agents:
    get:
      tags: [Agency]
      summary: Get all agents for agency
      responses:
        '200':
          description: List of agents
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
    post:
      tags: [Agency]
      summary: Create a new agent
      responses:
        '201':
          description: Agent created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

# Invitation Endpoints
  /invitation/invitations:
    post:
      tags: [Invitation]
      summary: Create invitation
      responses:
        '201':
          description: Invitation created
    put:
      tags: [Invitation]
      summary: Update invitation status
      responses:
        '200':
          description: Invitation updated
    delete:
      tags: [Invitation]
      summary: Revoke invitation
      responses:
        '204':
          description: Invitation revoked
  /invitation/accept:
    post:
      tags: [Invitation]
      summary: Accept invitation
      responses:
        '200':
          description: Invitation accepted

# Notification Endpoints
  /notification/notifications:
    post:
      tags: [Notification]
      summary: Create notification
      responses:
        '201':
          description: Notification created
    get:
      tags: [Notification]
      summary: Get all notifications
      responses:
        '200':
          description: List of notifications
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
  /notification/notifications/{id}:
    get:
      tags: [Notification]
      summary: Get notification by ID
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Notification details
    delete:
      tags: [Notification]
      summary: Delete notification
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '204':
          description: Notification deleted

# Traveler Endpoints
  /traveler/validate:
    post:
      tags: [Traveler]
      summary: Validate traveler data
      responses:
        '200':
          description: Traveler valid
    get:
      tags: [Traveler]
      summary: Get all travelers
      responses:
        '200':
          description: List of travelers
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
  /traveler/{id}:
    get:
      tags: [Traveler]
      summary: Get traveler by ID
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Traveler details
    put:
      tags: [Traveler]
      summary: Update traveler
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Traveler updated
    delete:
      tags: [Traveler]
      summary: Delete traveler
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '204':
          description: Traveler deleted

# Feedback Endpoint
  /submit-feedback:
    post:
      tags: [Feedback]
      summary: Submit feedback
      responses:
        '201':
          description: Feedback submitted

# Booking Session Endpoints
  /booking-session/start:
    post:
      tags: [BookingSession]
      summary: Start booking session
      responses:
        '200':
          description: Session started
    get:
      tags: [BookingSession]
      summary: Get all booking sessions
      responses:
        '200':
          description: List of booking sessions
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
  /booking-session/validate:
    post:
      tags: [BookingSession]
      summary: Validate booking session
      responses:
        '200':
          description: Session valid
