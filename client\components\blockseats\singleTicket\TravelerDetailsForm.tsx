"use client";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  Traveler,
  TravelerType,
} from "@/utils/definitions/blockSeatsDefinitions";
import { TravelerForm } from "./TravelerForm";

import { FlightTicketRes } from "@/utils/definitions/blockSeatsDefinitions";

import { useSearchTickets } from "@/components/hooks/useSearchTicket";
import { CircleAlert } from "lucide-react";
import { getBookingType } from "@/lib/data/bookingData";

export default function TravelerDetailsForm({
  ticket,
  onFormValidChange,
  onProceedToPayment,
  onTravelerDataChange,
}: {
  ticket: FlightTicketRes;
  onFormValidChange?: (isValid: boolean) => void;
  onProceedToPayment?: (travelers: Traveler[]) => void;
  onTravelerDataChange?: (travelers: Traveler[]) => void;
}) {
  const initialTravelerState: Traveler = {
    title: "",
    gender: "",
    firstName: "",
    lastName: "",
    contactEmail: "",
    contactPhone: "",
    dateOfBirth: "",
    nationality: "",
    passportNumber: "",
    issuingCountry: "",
    passportExpiry: "",
    type: TravelerType.ADULT, // Default to ADULT as it's a required field
    errors: {},
  };
  const { state } = useSearchTickets();

  // Calculate total number of travelers based on adults, children, and infants in searchState
  const totalPassengers =
    state.passengers.adults +
    state.passengers.children +
    state.passengers.infants;

  // Initialize travelers state
  const [travelers, setTravelers] = useState<Traveler[]>([]);
  // Track which traveler form is currently open (default to first one)
  const [openTravelerIndex, setOpenTravelerIndex] = useState<number>(0);

  // Effect to initialize travelers when totalPassengers changes
  useEffect(() => {
    // Update the travelers array whenever totalPassengers changes
    // Create a new object for each traveler to avoid reference issues
    const updatedTravelers = Array.from({ length: totalPassengers }, () => ({
      ...JSON.parse(JSON.stringify(initialTravelerState)), // Deep clone to ensure completely new objects
    }));
    setTravelers(updatedTravelers);
    // Reset open traveler to the first one when total passengers changes
    setOpenTravelerIndex(0);

    // Ensure form validation state is updated
    if (onFormValidChange) {
      // Initially the form is invalid until all fields are filled
      onFormValidChange(false);
    }
  }, [totalPassengers, onFormValidChange]);

  // Separate effect to notify parent about traveler data changes
  useEffect(() => {
    // Only notify parent if travelers array is not empty
    if (travelers.length > 0 && onTravelerDataChange) {
      onTravelerDataChange(travelers);
    }
  }, [travelers, onTravelerDataChange]);

  // Calculate form validity using useMemo (without side effects)
  const isFormValid = useMemo(() => {
    // Check if all travelers have all required fields filled and no errors
    const isTravelerValid = travelers.every((traveler) => {
      // Check if there are any validation errors (excluding empty string errors)
      const hasErrors =
        traveler.errors &&
        Object.entries(traveler.errors).some(([field, error]) => {
          // Ignore empty string errors for required fields as they'll be caught by missingFields check
          return error !== "" && error !== undefined && error !== null;
        });

      // Check if all required fields are filled
      const requiredFields = [
        "title",
        "firstName",
        "lastName",
        // "contactEmail",
        // "contactPhone",
        "dateOfBirth",
        "nationality",
        "passportNumber",
        "issuingCountry",
        "passportExpiry",
        "gender",
      ];

      // Check each required field
      const missingFields = [];
      for (const field of requiredFields) {
        const value = traveler[field as keyof Traveler];
        if (!value || String(value).trim() === "") {
          missingFields.push(field);
        }
      }

      // Validate date formats only if dates exist
      let isDOBValid = false;
      let isPassportExpiryValid = false;

      if (traveler.dateOfBirth) {
        const dateOfBirth = new Date(traveler.dateOfBirth);
        isDOBValid = !isNaN(dateOfBirth.getTime()) && dateOfBirth < new Date();
      }

      if (traveler.passportExpiry) {
        const passportExpiry = new Date(traveler.passportExpiry);
        isPassportExpiryValid =
          !isNaN(passportExpiry.getTime()) && passportExpiry > new Date();
      }

      return (
        !hasErrors &&
        missingFields.length === 0 &&
        (traveler.dateOfBirth ? isDOBValid : false) &&
        (traveler.passportExpiry ? isPassportExpiryValid : false)
      );
    });

    const allValid = isTravelerValid && travelers.length > 0;
    return allValid;
  }, [travelers]);

  // Use useEffect to notify parent about form validity changes
  // This ensures the state update happens after render, not during
  useEffect(() => {
    if (onFormValidChange) {
      onFormValidChange(isFormValid);
    }
  }, [isFormValid, onFormValidChange]);

  // handle traveler data update
  const handleTravelerUpdate = useCallback(
    (index: number, updatedTraveler: Traveler) => {
      setTravelers((prevTravelers) => {
        const newTravelers = [...prevTravelers];
        newTravelers[index] = updatedTraveler;
        return newTravelers;
      });
    },
    []
  );

  const handleSubmit = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (onProceedToPayment) onProceedToPayment(travelers);
  };

  // Booking type state
  const [bookingType, setBookingType] = useState<
    "INTERNAL" | "THIRD_PARTY" | null
  >(null);

  const [bookingTypeError, setBookingTypeError] = useState<string | null>(null);
  const [checkingBookingType, setCheckingBookingType] = useState<boolean>(true);

  // Check booking type after traveler details, before options
  useEffect(() => {
    if (!ticket.id) {
      setBookingTypeError("Ticket ID not found. Cannot proceed with booking.");
      setCheckingBookingType(false);
      return;
    }
    setCheckingBookingType(true);
    getBookingType(ticket.id)
      .then((type) => {
        const normalizedType =
          type === "INTERNAL"
            ? "INTERNAL"
            : type === "THIRD_PARTY"
            ? "THIRD_PARTY"
            : null;
        setBookingType(normalizedType);
        setBookingTypeError(null);
      })
      .catch((error: any) => {
        const message =
          error?.message ||
          "Unknown error occurred while checking booking type.";
        if (
          message.includes("403") ||
          message.toLowerCase().includes("forbidden")
        ) {
          setBookingTypeError(
            "Access denied: Your agency is not allowed to book this ticket."
          );
        } else if (
          message.includes("400") ||
          message.toLowerCase().includes("invalid")
        ) {
          setBookingTypeError(
            "Invalid request. Please check your ticket information."
          );
        } else {
          setBookingTypeError(message);
        }
      })
      .finally(() => {
        setCheckingBookingType(false);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ticket.id]);

  return (
    <div className="border-0 shadow-lg mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg">
      <section className="flex flex-wrap justify-between">
        <div>
          <h3 className="text-xl md:text-2xl font-bold leading-8 tracking-tight text-gray-800 dark:text-white pt-1 md:pt-5 px-6">
            Traveler Details
          </h3>
          <div className="dark:text-white text-gray-700 text-sm px-6">
            {travelers.length}{" "}
            {travelers.length === 1 ? "Traveler" : "Travelers"}
          </div>
        </div>
        {bookingType === "INTERNAL" && (
          <button
            type="button"
            className="m-4 bg-blue-500 hover:bg-blue-600 text-white shadow text-lg py-2 px-4 font-semibold rounded-md transition-colors duration-300"
            onClick={() => {
              setTravelers((prev) =>
                prev.map(() => ({
                  title: "Mr.",
                  gender: "male",
                  firstName: "John",
                  lastName: "Doe",
                  contactEmail: "<EMAIL>",
                  contactPhone: "1234567890",
                  dateOfBirth: "1990-01-01",
                  nationality: "US",
                  passportNumber: "A1234567",
                  issuingCountry: "US",
                  passportExpiry: "2030-01-01",
                  type: TravelerType.ADULT,
                  errors: {},
                }))
              );
            }}
          >
            Skip for Now
          </button>
        )}
      </section>
      <div className="p-5">
        {travelers.map((traveler, index) => (
          <TravelerForm
            key={index}
            travelerNumber={index + 1}
            traveler={traveler}
            isOpen={index === openTravelerIndex}
            onToggle={() => setOpenTravelerIndex(index)}
            onUpdate={(updatedTraveler: Traveler) =>
              handleTravelerUpdate(index, updatedTraveler)
            }
          />
        ))}

        {/* Banner */}
        <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 flex flex-col md:flex-row items-start justify-start gap-3">
          <CircleAlert className="text-green-500 w-5 md:w-10" />
          <p className="text-lg text-gray-700 dark:text-gray-300">
            We comply with IATA security standards. Your personal information
            and travel documents are encrypted and protected throughout the
            booking process.
          </p>
        </section>
        <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 flex flex-col md:flex-row items-start justify-start gap-3">
          <CircleAlert className="text-yellow-500 w-5 md:w-10" />
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Please ensure all traveler names match exactly as shown on
            passports/travel documents to avoid issues at check-in.
          </p>
        </section>
        <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 flex flex-col md:flex-row items-start justify-start gap-3">
          <CircleAlert className="text-red-500 w-5 md:w-10" />
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Ensure all contact details are current. We'll send important flight
            updates and notifications to the email/phone provided.
          </p>
        </section>
        <section className="border-0 shadow-lg mb-6 bg-white dark:bg-gray-600 rounded-lg p-4 md:p-8 flex flex-col md:flex-row items-start justify-start gap-3">
          <CircleAlert className="text-blue-500 w-5 md:w-10" />
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Changes to your booking after purchase may result in fare
            differences and change fees.
          </p>
        </section>
      </div>
    </div>
  );
}
