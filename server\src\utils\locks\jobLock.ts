import { prisma } from "../../prisma";
import logger from "../logger";

/**
 * Acquires a DB lock for a named job. Returns true if lock acquired, false otherwise.
 * @param jobName Unique name for the job (e.g., 'bookingHoldExpiration')
 * @param lockDurationMs How long the lock should last (default: 5 minutes)
 */
export async function acquireLock(jobName: string, lockDurationMs: number = 5 * 60 * 1000): Promise<boolean> {
  const now = new Date();
  const expiresAt = new Date(now.getTime() + lockDurationMs);

  try {
    // First try to find an existing lock
    const existing = await prisma.jobLock.findUnique({ 
      where: { jobName } 
    });

    if (existing) {
      // If lock exists but is expired, update it
      if (existing.expiresAt < now) {
        await prisma.jobLock.update({
          where: { jobName },
          data: { 
            lockedAt: now,
            expiresAt
          }
        });
        logger.debug(`[JobLock] Updated expired lock '${jobName}'`);
        return true;
      }
      logger.debug(`[JobLock] Lock '${jobName}' is still active`);
      return false;
    }

    // If no existing lock, create a new one
    await prisma.jobLock.create({
      data: { 
        jobName, 
        lockedAt: now, 
        expiresAt 
      },
    });
    logger.debug(`[JobLock] Successfully acquired lock '${jobName}'`);
    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = (error as { code?: string })?.code;
    
    // Handle connection errors
    if (errorCode === 'ECONNRESET' || errorCode === 'ECONNREFUSED') {
      logger.error(`[JobLock] Database connection error when acquiring lock '${jobName}': ${errorMessage}`, {
        error: errorMessage,
        code: errorCode
      });
      return false;
    }
    
    // Log other errors
    logger.error(`[JobLock] Error acquiring lock '${jobName}':`, {
      error: errorMessage,
      code: errorCode,
      stack: error instanceof Error ? error.stack : undefined
    });
    
    return false;
  }
}

/**
 * Releases a DB lock for a named job.
 * @param jobName Unique name for the job
 */
export async function releaseLock(jobName: string): Promise<void> {
  try {
    // First try to delete the lock
    await prisma.jobLock.delete({ 
      where: { jobName } 
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorCode = (error as { code?: string })?.code;
    
    // If the lock doesn't exist (Prisma error codes for not found)
    if (errorCode && ['P2025', 'P2016', 'ECONNRESET', 'ECONNREFUSED', 'P1001', 'P1017'].includes(errorCode)){
      logger.debug(`[JobLock] Lock '${jobName}' not found, considering it released`);
      return;
    }
    
    // If it's a connection error, log it but still continue
    if (errorCode && ['ECONNRESET', 'ECONNREFUSED'].includes(errorCode)) {
      logger.error(`[JobLock] Database connection error when releasing lock '${jobName}': ${errorMessage}`, {
        error: errorMessage,
        code: errorCode
      });
      return;
    }
    
    // For other errors, log and rethrow
    logger.error(`[JobLock] Error releasing lock '${jobName}':`, { 
      error: errorMessage,
      code: errorCode,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
