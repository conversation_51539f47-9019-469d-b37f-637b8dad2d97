export enum AgentRole {
  ADMIN = "admin",
  ACCOUNTANT = "accountant",
  OPERATION = "operation",
  SALES = "sales",
}

export enum Department {
  CUSTOMER_SUPPORT = "customer_support",
  MANAGEMENT = "management",
  FINANCE = "finance",
  MARKETING = "marketing",
  SALES = "sales",
  IT = "it",
  OPERATIONS = "operations",
}

export interface AgencyUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  department: string;
  status: string;
  lastLogin?: string;
  createdAt?: string;
}

export interface FormErrors {
  [key: string]: string;
}

export // Interface for UserActions props
interface UserActionsProps {
  member: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: string;
    password: string;
    confirmPassword: string;
    subRole: string;
    department: string;
    status: string;
    lastLogin: string;
  };
  onAction: (
    action: "view" | "edit" | "suspend" | "delete",
    member: UserActionsProps["member"]
  ) => void;
}

// Interface for SearchAndFilters props
export interface SearchAndFiltersProps {
  selectedRoles: string[];
  onRoleToggle: (subRole: string | null) => void;
  onAddUser: () => void;
  setIsAddModalOpen: (isOpen: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}
