import { Request, Response } from "express";
import notificationService from "../utils/services/notification.service";
import { CreateNotificationDto } from "../utils/services/notification.service";
import { markNotificationRead, sendRealTimeNotification } from "../socket";

declare global {
  namespace Express {
    interface Request {
      io?: any;
      userId?: string;
    }
  }
}

class NotificationController {
  async createNotification(req: Request, res: Response) {
    try {
      const data: CreateNotificationDto = req.body;
      const notification = await notificationService.createNotification(data);
      sendRealTimeNotification(data.userId, notification as any);
      // Emit the new notification
      if (req.io) {
        req.io.emit("new-notification", {
          id: notification.id,
          userId: notification.userId,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          read: notification.read,
          createdAt: notification.createdAt,
          relatedId: notification.relatedId,
          link: notification.link,
          priority: notification.priority,
        });
      }
      res.status(201).json(notification);
    } catch (error: unknown) {
      if (
        error &&
        typeof error === "object" &&
        "code" in error &&
        error.code === "P2025"
      ) {
        // Record not found
        return res.status(404).json({ error: "Notification not found" });
      }
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      res
        .status(400)
        .json({ error: `Failed to create notification: ${errorMessage}` });
    }
  }

  async getUserNotifications(req: Request, res: Response) {
    try {
      const userId = req.userId!;
      const notifications =
        await notificationService.getUserNotifications(userId);
      res.json({
        success: true,
        results: {
          notifications,
          totalNotifications: notifications.length,
          nextCursor: null,
        },
      });
    } catch (error: unknown) {
      if (
        error &&
        typeof error === "object" &&
        "code" in error &&
        error.code === "P2025"
      ) {
        // Record not found
        return res.status(404).json({ error: "Notification not found" });
      }
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      res
        .status(500)
        .json({ error: `Failed to fetch notifications: ${errorMessage}` });
    }
  }

  async getNotification(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: "Notification ID is required" });
      }
      const notification = await notificationService.getNotificationById(id);
      if (!notification) {
        return res.status(404).json({ error: "Notification not found" });
      }
      res.json(notification);
    } catch (error) {
      if (
        error &&
        typeof error === "object" &&
        "code" in error &&
        error.code === "P2025"
      ) {
        // Record not found
        return res.status(404).json({ error: "Notification not found" });
      }
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      res
        .status(500)
        .json({ error: `Failed to fetch notification: ${errorMessage}` });
    }
  }

  async markAsRead(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: "Notification ID is required" });
      }
      const notification = await notificationService.markAsRead(id);
      if (!notification) {
        return res.status(404).json({ error: "Notification not found" });
      }
      markNotificationRead(notification.userId, notification.id);
      res.json(notification);
    } catch (error) {
      if (
        error &&
        typeof error === "object" &&
        "code" in error &&
        error.code === "P2025"
      ) {
        // Record not found
        return res.status(404).json({ error: "Notification not found" });
      }
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      res.status(500).json({
        error: `Failed to mark notification as read: ${errorMessage}`,
      });
    }
  }

  async markAllAsRead(req: Request, res: Response) {
    try {
      await notificationService.markAllAsRead(req.userId!);
      res.status(204).send();
    } catch (error: unknown) {
      if (
        error &&
        typeof error === "object" &&
        "code" in error &&
        error.code === "P2025"
      ) {
        // Record not found
        return res.status(404).json({ error: "Notification not found" });
      }
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      res.status(500).json({
        error: `Failed to mark all notifications as read: ${errorMessage}`,
      });
    }
  }
  async getUnreadCount(req: Request, res: Response) {
    try {
      const userId = req.userId!;
      const unreadCount = await notificationService.getUnreadCount(userId);
      res.json({ success: true, unreadCount });
    } catch (error) {
      const err = error instanceof Error ? error.message : "Unknown error";
      res.status(500).json({ error: `Failed to get unread count: ${err}` });
    }
  }

  async storePushSubscription(req: Request, res: Response) {
    try {
      const userId = req.userId!;
      const subscription = req.body;
      await notificationService.storePushSubscription(userId, subscription);
      res.status(200).json({ success: true });
    } catch (error) {
      const err = error instanceof Error ? error.message : "Unknown error";
      res
        .status(500)
        .json({ error: `Failed to store push subscription: ${err}` });
    }
  }

  async deleteNotification(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: "Notification ID is required" });
      }
      const deleteResult = await notificationService.deleteNotification(id);
      if (deleteResult.count === 0) {
        return res.status(404).json({ error: "Notification not found" });
      }
      return res.status(204).json({ success: true });
    } catch (error: unknown) {
      if (
        error &&
        typeof error === "object" &&
        "code" in error &&
        error.code === "P2025"
      ) {
        // Record not found
        return res.status(404).json({ error: "Notification not found" });
      }
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      res
        .status(500)
        .json({ error: `Failed to delete notification: ${errorMessage}` });
    }
  }
}

export default new NotificationController();
