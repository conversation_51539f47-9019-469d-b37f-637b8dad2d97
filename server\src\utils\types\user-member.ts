// import { Role, Department } from "@prisma/client";

export interface UserValidationError {
  //   field: string;
  //   message: string;
  //   success: boolean;
  [key: string]: string | boolean;
  //   validationErrors: { [key: string]: string };
}

export interface UserInput {
  firstName: string;
  lastName: string;
  //   email: string;
  //   password?: string;
  //   phoneNumber?: string;
}

export interface SanitizedUser {
  firstName: string;
  lastName: string;
  //   email: string;
  //   password?: string;
  //   phoneNumber?: string;
  validationErrors: Record<string, string>;
}
