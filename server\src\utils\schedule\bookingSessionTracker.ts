import jwt from "jsonwebtoken";
import logger from "../logger";
import { getIO } from "../../socket";
import { BOOKING_SESSION_TIMEOUT_MS } from "../../utils/constants/timeVariables";

/**
 * Module for managing user booking sessions in-memory and via JWT.
 * Handles session start, validation, and expiration notification.
 * Uses robust logging, clear error handling, and DRY patterns.
 */

// Constants for session duration and JWT secret
// JWT secret for signing session tokens
const JWT_SECRET = process.env.SECRET_KEY || "your-secret-key";

// Warn in production if JWT_SECRET is not set, but don't fail fatally
if (!process.env.SECRET_KEY && process.env.NODE_ENV === "production") {
  logger.warn("[bookingSessionTracker] WARNING: SECRET_KEY is not set in production. Using fallback secret. For security, please set SECRET_KEY in environment variables.");
}

const JOB_NAME = "bookingSessionTracker";

// In-memory store for active booking sessions
const bookingSessions = new Map<
  string,
  { expiryTime: number; timeoutId: NodeJS.Timeout }
>();

/**
 * Adaptive cleanup: Use a min-heap (priority queue) to track soonest expiry and schedule cleanup jobs dynamically.
 * Processes expired sessions in batches to avoid blocking the event loop.
 */
type SessionHeapItem = { userId: string; expiryTime: number };
const sessionExpiryHeap: SessionHeapItem[] = [];

function heapify() {
  sessionExpiryHeap.sort((a, b) => a.expiryTime - b.expiryTime);
}

function pushToHeap(userId: string, expiryTime: number) {
  sessionExpiryHeap.push({ userId, expiryTime });
  heapify();
}

function removeFromHeap(userId: string) {
  const idx = sessionExpiryHeap.findIndex(item => item.userId === userId);
  if (idx !== -1) {
    sessionExpiryHeap.splice(idx, 1);
    heapify();
  }
}

function scheduleAdaptiveCleanup() {
  if (sessionExpiryHeap.length === 0) return;
  const now = Date.now();
  const soonest = sessionExpiryHeap[0];
  const delay = Math.max(soonest.expiryTime - now, 1000); // at least 1s
  setTimeout(() => {
    adaptiveCleanupBatch();
    scheduleAdaptiveCleanup();
  }, delay);
}

function adaptiveCleanupBatch(batchSize = 50) {
  const now = Date.now();
  let cleaned = 0;
  let processed = 0;
  while (sessionExpiryHeap.length > 0 && processed < batchSize) {
    const { userId, expiryTime } = sessionExpiryHeap[0];
    if (expiryTime > now) break;
    // Remove from heap and session map
    sessionExpiryHeap.shift();
    const session = bookingSessions.get(userId);
    if (session && session.expiryTime <= now) {
      clearTimeout(session.timeoutId);
      bookingSessions.delete(userId);
      cleaned++;
    }
    processed++;
  }
  if (cleaned > 0) {
    logger.info(`[${JOB_NAME}] Adaptive cleanup: removed ${cleaned} expired booking sessions.`);
  }
  // If there are still expired sessions, schedule next batch soon
  if (sessionExpiryHeap.length > 0 && sessionExpiryHeap[0].expiryTime <= now) {
    setTimeout(() => adaptiveCleanupBatch(batchSize), 10); // next batch after 10ms
  }
}

// Hook heap into session creation and clearing
const _origSet = bookingSessions.set.bind(bookingSessions);
bookingSessions.set = function(userId, value) {
  pushToHeap(userId, value.expiryTime);
  return _origSet(userId, value);
};
const _origDelete = bookingSessions.delete.bind(bookingSessions);
bookingSessions.delete = function(userId) {
  removeFromHeap(userId);
  return _origDelete(userId);
};

// No explicit locking is required for adaptive cleanup:
// - The scheduler is recursive and only ever schedules one cleanup at a time.
// - Node.js is single-threaded, so no concurrent executions occur.
// - If adding external triggers or running in a distributed setup, add locking.

// Start the first adaptive cleanup scheduler after startup
scheduleAdaptiveCleanup();


/**
 * Starts a booking session for a user.
 * Clears any existing session, sets a timeout for expiration,
 * and returns a signed JWT and expiry timestamp.
 * @param userId - The unique user identifier
 * @returns { token: string; expiryTime: number }
 */
export const startBookingSession = (userId: string): { token: string; expiryTime: number } => {
  try {
    // Clear any existing session for this user
    clearBookingSession(userId);

    const expiryTime = Date.now() + BOOKING_SESSION_TIMEOUT_MS;
    // Issue a JWT with expiry
    // JWT 'expiresIn' must be a string (e.g., '600s'), not a number+string
    const expiresInSeconds = Math.floor(BOOKING_SESSION_TIMEOUT_MS / 1000);
    const token = jwt.sign({ userId, expiryTime }, JWT_SECRET as string, {
      expiresIn: expiresInSeconds,
    });

    // Set a timeout to expire the session and notify the client
    const timeoutId = setTimeout(() => {
      try {
        const io = getIO();
        io.to(userId).emit("bookingSessionExpired");
        logger.info(`[${JOB_NAME}] Booking session expired for user ${userId}`);
      } catch (err) {
        logger.error(`[${JOB_NAME}] Error emitting session expiration for user ${userId}:`, err);
      } finally {
        clearBookingSession(userId);
      }
    }, BOOKING_SESSION_TIMEOUT_MS);

    bookingSessions.set(userId, { expiryTime, timeoutId });
    logger.info(`[${JOB_NAME}] Started booking session for user ${userId} (expires at ${new Date(expiryTime).toISOString()})`);
    return { token, expiryTime };
  } catch (error) {
    logger.error(`[${JOB_NAME}] Failed to start booking session for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Clears a user's booking session, both in-memory and (optionally) in persistent store.
 * Cancels the expiration timeout and removes the session from the map.
 * @param userId - The unique user identifier
 */
export const clearBookingSession = (userId: string) => {
  const session = bookingSessions.get(userId);
  if (session) {
    clearTimeout(session.timeoutId);
    bookingSessions.delete(userId);
  }
};

/**
 * Validates a booking session by checking both JWT expiry and in-memory session expiry.
 * - If session is present in memory, both JWT and session expiry must be valid.
 * - If session is not found in memory, fallback to JWT expiry only.
 * @param token - The JWT token for the session
 * @returns boolean - true if session is valid, false otherwise
 */
export const validateBookingSession = (token: string): boolean => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as {
      userId: string;
      expiryTime: number;
      exp?: number;
    };
    const session = bookingSessions.get(decoded.userId);
    const now = Date.now();
    // Check JWT expiry (either 'expiryTime' or 'exp' in payload)
    const jwtExpiry = decoded.expiryTime || (decoded.exp ? decoded.exp * 1000 : 0);
    // If session is present in memory, require both session and JWT to be valid
    if (session) {
      if (session.expiryTime > now && jwtExpiry > now) {
        logger.info(`[${JOB_NAME}] Session valid for user ${decoded.userId} (in-memory + JWT)`);
        return true;
      } else {
        logger.info(`[${JOB_NAME}] Session expired for user ${decoded.userId} (in-memory or JWT)`);
        return false;
      }
    } else {
      // If session is not present in memory, fallback to JWT expiry only
      if (jwtExpiry > now) {
        logger.info(`[${JOB_NAME}] Session valid for user ${decoded.userId} (JWT only)`);
        return true;
      } else {
        logger.info(`[${JOB_NAME}] Session expired for user ${decoded.userId} (JWT only)`);
        return false;
      }
    }
  } catch (err) {
    logger.warn(`[${JOB_NAME}] Failed to validate session: invalid or expired JWT`, err);
    return false;
  }
}
