import { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { TravelersDropdown } from "./TravelersDropdown";

export const TravelersSelector = ({
  travelers,
  setTravelers,
  error,
}: {
  travelers: { adults: number; children: number; infants: number };
  setTravelers: (travelers: {
    adults: number;
    children: number;
    infants: number;
  }) => void;
  error?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative mb-6" ref={dropdownRef}>
      <div
        onClick={() => setIsOpen(!isOpen)}
        tabIndex={0}
        className="w-full min-w-48 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-white py-3 px-4 pr-2 rounded-lg border-none focus:outline-none focus:ring-2 focus:ring-red-500 cursor-pointer flex justify-between items-center"
      >
        <span>Select Travelers</span>
        <ChevronDown className="text-gray-500" size={20} />
      </div>
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}

      <TravelersDropdown
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        travelers={travelers}
        setTravelers={setTravelers}
      />
    </div>
  );
};
