import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { FlightTicketRes } from "@/utils/definitions/blockSeatsDefinitions";
import { fetchTickets } from "./bookingThunks";

// Types for the booking confirmation and traveler data
export interface BookingConfirmationState {
  bookingResult: any | null;
  travelerData: any | null;
  ticket: any | null;
  fullTicket: any | null;
  departureTicket: FlightTicketRes | null;
  returnTicket: FlightTicketRes | null;
  passengerCounts: {
    adults: number;
    children: number;
    infants: number;
    travelClass: string;
  };
  bookingType: "INTERNAL" | "THIRD_PARTY" | null;
  itinerary: "one way" | "round trip" | null;
  ticketsLoading: boolean;
}

const initialState: BookingConfirmationState = {
  bookingResult: null,
  travelerData: null,
  ticket: null,
  fullTicket: null,
  departureTicket: null,
  returnTicket: null,
  passengerCounts: {
    adults: 1,
    children: 0,
    infants: 0,
    travelClass: "Economy",
  },
  bookingType: null,
  itinerary: null,
  ticketsLoading: false,
};

const bookingConfirmationSlice = createSlice({
  name: "bookingConfirmation",
  initialState,
  reducers: {
    setBookingConfirmationData: (
      state,
      action: PayloadAction<{
        bookingResult: any;
        travelerData: any;
        ticket: any;
        fullTicket: any;
        passengerCounts: {
          adults: number;
          children: number;
          infants: number;
          travelClass: string;
        };
      }>
    ) => {
      const {
        bookingResult,
        travelerData,
        ticket,
        fullTicket,
        passengerCounts,
      } = action.payload;

      state.bookingResult = bookingResult;
      state.travelerData = travelerData;
      state.ticket = ticket;
      state.passengerCounts = passengerCounts;
      console.log("Booking Result 76", bookingResult);
      console.log("Full Ticket 77", fullTicket);
      // console.log("Full Ticket 1", bookingResult[0]);
      // console.log("Full Ticket 2", bookingResult[1]);

      // Handle tickets - ensure we store both outbound and return tickets
      // if (Array.isArray(bookingResult) && bookingResult.length > 0) {
      //   if (bookingResult.length === 2 && bookingResult[0].meta?.departure && bookingResult[1].meta?.return) {
      //     // For round trip with both outbound and return bookings
      //     const outboundTicket = {
      //       ...fullTicket,
      //       isReturn: false,
      //       id: bookingResult[0].ticketId, // Use the actual ticket ID from booking result
      //       meta: {
      //         ...fullTicket.meta,
      //         departure: bookingResult[0].meta.departure,
      //         return: bookingResult[0].meta.return
      //       }
      //     };

      //     const returnTicket = {
      //       ...fullTicket,
      //       isReturn: true,
      //       id: bookingResult[1].ticketId, // Use the actual ticket ID from booking result
      //       meta: {
      //         ...fullTicket.meta,
      //         departure: bookingResult[1].meta.departure, // Swap departure/return for return flight
      //         return: bookingResult[1].meta.return
      //       }
      //     };

      //     state.fullTicket = [outboundTicket, returnTicket];
      //   } else {
      //     // For one-way or if we can't determine, just store what we have
      //     state.fullTicket = fullTicket ? [fullTicket] : null;
      //   }
      // } else {
      //   // Fallback to the original behavior if no booking results
      //   state.fullTicket = fullTicket ? [fullTicket] : null;
      // }
      // Handle tickets based on booking result
      if (Array.isArray(bookingResult)) {
        // Find departure and return tickets
        const departureBooking = bookingResult.find((b) => b.meta?.departure);
        const returnBooking = bookingResult.find(
          (b) => b.meta?.return && b.id !== departureBooking?.id
        );

        // Set departure ticket if found
        if (departureBooking) {
          state.departureTicket = {
            ...fullTicket,
            isReturn: false,
            id: departureBooking.ticketId,
            meta: {
              ...(fullTicket?.meta || {}),
              ...departureBooking.meta,
            },
          };
        }

        // Set return ticket if found
        if (returnBooking) {
          state.returnTicket = {
            ...fullTicket,
            isReturn: true,
            id: returnBooking.ticketId,
            meta: {
              ...(fullTicket?.meta || {}),
              ...returnBooking.meta,
            },
          };
        }
      }else {
        // Fallback to the original behavior if no booking results
        state.fullTicket = fullTicket ? fullTicket : null;
      }
    },
    clearBookingConfirmationData: (state) => {
      state.bookingResult = null;
      state.travelerData = null;
      state.ticket = null;
      state.fullTicket = null;
      state.passengerCounts = {
        adults: 1,
        children: 0,
        infants: 0,
        travelClass: "Economy",
      };
      console.log("Booking Confirmation State Cleared", {
        timestamp: new Date().toISOString(),
      });
    },
    setBookingType: (
      state,
      action: PayloadAction<"INTERNAL" | "THIRD_PARTY" | null>
    ) => {
      state.bookingType = action.payload;
    },
    setItinerary: (
      state,
      action: PayloadAction<"one way" | "round trip" | null>
    ) => {
      state.itinerary = action.payload;
    },
    setFullTicket: (state, action: PayloadAction<any>) => {
      // state.fullTicket = action.payload;
      // Handle both single ticket and array of tickets
      if (Array.isArray(action.payload)) {
        state.fullTicket = action.payload;
      } else {
        state.fullTicket = action.payload ? [action.payload] : null;
      }
    },
    setTravelerData: (state, action: PayloadAction<any>) => {
      state.travelerData = action.payload;
    },
    setDepartureTicket: (
      state,
      action: PayloadAction<FlightTicketRes | null>
    ) => {
      state.departureTicket = action.payload;
    },
    setReturnTicket: (state, action: PayloadAction<FlightTicketRes | null>) => {
      state.returnTicket = action.payload;
    },
    setTicketsLoading: (state, action: PayloadAction<boolean>) => {
      state.ticketsLoading = action.payload;
    },
    setPassengerCounts: (
      state,
      action: PayloadAction<{
        adults: number;
        children: number;
        infants: number;
        travelClass: string;
      }>
    ) => {
      state.passengerCounts = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTickets.fulfilled, (state, action) => {
        const { departureTicket, returnTicket } = action.payload;
        if (departureTicket) {
          state.departureTicket = departureTicket;
        }
        if (returnTicket) {
          state.returnTicket = returnTicket;
        }
        state.ticketsLoading = false;
      })
      .addCase(fetchTickets.pending, (state) => {
        state.ticketsLoading = true;
      })
      .addCase(fetchTickets.rejected, (state) => {
        state.ticketsLoading = false;
      });
  },
});

export const {
  setBookingConfirmationData,
  clearBookingConfirmationData,
  setBookingType,
  setItinerary,
  setFullTicket,
  setTravelerData,
  setPassengerCounts,
  setDepartureTicket,
  setReturnTicket,
  setTicketsLoading,
} = bookingConfirmationSlice.actions;

// Selector for ticket
export const selectTicket = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.ticket;

export const selectFullTicket = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.fullTicket;

export const selectBookingResult = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.bookingResult;

export const selectTravelerData = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.travelerData;

export const selectPassengerCounts = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.passengerCounts;

export const selectBookingType = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.bookingType;

export const selectItinerary = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.itinerary;

export const selectDepartureTicket = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.departureTicket;

export const selectReturnTicket = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.returnTicket;

export const selectTicketsLoading = (state: {
  bookingConfirmation: BookingConfirmationState;
}) => state.bookingConfirmation.ticketsLoading;

export default bookingConfirmationSlice.reducer;
