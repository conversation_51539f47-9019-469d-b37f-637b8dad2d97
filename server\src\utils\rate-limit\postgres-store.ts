/**
 * Prisma-based rate limiting store with connection pooling
 * Implements sliding window algorithm with atomic updates
 * Uses Prisma's upsert for concurrency-safe increments
 */

import { Store, IncrementResponse } from "express-rate-limit";
import { PrismaClient } from "@prisma/client";
import { rateLimitHits, rateLimitErrors } from "./rateLimitMetrics";

// Simple in-memory fallback store
const fallbackMemory: Record<string, { count: number; expiresAt: number }> = {};

export class PrismaRateLimitStore implements Store {
  private prisma: PrismaClient;
  private windowMs: number;

  constructor(prisma: PrismaClient, windowMs: number) {
    this.prisma = prisma;
    this.windowMs = windowMs;
  }

  /**
   * Increments the rate limit count for the given key
   * Will reset the count after the specified windowMs
   * If the key does not exist, it will be created
   * Returns the total hits and reset time of the key
   * @param key The key to increment
   * @returns Promise with the total hits and reset time
   */
  private async executeWithRetry<T>(operation: () => Promise<T>, retries = 3, delay = 100): Promise<T> {
    try {
      return await operation();
    } catch (error: any) {
      if (retries > 0 && (error.code === 'ECONNRESET' || error.code === 'P1001')) {
        // Wait for the delay before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.executeWithRetry(operation, retries - 1, delay * 2);
      }
      throw error;
    }
  }

  async increment(key: string): Promise<IncrementResponse> {
    const prefixedKey = `rl:${key}`;
    const now = Date.now();
    const expiresAt = new Date(now + this.windowMs);

    try {
      rateLimitHits.inc();
      return await this.executeWithRetry(async () => {
        return this.prisma.$transaction(async (tx: any) => {
        // First check if the record exists and get its current count
        const existingRecord = await tx.rateLimit.findUnique({
          where: { key: prefixedKey },
          select: { count: true, expiresAt: true }
        });
        
        let result;
        
        if (existingRecord) {
          // Record exists, update it
          result = await tx.rateLimit.update({
            where: { key: prefixedKey },
            data: {
              count: { increment: 1 },
              expiresAt: expiresAt,
            },
            select: {
              count: true,
              expiresAt: true,
            },
          });
        } else {
          // Record doesn't exist, create it
          try {
            result = await tx.rateLimit.create({
              data: {
                key: prefixedKey,
                count: 1,
                expiresAt,
              },
              select: {
                count: true,
                expiresAt: true,
              },
            });
          } catch (error: any) {
            // If creation fails due to a race condition (another request created it first),
            // try to update it instead
            if (error.code === 'P2002') {
              result = await tx.rateLimit.update({
                where: { key: prefixedKey },
                data: {
                  count: { increment: 1 },
                  expiresAt: expiresAt,
                },
                select: {
                  count: true,
                  expiresAt: true,
                },
              });
            } else {
              throw error;
            }
          }
        }

        return {
          totalHits: result.count,
          resetTime: result.expiresAt,
        };
        }, {
          isolationLevel: 'ReadCommitted',
          maxWait: 2000,
          timeout: 5000
        });
      });
    } catch (error) {
      rateLimitErrors.inc();
      // Fallback: use in-memory store (basic, not distributed)
      if (
        !fallbackMemory[prefixedKey] ||
        fallbackMemory[prefixedKey].expiresAt < now
      ) {
        fallbackMemory[prefixedKey] = {
          count: 1,
          expiresAt: now + this.windowMs,
        };
      } else {
        fallbackMemory[prefixedKey].count++;
      }
      return {
        totalHits: fallbackMemory[prefixedKey].count,
        resetTime: new Date(fallbackMemory[prefixedKey].expiresAt),
      };
    }
  }

  // Other required Store methods
  async decrement(key: string): Promise<void> {
    const prefixedKey = `rl:${key}`;
    await this.prisma.rateLimit.update({
      where: { key: prefixedKey },
      data: { count: { decrement: 1 } },
    });
  }

  /**
   * Reset the rate limit count for a given key.
   * @param key the key to reset
   */
  async resetKey(key: string): Promise<void> {
    const prefixedKey = `rl:${key}`;
    await this.prisma.rateLimit.delete({ where: { key: prefixedKey } });
  }
}
