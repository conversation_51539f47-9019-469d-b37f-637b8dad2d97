import { Request, Response, NextFunction } from "express";
import { validateBookingSession } from "../utils/schedule/bookingSessionTracker";

interface RequestWithUser extends Request {
  user: {
    id: string;
  };
}

export const validateBookingSessionMiddleware = async (
  req: RequestWithUser,
  res: Response,
  next: NextFunction
) => {
  try {
    const isValid = await validateBookingSession(req.user.id);
    if (!isValid) {
      return res.status(403).json({
        success: false,
        message: "Booking session has expired",
      });
    }
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to validate booking session",
    });
  }
};
