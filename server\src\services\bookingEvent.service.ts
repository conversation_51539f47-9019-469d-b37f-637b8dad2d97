import { PrismaClient, BookingEventType, BookingStatus, ActorType } from '@prisma/client';
import { Request } from 'express';

import { prisma } from "../prisma";

type BookingEventData = {
  bookingId: string;
  eventType: BookingEventType;
  actorId?: string;
  actorType: ActorType;
  oldStatus?: BookingStatus;
  newStatus?: BookingStatus;
  reason?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
};

export class BookingEventService {
  static async createEvent(data: BookingEventData, req?: Request) {
    const ip = req?.ip || req?.socket?.remoteAddress;
    const userAgent = req?.headers['user-agent'];

    return prisma.bookingHistoryLog.create({
      data: {
        bookingId: data.bookingId,
        eventType: data.eventType,
        actorId: data.actorId || null,
        actorType: data.actorType,
        oldStatus: data.oldStatus || null,
        newStatus: data.newStatus || null,
        reason: data.reason || null,
        metadata: data.metadata || {},
        ipAddress: data.ipAddress || ip,
        userAgent: data.userAgent || userAgent,
      },
    });
  }

  static async getLatestEvent(bookingId: string) {
    return prisma.bookingHistoryLog.findFirst({
      where: { bookingId },
      orderBy: { createdAt: 'desc' },
    });
  }

  static async getBookingTimeline(bookingId: string) {
    return prisma.bookingHistoryLog.findMany({
      where: { bookingId },
      orderBy: { createdAt: 'asc' },
    });
  }

  // Helper methods for common events
  static async logSeatHeld(bookingId: string, actorId: string, actorType: ActorType, holdType: 'QUICK_HOLD' | 'RESERVE', req?: Request) {
    return this.createEvent({
      bookingId,
      eventType: 'SEAT_HELD',
      actorId,
      actorType,
      newStatus: 'PENDING_APPROVAL',
      metadata: { holdType, holdDuration: holdType === 'QUICK_HOLD' ? 3600 : 900 },
    }, req);
  }

  static async logBookingApproved(bookingId: string, actorId: string, actorType: ActorType, notes?: string, req?: Request) {
    return this.createEvent({
      bookingId,
      eventType: 'BOOKING_APPROVED',
      actorId,
      actorType,
      newStatus: 'BOOKING_CONFIRMED',
      reason: notes,
    }, req);
  }

  static async logBookingRejected(bookingId: string, actorId: string, actorType: ActorType, reason: string, req?: Request) {
    return this.createEvent({
      bookingId,
      eventType: 'BOOKING_REJECTED',
      actorId,
      actorType,
      newStatus: 'BOOKING_REJECTED',
      reason,
    }, req);
  }

  static async logBookingCancelled(
    bookingId: string, 
    actorId: string, 
    actorType: ActorType, 
    reason?: string, 
    req?: Request,
    isSystemInitiated: boolean = false
  ) {
    return this.createEvent({
      bookingId,
      eventType: 'BOOKING_CANCELLED',
      actorId,
      actorType,
      newStatus: isSystemInitiated ? 'CANCELLED_BY_SYSTEM' : 'CANCELLED_BY_USER',
      reason,
    }, req);
  }

  static async logBookingTimedOut(bookingId: string, req?: Request) {
    return this.createEvent({
      bookingId,
      eventType: 'BOOKING_TIMED_OUT',
      actorType: 'SYSTEM',
      newStatus: 'TIMED_OUT',
    }, req);
  }
}
