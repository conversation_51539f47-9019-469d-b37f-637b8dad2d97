"use client";
import React, { useEffect, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import {
  setBookingType,
  selectBookingType,
  selectItinerary,
  setItinerary,
} from "@/redux/features/BookingConfirmationSlice";
import { getBookingType, getBookingStatus } from "@/lib/data/bookingData";
import PaymentCard from "./PaymentCard";
import FareSummary from "./FareSummary";
import { calculateFare } from "./FareSummary";
import {
  CreateInternalBookingDto,
  TravelerDto,
} from "@/utils/types/booking.types";
import { BookingSource } from "@/utils/types/booking.types";
import Image from "next/image";
import imgBanner from "@/public/images/support-banner.png";
import { useRouter, useSearchParams, useParams } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/features/AuthSlice";
import { CheckCircle } from "lucide-react";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { setFareResult } from "@/redux/features/FareResultSlice";
import { RootState } from "@/redux/store";
import { useBookingTimer } from "@/hooks/useBookingTimer";

interface AgentOperations {
  bookingReference: string;
  ticketId: string;
  issuingBookingSource: string;
  agent: {
    name: string | null;
    role: string | null;
    agency: string | null;
  };
  formFiller: {
    name: string | null;
    email: string | null;
    role: string | null;
  };
}

export default function PaymentDetails() {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();

  // Extract and process ticket IDs from URL
  const { ticketId, returnTicketId } = useMemo(() => {
    // Get the ticket ID from URL params
    const ticketIdParam = params.ticketId;
    let ticketIdStr = "";
    let extractedReturnTicketId: string | undefined;

    // Handle case where ticketIdParam is an array or string
    if (Array.isArray(ticketIdParam)) {
      ticketIdStr = ticketIdParam[0] || "";
    } else if (ticketIdParam) {
      ticketIdStr = ticketIdParam;
    }

    // Check if the ticketId contains both departure and return ticket IDs (separated by underscore)
    let extractedTicketId = "";
    if (ticketIdStr && typeof ticketIdStr === "string") {
      const ticketIds = ticketIdStr.split("_");
      extractedTicketId = ticketIds[0];
      if (ticketIds.length > 1) {
        extractedReturnTicketId = ticketIds[1];
      }
    }

    // Fallback to search params if not found in URL path
    if (!extractedTicketId) {
      extractedTicketId =
        searchParams.get("tktId") || searchParams.get("ticketId") || "";
    }

    return {
      ticketId: extractedTicketId,
      returnTicketId: extractedReturnTicketId,
    };
  }, [params.ticketId, searchParams]);

  // Booking type state
  const bookingType = useSelector(selectBookingType);
  const itinerary = useSelector(selectItinerary);

  // Log the full Redux state for debugging
  const reduxState = useSelector((state: RootState) => state);

  // Get itinerary from URL params and sync with Redux
  const urlItinerary = searchParams.get("itinerary");

  // Sync initial itinerary from searchParams to Redux store
  useEffect(() => {
    if (urlItinerary && (!itinerary || itinerary !== urlItinerary)) {
      dispatch(setItinerary(urlItinerary as "one way" | "round trip"));
    }
  }, [urlItinerary, dispatch, itinerary]);

  // Calculate tripTypeValue based on URL params first, then fall back to Redux state
  const tripTypeValue = useMemo(() => {
    // First try to get from URL params
    if (urlItinerary) {
      return urlItinerary.toLowerCase() === "round trip"
        ? "ROUND_TRIP"
        : "ONE_WAY";
    }
    // Fall back to Redux state
    return itinerary &&
      typeof itinerary === "string" &&
      itinerary.toLowerCase() === "round trip"
      ? "ROUND_TRIP"
      : "ONE_WAY";
  }, [urlItinerary, itinerary]);

  const [bookingTypeError, setBookingTypeError] = useState<string | null>(null);
  const [checkingBookingType, setCheckingBookingType] = useState<boolean>(true);
  const [isBookingConfirmed, setIsBookingConfirmed] = useState(false);
  const [quickHold, setQuickHold] = useState(false);
  const [submitBooking, setSubmitBooking] = useState(false);

  // Handler for booking result
  // Validate traveler dates before submitting booking
  const validateTravelerDates = (travelers: TravelerDto[]): string | null => {
    const isIsoDate = (date: string) => /^\d{4}-\d{2}-\d{2}$/.test(date);
    for (let i = 0; i < travelers.length; i++) {
      const t = travelers[i];
      if (!t.dateOfBirth || !isIsoDate(t.dateOfBirth)) {
        return `Traveler ${
          i + 1
        }: Date of Birth must be provided in YYYY-MM-DD format.`;
      }
      if (!t.expirationDate || !isIsoDate(t.expirationDate)) {
        return `Traveler ${
          i + 1
        }: Passport Expiry must be provided in YYYY-MM-DD format.`;
      }
    }
    return null;
  };

  const handleBookingResult = (result: any) => {
    // Create a serializable version of the result
    const serializableResult = {
      ...result,
      // If there's an error, convert it to a plain object
      error: result.error
        ? {
            message: result.error.message || "An unknown error occurred",
            name: result.error.name,
            stack:
              process.env.NODE_ENV === "development"
                ? result.error.stack
                : undefined,
          }
        : null,
    };

    dispatch(setFareResult(serializableResult));

    if (result.success) {
      setIsBookingConfirmed(true);
      dispatch(setMsg({ message: result.message, success: true }));
    } else {
      // Safely access the error message
      const errorMessage = result.error?.message || "An unknown error occurred";
      dispatch(setMsg({ message: errorMessage, success: false }));
    }
  };

  // Function to fetch booking status
  const fetchBookingStatus = async (bookingId: string) => {
    try {
      // Use the imported function to get booking status
      const status = await getBookingStatus(bookingId);
      // You can update state or perform actions based on the status here
    } catch (error) {
      console.error("Error fetching booking status:", error);
    }
  };

  // Fetch booking status when component mounts or booking ID changes
  const fareResult = useSelector((state: RootState) => state.fareResult);

  // Get the clearBookingSession function from the useBookingTimer hook
  const { clearBookingSession } = useBookingTimer();

  // Reset timer when component mounts (handles browser back button navigation)
  useEffect(() => {
    // Reset booking timer only if not in a booking flow
    // This preserves the timer when navigating between booking pages
    clearBookingSession(false); // false means don't force reset
  }, [clearBookingSession]);

  useEffect(() => {
    if (isBookingConfirmed && fareResult?.fareResult?.booking?.id) {
      fetchBookingStatus(fareResult.fareResult.booking.id);
    }
  }, [isBookingConfirmed, fareResult]);

  // --- Booking data validation for both internal and third-party bookings ---
  function validateBookingData(data: CreateInternalBookingDto): string | null {
    if (!data.ticketId) return "Missing ticketId.";
    if (!data.userId) return "Missing userId.";
    if (!data.agencyAgentId) return "Missing agencyAgentId.";
    if (!data.travelers || !data.travelers.length)
      return "At least one traveler is required.";
    if (!data.seats || !data.seats.length)
      return "At least one seat is required.";
    if (!data.type) return "Missing booking type.";
    if (!data.source) return "Missing booking source.";
    // For third-party bookings, seatNumber must not be empty
    if (
      data.source === "THIRD_PARTY" &&
      data.seats.some((seat) => !seat.seatNumber)
    ) {
      return "Seat number is required for all seats in third-party bookings.";
    }
    // For all bookings, check traveler date formats
    for (let i = 0; i < data.travelers.length; i++) {
      const t = data.travelers[i];
      if (!/^\d{4}-\d{2}-\d{2}$/.test(t.dateOfBirth)) {
        return `Traveler ${i + 1}: Date of Birth must be in YYYY-MM-DD format.`;
      }
      if (!/^\d{4}-\d{2}-\d{2}$/.test(t.expirationDate)) {
        return `Traveler ${
          i + 1
        }: Passport Expiry must be in YYYY-MM-DD format.`;
      }
    }
    return null;
  }
  // Ticket ID is now handled in the useMemo hook at the top of the component

  // Check booking type after traveler details, before options
  useEffect(() => {
    if (!ticketId) {
      setBookingTypeError("Ticket ID not found. Cannot proceed with booking.");
      setCheckingBookingType(false);
      return;
    }
    setCheckingBookingType(true);
    getBookingType(ticketId)
      .then((type) => {
        const normalizedType =
          type === "INTERNAL"
            ? "INTERNAL"
            : type === "THIRD_PARTY"
            ? "THIRD_PARTY"
            : null;
        dispatch(setBookingType(normalizedType));
        setBookingTypeError(null);
      })
      .catch((error: any) => {
        const message =
          error?.message ||
          "Unknown error occurred while checking booking type.";
        if (
          message.includes("403") ||
          message.toLowerCase().includes("forbidden")
        ) {
          setBookingTypeError(
            "Access denied: Your agency is not allowed to book this ticket."
          );
        } else if (
          message.includes("400") ||
          message.toLowerCase().includes("invalid")
        ) {
          setBookingTypeError(
            "Invalid request. Please check your ticket information."
          );
        } else {
          setBookingTypeError(message);
        }
      })
      .finally(() => {
        setCheckingBookingType(false);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ticketId]);

  // Extract URL parameters
  const travelClass = searchParams.get("travelClass");
  const adults = parseInt(searchParams.get("adults") || "0");
  const children = parseInt(searchParams.get("children") || "0");
  const infants = parseInt(searchParams.get("infants") || "0");

  let price;
  try {
    const priceParam = searchParams.get("price");
    if (priceParam) {
      price = JSON.parse(priceParam);
    }
  } catch (error) {
    console.error("Error parsing price from URL in PaymentDetails:", error);
    price = null;
  }

  let agentOperations;
  try {
    const agentOpsParam = searchParams.get("agentOperations");
    if (agentOpsParam) {
      agentOperations = JSON.parse(agentOpsParam);
    }
  } catch (error) {
    console.error(
      "Error parsing agentOperations from URL in PaymentDetails:",
      error
    );
    agentOperations = null; // Default value if parsing fails
  }

  const departureCode = searchParams.get("departureCode");
  const departureCity = searchParams.get("departureCity");
  const departureCountry = searchParams.get("departureCountry");
  const departureAirport = searchParams.get("departureAirport");
  const duration = searchParams.get("duration");
  const arrivalCode = searchParams.get("arrivalCode");
  const arrivalCity = searchParams.get("arrivalCity");
  const arrivalCountry = searchParams.get("arrivalCountry");
  const arrivalAirport = searchParams.get("arrivalAirport");
  const departureCarrier = searchParams.get("departureCarrier");
  const departureFlightNumber = searchParams.get("departureFlightNumber");
  const returnCarrier = searchParams.get("returnCarrier");
  const returnFlightNumber = searchParams.get("returnFlightNumber");
  const stops = searchParams.get("stops");
  const departureTime = searchParams.get("departureTime");
  const arrivalTime = searchParams.get("arrivalTime");
  const flightNumber = searchParams.get("flightNumber");
  const departureFlightDate = searchParams.get("flightDate");
  const arrivalFlightDate = searchParams.get("returnDate");

  const bookRef = searchParams.get("bookRef");
  const tktId = searchParams.get("tktId");
  const source = searchParams.get("source");
  const agentName = searchParams.get("agentName");
  const agentRole = searchParams.get("agentRole");
  const agentAgency = searchParams.get("agentAgency");
  const fillerName = searchParams.get("fillerName");
  const fillerEmail = searchParams.get("fillerEmail");
  const fillerRole = searchParams.get("fillerRole");
  const fillerAgency = searchParams.get("fillerAgency");

  // --- User/agent IDs (replace with your real auth/session logic if you have it) ---
  const user = useAppSelector(selectUser);
  const userId = user && "id" in user ? user.id : undefined;
  const agencyAgentId = searchParams.get("agentId") || "demo-agent-id";
  const teamMemberId = searchParams.get("teamMemberId") || undefined;

  // --- Booking type ---
  const bookingTypeValue = quickHold ? "QUICK_HOLD" : "SUBMIT_BOOKING";

  // --- Travelers: collect traveler details from user input ---
  // --- Auto-populate traveler details from URL params ---
  // Normalizes traveler title to match allowed enum values for backend
  function normalizeTitle(title: string): string {
    if (!title) return "MR";
    const t = title.replace(".", "").trim().toLowerCase();
    if (t === "mr") return "MR";
    if (t === "mrs") return "MRS";
    if (t === "ms") return "MS";

    return "MR"; // fallback to 'Mr' if not recognized
  }

  // --- Multi-traveler validation and extraction ---
  function validateTravelersFromParams(searchParams: URLSearchParams): {
    valid: boolean;
    error?: string;
    travelers?: TravelerDto[];
  } {
    const travelers: TravelerDto[] = [];
    let idx = 0;
    while (true) {
      // Check if this traveler exists (by title or firstName)
      const prefix = `traveler_${idx}_`;
      const title = searchParams.get(prefix + "title");
      const firstName = searchParams.get(prefix + "firstName");
      if (!title && !firstName) break; // No more travelers

      // Required fields and error labels
      const requiredFields = [
        { key: "title", label: "Title" },
        { key: "firstName", label: "First Name" },
        { key: "lastName", label: "Last Name" },
        { key: "nationality", label: "Nationality" },
        { key: "dateOfBirth", label: "Date of Birth" },
        { key: "gender", label: "Gender" },
        { key: "passportNumber", label: "Passport Number" },
        { key: "passportExpiry", label: "Passport Expiry" },
        // { key: "contactEmail", label: "Email" },
        // { key: "contactPhone", label: "Phone Number" },
        { key: "issuingCountry", label: "Issuing Country" },
      ];
      for (const field of requiredFields) {
        const value = (searchParams.get(prefix + field.key) || "").trim();
        if (!value) {
          return {
            valid: false,
            error: `Traveler ${idx + 1}: ${field.label} is required.`,
          };
        }
      }
      // Validate date fields
      const dob = (searchParams.get(prefix + "dateOfBirth") || "").trim();
      if (!dob || !/^\d{4}-\d{2}-\d{2}$/.test(dob)) {
        return {
          valid: false,
          error: `Traveler ${
            idx + 1
          }: Date of Birth must be in YYYY-MM-DD format.`,
        };
      }
      const expiry = (searchParams.get(prefix + "passportExpiry") || "").trim();
      if (!/^\d{4}-\d{2}-\d{2}$/.test(expiry)) {
        return {
          valid: false,
          error: `Traveler ${
            idx + 1
          }: Passport Expiry must be in YYYY-MM-DD format.`,
        };
      }
      // Normalize title
      const normalizedTitle = normalizeTitle(title || "");
      // Build traveler object
      travelers.push({
        title: normalizedTitle,
        gender: (searchParams.get(prefix + "gender") || "").trim(),
        firstName: (searchParams.get(prefix + "firstName") || "").trim(),
        lastName: (searchParams.get(prefix + "lastName") || "").trim(),
        contactEmail: (searchParams.get(prefix + "contactEmail") || "").trim(),
        contactPhone: (searchParams.get(prefix + "contactPhone") || "").trim(),
        nationality: (searchParams.get(prefix + "nationality") || "").trim(),
        documentType: "PASSPORT", // Default, can be improved if param exists
        documentNumber: (
          searchParams.get(prefix + "passportNumber") || ""
        ).trim(),
        issuingCountry: (
          searchParams.get(prefix + "issuingCountry") || ""
        ).trim(),
        expirationDate: expiry,
        dateOfBirth: dob,
      });
      idx++;
    }
    if (travelers.length === 0) {
      return { valid: false, error: "At least one traveler is required." };
    }
    return { valid: true, travelers };
  }

  // Use the validation before submitting booking
  const result = validateTravelersFromParams(searchParams);
  const bookingTravelers: TravelerDto[] =
    result.valid && result.travelers ? result.travelers : [];
  if (!result.valid) {
    // You can replace this with your UI error handler
    dispatch(setMsg({ message: result?.error || null, success: false }));
    // Optionally, return or block further logic here
  }

  // --- Create the params object for FareSummary ---
  const fareSummaryParams = {
    itinerary,
    travelClass,
    adults,
    children,
    infants,
    price,
    agentOperations,
    departureCode,
    departureCity,
    departureCountry,
    departureAirport,
    arrivalCode,
    arrivalCity,
    arrivalCountry,
    arrivalAirport,
    departureCarrier,
    duration,
    departureFlightNumber,
    returnCarrier,
    returnFlightNumber,
    flightNumber,
    departureFlightDate,
    arrivalFlightDate,
    stops,
    departureTime,
    arrivalTime,
    bookRef,
    tktId,
    source,
    agentName,
    agentRole,
    agentAgency,
    fillerName,
    fillerEmail,
    fillerRole,
    fillerAgency,
    travelers: bookingTravelers,
    seats: [], // Empty array for initial calculation
  };

  // --- Calculate fare using fareSummaryParams ---
  const fare = calculateFare(fareSummaryParams);

  const flightClass = fareSummaryParams.travelClass || "Economy";

  // --- Now set seats and bookingSeats with dynamic fare.total ---
  const seats = [{ seatNumber: "1A", flightClass, totalPrice: fare.total }];
  const bookingSeats = [
    { seatNumber: "", flightClass, totalPrice: fare.total },
  ];

  // Get return ticket ID from URL params if not already set
  let finalReturnTicketId = returnTicketId;
  if (!finalReturnTicketId) {
    // First try to get it from the URL search params
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      finalReturnTicketId = urlParams.get("returnId") || undefined;
    }

    // If still not found, try to get it from the searchParams prop
    if (!finalReturnTicketId) {
      finalReturnTicketId = searchParams.get("returnId") || undefined;
    }
  }

  // Ensure returnTicketId is included for round-trip bookings
  if (tripTypeValue === "ROUND_TRIP" && !finalReturnTicketId) {
    console.warn("WARNING: Round-trip booking is missing returnTicketId");
  } else if (tripTypeValue === "ROUND_TRIP" && finalReturnTicketId) {
    console.log("Round-trip booking with returnTicketId:", finalReturnTicketId);
  }

  // Create the base booking data
  const bookingData: CreateInternalBookingDto = {
    ticketId: ticketId || "",
    returnTicketId:
      tripTypeValue === "ROUND_TRIP" ? finalReturnTicketId : undefined,
    userId: userId || "",
    agencyAgentId,
    teamMemberId,
    type: bookingTypeValue,
    source: bookingType === "INTERNAL" ? "INTERNAL" : "THIRD_PARTY",
    tripType: tripTypeValue,
    travelers: bookingTravelers,
    referenceNumber: "",
    seats,
  };

  // Construct the agentOperations object for PaymentCard using real user data
  const agentOpsForPaymentCard: AgentOperations = agentOperations
    ? { ...agentOperations }
    : {
        bookingReference: searchParams.get("departureId") || ticketId || "",
        ticketId: searchParams.get("departureId") || ticketId || "",
        issuingBookingSource: searchParams.get("source") || "Internal",
        agent: {
          name: ("name" in user ? user.name : null) || agentName || "",
          role: ("role" in user ? user.role : null) || agentRole || "",
          agency: ("agency" in user ? user.agency : null) || agentAgency || "",
        },
        formFiller: {
          name: ("name" in user ? user.name : null) || fillerName || "",
          email: ("email" in user ? user.email : null) || fillerEmail || "",
          role: ("role" in user ? user.role : null) || fillerRole || "",
        },
      };

  // Show loading or error before booking options
  if (checkingBookingType) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <div className="text-lg text-gray-600 dark:text-gray-300">
          Checking booking type...
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto pt-8 font-sans">
      <section className="flex items-center gap-3">
        <h1 className="font-bold text-3xl leading-10 text-gray-700 dark:text-white">
          {bookingType === "INTERNAL"
            ? "Internal Booking"
            : "Third-Party Booking"}
        </h1>
      </section>
      <Image
        src={imgBanner}
        alt="img"
        width={800}
        height={120}
        className="w-full rounded-lg shadow-lg my-6"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:gap-8 space-y-4 lg:space-y-0">
        <div className="flex-grow bg-gray-200 dark:bg-gray-800 rounded-lg shadow-xl p-4 md:p-8 col-span-2">
          <PaymentCard
            quickHold={quickHold}
            setQuickHold={setQuickHold}
            submitBooking={submitBooking}
            setSubmitBooking={setSubmitBooking}
            bookingType={bookingType}
            fare={fare}
            agentOperations={{
              ...agentOpsForPaymentCard,
              formFiller: {
                id: ("id" in user ? user.id : "") || "",
                name:
                  ("name" in user
                    ? typeof user.name === "string"
                      ? user.name
                      : ""
                    : "") ||
                  fillerName ||
                  "",
                email:
                  ("email" in user
                    ? typeof user.email === "string"
                      ? user.email
                      : ""
                    : null) ||
                  fillerEmail ||
                  "",
                role:
                  ("role" in user
                    ? typeof user.role === "string"
                      ? user.role
                      : ""
                    : null) ||
                  fillerRole ||
                  "",
                agency:
                  ("agency" in user
                    ? typeof user.agency === "string"
                      ? user.agency
                      : ""
                    : "") ||
                  agentAgency ||
                  "",
              },
            }}
          />
          {/* Mobile screens */}
          <div className="xl:hidden mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <FareSummary
              quickHold={quickHold}
              submitBooking={submitBooking}
              bookingData={bookingData}
              onBookingResult={handleBookingResult}
              bookingType={bookingType}
              fare={fare}
            />
          </div>
        </div>
        {/* Right side for large screens */}
        <div className="hidden xl:block xl:sticky xl:top-4 self-start w-full mt-[140px] col-span-1">
          <FareSummary
            quickHold={quickHold}
            submitBooking={submitBooking}
            bookingData={bookingData}
            onBookingResult={handleBookingResult}
            bookingType={bookingType}
            fare={fare}
          />
        </div>
      </div>
    </div>
  );
}
