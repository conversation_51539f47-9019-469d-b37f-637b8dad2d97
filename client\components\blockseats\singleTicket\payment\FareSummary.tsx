"use client";
import React, { useEffect, useState } from "react";
import { formattedPrice, getFormatDate } from "@/utils/functions/functions";
import { useRouter, useSearchParams } from "next/navigation";
import { FaArrowRight } from "react-icons/fa";
import { ChevronDown, CircleAlert } from "lucide-react";
import { useBookingTimer } from "@/hooks/useBookingTimer";
// Get expiry timestamp from query params if available
function getExpiresAt(queryParams: any) {
  if (queryParams && queryParams.expiresAt) {
    const ts = parseInt(queryParams.expiresAt);
    return isNaN(ts) ? undefined : ts;
  }
  return undefined;
}
import PopupConfirmation from "./PopupConfirmation";
import PopupPaymentForm from "./PopupPaymentForm";
import LoadingPopup from "./LoadingPopup";
import { CreateInternalBookingDto } from "@/utils/types/booking.types";
import { validateBookingData } from "@/utils/validation/bookingValidation";
import FareBreakdownRow from "@/components/common/FareBreakdownRow";

/**
 * FareSummary component displays the fare summary for a flight booking.
 * It takes in departure and return ticket data, calculates the fare, and displays it in a formatted manner.
 *
 * @param departureTicket - The departure flight ticket data.
 * @param returnTicket - The return flight ticket data (optional).
 *
 * @returns A React component displaying the fare summary.
 */
interface FareSummaryProps {
  quickHold: boolean;
  submitBooking: boolean;
  bookingData: CreateInternalBookingDto;
  onBookingResult?: (result: any) => void;
  bookingType: "INTERNAL" | "THIRD_PARTY" | null;
  fare?: any;
  setBookingResult?: (result: any) => void;
}

export default function FareSummary({
  quickHold,
  submitBooking,
  bookingData,
  onBookingResult,
  bookingType,
  fare,
}: FareSummaryProps) {
  // Error state for validation
  const [error, setError] = useState<string>("");
  const queryParams = useUrlParams();
  const expiresAt = getExpiresAt(queryParams);
  const router = useRouter();
  const { formattedTime, isExpired } = useBookingTimer({
    expiresAt,
    onExpire: () => {},
    redirectOnExpire: true, // Enable automatic redirection to /blockseats/list
  });

  // Use fare prop if provided, otherwise calculate internally
  const departureFare = fare ?? calculateFare(queryParams);

  // Calculate return fare if it's a round trip (if needed)
  let returnFare = null;

  // // Add state for modal
  // const [confirmationOpen, setConfirmationOpen] = useState(false);
  // const [actionType, setActionType] = useState<
  //   "quickHold" | "reserveSeat" | null
  // >(null);
  // Centralized modal state

  type ModalState =
    | null
    | { type: "confirmation"; actionType: "quickHold" | "reserveSeat" }
    | { type: "payment" }
    | { type: "loading" };

  const [modalState, setModalState] = useState<ModalState>(null);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [actionType, setActionType] = useState<
    "quickHold" | "reserveSeat" | null
  >(null); // For legacy code, can be removed after migration

  // Block back navigation (fixed: useEffect for single mount)
  useEffect(() => {
    const blockBackNavigation = (e: PopStateEvent) => {
      e.preventDefault();
      window.history.pushState(null, "", window.location.href);
    };
    window.history.pushState(null, "", window.location.href);
    window.addEventListener("popstate", blockBackNavigation);
    return () => {
      window.removeEventListener("popstate", blockBackNavigation);
    };
  }, []);

  // Defensive: close modal if booking expires
  useEffect(() => {
    if (isExpired) {
      setModalState(null);
    }
  }, [isExpired]);

  const handleQuickHoldClick = () => {
    // setActionType("quickHold");
    // setConfirmationOpen(true);
    setModalState({ type: "confirmation", actionType: "quickHold" });
  };

  const handleReserveSeatClick = () => {
    // setActionType("reserveSeat");
    // setConfirmationOpen(true);
    setModalState({ type: "confirmation", actionType: "reserveSeat" });
  };

  // Open Payment Modal
  const handleOpenPayment = () => {
    setModalState({ type: "payment" });
  };

  // Close all modals
  const handleCloseModal = () => {
    setModalState(null);
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
  };

  // Helper to show loading modal for payment
  const showLoadingModal = () => {
    setModalState({ type: "loading" });
    // Fallback: hide loading after 15s
    const timeout = setTimeout(() => {
      setModalState(null);
    }, 15000);
    setLoadingTimeout(timeout);
  };

  // Helper to hide loading modal
  const hideLoadingModal = () => {
    setModalState(null);
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
  };
  try {
    if (queryParams.price) {
      const priceObj =
        typeof queryParams.price === "string"
          ? JSON.parse(queryParams.price)
          : queryParams.price;

      // Check if this is a round trip by examining the returnCurrency property
      const hasReturnFlight =
        priceObj.returnCurrency && priceObj.returnCurrency !== "";
      if (hasReturnFlight) {
        // Pass returnFare flag to use return prices instead of departure prices
        returnFare = calculateFare(queryParams, true);
      }
    }
  } catch (error) {
    console.error("Error parsing price JSON in FareSummary:", error);
  }

  // Check if it's a round trip
  const isRoundTrip = !!returnFare;

  return (
    <section className="border-0 shadow-lg mb-6 bg-gray-200 dark:bg-gray-800 rounded-lg p-4 md:p-6 space-y-4">
      {/* Header */}
      <div className="w-full">
        <h2 className="text-2xl font-bold text-gray-700 dark:text-white">
          Fare Summary
        </h2>
      </div>
      <div className="text-base mb-6 space-y-3">
        {/* Combined Flight Details for Round Trip */}
        {isRoundTrip ? (
          <CombinedFlightDetails queryParams={queryParams} />
        ) : (
          /* Flight Details for One Way */
          <FlightDetailsSection queryParams={queryParams} />
        )}

        {/* Price Details */}
        {isRoundTrip ? (
          <CombinedPriceDetails
            departureFare={departureFare}
            returnFare={returnFare!}
            queryParams={queryParams}
          />
        ) : (
          <FareSection fare={departureFare} showPreTotal={false} />
        )}

        {/* Offer & Discount */}
        <OfferDiscount />

        {/* Timer/Expired Banner */}
        <section className="rounded-xl p-4 mb-4 bg-gray-100 dark:bg-gray-700">
          <div className="flex items-start space-x-2">
            <CircleAlert
              className={`w-4 md:w-8 flex-shrink-0 mt-0.5 ${
                isExpired
                  ? "text-gray-400 dark:text-gray-500"
                  : "text-red-500 dark:text-red-400"
              }`}
            />
            {isExpired ? (
              <span className="text-sm text-gray-500 dark:text-gray-300 leading-relaxed font-medium">
                Booking session expired. Please start again.
              </span>
            ) : (
              <p className="text-sm text-red-500 dark:text-red-400 leading-relaxed font-medium">
                Please complete booking within{" "}
                <span className="font-bold">{formattedTime}</span> minutes
              </p>
            )}
          </div>
        </section>

        {/*  Payment Button */}
        <div className="relative z-10 mt-10 pt-4 border-t border-gray-300 dark:border-gray-700">
          {/* Display error if present */}
          {error && (
            <div className="text-red-500 text-sm mb-2 font-semibold text-center">
              {error}
            </div>
          )}
          {(() => {
            const isThirdPartyQuickHoldInvalid =
              bookingType === "THIRD_PARTY" && quickHold;
            const isButtonDisabled =
              (!quickHold && !submitBooking) ||
              isExpired ||
              isThirdPartyQuickHoldInvalid;
            return (
              <button
                type="button"
                className={`w-full ${
                  isButtonDisabled
                    ? "bg-gray-600 cursor-not-allowed"
                    : "bg-red-500 hover:bg-red-600"
                } text-white py-3 px-4 rounded-lg font-semibold transition-colors duration-300`}
                disabled={isButtonDisabled}
                onClick={() => {
                  // --- Booking Data Validation Integration ---
                  // const validateBookingData = (data: CreateInternalBookingDto): string | null => {
                  //   if (!data.ticketId) return 'Missing ticketId.';
                  //   if (!data.userId) return 'Missing userId.';
                  //   if (!data.agencyAgentId) return 'Missing agencyAgentId.';
                  //   if (!data.travelers || !data.travelers.length) return 'At least one traveler is required.';
                  //   if (!data.seats || !data.seats.length) return 'At least one seat is required.';
                  //   if (!data.type) return 'Missing booking type.';
                  //   if (!data.source) return 'Missing booking source.';
                  //   if (data.source === 'THIRD_PARTY' && data.seats.some(seat => !seat.seatNumber)) {
                  //     return 'Seat number is required for all seats in third-party bookings.';
                  //   }
                  //   for (let i = 0; i < data.travelers.length; i++) {
                  //     const t = data.travelers[i];
                  //     if (!/^\d{4}-\d{2}-\d{2}$/.test(t.dateOfBirth)) {
                  //       return `Traveler ${i + 1}: Date of Birth must be in YYYY-MM-DD format.`;
                  //     }
                  //     if (!/^\d{4}-\d{2}-\d{2}$/.test(t.expirationDate)) {
                  //       return `Traveler ${i + 1}: Passport Expiry must be in YYYY-MM-DD format.`;
                  //     }
                  //   }
                  //   return null;
                  // };
                  const validationError = validateBookingData(bookingData);
                  if (validationError) {
                    setError(validationError);
                    return;
                  } else {
                    setError("");
                  }
                  // setConfirmationOpen(true);
                  // setActionType(quickHold ? "quickHold" : "reserveSeat");
                  setModalState({
                    type: "confirmation",
                    actionType: quickHold ? "quickHold" : "reserveSeat",
                  });
                }}
              >
                {isExpired
                  ? "Session Expired"
                  : quickHold
                  ? bookingType === "INTERNAL"
                    ? "Quick Hold"
                    : "Select Payment Method"
                  : submitBooking
                  ? bookingType === "INTERNAL"
                    ? "Reserve Seat"
                    : "Confirm Booking"
                  : bookingType === "INTERNAL"
                  ? "Select Booking Action"
                  : "Select Payment Method"}
              </button>
            );
          })()}
        </div>
      </div>

      <div className="text-xs text-gray-600 dark:text-gray-300 text-center">
        By confirming, you agree to our{" "}
        <button
          className="text-red-500 hover:underline"
          // aria-controls="feedback-modal"
          // onClick={() => setFeedbackModalOpen(true)}
        >
          cancellation policy
          {/* <CancelModal
            feedbackModalOpen={feedbackModalOpen}
            setFeedbackModalOpen={setFeedbackModalOpen}
            ticket={departureTicket as unknown as FlightTicketRes}
          /> */}
        </button>
      </div>
      {/* Centralized Modal Rendering */}
      {modalState?.type === "confirmation" && (
        <PopupConfirmation
          onCancel={handleCloseModal}
          onConfirm={() => {}}
          actionType={modalState.actionType}
          bookingData={bookingData}
          onBookingResult={(result) => {
            if (onBookingResult) onBookingResult(result as any);
            setTimeout(() => {
              handleCloseModal();
            }, 2000);
          }}
          // onTimerExpired={() => router.push(currentUrl)}
        />
      )}
      {modalState?.type === "payment" && (
        <PopupPaymentForm
          initialVisibility={true}
          onClose={handleCloseModal}
          onSubmit={(_paymentData) => {
            // Show loading modal immediately after payment submit
            showLoadingModal();
          }}
        />
      )}
      {modalState?.type === "loading" && (
        <LoadingPopup message="Processing your payment…" />
      )}
    </section>
  );
}

function OfferDiscount() {
  return (
    <section className="text-lg text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 rounded-lg shadow p-4 h-fit">
      <h3 className="font-semibold mb-4">Offer & Discount</h3>
      <div className="flex gap-2">
        <label htmlFor="coupon_code" className="sr-only">
          Coupon Code
        </label>
        <input
          id="coupon_code"
          type="text"
          placeholder="Coupon code"
          className="w-full flex-grow border-none focus:border rounded-l-lg px-4 py-2 bg-gray-300 dark:bg-gray-600 dark:text-gray-100 flex-1 text-gray-600 p-2 rounded-md border-0 focus:ring-2 focus:ring-red-500 focus:outline-none placeholder:text-gray-600 dark:placeholder:text-gray-400"
          aria-label="Coupon code"
          title="Enter your coupon code here"
          autoComplete="off"
        />
        <button
          className="text-lg py-2 px-4 bg-red-500 text-white font-semibold rounded-md hover:bg-red-600 transition-colors duration-300"
          aria-label="Apply coupon code"
          title="Apply the entered coupon code"
        >
          Apply
        </button>
      </div>
    </section>
  );
}

/**
 * FareSection component displays the fare details for a specific flight segment.
 * It takes in departure and arrival airports, travel class, fare details, and a flag to show pre-total fare.
 * It renders the fare breakdown, tax, and pre-total fare if applicable.
 *
 * @param departure - The departure airport code.
 * @param arrival - The arrival airport code.
 * @param travelClass - The travel class (e.g., economy, business, first class).
 * @param fare - The fare details for the flight segment.
 * @param showPreTotal - A flag indicating whether to show the pre-total fare.
 *
 * @returns A React component displaying the fare details for a specific flight segment.
 */
/**
 * CombinedFlightDetails component displays both departure and return flight details in a single card.
 *
 * @param departureTicket - The departure flight ticket data.
 * @param returnTicket - The return flight ticket data.
 * @param travelClass - The travel class (e.g., economy, business, first class).
 * @param queryParams - The URL query parameters.
 *
 * @returns A React component displaying combined flight details.
 */
function CombinedFlightDetails({
  // departureTicket,
  // returnTicket,
  // travelClass,
  queryParams,
}: {
  // departureTicket: FlightTicketRes;
  // returnTicket: FlightTicketRes;
  // travelClass: string;
  queryParams: any;
}) {
  const {
    adults,
    children,
    infants,
    departureCode,
    arrivalCode,
    returnDepartureCode,
    returnArrivalCode,
    departureCarrier,
    departureFlightNumber,
    returnCarrier,
    returnFlightNumber,
    departureFlightDate,
    arrivalFlightDate,
    travelClass,
  } = queryParams;

  return (
    <section className="space-y-3 p-4 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-white mb-4">
      <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
        Flight Details
      </h3>

      {/* Departure Flight */}
      <div className="mb-4">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
          OUTBOUND
        </h4>
        <h3 className="font-bold flex flex-wrap flex-row justify-between text-sm">
          <p className="flex items-center">
            {/* {departureTicket.departure.airportCode} */}
            {departureCode}
            <FaArrowRight className="md:w-5 md:h-5 md:mx-4 text-gray-500" />{" "}
            {/* {departureTicket.arrival.airportCode}{" "} */}
            {arrivalCode}
          </p>
        </h3>
        <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
          {/* <p>{getFormatDate(departureTicket.departureTime)}</p> */}
          <p>{getFormatDate(departureFlightDate)}</p>
          <p> • </p>
          <p>
            {/* {departureTicket.segments[0].carrier} ({" "} */}
            {/* {departureTicket.segments[0].flightNumber} ) */}
            {departureCarrier} ({departureFlightNumber})
          </p>
        </span>
      </div>

      {/* Return Flight */}
      <div className="mt-2">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-blue-500/20 px-2 py-0.5 rounded w-fit">
          RETURN
        </h4>
        <h3 className="font-bold flex flex-wrap flex-row justify-between text-sm">
          <p className="flex items-center">
            {/* {returnTicket.departure.airportCode} */}
            {returnDepartureCode ?? arrivalCode}
            <FaArrowRight className="md:w-5 md:h-5 md:mx-4 text-gray-500" />{" "}
            {/* {returnTicket.arrival.airportCode}{" "} */}
            {returnArrivalCode ?? departureCode}
          </p>
        </h3>
        <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
          <p>{getFormatDate(arrivalFlightDate)}</p>
          <p> • </p>
          <p>
            {/* {returnTicket.segments[0].carrier} ({" "}
            {returnTicket.segments[0].flightNumber} ) */}
            {returnCarrier} ({returnFlightNumber})
          </p>
        </span>
      </div>

      {/* Passenger Information */}
      <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300 mt-3 pt-2 border-t border-gray-300 dark:border-gray-600">
        {adults > 0 && <p>{adults} Adults</p>}
        {children > 0 && (
          <>
            <p> • </p>
            <p>{children} Children</p>
          </>
        )}
        {infants > 0 && (
          <>
            <p> • </p>
            <p>{infants} Infants</p>
          </>
        )}

        <p> • </p>
        <p className="capitalize">{travelClass}</p>
      </span>
    </section>
  );
}

/**
 * FlightDetailsSection component displays flight details for a single flight.
 *
 * @param ticket - The flight ticket data.
 * @param travelClass - The travel class (e.g., economy, business, first class).
 * @param queryParams - The URL query parameters.
 *
 * @returns A React component displaying flight details for a single flight.
 */
function FlightDetailsSection({
  // ticket,
  // travelClass,
  queryParams,
}: {
  // ticket: FlightTicketRes;
  // travelClass: string;
  queryParams: any;
}) {
  const {
    adults,
    children,
    infants,
    departureCode,
    arrivalCode,
    departureCarrier,
    arrivalCarrier,
    departureFlightNumber,
    arrivalFlightNumber,
    departureFlightDate,
    arrivalFlightDate,
    stops,
    travelClass,
  } = queryParams;

  return (
    <section className="space-y-3 p-4 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-white mb-4">
      <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
        Flight Details
      </h3>
      <h3 className="font-bold flex flex-wrap flex-row justify-between text-sm">
        <p className="flex items-center">
          {/* {ticket.departure.airportCode} */}
          {departureCode}
          <FaArrowRight className="md:w-5 md:h-5 md:mx-4 text-gray-500" />{" "}
          {/* {ticket.arrival.airportCode}{" "} */}
          {arrivalCode}
        </p>
      </h3>
      <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
        {/* <p>{getFormatDate(ticket.departureTime)}</p> */}
        <p>{getFormatDate(departureFlightDate)}</p>
        <p> • </p>
        <p>
          {/* {ticket.segments[0].carrier} ( {ticket.segments[0].flightNumber} ) */}
          {departureCarrier}({departureFlightNumber})
        </p>
      </span>
      <span className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
        {adults > 0 && <p>{adults} Adults</p>}
        {children > 0 && <p> • {children} Children</p>}
        {infants > 0 && <p> • {infants} Infants</p>}
        <p> • </p> <p className="capitalize">{travelClass}</p>
      </span>
    </section>
  );
}

/**
 * CombinedPriceDetails component displays price details for both departure and return flights in a single card.
 *
 * @param departureFare - The fare details for the departure flight.
 * @param returnFare - The fare details for the return flight.
 *
 * @returns A React component displaying combined price details.
 */
function CombinedPriceDetails({
  departureFare,
  returnFare,
  queryParams,
}: {
  departureFare: any;
  returnFare: any;
  queryParams: any;
}) {
  const totalFare = departureFare.total + returnFare.total;
  const currency = departureFare.currency;

  return (
    <section className="space-y-3 p-4 rounded-lg bg-gray-100 dark:bg-gray-700 mb-4">
      <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
        Price Details
      </h3>

      {/* Departure Fare */}
      <div className="mb-4">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
          OUTBOUND
        </h4>
        {/* {departureFare.fareBreakdown
          .filter((item: any) => item.count > 0)
          .map((item: any) => (
            <TravelerFareItem
              key={`departure-${item.label}`}
              label={item.label}
              value={formattedPrice(item.value, departureFare.currency)}
              perPersonValue={formattedPrice(
                item.perPersonValue,
                departureFare.currency
              )}
              count={item.count}
            />
          ))} */}
        {/* Outbound Base Fare */}
        <div className="block mb-3">
          {/* <span className="dark:text-gray-300 text-gray-700">Base Fare</span>
          <span className="dark:text-white text-gray-700"> */}
          {/* {departureFare.fareBreakdown
              ? `${departureFare.fareBreakdown
                  .filter((item: any) => item.count > 0)
                  .reduce((total: number, item: any) => total + item.value, 0)
                  .toFixed(2)} ${departureFare.currency}`
              : "N/A"} */}
          {departureFare.fareBreakdown
            .filter((item: any) => item.count > 0)
            .map((item: any) => (
              <FareBreakdownRow
                key={`departure-${item.label}`}
                item={item}
                currency={departureFare.currency}
              />
            ))}
          {/* </span> */}
        </div>
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full mb-3"></div>
        <FareItem
          label={`Taxes (${departureFare.taxRate.toFixed(2)}%)`}
          value={`${formattedPrice(departureFare.tax, departureFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <FareItem
          label="Transaction Fee"
          value={`${formattedPrice(0.0, departureFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full my-3"></div>
        <FareItem
          label="Subtotal"
          value={`${formattedPrice(
            departureFare.total,
            departureFare.currency
          )}`}
          className="font-semibold text-gray-700 dark:text-gray-300"
        />
      </div>

      {/* Return Fare */}
      <div className="mb-4 border-t border-gray-300 dark:border-gray-500 pt-3">
        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-blue-500/20 px-2 py-0.5 rounded w-fit">
          RETURN
        </h4>
        {/* {returnFare.fareBreakdown
          .filter((item: any) => item.count > 0)
          .map((item: any) => (
            <TravelerFareItem
              key={`return-${item.label}`}
              label={item.label}
              value={formattedPrice(item.value, returnFare.currency)}
              perPersonValue={formattedPrice(
                item.perPersonValue,
                returnFare.currency
              )}
              count={item.count}
            />
          ))} */}
        {/* Return Base Fare */}
        <div className="block mb-3">
          {/* <span className="dark:text-gray-300 text-gray-700">Base Fare</span>
          <span className="dark:text-white text-gray-700">
            {returnFare.fareBreakdown
              ? `${returnFare.fareBreakdown
                  .filter((item: any) => item.count > 0)
                  .reduce((total: number, item: any) => total + item.value, 0)
                  .toFixed(2)} ${returnFare.currency}`
              : "N/A"}
          </span> */}
          {returnFare.fareBreakdown
            .filter((item: any) => item.count > 0)
            .map((item: any) => (
              <FareBreakdownRow
                key={`return-${item.label}`}
                item={item}
                currency={returnFare.currency}
              />
            ))}
        </div>
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full my-3"></div>
        <FareItem
          label={`Taxes (${returnFare.taxRate.toFixed(2)}%)`}
          value={`${formattedPrice(returnFare.tax, returnFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <FareItem
          label="Transaction Fee"
          value={`${formattedPrice(0.0, returnFare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full my-3"></div>
        <FareItem
          label="Subtotal"
          value={`${formattedPrice(returnFare.total, returnFare.currency)}`}
          className="font-semibold text-gray-700 dark:text-gray-300"
        />
      </div>

      {/* Total Fare */}
      <div className="border-t border-gray-300 dark:border-gray-500 pt-3">
        <FareItem
          label="Total"
          value={`${formattedPrice(totalFare, currency)}`}
          className="font-semibold text-lg leading-8 tracking-wide text-gray-700 dark:text-white"
        />
      </div>
    </section>
  );
}

function FareSection({
  fare,
  showPreTotal,
}: {
  fare: any;
  showPreTotal: boolean;
}) {
  const totalFare = fare.total;

  return (
    <div className="space-y-2">
      {/* Price Details */}

      <section className="space-y-3 p-4 rounded-lg bg-gray-100 dark:bg-gray-700 mb-4">
        <h3 className="font-semibold text-lg mb-3 text-gray-700 dark:text-white border-b border-gray-300 dark:border-gray-500 pb-2">
          Price Details
        </h3>

        <h4 className="font-medium text-md dark:text-white text-gray-700 mb-2 pb-1 flex items-center text-xs bg-red-500/20 px-2 py-0.5 rounded w-fit">
          OUTBOUND
        </h4>

        {/* {fare.fareBreakdown
          .filter((item: any) => item.count > 0)
          .map((item: any) => (
            <TravelerFareItem
              key={item.label}
              label={item.label}
              value={formattedPrice(item.value, fare.currency)}
              perPersonValue={formattedPrice(
                item.perPersonValue,
                fare.currency
              )}
              count={item.count}
            />
          ))} */}
        {/* Outbound Base Fare */}
        <div className="block mb-3">
          {/* <span className="dark:text-gray-300 text-gray-700">Base Fare</span>
          <span className="dark:text-white text-gray-700"> */}
          {/* {fare.fareBreakdown
              ? `${fare.fareBreakdown
                  .filter((item: any) => item.count > 0)
                  .reduce((total: number, item: any) => total + item.value, 0)
                  .toFixed(2)} ${fare.currency}`
              : "N/A"} */}
          {fare.fareBreakdown
            .filter((item: any) => item.count > 0)
            .map((item: any) => (
              <FareBreakdownRow
                key={`departure-${item.label}`}
                item={item}
                currency={fare.currency}
              />
            ))}
          {/* </span> */}
        </div>
        <div className="h-px dark:bg-gray-500 bg-gray-300 w-full mb-3"></div>
        <FareItem
          label={`Taxes ( ${fare.taxRate.toFixed(2)} % )`}
          value={`${formattedPrice(fare.tax, fare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        <FareItem
          label="Transaction Fee"
          value={`${formattedPrice(0.0, fare.currency)}`}
          className="text-gray-700 dark:text-gray-300"
        />
        {showPreTotal && (
          <FareItem
            label="PreTotal"
            value={`${formattedPrice(fare.total, fare.currency)}`}
            className="font-extrabold text-gray-700 dark:text-gray-300"
          />
        )}

        {/* Total Fare */}
        <div className="border-t border-gray-300 dark:border-gray-500 pt-3">
          <FareItem
            label="Total"
            value={`${formattedPrice(totalFare, fare.currency)}`}
            className="font-semibold text-lg leading-8 tracking-wide text-gray-700 dark:text-white"
          />
        </div>
      </section>
    </div>
  );
}

/**
 * TravelerFareItem component displays a single fare item with a label, value, and per person value.
 * It also includes a collapsible section to show the count and per person value.
 *
 * @param label - The label for the fare item.
 * @param value - The total value for the fare item.
 * @param perPersonValue - The per person value for the fare item.
 * @param count - The count of the fare item.
 *
 * @returns A React component displaying a single fare item with collapsible details.
 */
function TravelerFareItem({
  label,
  value,
  perPersonValue,
  count,
}: {
  label: string;
  value: string;
  perPersonValue: string;
  count: number;
}) {
  const [showDetails, setShowDetails] = useState(false);
  return (
    <div>
      <div
        className="flex justify-between items-center font-extrabold text-gray-700 dark:text-white"
        onClick={() => setShowDetails(!showDetails)}
      >
        <span className="font-medium flex items-center justify-between">
          <span className="font-medium w-full">{label}</span>
          <ChevronDown
            size={16}
            className={`transform transition-transform duration-300 ${
              showDetails ? "rotate-180" : "rotate-0"
            }`}
          />
        </span>
        <span className="font-medium">{value}</span>
      </div>
      {showDetails && (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          {count} X {perPersonValue}
        </div>
      )}
    </div>
  );
}

/**
 * FareItem component displays a single fare item with a label and value.
 *
 * @param label - The label for the fare item.
 * @param value - The value for the fare item.
 * @param info - Indicates whether to show an info icon (optional, default is false).
 * @param className - Additional CSS classes for the fare item (optional).
 *
 * @returns A React component displaying a single fare item.
 */
function FareItem({
  label,
  value,
  info = false,
  className = "",
}: {
  label: string;
  value: string;
  info?: boolean;
  className?: string;
}) {
  return (
    <div className={`flex justify-between items-center`}>
      <div className="flex items-center">
        <span className={`${className}`}>{label}</span>
        {info && (
          <svg
            className="w-4 h-4 ml-1 text-gray-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </div>
      <span className={`font-medium ${className}`}>{value}</span>
    </div>
  );
}

/**
 * calculateFare function calculates the fare for a given flight ticket and query parameters.
 *
 * @param ticket - The flight ticket data.
 * @param queryParams - The query parameters for the flight booking (optional).
 *
 * @returns An object containing the calculated fare details.
 */
export const calculateFare = (
  // ticket: FlightTicketRes,
  queryParams?: any,
  isReturnFare: boolean = false
) => {
  const {
    adults = 0,
    children = 0,
    infants = 0,
    travelClass = "economy",
  } = queryParams;

  // Define the extended price data type to include return prices
  type ExtendedPriceData = {
    adult: number;
    child: number;
    infant: number;
    tax: number;
    currency: string;
    returnAdult: number;
    returnChild: number;
    returnInfant: number;
    returnTax: number;
    returnCurrency: string;
  };

  // Parse price from URL parameter
  let priceData: ExtendedPriceData = {
    adult: 0, // default prices
    child: 0,
    infant: 0,
    tax: 0,
    currency: "JOD",
    returnAdult: 0,
    returnChild: 0,
    returnInfant: 0,
    returnTax: 0,
    returnCurrency: "",
  };

  try {
    if (queryParams.price) {
      let rawPriceData: any;
      if (typeof queryParams.price === "string") {
        rawPriceData = JSON.parse(queryParams.price);
      } else {
        rawPriceData = queryParams.price;
      }

      // Map the price data to the expected format
      priceData = {
        adult: Number(rawPriceData.departureAdultPrice) || 0,
        child: Number(rawPriceData.departureChildPrice) || 0,
        infant: Number(rawPriceData.departureInfantPrice) || 0,
        tax: Number(rawPriceData.departureTaxPrice) || 0,
        currency: rawPriceData.departureCurrency || "JOD",
        returnAdult: Number(rawPriceData.returnAdultPrice) || 0,
        returnChild: Number(rawPriceData.returnChildPrice) || 0,
        returnInfant: Number(rawPriceData.returnInfantPrice) || 0,
        returnTax: Number(rawPriceData.returnTaxPrice) || 0,
        returnCurrency: rawPriceData.returnCurrency || "",
      };
    }
  } catch (error) {
    console.error("Error parsing price data:", error);
  }

  // Base prices from price parameter - use return or departure prices based on isReturnFare flag
  const adultPrice = isReturnFare
    ? typeof priceData.returnAdult === "number"
      ? parseFloat(priceData.returnAdult.toFixed(2))
      : 0
    : typeof priceData.adult === "number"
    ? parseFloat(priceData.adult.toFixed(2))
    : 0;

  const childPrice = isReturnFare
    ? typeof priceData.returnChild === "number"
      ? parseFloat(priceData.returnChild.toFixed(2))
      : 0
    : typeof priceData.child === "number"
    ? parseFloat(priceData.child.toFixed(2))
    : 0;

  const infantPrice = isReturnFare
    ? typeof priceData.returnInfant === "number"
      ? parseFloat(priceData.returnInfant.toFixed(2))
      : 0
    : typeof priceData.infant === "number"
    ? parseFloat(priceData.infant.toFixed(2))
    : 0;

  const taxRate = isReturnFare
    ? typeof priceData.returnTax === "number"
      ? parseFloat(priceData.returnTax.toFixed(2))
      : 0
    : typeof priceData.tax === "number"
    ? parseFloat(priceData.tax.toFixed(2))
    : 0;

  const currencyData = isReturnFare
    ? priceData.returnCurrency || "JOD"
    : priceData.currency || "JOD";

  // Calculate subtotals
  const adultsPrice = adults * adultPrice;
  const childrenPrice = children * childPrice;
  const infantsPrice = infants * infantPrice;
  const subtotal = adultsPrice + childrenPrice + infantsPrice;

  // Calculate tax and total
  const taxData = subtotal * (taxRate / 100);
  const total = subtotal + taxData;

  // Create fare breakdown
  const fareBreakdown = [
    {
      label: `${adults} Adult`,
      value: adultsPrice,
      perPersonValue: adultPrice,
      count: adults,
      className: "text-gray-600 dark:text-gray-100",
    },
    {
      label: `${children} Child`,
      value: childrenPrice,
      perPersonValue: childPrice,
      count: children,
      className: "text-gray-600 dark:text-gray-100",
    },
    {
      label: `${infants} Infant`,
      value: infantsPrice,
      perPersonValue: infantPrice,
      count: infants,
      className: "text-gray-600 dark:text-gray-100",
    },
  ].filter((item) => item.count > 0); // Only include items with count > 0

  return {
    subtotal,
    tax: taxData,
    total,
    currency: currencyData,
    adultsPrice,
    childrenPrice,
    infantsPrice,
    taxRate,
    fareBreakdown,
  };
};

/**
 * useUrlParams function retrieves the query parameters from the URL.
 *
 * @returns An object containing the retrieved query parameters.
 */
const useUrlParams = () => {
  const searchParams = useSearchParams();

  const itinerary = searchParams.get("itinerary");
  const travelClass = searchParams.get("travelClass");
  const adults = parseInt(searchParams.get("adults") || "0");
  const children = parseInt(searchParams.get("children") || "0");
  const infants = parseInt(searchParams.get("infants") || "0");

  let price;
  try {
    const priceParam = searchParams.get("price");
    if (priceParam) {
      price = JSON.parse(priceParam);
    }
  } catch (error) {
    console.error("Error parsing price from URL:", error);
    price = null;
  }

  let agentOperations;
  try {
    const agentOpsParam = searchParams.get("agentOperations");
    if (agentOpsParam) {
      agentOperations = JSON.parse(agentOpsParam);
    }
  } catch (error) {
    console.error("Error parsing agentOperations from URL:", error);
    agentOperations = null;
  }

  const departureCode = searchParams.get("departureCode");
  const departureCity = searchParams.get("departureCity");
  const departureCountry = searchParams.get("departureCountry");
  const departureAirport = searchParams.get("departureAirport");
  const arrivalCode = searchParams.get("arrivalCode");
  const arrivalCity = searchParams.get("arrivalCity");
  const arrivalCountry = searchParams.get("arrivalCountry");
  const arrivalAirport = searchParams.get("arrivalAirport");
  const departureCarrier = searchParams.get("departureCarrier");
  const departureFlightNumber = searchParams.get("departureFlightNumber");
  const returnCarrier = searchParams.get("returnCarrier");
  const returnFlightNumber = searchParams.get("returnFlightNumber");
  const flightNumber = searchParams.get("flightNumber");
  const departureFlightDate = searchParams.get("flightDate");
  const arrivalFlightDate = searchParams.get("returnDate");
  const stops = searchParams.get("stops");

  const bookRef = searchParams.get("bookRef");
  const tktId = searchParams.get("tktId");
  const source = searchParams.get("source");
  const agentName = searchParams.get("agentName");
  const agentRole = searchParams.get("agentRole");
  const agentAgency = searchParams.get("agentAgency");
  const fillerName = searchParams.get("fillerName");
  const fillerEmail = searchParams.get("fillerEmail");
  const fillerRole = searchParams.get("fillerRole");
  const fillerAgency = searchParams.get("fillerAgency");

  const travelers = [];
  let travelerIndex = 0;
  while (true) {
    const title = searchParams.get(`traveler_${travelerIndex}_title`);
    if (!title) break; // Stop if the first field (title) is missing for this index

    travelers.push({
      title,
      gender: searchParams.get(`traveler_${travelerIndex}_gender`),
      firstName: searchParams.get(`traveler_${travelerIndex}_firstName`),
      lastName: searchParams.get(`traveler_${travelerIndex}_lastName`),
      email: searchParams.get(`traveler_${travelerIndex}_email`),
      phoneNumber: searchParams.get(`traveler_${travelerIndex}_phoneNumber`),
      dateOfBirth: searchParams.get(`traveler_${travelerIndex}_dateOfBirth`),
      nationality: searchParams.get(`traveler_${travelerIndex}_nationality`),
      passportNumber: searchParams.get(
        `traveler_${travelerIndex}_passportNumber`
      ),
      issuingCountry: searchParams.get(
        `traveler_${travelerIndex}_issuingCountry`
      ),
      passportExpiry: searchParams.get(
        `traveler_${travelerIndex}_passportExpiry`
      ),
    });
    travelerIndex++;
  }

  return {
    itinerary,
    travelClass,
    adults,
    children,
    infants,
    price,
    agentOperations,
    departureCode,
    departureCity,
    departureCountry,
    departureAirport,
    arrivalCode,
    arrivalCity,
    arrivalCountry,
    arrivalAirport,
    departureCarrier,
    departureFlightNumber,
    returnCarrier,
    returnFlightNumber,
    flightNumber,
    departureFlightDate,
    arrivalFlightDate,
    stops,
    bookRef,
    tktId,
    source,
    agentName,
    agentRole,
    agentAgency,
    fillerName,
    fillerEmail,
    fillerRole,
    fillerAgency,
    travelers,
  };
};
