interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validatePassword = (
  password: string
): PasswordValidationResult => {
  const errors: string[] = [];

  // Minimum length check
  if (password.length < 8) {
    errors.push("Please make sure your password is at least 8 characters long");
  }

  // Check for uppercase letters
  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter4");
  }

  // Check for lowercase letters
  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter");
  }

  // Check for numbers
  if (!/\d/.test(password)) {
    errors.push("Please include at least one number in your password");
  }

  // Check for special characters
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push(
      "Please include at least one special character in your password (e.g., !, @, #, $)"
    );
  }

  // Check for common patterns
  const commonPatterns = [
    "password",
    "123456",
    "qwerty",
    "admin",
    "letmein",
    "welcome",
  ];
  if (
    commonPatterns.some((pattern) => password.toLowerCase().includes(pattern))
  ) {
    errors.push("Password contains common patterns that are easily guessable");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
