import {
  CreateInternalBookingDto,
  Booking,
  BookingHoldType,
  BookingSource,
  ThirdPartyBookingResponse,
} from "@/utils/types/booking.types";
import bookingUrl from "../endpoints/bookingEndpoint";
import axios, { AxiosRequestConfig, AxiosError } from "axios";

/**
 * bookingEndpoint.ts
 * Provides functions for booking-related backend API calls.
 */

/**
 * Creates a new internal booking.
 * @param data - The booking data to create
 * @returns The created booking object
 * @throws Error if the request fails or the response is invalid
 */
// Create an internal booking (expects source and type in data)
interface CreateInternalBookingResponse {
  message: string;
  booking: {
    booking: Booking;
    eTicket: any;
    receipt: any;
  };
}

export async function createInternalBooking(
  data: CreateInternalBookingDto
): Promise<CreateInternalBookingResponse> {
  try {
    const response = await axios.post(bookingUrl.createInternalBooking, data, {
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
    });

    console.log("createInternalBooking response:", response.data);
    const result = response.data;

    // Check if result.booking.booking is an array (round trip) or single booking
    const bookingData = result?.booking?.booking;
    let isValidResponse = false;

    if (Array.isArray(bookingData)) {
      // For round trips, check all bookings in the array have valid IDs
      isValidResponse =
        bookingData.length > 0 && bookingData.every((booking) => booking?.id);
    } else {
      // For one-way trips, check single booking has an ID
      isValidResponse = !!bookingData?.id;
    }

    if (!result || !isValidResponse) {
      console.error("Invalid booking response:", result);
      throw new Error("Invalid booking data received");
    }

    // Return the nested booking structure
    return {
      message: result.message || "Booking created successfully",
      booking: result.booking,
    };
  } catch (error: any) {
    let message = "Failed to create internal booking";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Identifies the booking type for a given ticket.
 * @param ticketId - The ticket ID to check
 * @returns The booking type: 'internal' or 'third_party'
 * @throws Error if the request fails or the response is invalid
 */
export async function getBookingType(ticketId: string): Promise<BookingSource> {
  try {
    const response = await axios.post(
      bookingUrl.identifyBookingType,
      { ticketId },
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );
    if (
      !response.data ||
      (response.data.bookingType !== "INTERNAL" &&
        response.data.bookingType !== "THIRD_PARTY")
    ) {
      throw new Error("Invalid booking type received");
    }
    return response.data.bookingType;
  } catch (error: any) {
    let message = "Failed to check booking type";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Retrieves a booking by its ID.
 * @param id - The booking ID to retrieve
 * @returns The booking object or null if not found
 * @throws Error if the request fails or the response is invalid
 */
export async function getBookingById(id: string): Promise<Booking> {
  try {
    const response = await axios.get(bookingUrl.getBookingById(id), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });
    const responseData = response.data;

    let bookingData;
    if (responseData.success && responseData.data) {
      // Response is wrapped in a success/data structure
      bookingData = responseData.data;
    } else if (responseData.id) {
      // Response is the direct booking object
      bookingData = responseData;
    } else {
      console.error("Unexpected API response structure:", responseData);
      throw new Error("Invalid booking data format received");
    }

    // Validate that data has required fields for a Booking
    if (
      !bookingData ||
      !bookingData.id ||
      !bookingData.ticketId ||
      !bookingData.requestId
    ) {
      throw new Error("Invalid booking data received");
    }
    console.log("bookingData", bookingData);
    return bookingData;
  } catch (error: any) {
    let message = "Failed to fetch booking by id";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

// 4. Get bookings by user
/**
 * Retrieves all bookings for a user, optionally filtered by status.
 * @param userId - The user's unique identifier
 * @param status - Optional status filter
 * @returns An array of bookings
 */
export async function getBookingsByUser(userId: string): Promise<Booking[]> {
  try {
    const response = await axios.get(bookingUrl.getBookingsByUser(userId), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });
    const data = response.data;
    // Validate that data is an array of bookings
    if (!data || !Array.isArray(data)) {
      throw new Error("Invalid bookings data received");
    }
    return data;
  } catch (error: any) {
    let message = "Failed to fetch bookings by user";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Fetches unique agent names from the database
 * @returns Array of unique agent names
 */
export async function getBookingAgentNames(): Promise<string[]> {
  try {
    const response = await axios.get(bookingUrl.getBookingAgentNames(), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });

    if (response.data && Array.isArray(response.data)) {
      return ["All", ...response.data];
    }

    return ["All"];
  } catch (error) {
    console.error("Error fetching agent names:", error);
    return ["All"];
  }
}

/**
 * Fetches unique agent names from the database
 * @returns Array of unique agent names
 */
export async function getSalesAgentNames(): Promise<string[]> {
  try {
    const response = await axios.get(bookingUrl.getSalesAgentNames(), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });

    if (response.data && Array.isArray(response.data)) {
      return ["All", ...response.data];
    }

    return ["All"];
  } catch (error) {
    console.error("Error fetching agent names:", error);
    return ["All"];
  }
}

/**
 * Fetches unique agent names from the database
 * @returns Array of unique agent names
 */
export async function getGlobalAgentNames(): Promise<string[]> {
  try {
    const response = await axios.get(bookingUrl.getGlobalAgentNames(), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });

    if (response.data && Array.isArray(response.data)) {
      return ["All", ...response.data];
    }

    return ["All"];
  } catch (error) {
    console.error("Error fetching agent names:", error);
    return ["All"];
  }
}

/**
 * Fetches unique carrier names from the database
 * @returns Array of unique carrier names
 */
export async function getCarrierNames(): Promise<string[]> {
  try {
    const response = await axios.get(bookingUrl.getCarrierNames(), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });

    const names: string[] = response.data?.carrierNames ?? [];
    return ["All", ...names];
  } catch (error) {
    console.error("Error fetching carrier names:", error);
    return ["All"];
  }
}

/**
 * Fetches unique agency names from the database
 * @returns Array of unique agency names
 */
export async function getAgencyNames(): Promise<string[]> {
  try {
    const response = await axios.get(bookingUrl.getAgencyNames(), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });

    const names: string[] = response.data?.agencyNames ?? [];
    return ["All", ...names];
  } catch (error) {
    console.error("Error fetching agency names:", error);
    return ["All"];
  }
}

// 5. Get bookings by status
/**
 * Retrieves all bookings (admin, paginated).
 * @param page Page number (default: 1)
 * @param pageSize Number of bookings per page (default: 20)
 * @param cursor Cursor for pagination (optional)
 * @param userId User ID (optional)
 * @param accountType Account type (optional)
 * @param agencyId Agency ID (optional)
 * @param bookingType Booking type (optional)
 * @param status Booking status (optional)
 * @param source Booking source (optional)
 * @param startDate Start date (optional)
 * @param endDate End date (optional)
 * @param search Search query (optional)
 * @returns Array of bookings
 */
export async function getAllBookings(
  cursor?: string | null,
  pageSize = cursor ? 10 : 20, // Use 20 for initial load, 10 for subsequent loads
  userId?: string,
  accountType?: string,
  agencyId?: string,
  bookingType?: "buyer" | "seller", // New parameter to filter by buyer or seller agency
  status?: string | string[],
  source?: string | string[],
  agentName?: string | string[],
  travelDateRange?: string,
  search?: string
): Promise<any> {
  try {
    // Construct URL with proper cursor-based pagination
    let url = bookingUrl.getAllBookings(1, pageSize);

    // Add cursor parameter if provided
    if (cursor) {
      url = `${url}&cursor=${cursor}`;
    }

    // Add userId and accountType parameters if provided
    if (userId) {
      url = `${url}&userId=${userId}`;
    }

    if (accountType) {
      url = `${url}&accountType=${accountType}`;
    }

    // Add agencyId parameter if provided to filter by agency
    if (agencyId) {
      url = `${url}&agencyId=${agencyId}`;
    }

    // Add bookingType parameter to filter by buyer or seller agency
    if (bookingType) {
      url = `${url}&bookingType=${bookingType}`;
    }

    // Add status parameter if provided
    if (status) {
      if (Array.isArray(status)) {
        status = status.join(",");
      }
      url = `${url}&status=${status}`;
    }

    // Add source parameter if provided
    if (source) {
      if (Array.isArray(source)) {
        source = source.join(",");
      }
      url = `${url}&source=${source}`;
    }

    // Add agentName parameter if provided
    if (agentName) {
      if (Array.isArray(agentName)) {
        agentName = agentName.join(",");
      }
      url = `${url}&agentName=${agentName}`;
    }

    // Add startDate parameter if provided
    if (travelDateRange) {
      url = `${url}&travelDateRange=${travelDateRange}`;
    }

    // Add search parameter if provided
    if (search) {
      url = `${url}&search=${search}`;
    }

    const response = await axios.get(url, { withCredentials: true });

    if (!response.data || !response.data.results) {
      throw new Error("Invalid bookings data received");
    }
    return response.data;
  } catch (error: any) {
    let message = "Failed to fetch all bookings";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Approves a booking (admin).
 * @param bookingId Booking ID
 * @returns Updated booking
 */
export async function approveBooking(bookingId: string): Promise<Booking> {
  try {
    const response = await axios.post(
      bookingUrl.approveBooking(bookingId),
      {},
      { withCredentials: true }
    );
    // Handle both response formats for backward compatibility
    return response.data.booking || response.data;
  } catch (error: any) {
    let message = "Failed to approve booking";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Rejects a booking (admin).
 * @param bookingId Booking ID
 * @returns Updated booking
 */
export async function rejectBooking(bookingId: string): Promise<Booking> {
  try {
    const response = await axios.post(
      bookingUrl.rejectBooking(bookingId),
      {},
      { withCredentials: true }
    );

    // Handle different response structures
    if (response.data && response.data.booking) {
      return response.data.booking;
    } else if (response.data) {
      // If response.data is the booking directly
      return response.data;
    }

    throw new Error("Invalid response format from server");
  } catch (error: any) {
    let message = "Failed to reject booking";
    if (error?.response?.data?.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    } else if (typeof error === "string") {
      message = error;
    }

    // Create a more detailed error message for debugging
    const errorDetails = {
      message,
      status: error?.response?.status,
      statusText: error?.response?.statusText,
      data: error?.response?.data,
    };

    console.error("Error in rejectBooking:", errorDetails);
    throw new Error(message);
  }
}

/**
 * Times out a booking (system/admin action).
 * @param bookingId Booking ID
 * @returns Updated booking
 */
export async function timeoutBooking(bookingId: string): Promise<Booking> {
  try {
    const response = await axios.post(
      bookingUrl.timeoutBooking(bookingId),
      {},
      { withCredentials: true }
    );
    return response.data.booking;
  } catch (error: any) {
    let message = "Failed to timeout booking";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Releases a seat for a booking (agent/system).
 * @param bookingId Booking ID
 * @returns Updated booking
 */
export async function releaseSeat(bookingId: string): Promise<Booking> {
  try {
    const response = await axios.post(
      bookingUrl.releaseSeat(bookingId),
      {},
      { withCredentials: true }
    );
    return response.data.booking;
  } catch (error: any) {
    let message = "Failed to release seat";
    if (error.response && error.response.data) {
      message =
        error.response.data.error || error.response.data.message || message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Completes payment for a booking.
 * @param bookingId Booking ID
 * @param paymentDetails Payment details
 * @returns Updated booking
 */
export async function completePayment(
  bookingId: string,
  paymentDetails: any
): Promise<Booking> {
  try {
    const response = await axios.post(
      bookingUrl.completePayment(bookingId),
      paymentDetails,
      { withCredentials: true }
    );
    return response.data.booking;
  } catch (error: any) {
    let message = "Failed to complete payment";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Retrieves all bookings for a user, optionally filtered by status.
 * @param userId - The user's unique identifier
 * @param status - Optional status filter
 * @returns An array of bookings
 */
export async function getBookingsByStatus(status: string): Promise<Booking[]> {
  try {
    const response = await axios.get(bookingUrl.getBookingsByStatus(status), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });
    const data = response.data;
    // Validate that data is an array of bookings
    if (!data || !Array.isArray(data)) {
      throw new Error("Invalid bookings data received");
    }
    return data;
  } catch (error: any) {
    let message = "Failed to fetch bookings by status";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Gets the status of a specific booking by ID.
 * @param bookingId - The booking ID to get status for
 * @returns The booking status object
 */
export async function getBookingStatus(bookingId: string): Promise<any> {
  try {
    const response = await axios.get(bookingUrl.getBookingStatus(bookingId), {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true, // Ensure credentials are included
    });

    if (!response.data || !response.data.success) {
      throw new Error("Invalid booking status data received");
    }

    return response.data.data;
  } catch (error: any) {
    let message = "Failed to fetch booking status";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

// 6. Cancel booking
/**
 * Cancels a booking by its ID.
 * @param bookingId - The booking ID to cancel
 * @returns The cancelled booking object or null if not found
 * @throws Error if the request fails or the response is invalid
 */
export interface CancelBookingResponse {
  success: boolean;
  message?: string;
  data?: {
    bookingId: string;
    status: string;
    requestId: string;
    cancellationReason: string;
    meta: Record<string, any>;
    updatedAt: string;
  };
}

export async function cancelBooking(
  bookingId: string
): Promise<CancelBookingResponse> {
  try {
    console.log("Sending cancel request for booking:", bookingId);
    const response = await axios.post(
      bookingUrl.cancelBooking(bookingId),
      { bookingId },
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );

    console.log("Raw cancel booking response:", response);

    const responseData = response.data;
    console.log("Response data:", responseData);

    // If the API returns success: true with data
    if (responseData?.success === true && responseData.data) {
      console.log(
        "Successfully processed booking cancellation for:",
        responseData.data.bookingId
      );
      return {
        success: true,
        message: "Booking cancelled successfully",
        data: responseData.data,
      };
    }

    // If the API returns directly with booking data (without success wrapper)
    if (responseData?.bookingId) {
      console.log(
        "Successfully processed booking cancellation for:",
        responseData.bookingId
      );
      return {
        success: true,
        message: "Booking cancelled successfully",
        data: responseData,
      };
    }

    // If we get here, the response format is unexpected
    console.error("Unexpected response format:", responseData);
    throw new Error("Unexpected response format from server");
  } catch (error: any) {
    let message = "Failed to cancel booking";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

// 7. Confirm booking
/**
 * Confirms a booking by its ID.
 * @param bookingId - The booking ID to confirm
 * @returns The confirmed booking object or null if not found
 * @throws Error if the request fails or the response is invalid
 */
export async function confirmBooking(bookingId: string): Promise<Booking> {
  try {
    const res = await axios.post(bookingUrl.confirmBooking(bookingId), {
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ bookingId }),
    });
    return res.data;
  } catch (error: any) {
    let message = "Failed to confirm booking";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Creates a new third-party booking.
 * @param data - The booking data to create
 * @returns The created booking object
 * @throws Error if the request fails or the response is invalid
 */
// Create a third-party booking (expects source and type in data)
export async function createThirdPartyBooking(
  data: CreateInternalBookingDto
): Promise<ThirdPartyBookingResponse> {
  try {
    const response = await axios.post(
      bookingUrl.createThirdPartyBooking,
      data,
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );
    const booking = response.data;
    // Validate that data has required fields for a Booking
    if (
      // !booking ||
      !booking.booking ||
      !booking.booking.id ||
      !booking.booking.ticketId
    ) {
      throw new Error("Invalid booking data received");
    }
    console.log("booking", booking);
    return booking;
  } catch (error: any) {
    let message = "Failed to create third-party booking";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Reschedules a booking to a new flight ticket.
 * @param bookingId - The ID of the booking to reschedule
 * @returns The updated booking object
 */
export async function rescheduleBooking(bookingId: string): Promise<Booking> {
  try {
    const response = await axios.post(bookingUrl.rescheduleBooking(bookingId), {
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
    });

    if (!response.data.success || !response.data.data) {
      throw new Error("Invalid response from reschedule booking request");
    }

    return response.data.data;
  } catch (error: any) {
    let message = "Failed to reschedule booking";
    if (error.response?.data?.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Gets ticket information for rescheduling a booking.
 * @param bookingId - The ID of the booking to reschedule
 * @returns The ticket information including refId and route details
 */
export async function getTicketForReschedule(bookingId: string): Promise<any> {
  try {
    const response = await axios.get(
      bookingUrl.getTicketForReschedule(bookingId),
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(
        "Invalid response from get ticket for reschedule request"
      );
    }

    return response.data.data;
  } catch (error: any) {
    let message = "Failed to get ticket information for rescheduling";
    if (error.response?.data?.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Gets all bookings (no filters for master users).
 * Always returns { success, results: { bookings, bookingsTotal, nextCursor } }.
 * @param cursor - Cursor for pagination (optional)
 * @param pageSize - The number of bookings per page (default: 20)
 * @returns An object with { success, results: { bookings, bookingsTotal, nextCursor } }
 */
export async function getAllGlobalBookings(
  cursor?: string | null,
  pageSize = cursor ? 10 : 20,
  filters: Record<string, any> = {}
): Promise<{
  success: boolean;
  results: {
    bookings: Booking[];
    bookingsTotal: number;
    nextCursor: string | null;
  };
}> {
  try {
    let url = bookingUrl.getAllGlobalBookings(1, pageSize);
    if (cursor) {
      url += `&cursor=${cursor}`;
    }

    // append each filter that has a value other than undefined / “All”
    Object.entries(filters).forEach(([key, val]) => {
      if (val && val !== "All" && val !== "All Time") {
        url += `&${encodeURIComponent(key)}=${encodeURIComponent(
          val as string
        )}`;
      }
    });

    const response = await axios.get(url, {
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
    });
    // The backend already returns the correct structure
    console.log("Global bookings response:", response.data);
    return response.data;
  } catch (error: any) {
    let message = "Failed to fetch global bookings";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Gets all sales (no filters for master users).
 * Always returns { success, results: { bookings, bookingsTotal, nextCursor } }.
 * @param cursor - Cursor for pagination (optional)
 * @param pageSize - The number of bookings per page (default: 20)
 * @returns An object with { success, results: { bookings, bookingsTotal, nextCursor } }
 */
export async function getAllSales(
  cursor?: string | null,
  pageSize = cursor ? 10 : 20,
  status?: string | string[],
  source?: string | string[],
  agentName?: string | string[],
  travelDateRange?: string,
  paymentMethod?: string | string[],
  saleDate?: string,
  search?: string
): Promise<{
  success: boolean;
  results: {
    bookings: Booking[];
    bookingsTotal: number;
    nextCursor: string | null;
  };
}> {
  try {
    let url = bookingUrl.getAllSales(1, pageSize);
    if (cursor) {
      url = `${url}&cursor=${cursor}`;
    }
    if (status) {
      url = `${url}&status=${status}`;
    }
    if (source) {
      url = `${url}&source=${source}`;
    }
    if (agentName) {
      url = `${url}&agentName=${agentName}`;
    }
    if (travelDateRange) {
      url = `${url}&travelDateRange=${travelDateRange}`;
    }
    if (paymentMethod) {
      url = `${url}&paymentMethod=${paymentMethod}`;
    }
    if (saleDate) {
      url = `${url}&saleDate=${saleDate}`;
    }
    if (search) {
      url = `${url}&search=${search}`;
    }
    const response = await axios.get(url, {
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
    });
    // The backend already returns the correct structure
    return response.data;
  } catch (error: any) {
    let message = "Failed to fetch sales";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}

/**
 * Refunds a booking by its ID.
 * @param bookingId - The booking ID to refund
 * @returns The refunded booking object or null if not found
 * @throws Error if the request fails or the response is invalid
 */
export async function refundBooking(bookingId: string): Promise<{
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  code?: string;
}> {
  try {
    const response = await axios.post(
      bookingUrl.refundBooking(bookingId),
      { bookingId },
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );

    const responseData = response.data;
    console.log("Response data:", responseData);

    // If the API returns success: true with data
    if (responseData?.success === true && responseData.data) {
      console.log(
        "Successfully processed booking refund for:",
        responseData.data.bookingId
      );
      return {
        success: true,
        message: "Booking refunded successfully",
        data: responseData.data,
      };
    }

    // If the API returns directly with booking data (without success wrapper)
    if (responseData?.bookingId) {
      console.log(
        "Successfully processed booking refund for:",
        responseData.bookingId
      );
      return {
        success: true,
        message: "Booking refunded successfully",
        data: responseData,
      };
    }

    // If we get here, the response format is unexpected
    console.error("Unexpected response format:", responseData);
    return {
      success: false,
      message: "Unexpected response format from server",
      error: "INVALID_RESPONSE_FORMAT",
    };
  } catch (error: any) {
    console.error("Error refunding booking:", error);

    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      const errorResponse = error.response?.data || {};
      return {
        success: false,
        message: errorResponse.message || "Failed to process refund",
        error: errorResponse.error || "UNKNOWN_ERROR",
        code: errorResponse.code,
      };
    }

    // Handle other errors
    return {
      success: false,
      message: error.message || "An unexpected error occurred",
      error: "UNKNOWN_ERROR",
    };
  }
}

/**
 * Updates traveler information for a booking.
 * @param bookingId - The ID of the booking to update traveler information for
 * @param travelerInfo - The traveler information to update
 * @returns The updated booking object or null if not found
 * @throws Error if the request fails or the response is invalid
 */
export async function updateTravelerInfo(
  bookingId: string,
  travelerInfo: any
): Promise<Booking | null> {
  try {
    const response = await axios.put(
      bookingUrl.updateTravelerInfo(bookingId),
      travelerInfo,
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );
    const responseData = response.data;

    // Validate response structure
    if (!responseData || !responseData.data) {
      throw new Error("Invalid response format from server");
    }

    return responseData.data;
  } catch (error: any) {
    let message = "Failed to update traveler information";
    if (error.response && error.response.data && error.response.data.message) {
      message = error.response.data.message;
    } else if (error.message) {
      message = error.message;
    }
    throw new Error(message);
  }
}
