// import { Team<PERSON><PERSON>ber } from '@prisma/client';
import { AuthRequest } from "../definitions";
import { prisma } from "../../prisma";

export const handleModeratorDeletion = async (teamMemberId: string) => {
  try {
    // Get the moderator and their created tickets
    const moderator = await prisma.teamMember.findUnique({
      where: { id: teamMemberId },
      include: {
        createdBy: true,
      },
    });

    if (!moderator || !moderator.createdBy) {
      throw new Error("Moderator or creator not found");
    }

    // Store the creator in a variable to help TypeScript with type narrowing
    const creator = moderator.createdBy;

    // Get all tickets that belong to the moderator
    const tickets = await prisma.flightTicket.findMany({
      where: {
        ownerId: teamMemberId,
      },
    });

    // Transfer all tickets to the master user who created the moderator
    await prisma.flightTicket.updateMany({
      where: {
        ownerId: teamMemberId,
      },
      data: {
        ownerId: creator.id,
      },
    });

    // Log the transfer for each ticket
    await Promise.all(
      tickets.map((ticket: any) =>
        prisma.ticketHistoryLog.create({
          data: {
            ticketId: ticket.id,
            changeType: "TICKET_OWNERSHIP_TRANSFER",
            changeDetails: JSON.stringify({
              comment: `Ticket transferred from deleted moderator ${moderator.firstName} ${moderator.lastName} to master user ${creator.firstName} ${creator.lastName}`,
            }),
            agencyId: creator.id,
          },
        })
      )
    );
  } catch (error) {
    console.error("Error in handleModeratorDeletion:", error);
    throw error;
  }
};
