import { Request, Response, NextFunction } from "express";
import logger from "../utils/logger";

/**
 * Centralized error handler middleware for Express.
 * Always returns JSON with { code, message } for all errors.
 *
 * Usage: Place as the LAST app.use() in your Express app.
 */

interface RequestWithId extends Request {
  id?: string;
}

interface CustomError extends Error {
  code?: string;
  status?: number;
  details?: any;
}

export function errorHandler(
  err: CustomError,
  req: RequestWithId,
  res: Response,
  next: NextFunction
) {
  // Log error details
  logger.error({
    message: err.message,
    stack: err.stack,
    code: err.code,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userId: (req as any).user?.id,
  });

  // If response already sent, delegate to default handler
  if (res.headersSent) {
    return next(err);
  }

  // Known error with code/message
  if (err.code && err.message) {
    return res.status(err.status || 400).json({
      code: err.code,
      message: err.message,
    });
  }

  // Handle different types of errors
  if (err.name === "PrismaClientKnownRequestError") {
    return res.status(400).json({
      code: "DATABASE_ERROR",
      message: "Database operation failed",
      details: process.env.NODE_ENV === "development" ? err.details : undefined,
    });
  }

  if (err.name === "TokenExpiredError") {
    return res.status(401).json({
      code: "TOKEN_EXPIRED",
      message: "Your session has expired",
      forceLogout: true,
    });
  }

  // Fallback for unknown errors
  // Default error response
  res.status(err.status || 500).json({
    code: err.code || "SERVER_ERROR",
    message:
      process.env.NODE_ENV === "production"
        ? "An unexpected error occurred"
        : err.message,
    requestId: req?.id, // Add request tracking ID
  });
}
