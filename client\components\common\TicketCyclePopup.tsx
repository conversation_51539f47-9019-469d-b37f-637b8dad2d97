import { useState } from "react";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  HelpCircle,
  Lock,
  PauseCircle,
  RefreshCcw,
  XCircle,
  X,
} from "lucide-react";

const stages = [
  {
    name: "Pending",
    icon: Clock,
    color: "text-yellow-600 dark:text-yellow-500",
    bgColor: "bg-yellow-100 dark:bg-yellow-500/10",
    description:
      "This is the first stage of a newly created ticket listing. The Airvilla Team is currently reviewing it, and it will only become available to customers once approved.",
  },
  {
    name: "Available",
    icon: CheckCircle,
    color: "text-green-600 dark:text-green-500",
    bgColor: "bg-green-100 dark:bg-green-500/10",
    description:
      "The ticket listing is active and available for booking. All details have been verified and approved by the Airvilla Team.",
  },
  {
    name: "Updated",
    icon: RefreshCcw,
    color: "text-blue-600 dark:text-blue-500",
    bgColor: "bg-blue-100 dark:bg-blue-500/10",
    description:
      "The ticket listing has been updated, and the changes are now pending review by the Airvilla Team. The original version remains active and available for booking until the changes are approved.",
  },
  {
    name: "Unavailable",
    icon: XCircle,
    color: "text-red-600 dark:text-red-500",
    bgColor: "bg-red-100 dark:bg-red-500/10",
    description:
      "The ticket listing has been temporarily removed from search results and is not available for booking. The agency responsible for the listing can toggle the status between 'Available' and 'Unavailable' as needed.",
  },
  {
    name: "On Hold",
    icon: PauseCircle,
    color: "text-orange-600 dark:text-orange-500",
    bgColor: "bg-orange-100 dark:bg-orange-500/10",
    description:
      "The ticket listing has been temporarily put on hold by the Airvilla Team. No changes can be made until the hold status is lifted.",
  },
  {
    name: "Rejected",
    icon: AlertCircle,
    color: "text-red-600 dark:text-red-500",
    bgColor: "bg-red-100 dark:bg-red-500/10",
    description:
      "The Airvilla Team has declined to approve the ticket listing or its changes. Please review the provided feedback, make the necessary adjustments, and resubmit for review.",
  },
  {
    name: "Blocked",
    icon: Lock,
    color: "text-purple-600 dark:text-purple-500",
    bgColor: "bg-purple-100 dark:bg-purple-500/10",
    description:
      "This ticket listing has been permanently blocked by the Airvilla Team and cannot be modified or resubmitted for approval. The agency responsible for the listing may delete it from the system if necessary",
  },
  {
    name: "Expired",
    icon: Clock,
    color: "text-gray-600 dark:text-gray-400",
    bgColor: "bg-gray-100 dark:bg-gray-500/10",
    description:
      "The flight date for the ticket listing has passed. It is no longer valid for booking and will be archived by the system accordingly.",
  },
];

export function TicketCyclePopup({ status }: { status: string }) {
  const [open, setOpen] = useState(false);

  return (
    <div>
      <button
        onClick={() => setOpen(true)}
        className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-300 flex items-center"
      >
        <HelpCircle size={18} className="m-[2px]" />
        {/* <span className="hidden md:inline"> */}
        {status}
        {/* </span> */}
      </button>

      {open && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 dark:bg-black/50 overflow-y-auto">
          <div className="relative w-full max-w-4xl min-h-[60vh] max-h-[90vh] my-4 mx-2 bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex flex-col">
            {/* Sticky header */}
            <div className="sticky top-0 bg-white dark:bg-gray-800 pb-2 z-10">
              <button
                onClick={() => setOpen(false)}
                className="absolute right-4 top-4 rounded bg-red-500 p-2 text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 h-8 w-8 flex items-center justify-center"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-6">
                Ticket Status Info
              </h2>
            </div>

            {/* Scrollable content */}
            <div className="flex-1 overflow-y-auto px-1 py-2 custom-scrollbar">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {stages.map((stage) => (
                  <div
                    key={stage.name}
                    className="flex flex-col items-center text-center p-2 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer group border border-gray-200 dark:border-transparent"
                  >
                    <div
                      className={`w-10 h-10 rounded-full ${stage.bgColor} flex items-center justify-center mb-2 transition-all duration-300 group-hover:scale-110`}
                    >
                      <stage.icon
                        className={`w-5 h-5 ${
                          stage.color
                        } transition-transform duration-300 group-hover:rotate-12 ${
                          stage.name === "Updated"
                            ? "group-hover:rotate-180"
                            : stage.name === "Pending" ||
                              stage.name === "Expired"
                            ? "group-hover:-rotate-12"
                            : stage.name === "On Hold"
                            ? "group-hover:scale-110"
                            : "group-hover:rotate-12"
                        }`}
                      />
                    </div>
                    <h3 className="text-sm font-semibold mb-1 text-gray-800 dark:text-white">
                      {stage.name}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {stage.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default TicketCyclePopup;
