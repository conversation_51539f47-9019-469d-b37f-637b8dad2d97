"use client";
import { loginUser, selectUser } from "@/redux/features/AuthSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { AuthResTypes, StoredUser } from "@/utils/definitions/authDefinitions";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  fetchEmailVerification,
  fetchSendEmailVerification,
} from "@/lib/data/authData";
import AuthHeader from "@/app/(auth)/auth-header";
import { setMsg } from "@/redux/features/ActionMsgSlice";

export default function Verification({ userToken }: { userToken: string }) {
  const user = useAppSelector(selectUser);
  const dispatch = useAppDispatch();
  const router = useRouter();

  //   ############# STATES ##############
  const [loading, setLoading] = useState<boolean>(true);
  const [emailRes, setEmailRes] = useState<null | AuthResTypes>(null);

  // ############# functions ##########
  const handleEmailVerification = async () => {
    setLoading(true);
    try {
      // send email request
      const emailVerification = await fetchEmailVerification(userToken);
      setEmailRes(emailVerification);

      if (emailVerification?.success && emailVerification?.results) {
        dispatch(loginUser(emailVerification?.results));
        dispatch(
          setMsg({
            success: true,
            message: emailVerification.message,
          })
        );
        router.push("/signup-process/not-accepted");
      } else if (emailVerification?.code === "TOKEN_EXPIRED") {
        dispatch(
          setMsg({
            success: false,
            message: emailVerification.message,
          })
        );
        // Automatically request a new verification email
        const resendResult = await fetchSendEmailVerification();
        if (resendResult?.success) {
          dispatch(
            setMsg({
              success: true,
              message: "A new verification email has been sent to your inbox.",
            })
          );
        }
        router.push("/signup-process/not-verified");
      } else {
        dispatch(
          setMsg({
            success: false,
            message: emailVerification?.message || "Failed to verify email",
          })
        );
      }
    } catch (error) {
      console.error("Verification error:", error);
      dispatch(
        setMsg({
          success: false,
          message: "An error occurred during verification",
        })
      );
    } finally {
      setLoading(false);
    }
  };

  //   ############## useEffect ##########
  // Redirect to homepage if already logged in
  useEffect(() => {
    if (user.isLogin && (user as StoredUser).verified) {
      router.push("/blockseats");
      return;
    }

    if (userToken) {
      handleEmailVerification();
      setLoading(false);
      return;
    }

    setLoading(false);
  }, []);

  // ############# RETURNS #################
  if (!loading) {
    return (
      <PageWrapper>
        <VerificationMessage>Verifying your account.....</VerificationMessage>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      {!emailRes === null && !emailRes?.success && (
        <VerificationMessage>
          <div className="text-center px-4">
            Something went wrong, or the verification session was expired!!
          </div>
          <button
            className="btn bg-red-500 hover:bg-red-600 text-white ml-3 w-[6rem] mt-6"
            type="button"
            onClick={() => router.push("/signin")}
          >
            Try Again
          </button>
        </VerificationMessage>
      )}
    </PageWrapper>
  );
}

// New component to avoid repetition
const VerificationMessage = ({ children }: { children: React.ReactNode }) => (
  <div className="max-w-2xl m-auto border-2 border-gray-500 dark:border-gray-400 p-5 rounded-md px-3 py-5 bg-white dark:bg-gray-800 dark:bg-opacity-30">
    <div className="text-center px-4">{children}</div>
  </div>
);

const PageWrapper = ({ children }: { children: React.ReactNode }) => (
  <main className="bg-gray-200 dark:bg-gray-900">
    <div className="relative md:flex">
      <div className="md:w-full">
        <div className="min-h-[100dvh] h-full flex flex-col after:flex-1">
          <AuthHeader />
          {children}
        </div>
      </div>
    </div>
  </main>
);
