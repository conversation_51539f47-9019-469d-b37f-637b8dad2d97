import {
  Send,
  AlertCircle,
  MessageSquare,
  Plane,
  Upload,
  X,
} from "lucide-react";

export interface CustomField {
  name: string;
  label: string;
  type: "select" | "text";
  options?: string[];
  required?: boolean;
  placeholder?: string;
}

export type Category = "bug" | "ticket" | "general";

export const categories = [
  {
    value: "general",
    label: "Customer Support",
    icon: MessageSquare,
    color: "text-blue-500",
  },
  {
    value: "ticket",
    label: "Ticket Support",
    icon: Plane,
    color: "text-gray-800 dark:text-white",
  },
  {
    value: "bug",
    label: "Bug Report",
    icon: AlertCircle,
    color: "text-red-500",
  },
];

export const categoryFields: Record<Category, CustomField[]> = {
  bug: [
    {
      name: "severity",
      label: "Severity",
      type: "select",
      options: ["Low", "Medium", "High", "Critical"],
      required: true,
    },
  ],
  ticket: [
    {
      name: "bookingReference",
      label: "Booking Reference Number",
      type: "text",
      required: true,
      placeholder: "e.g., ABC123456 or CONF-987654",
    },
    {
      name: "requestType",
      label: "Request Type",
      type: "select",
      options: [
        "Modification",
        "Refund",
        "Cancellation",
        "Name Change",
        "Other",
      ],
      required: true,
    },
    {
      name: "priority",
      label: "Priority",
      type: "select",
      options: [
        "Standard",
        "Urgent - Travel within 48 hours",
        "Immediate - Travel today",
      ],
      required: true,
    },
  ],
  general: [
    {
      name: "department",
      label: "Related Department",
      type: "select",
      options: [
        "Customer Support",
        "Technical Support",
        "Sales",
        "Billing and Payments",
        "Operations",
        "Other",
      ],
      required: false,
    },
  ],
};

export const departmentDescriptions: Record<string, string> = {
  "Customer Support":
    "Handles issues related to booking, cancellations, and general inquiries about flights and services.",
  "Technical Support":
    "Deals with website or app technical issues, bugs, and problems requiring technical assistance.",
  Sales:
    "Manages inquiries related to group bookings, special rates, and partnerships with travel agencies.",
  "Billing and Payments":
    "Handles questions and problems related to payment processing, refunds, and billing discrepancies.",
  Operations:
    "Addresses issues related to flight schedules, delays, and operational concerns.",
  Other: "For feedback not related to the above departments.",
};
