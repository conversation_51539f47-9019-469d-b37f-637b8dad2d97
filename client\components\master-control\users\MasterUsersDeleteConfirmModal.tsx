import React from "react";

const AlertDialog = ({
  open,
  children,
}: {
  open: boolean;
  children: React.ReactNode;
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 dark:bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full shadow-lg">
        {children}
      </div>
    </div>
  );
};

const AlertDialogContent = ({ children }: { children: React.ReactNode }) => (
  <div className="p-6">{children}</div>
);

const AlertDialogHeader = ({ children }: { children: React.ReactNode }) => (
  <div className="mb-4">{children}</div>
);

const AlertDialogFooter = ({ children }: { children: React.ReactNode }) => (
  <div className="mt-6 flex justify-end space-x-2">{children}</div>
);

const AlertDialogTitle = ({ children }: { children: React.ReactNode }) => (
  <h3 className="text-2xl font-bold text-red-600 dark:text-red-500 mb-2">
    {children}
  </h3>
);

const AlertDialogDescription = ({
  children,
}: {
  children: React.ReactNode;
}) => <div className={"text-gray-700 dark:text-gray-300"}>{children}</div>;

const AlertDialogAction = ({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}) => (
  <button
    onClick={onClick}
    className="bg-red-500 text-white hover:bg-red-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
  >
    {children}
  </button>
);

const AlertDialogCancel = ({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}) => (
  <button
    onClick={onClick}
    className="bg-gray-700 text-white hover:bg-gray-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
  >
    {children}
  </button>
);

const MasterUsersDeleteConfirmModal = ({
  showDeleteConfirmation,
  setShowDeleteConfirmation,
  handleDeleteConfirm,
}: {
  showDeleteConfirmation: boolean;
  setShowDeleteConfirmation: React.Dispatch<React.SetStateAction<boolean>>;
  handleDeleteConfirm: () => void;
}) => {
  const handleConfirmDeletion = () => {
    setShowDeleteConfirmation(false);
    handleDeleteConfirm();
  };
  const handleCancelDeletion = () => setShowDeleteConfirmation(false);

  return (
    <AlertDialog open={showDeleteConfirmation}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete User Account</AlertDialogTitle>
          <AlertDialogDescription>
            <p className="mb-4 text-gray-800 dark:text-gray-200">
              Are you sure you want to delete this user account?
            </p>

            <div className="p-4 rounded-lg mb-4 bg-gray-100 border border-gray-200 dark:border-gray-800 dark:bg-gray-700">
              <h4 className="text-base font-medium mb-3 text-gray-800 dark:text-gray-100">
                Effects of soft deleting this user account:
              </h4>
              <ul className="list-disc space-y-1 ml-6">
                {[
                  "User profile will be hidden from all other users",
                  "User will be logged out of all active sessions",
                  "User will not be able to log back in",
                  "All associated users with agency account will lose access",
                  "User will no longer have access to their wallet account and balance",
                  "All wallet balances will be frozen",
                  "Subscription billing will not be paused",
                  "Content created by user will be preserved but marked as from deleted user",
                ].map((item, index) => (
                  <li
                    key={index}
                    className="pl-2 text-gray-700 dark:text-gray-300"
                  >
                    <span className="block ml-1">{item}</span>
                  </li>
                ))}
              </ul>

              <div className="border-t border-gray-300 dark:border-gray-600 mt-4 pt-3">
                <h4 className="text-base font-medium mb-3 text-gray-800 dark:text-gray-100">
                  Warning:
                </h4>
                <p className="text-gray-700 dark:text-gray-300 mb-2">
                  Account data will be retained for 180 days. After this period,
                  all account information, including balances, tickets, and
                  associated users, will be permanently deleted with no
                  possibility of recovery.
                </p>
                <p className="text-gray-700 dark:text-gray-300 font-bold border-t border-gray-300 dark:border-gray-600 mt-4 pt-3">
                  Master Accounts can reverse this action within the 180-day
                  retention period by reactivating the account.
                </p>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancelDeletion}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirmDeletion}>
            Delete User
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default MasterUsersDeleteConfirmModal;
