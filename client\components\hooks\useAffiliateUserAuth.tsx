"use client";

import { selectUser } from "@/redux/features/AuthSlice";
import { useAppSelector } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import { AccountStatusEnum } from "@/utils/definitions/masterDefinitions";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const VALID_STATUSES: AccountStatusEnum[] = [
  AccountStatusEnum.accepted,
  AccountStatusEnum.suspended,
  AccountStatusEnum.disabled,
];
const LOGOUT_STATUSES: AccountStatusEnum[] = [
  AccountStatusEnum.suspended,
  AccountStatusEnum.disabled,
];

const useAffiliateUserAuth = () => {
  const user = useAppSelector(selectUser);
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // redirect the user to signin page if not signed in
    if (
      !user.isLogin ||
      LOGOUT_STATUSES.includes(
        (user as StoredUser).accountStatus as AccountStatusEnum
      )
    ) {
      console.log(
        "User is not logged in or is suspended, disabled, or deactivated in affiliate auth. Redirecting to signin page."
      );
      router.push("/signin");
      return;
    }

    // redirect the user to not verified page if not verified yet
    if (!(user as StoredUser).verified) {
      router.push("/signup-process/not-verified");
      return;
    }

    // redirect the user to not approved page if not approved yet
    if (
      !VALID_STATUSES.includes(
        (user as StoredUser).accountStatus as AccountStatusEnum
      )
    ) {
      router.push("/signup-process/not-accepted");
      return;
    }

    setLoading(false);
  }, [user, router]);

  return loading;
};

export default useAffiliateUserAuth;
