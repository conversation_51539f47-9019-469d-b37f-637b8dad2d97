/**
 * Traveler Form Validation
 *
 * This file contains validation rules for the traveler form using <PERSON><PERSON>.
 *
 * Validation rules:
 * - First name and last name: 2-30 characters, letters, spaces, apostrophes, and hyphens only
 * - Email: Valid email format with TLD validation disabled
 * - Phone number: Valid international format using libphonenumber-js
 * - Date of birth: Valid date in the past
 * - Nationality: Required string, automatically converted to uppercase
 * - Passport number: 6-15 characters, uppercase letters and numbers only
 * - Passport issuing country: Required string, automatically converted to uppercase
 * - Passport expiry: Valid date in the future
 */

import Joi from "joi";

// Add custom validation options
export const validationOptions = {
  abortEarly: false, // Return all errors, not just the first one
  allowUnknown: true, // Allow unknown keys that will be ignored
  stripUnknown: true, // Remove unknown elements from objects
};

// Helper function to validate traveler data
export const validateTravelerData = (travelerData: any) => {
  return travelerFormValidation.validate(travelerData, validationOptions);
};

// Helper function to validate a single field
export const validateTravelerField = (field: string, value: any) => {
  // Create a schema for just this field
  const fieldSchema = Joi.object({
    [field]: travelerFormValidation.extract(field),
  });

  return fieldSchema.validate({ [field]: value }, validationOptions);
};

export const travelerFormValidation = Joi.object({
  title: Joi.string().valid("Mr", "Mrs", "Ms").required().messages({
    "string.empty": "Title cannot be empty.",
    "any.only": "Title must be either 'Mr', 'Mrs', or 'Ms'",
    "any.required": "Title is required",
  }),
  gender: Joi.string().valid("male", "female").required().messages({
    "string.empty": "Gender cannot be empty.",
    "any.only": "Gender must be either 'male' or 'female'",
    "any.required": "Gender is required",
  }),
  firstName: Joi.string()
    .min(3)
    .max(30)
    .pattern(/^[A-Za-z\s'-]+$/)
    .required()
    .messages({
      "string.empty": "First name cannot be empty.",
      "string.min": "First name must be at least 3 characters long.",
      "string.max": "First name must be at most 30 characters long.",
      "string.pattern.base":
        "First name can only contain letters, apostrophes, hyphens, and spaces.",
    }),
  lastName: Joi.string()
    .min(3)
    .max(30)
    .pattern(/^[A-Za-z\s'-]+$/)
    .required()
    .messages({
      "string.empty": "Last name cannot be empty.",
      "string.min": "Last name must be at least 3 characters long.",
      "string.max": "Last name must be at most 30 characters long.",
      "string.pattern.base":
        "Last name can only contain letters, apostrophes, hyphens, and spaces.",
    }),
  contactEmail: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "string.empty": "Email cannot be empty.",
      "string.email": "Please enter a valid email address.",
      "any.required": "Email is required.",
    }),
  contactPhone: Joi.string().min(5).max(20).required().messages({
    "string.empty": "Phone number cannot be empty",
    "string.min": "Phone number must be at least 5 characters long",
    "string.max": "Phone number must be at most 20 characters long",
    "any.required": "Phone number is required",
  }),
  dateOfBirth: Joi.date()
    .required()
    .max("now") // Must be in the past
    .messages({
      "date.base": "Date of birth must be a valid date.",
      "date.max": "Date of birth cannot be in the future.",
      "any.required": "Date of birth is required.",
    }),
  nationality: Joi.string().required().uppercase().trim().messages({
    "string.empty": "Nationality cannot be empty.",
    "any.required": "Nationality is required.",
  }),
  passportNumber: Joi.string().required().min(6).max(15).trim().messages({
    "string.empty": "Passport number cannot be empty.",
    "string.min": "Passport number must be at least 6 characters long.",
    "string.max": "Passport number must be at most 15 characters long.",
    "any.required": "Passport number is required.",
  }),
  issuingCountry: Joi.string().required().uppercase().trim().messages({
    "string.empty": "Passport issuing country cannot be empty.",
    "any.required": "Passport issuing country is required.",
  }),
  passportExpiry: Joi.date()
    .required()
    .min("now") // Must be in the future
    .messages({
      "date.base": "Passport expiry must be a valid date.",
      "date.min": "Passport expiry date must be in the future.",
      "any.required": "Passport expiry is required.",
    }),
});
