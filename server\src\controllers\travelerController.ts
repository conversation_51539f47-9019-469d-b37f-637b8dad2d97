import { Request, Response } from "express";
import { validateTravelerData } from "../utils/validators/travelerValidation";
import { prisma } from '../prisma';

/**
 * Validate traveler information
 * This endpoint validates traveler information without saving it to the database
 */
export const validateTraveler = async (req: Request, res: Response) => {
  try {
    const travelerData = req.body;

    // Ensure phone number has a + prefix
    if (travelerData.phoneNumber && travelerData.phoneNumber[0] !== "+") {
      travelerData.phoneNumber = "+" + travelerData.phoneNumber;
    }

    // Validate the traveler data
    const { error, value } = validateTravelerData(travelerData);

    if (error) {
      const validationErrors = error.details.reduce(
        (acc, detail) => {
          if (detail.context && detail.context.key) {
            acc[detail.context.key] = detail.message;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      return res.status(400).json({
        success: false,
        validationErrors,
      });
    }

    // Return the validated data
    return res.status(200).json({
      success: true,
      data: value,
    });
  } catch (error: any) {
    console.error("Error validating traveler:", error);
    return res.status(500).json({
      success: false,
      message:
        error.message || "An error occurred while validating traveler data",
    });
  }
};

/**
 * Create a new traveler (customer info)
 */
export const createTraveler = async (req: Request, res: Response) => {
  try {
    const travelerData = req.body;

    // Ensure phone number has a + prefix
    if (travelerData.phoneNumber && travelerData.phoneNumber[0] !== "+") {
      travelerData.phoneNumber = "+" + travelerData.phoneNumber;
    }

    // Validate the traveler data
    const { error, value } = validateTravelerData(travelerData);

    if (error) {
      const validationErrors = error.details.reduce(
        (acc, detail) => {
          if (detail.context && detail.context.key) {
            acc[detail.context.key] = detail.message;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      return res.status(400).json({
        success: false,
        validationErrors,
      });
    }

    // Create the customer info record
    const customerInfo = await prisma.traveler.create({
      data: {
        title: value.title,
        gender: value.gender,
        firstName: value.firstName,
        lastName: value.lastName,
        nationality: value.nationality,
        dateOfBirth: new Date(value.dateOfBirth),
        documentType: value.documentType,
        documentNumber: value.documentNumber,
        issuingCountry: value.issuingCountry,
        expirationDate: new Date(value.expirationDate),
      },
    });

    return res.status(201).json({
      success: true,
      data: customerInfo,
    });
  } catch (error: any) {
    console.error("Error creating traveler:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while creating traveler",
    });
  }
};

/**
 * Get a traveler by ID
 */
export const getTravelerById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const traveler = await prisma.traveler.findUnique({
      where: { id },
    });

    if (!traveler) {
      return res.status(404).json({
        success: false,
        message: "Traveler not found",
      });
    }

    return res.status(200).json({
      success: true,
      data: traveler,
    });
  } catch (error: any) {
    console.error("Error getting traveler:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while getting traveler",
    });
  }
};

/**
 * Update a traveler
 */
export const updateTraveler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const travelerData = req.body;

    // Ensure phone number has a + prefix
    if (travelerData.phoneNumber && travelerData.phoneNumber[0] !== "+") {
      travelerData.phoneNumber = "+" + travelerData.phoneNumber;
    }

    // Validate the traveler data
    const { error, value } = validateTravelerData(travelerData);

    if (error) {
      const validationErrors = error.details.reduce(
        (acc, detail) => {
          if (detail.context && detail.context.key) {
            acc[detail.context.key] = detail.message;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      return res.status(400).json({
        success: false,
        validationErrors,
      });
    }

    // Check if the traveler exists
    const existingTraveler = await prisma.traveler.findUnique({
      where: { id },
    });

    if (!existingTraveler) {
      return res.status(404).json({
        success: false,
        message: "Traveler not found",
      });
    }

    // Update the traveler
    const updatedTraveler = await prisma.traveler.update({
      where: { id },
      data: {
        title: value.title,
        gender: value.gender,
        firstName: value.firstName,
        lastName: value.lastName,
        nationality: value.nationality,
        dateOfBirth: new Date(value.dateOfBirth),
        documentType: value.documentType,
        documentNumber: value.documentNumber,
        issuingCountry: value.issuingCountry,
        expirationDate: new Date(value.expirationDate),
      },
    });

    return res.status(200).json({
      success: true,
      data: updatedTraveler,
    });
  } catch (error: any) {
    console.error("Error updating traveler:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while updating traveler",
    });
  }
};

/**
 * Delete a traveler
 */
export const deleteTraveler = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if the traveler exists
    const existingTraveler = await prisma.traveler.findUnique({
      where: { id },
    });

    if (!existingTraveler) {
      return res.status(404).json({
        success: false,
        message: "Traveler not found",
      });
    }

    // Delete the traveler
    await prisma.traveler.delete({
      where: { id },
    });

    return res.status(200).json({
      success: true,
      message: "Traveler deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting traveler:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "An error occurred while deleting traveler",
    });
  }
};
