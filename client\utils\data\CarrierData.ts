export interface Airline {
  id: string;
  airlineCode: string;
  airlineName: string;
  country: string;
}

export const airlineData: Airline[] = [
  {
    id: "1",
    airlineCode: "EK",
    airlineName: "Emirates",
    country: "United Arab Emirates",
  },
  {
    id: "2",
    airlineCode: "EY",
    airlineName: "Etihad Airways",
    country: "United Arab Emirates",
  },
  {
    id: "3",
    airlineCode: "FZ",
    airlineName: "Flydubai",
    country: "United Arab Emirates",
  },
  {
    id: "4",
    airlineCode: "G9",
    airlineName: "Air Arabia",
    country: "United Arab Emirates",
  },
  {
    id: "5",
    airlineCode: "5W",
    airlineName: "Wizz Air Abu Dhabi",
    country: "United Arab Emirates",
  },
  {
    id: "6",
    airlineCode: "SV",
    airlineName: "Saudia (Saudi Arabian Airlines)",
    country: "Saudi Arabia",
  },
  {
    id: "7",
    airlineCode: "XY",
    airlineName: "Flynas",
    country: "Saudi Arabia",
  },
  {
    id: "8",
    airlineCode: "F3",
    airlineName: "Flyadeal",
    country: "Saudi Arabia",
  },
  {
    id: "9",
    airlineCode: "SGQ",
    airlineName: "SaudiGulf Airlines",
    country: "Saudi Arabia",
  },
  {
    id: "10",
    airlineCode: "NE",
    airlineName: "Nesma Airlines",
    country: "Saudi Arabia",
  },
  {
    id: "11",
    airlineCode: "QR",
    airlineName: "Qatar Airways",
    country: "Qatar",
  },
  {
    id: "12",
    airlineCode: "GF",
    airlineName: "Gulf Air",
    country: "Bahrain",
  },
  {
    id: "13",
    airlineCode: "WY",
    airlineName: "Oman Air",
    country: "Oman",
  },
  {
    id: "14",
    airlineCode: "OV",
    airlineName: "SalamAir",
    country: "Oman",
  },
  {
    id: "15",
    airlineCode: "KU",
    airlineName: "Kuwait Airways",
    country: "Kuwait",
  },
  {
    id: "16",
    airlineCode: "J9",
    airlineName: "Jazeera Airways",
    country: "Kuwait",
  },
  {
    id: "17",
    airlineCode: "RJ",
    airlineName: "Royal Jordanian",
    country: "Jordan",
  },
  {
    id: "18",
    airlineCode: "R5",
    airlineName: "Jordan Aviation",
    country: "Jordan",
  },
  {
    id: "19",
    airlineCode: "ME",
    airlineName: "Middle East Airlines (MEA)",
    country: "Lebanon",
  },
  {
    id: "20",
    airlineCode: "IA",
    airlineName: "Iraqi Airways",
    country: "Iraq",
  },
  {
    id: "21",
    airlineCode: "IF",
    airlineName: "FlyBaghdad",
    country: "Iraq",
  },
  {
    id: "22",
    airlineCode: "EBL",
    airlineName: "FlyErbil",
    country: "Iraq",
  },
  {
    id: "23",
    airlineCode: "IR",
    airlineName: "Iran Air",
    country: "Iran",
  },
  {
    id: "24",
    airlineCode: "W5",
    airlineName: "Mahan Air",
    country: "Iran",
  },
  {
    id: "25",
    airlineCode: "Y9",
    airlineName: "Kish Air",
    country: "Iran",
  },
  {
    id: "26",
    airlineCode: "QB",
    airlineName: "Qeshm Air",
    country: "Iran",
  },
  {
    id: "27",
    airlineCode: "EP",
    airlineName: "Iran Aseman Airlines",
    country: "Iran",
  },
  {
    id: "28",
    airlineCode: "IZ",
    airlineName: "Zagros Airlines",
    country: "Iran",
  },
  {
    id: "29",
    airlineCode: "RV",
    airlineName: "Caspian Airlines",
    country: "Iran",
  },
  {
    id: "30",
    airlineCode: "HH",
    airlineName: "Taban Air",
    country: "Iran",
  },
  {
    id: "31",
    airlineCode: "MS",
    airlineName: "EgyptAir",
    country: "Egypt",
  },
  {
    id: "32",
    airlineCode: "SM",
    airlineName: "Air Cairo",
    country: "Egypt",
  },
  {
    id: "33",
    airlineCode: "NP",
    airlineName: "Nile Air",
    country: "Egypt",
  },
];
