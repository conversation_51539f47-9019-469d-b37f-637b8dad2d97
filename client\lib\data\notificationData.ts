import axios, { AxiosRequestConfig, AxiosError } from "axios";
import notificationUrl from "../endpoints/notificationEndpoint";

export const fetchData = async (
  url: string,
  options: AxiosRequestConfig = {}
) => {
  try {
    // Merge default options with provided options
    const axiosOptions: AxiosRequestConfig = {
      url,
      withCredentials: true,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };
    const response = await axios.request(axiosOptions);

    if (response.status === 204) {
      return null;
    }
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      // Add status code to the error object for better handling
      const errorWithStatus = {
        ...error.response.data,
        status: error.response.status,
        statusText: error.response.statusText
      };
      throw errorWithStatus;
    } else {
      throw new Error("Network error occurred.");
    }
  }
};

// CREATE NOTIFICATION
export const createNotification = async (data: any) => {
  const url = notificationUrl.createNotification;
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: data,
  };

  return await fetchData(url, options);
};

// GET USER NOTIFICATIONS
export const getNotifications = async (userId: string) => {
  const url = notificationUrl.getUserNotifications(userId);
  const data = await fetchData(url);
  if (data && data.success && data.results && Array.isArray(data.results.notifications)) {
    return data.results.notifications;
  }
  return [];
};

// GET NOTIFICATION
export const getNotification = async (id: string) => {
  const url = notificationUrl.getNotification(id);
  return await fetchData(url);
};

// MARK AS READ
export const markAsReadNotification = async (id: string) => {
  try {
    const url = notificationUrl.markAsRead(id);
    const options = {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
    };
    return await fetchData(url, options);
  } catch (error: any) {
    // If we get a 404 error, it means the notification was deleted on the server
    // In this case, we should return a special object to indicate this
    if (error.status === 404) {
      return { deleted: true, id };
    }
    throw error;
  }
};

// MARK ALL AS READ
export const markAllAsReadNotification = async (userId: string) => {
  const url = notificationUrl.markAllAsRead(userId);
  const options = {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
  };
  return await fetchData(url, options);
};

// DELETE NOTIFICATION
export const deleteNotification = async (id: string) => {
  const url = notificationUrl.deleteNotification(id);
  const options = {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  };
  return await fetchData(url, options);
};
