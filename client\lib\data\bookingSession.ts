import axios from "axios";
import bookingSessionUrl from "@/lib/endpoints/bookingSessionEndpoint";

// Starts a booking session (calls backend endpoint)
export async function startBookingSession() {
  try {
    const response = await axios.post(
      bookingSessionUrl.startBookingSession,
      {}, // POST body (empty)
      {
        withCredentials: true, // Required for cookies/session auth
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    // Optionally, handle error more gracefully here
    throw error;
  }
}

// Validate a booking session (calls backend endpoint)
export async function validateBookingSession(token: string) {
  try {
    const response = await axios.post(
      bookingSessionUrl.validateBookingSession,
      { token }, // POST body
      {
        withCredentials: true, // Required for cookies/session auth
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    // Optionally, handle error more gracefully here
    throw error;
  }
}
