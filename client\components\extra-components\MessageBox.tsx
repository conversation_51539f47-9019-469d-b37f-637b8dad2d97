"use client";
import React, { useState, useEffect } from "react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectActionMsg, clearMsg } from "@/redux/features/ActionMsgSlice";
import Toast from "../common/toast";
const MessageBox = () => {
  const dispatch = useAppDispatch();
  const [isVisible, setIsVisible] = useState(false);
  const action = useAppSelector(selectActionMsg);
  const [toast2ErrorOpen, setToast2ErrorOpen] = useState<boolean>(false);
  const [toast2SuccessOpen, setToast2SuccessOpen] = useState<boolean>(false);

  // Handle toast visibility and auto-dismiss
  useEffect(() => {
    if (!action.message) {
      setIsVisible(false);
      return;
    }

    setIsVisible(true);
    
    // Auto-hide after 5 seconds
    const timer = setTimeout(() => {
      setIsVisible(false);
      setToast2ErrorOpen(false);
      setToast2SuccessOpen(false);
      dispatch(clearMsg()); // Clear the message from Redux store
    }, 5000);

    // Set the correct toast type to open
    if (action.success) {
      setToast2SuccessOpen(true);
      setToast2ErrorOpen(false);
    } else {
      setToast2ErrorOpen(true);
      setToast2SuccessOpen(false);
    }

    return () => {
      clearTimeout(timer);
      dispatch(clearMsg()); // Clean up on unmount
    };
  }, [action, dispatch]);

  if (
    action.success === null ||
    action.message === "Unauthorized" ||
    action.message?.toLowerCase() === "login"
  ) {
    return null;
  }

  return (
    <>
      {isVisible && (
        <div className="fixed bottom-3 right-3">
          {/* {action.success && action.message  */}
          {/* // success msg */}
          {action.success && action.message && (
            <Toast
              type="success"
              open={toast2SuccessOpen}
              setOpen={setToast2SuccessOpen}
            >
              {typeof action.message === "string" && action.message}
            </Toast>
          )}
          {/* // error message */}
          {!action.success && action.message && (
            <Toast
              type="error"
              open={toast2ErrorOpen}
              setOpen={setToast2ErrorOpen}
            >
              {typeof action.message === "string" && action.message}
            </Toast>
          )}
        </div>
      )}
    </>
  );
};

export default MessageBox;
