/**
 * Generic async batch retry utility with exponential backoff.
 * Retries a batch operation up to maxAttempts times.
 */
export async function retryBatch<T>(
  batchFn: (...args: any[]) => Promise<T>,
  batchArgs: any[] = [],
  maxAttempts = 3,
  baseDelayMs = 100
): Promise<T> {
  let attempt = 0;
  let lastError: any;
  while (attempt < maxAttempts) {
    try {
      return await batchFn(...batchArgs);
    } catch (err) {
      lastError = err;
      attempt++;
      if (attempt < maxAttempts) {
        const backoff = baseDelayMs * Math.pow(2, attempt - 1);
        await new Promise(res => setTimeout(res, backoff));
      }
    }
  }
  throw lastError;
}
