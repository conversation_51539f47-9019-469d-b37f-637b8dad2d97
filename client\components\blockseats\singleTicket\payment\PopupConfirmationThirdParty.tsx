import React, { useState, useCallback } from 'react';
import { Check<PERSON><PERSON><PERSON>, Loader, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';

/**
 * Popup Confirmation Component Props
 */
interface PopupConfirmationProps {
  /** 
   * Callback function when user confirms the action
   * @default undefined
   */
  onConfirm?: () => void;
  
  /** 
   * Callback function when user cancels the action
   * @default undefined
   */
  onCancel?: () => void;
  
  /**
   * Custom confirmation message 
   * @default "Are you sure you want to perform this action? This cannot be undone."
   */
  message?: string;
  
  /**
   * Custom confirmation title
   * @default "Confirm Action"
   */
  title?: string;
}

/**
 * Popup Confirmation Component
 * Displays a modal dialog for action confirmation
 */
const PopupConfirmationThirdParty: React.FC<PopupConfirmationProps> = ({
  onConfirm,
  onCancel,
  message = "Are you sure you want to perform this action? This cannot be undone.",
  title = "Confirm Action"
}) => {
  // State to control popup visibility, loading state, and success state
//   const [isVisible, setIsVisible] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const router = useRouter();
  
  // Handler for confirmation action
  const handleConfirm = useCallback(() => {
    setIsLoading(true);
    // We'll keep the modal visible while loading
    
    // Disable back navigation while processing
    const blockBackNavigation = (e: PopStateEvent) => {
      e.preventDefault();
      window.history.pushState(null, '', window.location.href);
    };
    
    // Add event listener to block back navigation
    window.history.pushState(null, '', window.location.href);
    window.addEventListener('popstate', blockBackNavigation);
    
    // Simulating a verification delay
    setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
      
      // After showing success message briefly, close the modal
      setTimeout(() => {
        // Remove event listener when done
        window.removeEventListener('popstate', blockBackNavigation);
        
        // Don't auto-close, let user click Continue button
      }, 0); // No auto-close delay
    }, 20000); // 20 second delay
  }, [onConfirm]);
  
  // Handler for cancellation action
  const handleCancel = useCallback(() => {
    // setIsVisible(false);
    if (onCancel) {
      onCancel();
    }
  }, [onCancel]);
  
//   // If popup is not visible, don't render anything
//   if (!isVisible) {
//     return null;
//   }
  
  return (
    <div className="p-8">
      {/* Confirmation Popup Modal */}
      <div className="fixed inset-0 flex items-center justify-center z-50">
        {/* Background overlay - click events blocked during loading/success */}
        {/* <div 
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={isLoading || isSuccess ? undefined : handleCancel}
        ></div> */}
         <div className="absolute inset-0 bg-black bg-opacity-50" />

        {/* Popup content */}
        <div 
          className="relative rounded-xl shadow-lg max-w-md w-full mx-4 p-6 z-10 bg-gray-800"
        >
            <div className="flex flex-col items-center text-center py-8 h-64 justify-center">
              {/* Success icon */}
              <div className="mb-4">
                <CheckCircle className="h-12 w-12 text-red-500" />
              </div>
              
              {/* Title */}
              <h2 className="text-2xl font-bold text-white mb-2">
                Booking Confirmed
              </h2>
              
              {/* Success message */}
              <p className="text-gray-300 mb-6">
                Your Booking Reference Number Is: <span className="text-blue-400">AVC-O4JC2SB0FY</span>
              </p>
              
              {/* Action buttons */}
              <div className="flex w-full space-x-4">
                <button 
                  className="w-1/2 py-3 px-6 rounded-lg font-semibold bg-gray-700 text-white hover:bg-gray-600"
                  // onClick={() => {
                  //   // setIsVisible(false);
                  //   if (onCancel) {
                  //     onCancel();
                  //     router.push("/blockseats");
                  //   }
                  // }}
                  onClick={onCancel}
                >
                  Close
                </button>
                <button 
                  className="w-1/2 py-3 px-6 rounded-lg font-semibold bg-red-500 text-white hover:bg-red-600"
                  // onClick={() => {
                  //   // setIsVisible(false);
                  //   if (onConfirm) {
                  //     onConfirm();
                  //     router.push("/ticket-hub/myBookings");
                  //   }
                  // }}
                  onClick={onConfirm}
                >
                  Continue
                </button>
              </div>
            </div>
        </div>
      </div>
    </div>
  );
};

export default PopupConfirmationThirdParty;