import { Response } from "express";
import { prisma } from "../prisma";
import checkUserAuth from "../utils/authorization/checkUserAuth";
import { AuthRequest } from "../utils/definitions";
import getMasterAccess from "../utils/access-check/getMasterAccess";
import { getIO } from "../socket";
import { capitalize, getCreatedTimeRange } from "../utils/functions";
import { userUpdateByMasterValidation } from "../utils/validators/masterValidation";
import bcrypt from "bcrypt";
import Joi from "joi";
import generateUserRefId from "../utils/generateUserRefId";
import { handleAgencySuspension } from "../utils/triggers/handleAgencySuspension";
import { handleAgencyDisablement } from "../utils/triggers/handleAgencyDisablement";
import { handleAgencyAcceptation } from "../utils/triggers/handleAgencyAcceptation";
import { AccountStatus } from "@prisma/client";
// import { AccountStatus } from "../types/prismaEnums";
import { clearSessionExpiration } from "../utils/schedule/trackSessionExpiration";
import { handleAgencyDeactivation } from "../utils/triggers/handleAgencyDeactivation";
import { validateUsers } from "../utils/validators/teamValidation";
import { emailRejected } from "../utils/email/emailRejected";
import { emailAccepted } from "../utils/email/emailAccepted";

/**
 * Invalidates all active sessions for an agency and its agents
 * @param agencyId - The ID of the agency
 * @param message - Optional message to include in the session expiration event
 */
const invalidateAgencySessions = async (
  agencyId: string,
  message = "Your account details have been updated by our support team."
) => {
  const io = getIO();

  // Invalidate agency owner's session
  io.to(agencyId).emit("sessionExpiration", { message });
  clearSessionExpiration(agencyId);

  // Find and invalidate all agents' sessions
  const agents = await prisma.agencyAgent.findMany({
    where: {
      agencyId,
      status: "active", // Only invalidate active agents
    },
    select: { id: true },
  });

  agents.forEach((agent) => {
    io.to(agent.id).emit("sessionExpiration", { message });
    clearSessionExpiration(agent.id);
  });
};
import passwordUpdateSuccess from "../utils/email/passwordUpdateSuccess";
import { emailDisabled } from "../utils/email/emailDisabled";
import { emailSuspended } from "../utils/email/emailSuspended";
import { emailDeactivated } from "../utils/email/emailDeactivated";
// user selector
const userSelector: any = {
  address: true,
  myTickets: true,
  purchasedTickets: true,
  agents: true,
};
type AccountType = "all" | "affiliate" | "agency" | "master";

interface SearchInputType {
  accountType: AccountType;
  subscriptionStatus: string;
  registrationDateFilter: string;
  lastLoginFilter: string;
  searchQuery: string;
}
/**
 * Get all users except the master user.
 * @param {AuthRequest} req - The request object containing the user's authorization token.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A JSON response with the success status and
 * the retrieved users. The response object will have the following
 * structure:
 * {
 *   success: boolean,
 *   results: User[],
 *   nextCursor: string | null,
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const getAllUsers = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
  const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;
  const { accountStatus } = req.query;

  // Get user filter parameters from request body
  const { accountType, subscriptionStatus, startDate, endDate } = req.body as {
    accountType: string;
    subscriptionStatus: string;
    startDate: string;
    endDate: string;
  };

  try {
    // authorize the user
    const user = await getMasterAccess(req, res);

    // Validate the ticketStatus query parameter
    const accountStatusValues = [
      // "all",
      "accepted",
      "pending",
      "rejected",
      "suspended",
      "deactivated",
      "disabled",
    ];
    if (
      accountStatus &&
      !accountStatusValues.includes(accountStatus as string)
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid accountStatus query parameter",
      });
    }

    // Apply filters to the query
    // Convert ticketStatus to the appropriate type
    const accountTypeFiltered = accountType as "all" &
      "affiliate" &
      "agency" &
      "master";

    const accountStatusFiltered = accountStatus as "accepted" & //  "all" &
      "pending" &
      "rejected" &
      "suspended" &
      "deactivated" &
      "disabled";

    // select options
    const options: any = [
      {
        id: {
          not: req.user?.id,
        },
      },
      // only get the verified users
      { verified: true },
      accountStatusFiltered
        ? {
            accountStatus: accountTypeFiltered,
          }
        : {},
      accountTypeFiltered
        ? {
            role: accountTypeFiltered,
          }
        : {},
      subscriptionStatus
        ? {
            subscriptionStatus: subscriptionStatus,
          }
        : {},
    ];

    // get all users except master user
    const [users, usersTotal] = await Promise.all([
      await prisma.user.findMany({
        // filter out the master user
        where: {
          AND: options,
        },
        // include the address, tickets, purchased tickets, and agents
        include: userSelector,
        // limit the number of users to the pageSize
        take: pageSize,
        // skip the number of users based on the cursor
        skip: cursor ? 1 : 0,
        // set the cursor to the id of the last user
        cursor: cursor ? { id: cursor } : undefined,
        // sort the users by id in ascending order
        orderBy: {
          id: "asc",
        },
      }),
      // get the total number of users
      prisma.user.count({
        where: {
          AND: options,
        },
      }),
      req.user?.id
        ? prisma.user.update({
            where: { id: req.user.id },
            data: {
              lastLogin: new Date(),
            },
          })
        : Promise.resolve(null),
    ]);

    // Handle generating `refId` if required
    for (const user of users) {
      if (!user.refId) {
        const refId = await generateUserRefId();
        // Update user with the generated refId
        await prisma.user.update({
          where: { id: user.id },
          data: { refId },
        });
      }
    }

    const nextCursor =
      users.length === pageSize ? users[users.length - 1].id : null;

    // return users
    return res.status(200).json({
      success: true,
      results: {
        users,
        usersTotal,
        nextCursor,
      },
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Get all users error", {
      message: err.message,
      stack: err.stack,
    });

    return res.status(500).json({
      success: false,
      message: "Failed to get users. Please try again later.",
    });
  }
};

/**
 * Retrieves a single user by their ID. This endpoint is accessible only to master users.
 * @param {AuthRequest} req - The request object containing the user's authorization token.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A JSON response with the success status and
 * the retrieved user. The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: User,
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const getSingleUserById = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { userId } = req.params;
  try {
    // Authorize the user and check their role
    const user = await getMasterAccess(req, res);

    // Get user by ID
    const singleUser = await prisma.user.findFirst({
      where: {
        id: userId,
        verified: true,
      },
      include: userSelector,
    });

    // Return 404 if user doesn't exist
    if (!singleUser) {
      return res
        .status(404)
        .json({ success: false, message: "User doesn't exist" });
    }

    // Return user
    return res.status(200).json({ success: true, results: singleUser });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Get single user error", {
      message: err.message,
      stack: err.stack,
    });

    // Return 500 error if something went wrong
    return res.status(500).json({
      success: false,
      message: "Failed to get user info. Please try again later.",
    });
  }
};

/**
 * Update a single user's account status by ID.
 *
 * @remarks
 * This function handles changing a user's account status (accepted, suspended, rejected, disabled, deactivated).
 * It ensures that appropriate emails are sent and necessary actions are taken based on the new status.
 * Each status change is handled exclusively, preventing multiple status changes from being processed simultaneously.
 *
 * @param {AuthRequest} req - The request object containing the user's authorization token and the new account status.
 * @param {Response} res - The response object to send the result.
 * @returns {Promise<Response>} A JSON response with the success status and
 * the updated user. The response object will have the following structure:
 * {
 *   success: boolean,
 *   message: string,
 *   results: User,
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const userAccountRequest = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { userId } = req.params;
  const input = req.body;
  try {
    // Authorize the user and check their role
    const user = await getMasterAccess(req, res);
    if (!user) {
      return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    // Check if the user exists
    const userToBeUpdate = await prisma.user.findFirst({
      where: { id: userId, verified: true },
    });

    // Return 404 if user doesn't exist
    if (!userToBeUpdate) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    const timestampPattern = /_(\d+)$/;

    // Update the user
    const updatedUser = await prisma.user.update({
      where: {
        id: userToBeUpdate.id,
      },
      data: {
        email:
          userToBeUpdate.accountStatus === AccountStatus.disabled
            ? userToBeUpdate.email
            : userToBeUpdate.email.replace(timestampPattern, ""),
        // Update account status and role, if provided
        accountStatus: input?.accountStatus?.trim().toLowerCase() ?? undefined,
        role: input?.role ?? undefined,
        // Set deactivationDate based on the new account status
        deactivationDate:
          input?.accountStatus === AccountStatus.disabled ||
          input?.accountStatus === AccountStatus.deactivated
            ? new Date()
            : input?.accountStatus === AccountStatus.accepted
              ? null
              : undefined, // Keep existing value for other statuses
      },
      // Include the user's address, tickets, purchased tickets, and agents
      include: userSelector,
    });

    if (input?.accountStatus === "suspended") {
      await emailSuspended(updatedUser.email, updatedUser);
      await handleAgencySuspension(updatedUser.id);
    }
    if (input?.accountStatus === "rejected") {
      await emailRejected(updatedUser.email, updatedUser);
    }

    if (input?.accountStatus === "disabled") {
      await emailDisabled(updatedUser.email, updatedUser);
      await handleAgencyDisablement(updatedUser.id);
    }
    if (input?.accountStatus === "deactivated") {
      await emailDeactivated(updatedUser.email, updatedUser);
      await handleAgencyDeactivation(updatedUser.id);
    }

    if (input?.accountStatus === "accepted") {
      const email =
        updatedUser.accountStatus === AccountStatus.disabled
          ? updatedUser.email
          : updatedUser.email.replace(timestampPattern, "");
      await emailAccepted(email, updatedUser);
      await handleAgencyAcceptation(updatedUser.id);
      const io = getIO();
      io.to(updatedUser.id).emit("userAccepted", {
        accountStatus: updatedUser?.accountStatus,
      });
    }

    // If account status is suspended or rejected, emit socket event to force logout
    if (
      updatedUser?.accountStatus === "suspended" ||
      updatedUser?.accountStatus === "rejected" ||
      updatedUser?.accountStatus === "deactivated" ||
      updatedUser?.accountStatus === "disabled"
    ) {
      const io = getIO();
      io.to(updatedUser.id).emit("userRequestResponse", {
        accountStatus: updatedUser.accountStatus,
        forceLogout: true,
      });
      io.to(userId).emit("accountStatusChanged", {
        accountStatus: updatedUser.accountStatus,
        forceLogout: true,
      });
    }

    // Return the updated user
    return res.status(200).json({
      success: true,
      message: "User account status updated successfully",
      results: updatedUser,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Update user info error", {
      message: err.message,
      stack: err.stack,
    });

    // Return 500 error if something went wrong
    return res.status(500).json({
      success: false,
      message: "Failed to get update user's info. Please try again later.",
    });
  }
};

/**
 * Soft deletes a single user.
 *
 * @remarks
 * Only master users can access this endpoint.
 * The user's email is updated to include a timestamp of when the soft deletion occurred.
 * The user's status is changed to disabled when soft deleted, or accepted when restored.
 * Email notification is only sent when the user is being disabled, not when being restored.
 * @param req - The authenticated request object.
 * @param res - The response object.
 * @returns The response with the deleted user or an error message.
 */
export const softDeleteSingleUser = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { userId } = req.params;
  try {
    // Authorize the user and check their role
    const user = await getMasterAccess(req, res);

    // Get the user's current email and status
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, accountStatus: true },
    });

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if the email has a timestamp (soft deleted)
    const timestampPattern = /_(\d+)$/;
    const isSoftDeleted = timestampPattern.test(targetUser.email);
    let updatedEmail = targetUser.email;

    if (isSoftDeleted) {
      // Restore the original email (remove timestamp)
      updatedEmail = targetUser.email.replace(timestampPattern, "");
    } else {
      // Soft delete: Append timestamp
      const timestamp = new Date().getTime();
      updatedEmail = `${targetUser.email}_${timestamp}`;
    }

    // Toggle account status
    const newStatus = isSoftDeleted
      ? AccountStatus.accepted
      : AccountStatus.disabled;

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        email:
          newStatus === AccountStatus.disabled
            ? updatedEmail
            : targetUser.email.replace(timestampPattern, ""),
        accountStatus: newStatus,
        deactivationDate: new Date(),
      },
    });

    if (updatedUser?.accountStatus === AccountStatus.disabled) {
      await emailDisabled(updatedUser.email, updatedUser);
      await handleAgencyDisablement(updatedUser.id);
      const io = getIO();
      io.to(updatedUser.id).emit("userRequestResponse", {
        accountStatus: updatedUser.accountStatus,
        forceLogout: true,
      });
      io.to(userId).emit("accountStatusChanged", {
        accountStatus: updatedUser.accountStatus,
        forceLogout: true,
      });
      io.to(userId).emit("sessionExpiration", {
        accountStatus: updatedUser.accountStatus,
        forceLogout: true,
      });
      clearSessionExpiration(userId);
    }

    // Return the deleted user
    return res.status(200).json({
      success: true,
      message: `User has been ${isSoftDeleted ? "restored" : "deleted"} successfully`,
      results: updatedUser,
      forceLogout: updatedUser?.accountStatus === AccountStatus.disabled,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Soft delete user error", {
      message: err.message,
      stack: err.stack,
    });

    // Return 500 error if something went wrong
    return res.status(500).json({
      success: false,
      message: "Failed to soft delete user. Please try again later.",
    });
  }
};

/**
 * Hard deletes a single user.
 *
 * @remarks
 * Only master users can access this endpoint.
 * The user is deleted from the database and their address is also deleted.
 * @param req - The authenticated request object.
 * @param res - The response object.
 * @returns The response with the deleted user or an error message.
 */
export const hardDeleteSingleUser = async (
  req: AuthRequest,
  res: Response
): Promise<Response> => {
  const { userId } = req.params;
  try {
    // Authorize the user and check their role
    const user = await getMasterAccess(req, res);

    // Check if the user exists
    const userToBeDeleted = await prisma.user.findFirst({
      where: { id: userId },
    });

    // Return 404 if user doesn't exist
    if (!userToBeDeleted) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if user is a master account
    if (userToBeDeleted.role === "master") {
      return res.status(403).json({
        success: false,
        message: "Master accounts cannot be deactivated",
      });
    }

    // Delete the user and its address
    const deletedUser = await prisma.$transaction(async (transaction: any) => {
      // If the user has an address delete it
      if (userToBeDeleted.addressId) {
        await transaction.userAddress.delete({
          where: { id: userToBeDeleted.addressId },
        });
      }

      const agents = await prisma.agencyAgent.findMany({
        where: { agencyId: userToBeDeleted.id },
      });
      const io = getIO();
      // Invalidate all agent sessions
      for (const agent of agents) {
        // Emit session expiration event
        io.to(agent.id).emit("sessionExpiration", {
          message: "Your agency account has been deleted.",
          forceLogout: true,
        });

        // Cancel the session expiration timer
        clearSessionExpiration(agent.id as string);
      }

      // Emit session expiration event for the main user
      io.to(userToBeDeleted.id).emit("sessionExpiration", {
        message: "Your agency account has been deleted.",
        forceLogout: true,
      });

      // Cancel the session expiration timer
      clearSessionExpiration(userToBeDeleted.id as string);

      // Finally delete the user
      return await transaction.user.delete({
        where: { id: userToBeDeleted.id },
      });
    });
    return res.status(200).json({
      success: true,
      message: "User has been permanently deleted",
      results: deletedUser,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Hard delete user error", {
      message: err.message,
      stack: err.stack,
    });

    return res.status(500).json({
      success: false,
      message: "Failed to delete user. Please try again later.",
    });
  }
};

// Search all users
/**
 * Searches all users except the master user by searching the email,
 * username, firstName, lastName, username, and agencyName.
 * @param req - The request object containing the user's authorization token
 * @param res - The response object to send the results
 * @param searchInput - The search input string
 * @param pageSize - The number of users to return per page
 * @param cursor - The last user id to continue searching from
 * @returns A JSON response with the success status and the retrieved users.
 * The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: {
 *     users: User[],
 *     totalUsers: number,
 *     nextCursor: string | null
 *   }
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const searchAllUsers = async (req: AuthRequest, res: Response) => {
  const searchInput = req.query.input;
  const pageSize = parseInt(req.query.pageSize as string, 10) || 10;
  const cursor = req.query.cursor ? (req.query.cursor as string) : undefined;
  const accountStatus = req.query.accountStatus as string;

  try {
    // authorize the user
    const user = await getMasterAccess(req, res);

    // // Check if searchInput exists and is a string
    if (!searchInput || typeof searchInput !== "string") {
      return res.status(400).json({
        success: false,
        message: "Search input is required and must be a string",
      });
    }

    const parsed = JSON.parse(searchInput) as SearchInputType;
    const searchQuery = parsed.searchQuery
      ? parsed.searchQuery.toLowerCase()
      : "";

    // Validate the ticketStatus query parameter
    const accountStatusValues = [
      "all",
      "accepted",
      "pending",
      "rejected",
      "suspended",
      "deactivated",
      "disabled",
    ];
    if (
      accountStatus &&
      !accountStatusValues.includes(accountStatus as string)
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid accountStatus query parameter",
      });
    }

    // Create date ranges based on registrationDateFilter
    const registrationDateRange = parsed.registrationDateFilter
      ? getCreatedTimeRange(parsed.registrationDateFilter) // Assuming this helper function works for date ranges
      : {};

    // Create date ranges based on lastLoginFilter
    const lastLoginDateRange = parsed.lastLoginFilter
      ? getCreatedTimeRange(parsed.lastLoginFilter)
      : {};

    const searchUsersOptions: any = {
      AND: [
        {
          id: {
            not: req.user?.id,
          },
        },
        // only get the verified users
        { verified: true },
      ],
      NOT: [
        { role: "master" }, // Exclude users with the role of "master"
      ],
    };

    // Handle accountType filters
    if (parsed.accountType !== "all") {
      searchUsersOptions.AND.push({ role: parsed.accountType });
    } else {
      searchUsersOptions.AND.push({ role: { in: ["agency", "affiliate"] } });
    }

    // Handle subscriptionStatus filters
    if (parsed.subscriptionStatus !== "all") {
      searchUsersOptions.AND.push({
        subscriptionStatus: parsed.subscriptionStatus,
      });
    }
    // Handle accountStatus filter only if it's not "all"
    if (accountStatus && accountStatus !== "all") {
      searchUsersOptions.AND.push({ accountStatus });
    }

    // Add registration date filters
    if (registrationDateRange) {
      searchUsersOptions.AND.push({ createdAt: registrationDateRange });
    }

    // Add last login date filters
    if (lastLoginDateRange) {
      searchUsersOptions.AND.push({ lastLogin: lastLoginDateRange });
    }

    // Create the OR conditions for search queries
    const searchConditions = [
      {
        email: {
          contains: searchQuery,
          mode: "insensitive",
        },
      },
      {
        firstName: {
          contains: searchQuery,
          mode: "insensitive",
        },
      },
      {
        lastName: {
          contains: searchQuery,
          mode: "insensitive",
        },
      },
      {
        username: {
          contains: searchQuery,
          mode: "insensitive",
        },
      },
      {
        agencyName: {
          contains: searchQuery,
          mode: "insensitive",
        },
      },
    ];

    // Combine AND and OR conditions
    searchUsersOptions.AND.push({
      OR: searchConditions,
    });

    // search according to email, username, firstName, lastName, username, agencyName
    const [users, totalUsers] = await Promise.all([
      await prisma.user.findMany({
        where: searchUsersOptions,
        include: userSelector,
        take: pageSize,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: {
          id: "asc",
        },
      }),
      await prisma.user.count({
        where: searchUsersOptions,
      }),
      req.user?.id
        ? prisma.user.update({
            where: { id: req.user.id },
            data: {
              lastLogin: new Date(),
            },
          })
        : Promise.resolve(null),
    ]);

    const usersToUpdate = users.filter((user: any) => !user.refId);
    const updatePromises = usersToUpdate.map(async (user: any) => {
      const refId = await generateUserRefId();
      return prisma.user.update({
        where: { id: user.id },
        data: { refId },
      });
    });

    await Promise.all(updatePromises);

    // get the last user id
    const nextCursor =
      users.length === pageSize ? users[users.length - 1].id : null;

    // return user
    return res.status(200).json({
      success: true,
      results: {
        users,
        totalUsers,
        nextCursor,
      },
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: search all users info error", {
      message: err.message,
      stack: err.stack,
    });

    return res.status(500).json({
      success: false,
      message: "Failed to get delete user's info. Please try again later.",
    });
  }
};

/**
 * Updates user information for a master user.
 *
 * @param req - The request object containing the user's authorization token and the updated user information.
 * @param res - The response object to send the result.
 * @returns A JSON response with the success status and the updated user information.
 * The response object will have the following structure:
 * {
 *   success: boolean,
 *   results: User,
 * }
 * where `User` is the shape of a user object returned by Prisma.
 */
export const updateUserInfoForMaster = async (
  req: AuthRequest,
  res: Response
) => {
  // Destructure the request body and userId from the request parameters
  const input = req.body;
  const { userId } = req.params;

  try {
    // validate the first and last name
    const validatedData = validateUsers({
      firstName: input.firstName,
      lastName: input.lastName,
    });

    // Check if the phone number starts with a '+' sign, and prepend it if not
    if (input?.phoneNumber[0] !== "+") {
      input.phoneNumber = "+" + input.phoneNumber.toString();
    }
    // Authorize the user
    const user = await getMasterAccess(req, res);

    // Find the user info
    const userInfo = await prisma.user.findUnique({
      where: { id: userId },
      include: userSelector,
    });

    // If the user info is not found, return a 404 error
    if (!userInfo) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    // Validate the inputs
    const { error } = userUpdateByMasterValidation.validate(input, {
      abortEarly: false,
    });

    // // If there are validation errors, return them as a 400 error
    // if (error) {
    //   const errorDetails = error.details.reduce(
    //     (acc, detail) => {
    //       acc[detail.path.join(".")] = detail.message;
    //       return acc;
    //     },
    //     {} as Record<string, string>
    //   );

    //   return res
    //     .status(400)
    //     .json({ success: false, validationError: errorDetails });
    // }
    let validationError = { ...validatedData.validationErrors };

    if (error) {
      error.details.forEach((detail) => {
        if (detail.context && detail.context.key) {
          validationError[detail.context.key] = detail.message;
        }
      });
    }

    // Return if there are any validation errors
    if (Object.keys(validationError).length > 0) {
      return res.status(400).json({
        success: false,
        validationError,
      });
    }

    // Update the user info
    const updatedUserInfo = await prisma.user.update({
      where: { id: userId },
      data: {
        // Update user fields
        refId: input.refId?.trim().toLowerCase(),
        addressId: input.addressId?.trim().toLowerCase(),
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        username: input.username?.trim().toLowerCase(),
        agencyName: input.agencyName ? capitalize(input.agencyName) : null,
        email: input.email?.trim().toLowerCase(),
        phoneNumber: input.phoneNumber?.trim(),
        nationality: input.nationality?.trim().toLowerCase(),
        dateOfBirth: input.dateOfBirth?.trim(),
        gender: input.gender?.trim().toLowerCase(),
        logo: input.logo?.trim(),
        role: input.role?.trim().toLowerCase(),
        roleType: input.roleType?.trim().toLowerCase(),
        iataNo: input.iataNo?.trim(),
        commercialOperationNo: input.commercialOperationNo?.trim(),
        website: input.website?.toLowerCase().trim(),
        deactivationDate: input.deactivationDate || null,
        subscriptionStatus: input.subscriptionStatus,
        lastLogin: input.lastLogin || null,
        address: {
          update: {
            // Update address fields
            ...input.address,
            country: capitalize(input.address?.country),
            city: capitalize(input.address?.city),
            street: input.address?.street?.trim(),
          },
        },
      },
      include: userSelector,
    });

    // Update all agents' agency names
    if (input.agencyName) {
      const updatedAgents = await prisma.agencyAgent.updateMany({
        where: { agencyId: userId },
        data: {
          agencyName: capitalize(input.agencyName)
        }
      });
      console.log(`Updated agency name for ${updatedAgents.count} agents`);
    }

    const io = getIO();
    io.to(userId).emit("updateUserProfile");

    // Invalidate all sessions for the agency and its agents
    await invalidateAgencySessions(userId);

    // Return the updated user info
    return res.status(200).json({
      success: true,
      message: "Your account details have been updated by our support team.",
      results: updatedUserInfo,
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Update user info error", {
      message: err.message,
      stack: err.stack,
    });

    // Return 500 error if something went wrong
    return res.status(500).json({
      success: false,
      message: "Failed to get update user's info. Please try again later.",
    });
  }
};

/**
 * Updates the password for a user with the specified userId.
 *
 * @param req - The request object containing the password and confirmPassword in the request body,
 * and the userId in the request parameters.
 * @param res - The response object used to send the result of the update operation.
 * @returns A response object with the success status and the updated user information if the update is successful,
 * or an error message if the update fails.
 */
export const updateUserPasswordForMaster = async (
  req: AuthRequest,
  res: Response
) => {
  // Destructure the request body and userId from the request parameters
  const { password, confirmPassword } = req.body;
  const { userId } = req.params;

  try {
    // Authorize the user
    const user = await getMasterAccess(req, res);

    // Find the user info
    const userInfo = await prisma.user.findUnique({
      where: { id: userId },
      include: userSelector,
    });

    // If the user info is not found, return a 404 error
    if (!userInfo) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    // Validate the new password
    const schema = Joi.object({
      password: Joi.string()
        .empty()
        .pattern(new RegExp("(?=.*[a-z])")) // at least one lowercase letter
        .pattern(new RegExp("(?=.*[A-Z])")) // at least one uppercase letter
        .pattern(new RegExp("(?=.*[0-9])")) // at least one digit
        .pattern(new RegExp("(?=.*[!@#$%^&*])")) // at least one special character
        .min(8) // minimum length 8
        .max(30) // maximum length 30
        .required()
        .messages({
          "string.empty": "Password cannot be empty",
          "string.pattern.base":
            "Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character",
          "string.min":
            "Please make sure your password is at least 8 characters long",
          "string.max": "Password must be at most 30 characters long",
          "any.required": "Please enter your password",
        }),
      confirmPassword: Joi.string()
        .valid(Joi.ref("password"))
        .required()
        .custom(() => {})
        .messages({
          "any.only": "Password and Confirm Password must match",
          "string.empty": "Confirm Password cannot be empty",
          "any.required": "Please enter your confirm password",
        }),
    });

    const { error } = schema.validate({
      password,
      confirmPassword,
    });
    if (error) {
      const validationErrors = error.details.reduce((acc: any, curr: any) => {
        acc[curr.path[0]] = curr.message;
        return acc;
      }, {});

      return res
        .status(400)
        .json({ success: false, validationError: validationErrors });
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update the user's password
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        hashedPassword: hashedPassword,
      },
      include: userSelector,
    });

    await passwordUpdateSuccess(updatedUser.email, updatedUser);

    // If all correct, return success
    return res.status(200).json({
      success: true,
      results: updatedUser,
      message: "Password changed successfully",
    });
  } catch (error) {
    const err = error as Error;
    console.error("Master user: Update user password error", {
      message: err.message,
      stack: err.stack,
    });

    // Return 500 error if something went wrong
    return res.status(500).json({
      success: false,
      message: "Failed to update user password. Please try again later.",
    });
  }
};
