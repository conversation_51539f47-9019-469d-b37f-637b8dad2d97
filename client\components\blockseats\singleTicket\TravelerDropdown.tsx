"use client";
import { useEffect, useRef, useState } from "react";
import { Check, ChevronDown } from "lucide-react";
import { TravelerType } from "@/utils/definitions/blockSeatsDefinitions";

interface OptionType {
  value: string;
  label: string;
}

interface TravelerDropdownProps {
  id: string;
  label: string;
  value: string; // The actual selected value
  onChange: (value: string) => void;
  options: string[] | OptionType[];
  required?: boolean;
  error?: string;
  travelerNumber?: number;
  readOnly?: boolean;
  showRedBorder?: boolean; // New prop for red border
}

const TravelerDropdown = ({
  id,
  label,
  value,
  onChange,
  options,
  required = true,
  error,
  travelerNumber = 1,
  readOnly = false,
  showRedBorder = false,
}: TravelerDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedValue, setSelectedValue] = useState(value || "");
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update internal state when external value changes
  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  // Handle clicks outside the component
  useEffect(() => {
    // Only add the event listener if the dropdown is open
    // This prevents any interference when trying to open the dropdown
    if (!isOpen) return;

    const handleOutsideClick = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    // Use mousedown to catch clicks before they trigger other events
    document.addEventListener("mousedown", handleOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isOpen]); // Re-run when isOpen changes

  // Convert options to consistent format
  const normalizedOptions = options.map((option) =>
    typeof option === "string" ? { value: option, label: option } : option
  );

  // Filter options based on search term
  const filteredOptions = normalizedOptions.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle option selection
  const handleSelect = (option: string) => {
    if (readOnly) return;
    setSelectedValue(option);
    setSearchTerm("");
    setIsOpen(false);
    onChange(option);
  };

  // Handle input focus - don't toggle dropdown here to avoid conflicts
  const handleFocus = () => {
    // Only set to true, never to false on focus
    setIsOpen(true);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setIsOpen(true);
  };

  // Handle input click - explicitly open the dropdown
  const handleInputClick = (e: React.MouseEvent) => {
    // Prevent event bubbling to avoid double-triggering with the div's onClick
    e.stopPropagation();
    setIsOpen(true);
    inputRef.current?.focus();
  };

  return (
    <div ref={containerRef} className="relative">
      <label
        htmlFor={id}
        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        {label} {travelerNumber === 1 ? "" : travelerNumber}{" "}
        {required && <span className="text-red-500">*</span>}
      </label>

      <div className="relative inline-flex w-full">
        {/* Input field */}
        <div className="relative w-full">
          <input
            ref={inputRef}
            id={id}
            type="text"
            value={
              isOpen
                ? searchTerm
                : normalizedOptions.find((opt) => opt.value === selectedValue)
                    ?.label || selectedValue
            }
            onChange={handleInputChange}
            onFocus={handleFocus}
            onClick={handleInputClick}
            placeholder={`Select ${label}`}
            autoComplete="off"
            disabled={readOnly}
            className={`w-full p-2 pr-10 h-[40px] bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 rounded-lg focus:outline-none foucs:border-none focus:ring-2 focus:ring-red-500 ${
              showRedBorder || error
                ? "ring-2 ring-red-500 border-0 border-none"
                : "border border-gray-300 dark:border-gray-600"
            }${readOnly ? " cursor-not-allowed opacity-70" : ""}`}
          />
          <div
            className="absolute right-0 top-0 h-full flex items-center px-3 cursor-pointer"
            onClick={handleInputClick}
          >
            <ChevronDown
              className="text-gray-500 dark:text-gray-400"
              size={20}
            />
          </div>
        </div>

        {/* Dropdown menu */}
        {isOpen && (
          <div className="z-[9999] absolute top-full left-0 w-full max-h-60 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 py-1.5 rounded-lg shadow-lg overflow-hidden flex flex-col mt-1">
            <div className="font-medium text-sm text-gray-600 dark:text-gray-300 focus:outline-none overflow-y-auto custom-scrollbar flex-grow">
              {filteredOptions.length === 0 && (
                <div className="text-center py-3 px-3 text-gray-500 dark:text-gray-400">
                  <span>
                    {searchTerm ? "No Results Found" : "Type to search..."}
                  </span>
                </div>
              )}

              {filteredOptions.map((option) => {
                const isSelected = selectedValue === option.value;

                return (
                  <button
                    key={option.value}
                    type="button"
                    className={`flex items-center justify-between text-sm md:text-base w-full py-2 px-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0 ${
                      isSelected ? "text-red-500 font-semibold" : ""
                    }`}
                    onMouseDown={() => handleSelect(option.value)}
                  >
                    <div className="text-start text-base">
                      <div className="font-medium">{option.label}</div>
                    </div>
                    <Check
                      className={`shrink-0 mr-2 text-red-500 ${
                        isSelected ? "opacity-100" : "opacity-0"
                      }`}
                      size={20}
                    />
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );
};

export default TravelerDropdown;
