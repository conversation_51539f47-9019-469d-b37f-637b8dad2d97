"use client";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimeField } from "@mui/x-date-pickers/TimeField";
import dayjs from "dayjs";
import { useState, useEffect } from "react";
import { MasterTicketResultType } from "@/utils/definitions/masterDefinitions";

// Function to calculate duration between two times
const calculateDuration = (start: string, end: string) => {
  if (!start || !end) return "";
  const startTime = new Date(start).getTime();
  const endTime = new Date(end).getTime();
  if (isNaN(startTime) || isNaN(endTime)) return "";
  const diffMs = endTime - startTime;
  if (diffMs < 0) return "";
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  return `${diffHours}h ${diffMinutes}m`;
};

// Helper to update main card times/duration from segments
const updateMainCardTimesFromSegments = (ticketObj: any) => {
  if (!ticketObj.segments || ticketObj.segments.length === 0) return ticketObj;

  // Get the flight date (date part only)
  const flightDatePart = ticketObj.flightDate.split('T')[0];

  // Function to ensure time uses the correct date
  const ensureCorrectDate = (timeStr: string) => {
    if (!timeStr) return timeStr;
    const timePart = timeStr.split('T')[1];
    return timePart ? `${flightDatePart}T${timePart}` : timeStr;
  };

  // Find earliest departure and latest arrival
  const segments = ticketObj.segments;

  // Ensure all segment times use the correct date
  segments.forEach((seg: any) => {
    seg.departureTime = ensureCorrectDate(seg.departureTime);
    seg.arrivalTime = ensureCorrectDate(seg.arrivalTime);
  });

  // Assume segments are sorted, but just in case:
  const sortedByDep = [...segments].sort((a, b) => new Date(a.departureTime).getTime() - new Date(b.departureTime).getTime());
  const sortedByArr = [...segments].sort((a, b) => new Date(a.arrivalTime).getTime() - new Date(b.arrivalTime).getTime());

  const firstDep = sortedByDep[0].departureTime;
  const lastArr = sortedByArr[sortedByArr.length - 1].arrivalTime;

  // Set the main card times with the correct date
  ticketObj.departureTime = firstDep;
  ticketObj.arrivalTime = lastArr;
  ticketObj.duration = calculateDuration(firstDep, lastArr);

  return ticketObj;
};

export default function SegmentFlightTime({
  segmentIndex,
  act,
  ticket,
  setUpdatedTicket,
}: {
  segmentIndex: number;
  act: "departureTime" | "arrivalTime";
  ticket: MasterTicketResultType;
  setUpdatedTicket: React.Dispatch<React.SetStateAction<MasterTicketResultType | null>>;
}) {
  // Get the current segment safely
  const segments = ticket?.segments || [];
  const segment = segments[segmentIndex] || {};

  // Handle time change
  const handleTimeChange = (newTime: any) => {
    if (newTime) {
      // Get the time part only (HH:mm:ss.SSS)
      const timeOnly = dayjs(newTime).format("HH:mm:ss.SSS");

      // Update the ticket state with the new time
      setUpdatedTicket((prev) => {
        if (!prev) return null;

        // Create a new ticket object with updated segments
        const newTicket = { ...prev };
        newTicket.segments = [...newTicket.segments];

        // Get the flight date from the ticket (use the date part only)
        const flightDatePart = newTicket.flightDate.split('T')[0];

        // Combine flight date with the new time
        const formattedTime = `${flightDatePart}T${timeOnly}`;

        // Update the specific segment's time
        newTicket.segments[segmentIndex] = {
          ...newTicket.segments[segmentIndex],
          [act]: formattedTime,
        };

        // Update the segment's duration
        const depTime = act === 'departureTime' ? formattedTime : newTicket.segments[segmentIndex].departureTime;
        const arrTime = act === 'arrivalTime' ? formattedTime : newTicket.segments[segmentIndex].arrivalTime;
        newTicket.segments[segmentIndex].duration = calculateDuration(depTime, arrTime);

        // Update the main card times and duration
        return updateMainCardTimesFromSegments(newTicket);
      });
    }
  };

  return (
    <>
      {/* TIME */}
      <div className="flex-1 w-full">
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <TimeField
            className="custom-time-field w-full"
            format="HH:mm"
            value={segment && segment[act] ? dayjs(segment[act]) : null}
            onChange={handleTimeChange}
          />
        </LocalizationProvider>
      </div>
    </>
  );
}
