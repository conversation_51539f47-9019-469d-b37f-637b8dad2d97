import * as XLSX from "xlsx";
import { getFormatDateTable, getFormatTime } from "./functions";

// Constants for trip status calculation
const TRIP_STATUS = {
  UPCOMING: "Upcoming",
  COMPLETED: "Completed",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
  IN_PROGRESS: "In Progress",
  CANCELLED: "Cancelled",
};

const BOOKING_STATUS = {
  QUICK_HOLD: "QUICK_HOLD",
  TIMED_OUT: "TIMED_OUT",
  PENDING_APPROVAL: "PENDING_APPROVAL",
  BOOKING_CONFIRMED: "BOOKING_CONFIRMED",
  BOOKING_REJECTED: "BOOKING_REJECTED",
  CANCELLED_BY_USER: "CANCELLED_BY_USER",
  CANCELLED_BY_SYSTEM: "CANCELLED_BY_SYSTEM",
};

const SALE_STATUS = {
  COMPLETED: "Completed",
  REFUNDED: "Refunded",
  PENDING: "Pending",
  NOT_VALID: "Not Valid",
};

// Constants for sale source
const SALE_SOURCE = {
  INTERNAL: "Internal",
  THIRD_PARTY: "Third-Party",
};

/**
 * Exports data to a CSV file and triggers a download
 *
 * @param data - Array of objects to export
 * @param filename - Name of the file to download (without extension)
 * @param sheetName - Name of the worksheet in the Excel file
 */
export const exportToCSV = (
  data: any[],
  filename: string = "export",
  sheetName: string = "Sheet1"
) => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Convert data to worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // Generate the file and trigger download
    XLSX.writeFile(workbook, `${filename}.csv`);

    return true;
  } catch (error) {
    console.error("Error exporting data to CSV:", error);
    return false;
  }
};

/**
 * Validates booking status based on source
 */
export function validateBookingStatus(bookingAction: string, source: string) {
  if (
    source === "Third-Party" &&
    bookingAction === BOOKING_STATUS.BOOKING_REJECTED
  ) {
    return BOOKING_STATUS.CANCELLED_BY_USER; // Default to cancelled instead
  }
  return bookingAction;
}

/**
 * Validates sale status based on booking status
 */
const validateSaleStatus = (status: string, saleStatus: string = "") => {
  // If booking is timed out or rejected, sale status should be Not Valid
  if (
    status === BOOKING_STATUS.TIMED_OUT ||
    status === BOOKING_STATUS.BOOKING_REJECTED
  ) {
    return SALE_STATUS.NOT_VALID;
  }

  // If Booking Status is Quick Hold or Pending Approval, Sale Status is typically Pending
  if (
    status === BOOKING_STATUS.QUICK_HOLD ||
    status === BOOKING_STATUS.PENDING_APPROVAL
  ) {
    return SALE_STATUS.PENDING;
  }

  // If Booking Status is Confirmed, Sale Status is typically Completed
  if (status === BOOKING_STATUS.BOOKING_CONFIRMED) {
    return SALE_STATUS.COMPLETED;
  }

  // If Booking Status is Cancelled, Sale Status may be Refunded
  if (
    status === BOOKING_STATUS.CANCELLED_BY_USER ||
    status === BOOKING_STATUS.CANCELLED_BY_SYSTEM
  ) {
    return SALE_STATUS.REFUNDED;
  }

  // For other booking statuses, keep the original sale status
  return saleStatus || SALE_STATUS.COMPLETED;
};

/**
 * Determines trip status based on booking status and flight date
 */
const determineTripStatus = (
  bookingStatus: string,
  flightDateStr: string,
  fallbackStatus: string,
  saleStatus: string,
  source: string
) => {
  // If date is not in the expected format, return NOT_VALID
  if (!flightDateStr || flightDateStr === "-") {
    return TRIP_STATUS.NOT_VALID;
  }

  const today = new Date();

  // Parse the flight date
  let flightDate: Date;
  try {
    // Try to parse the date in various formats
    if (flightDateStr.includes("/")) {
      // Handle DD/MM/YYYY format
      const [day, month, year] = flightDateStr.split("/").map(Number);
      flightDate = new Date(year, month - 1, day);
    } else {
      // Try standard date parsing
      flightDate = new Date(flightDateStr);
    }

    // Check if the date is valid
    if (isNaN(flightDate.getTime())) {
      return TRIP_STATUS.NOT_VALID;
    }
  } catch (error) {
    return TRIP_STATUS.NOT_VALID;
  }

  // Compare dates
  const isPast = flightDate < today;
  const isToday = flightDate.toDateString() === today.toDateString();
  const isFuture = flightDate > today;

  // If sale status is Refunded, trip status is Not Valid
  if (saleStatus === SALE_STATUS.REFUNDED) {
    return TRIP_STATUS.NOT_VALID;
  }

  // Map booking statuses to trip statuses
  switch (bookingStatus) {
    case BOOKING_STATUS.BOOKING_CONFIRMED:
      if (isPast) return TRIP_STATUS.COMPLETED;
      if (isToday) return TRIP_STATUS.IN_PROGRESS;
      if (isFuture) return TRIP_STATUS.UPCOMING;
      break;

    case BOOKING_STATUS.PENDING_APPROVAL:
    case BOOKING_STATUS.QUICK_HOLD:
      return TRIP_STATUS.PENDING;

    case BOOKING_STATUS.BOOKING_REJECTED:
    case BOOKING_STATUS.TIMED_OUT:
      return TRIP_STATUS.NOT_VALID;

    case BOOKING_STATUS.CANCELLED_BY_USER:
    case BOOKING_STATUS.CANCELLED_BY_SYSTEM:
      return TRIP_STATUS.CANCELLED;

    default:
      return TRIP_STATUS.NOT_VALID;
  }

  // Fallback
  return TRIP_STATUS.NOT_VALID;
};

/**
 * Formats booking data for export
 *
 * @param bookings - Array of booking objects
 * @returns Formatted array ready for export
 */
export const formatBookingsForExport = (bookings: any[]) => {
  return bookings.map((booking) => {
    // Format dates to DD/MM/YYYY
    const formattedFlightDate =
      booking.date && booking.date !== "-"
        ? getFormatDateTable(booking.date)
        : booking.date;

    const formattedIssuedOn =
      booking.issuedOn && booking.issuedOn !== "-"
        ? getFormatDateTable(booking.issuedOn)
        : booking.issuedOn;

    // Calculate trip status if not already present
    let tripStatus = booking.tripStatus;
    if (!tripStatus) {
      const validBookingAction = validateBookingStatus(
        booking.status,
        booking.source
      );
      const validSaleStatus = validateSaleStatus(
        validBookingAction,
        booking.saleStatus
      );

      tripStatus = determineTripStatus(
        validBookingAction,
        formattedFlightDate,
        booking.status,
        validSaleStatus,
        booking.source
      );
    }

    return {
      ID: booking.reference || "",
      "Flight Date": formattedFlightDate || "",
      Passenger: booking.passenger || "",
      Route: booking.route || "",
      "Trip Type": booking.tripType || "",
      Carrier: booking.airline || "",
      Price: booking.price || "",
      "Trip Status": tripStatus || "",
      "Booking Status": booking.status || "",
      "Sale Status": booking.saleStatus || "",
      "Booking Source": booking.source || "",
      Agency: booking.agency || "",
      Agent: booking.agent || "",
      "Issued On": formattedIssuedOn || "",
    };
  });
};

/**
 * Formats sales data for export
 *
 * @param sales - Array of sale objects
 * @returns Formatted array ready for export
 */
export const formatSalesForExport = (sales: any[]) => {
  return sales.map((sale) => {
    // Format dates to DD/MM/YYYY
    const formattedFlightDate =
      sale.flightDate && sale.flightDate !== "-"
        ? getFormatDateTable(sale.flightDate)
        : sale.flightDate;

    const formattedSaleDate =
      sale.saleDate && sale.saleDate !== "-"
        ? getFormatDateTable(sale.saleDate)
        : sale.saleDate;

    // Normalize source for consistent comparison
    const normalizedSource = sale.source || "";

    // Handle payment method the same way as in the SaleTableRow component
    const displayPaymentMethod =
      normalizedSource === SALE_SOURCE.THIRD_PARTY
        ? "Airvilla Wallet"
        : sale.paymentMethod || "";

    // Handle agent display the same way as in the SaleTableRow component
    const displayAgent =
      normalizedSource === SALE_SOURCE.THIRD_PARTY
        ? "External Agent"
        : sale.agent || "";

    return {
      ID: sale.reference || "",
      "Flight Date": formattedFlightDate || "",
      Passenger: sale.passenger || "",
      Route: sale.route || "",
      "Trip Type": sale.tripType || "",
      Carrier: sale.carrier || "",
      "Sale Price": sale.price || "",
      "Sale Status": sale.status || "",
      "Sale Source": sale.source || "",
      Agent: displayAgent,
      "Payment Method": displayPaymentMethod,
      "Sale Date": formattedSaleDate || "",
    };
  });
};

/**
 * Formats manifest data for export
 *
 * @param manifests - Array of manifest objects
 * @returns Formatted array ready for export
 */
export const formatManifestForExport = (manifests: any[]) => {
  return manifests.map((manifest) => {
    // Format dates to DD/MM/YYYY
    const formattedFlightDate =
      manifest.flightDate && manifest.flightDate !== "-"
        ? getFormatDateTable(manifest.flightDate)
        : manifest.flightDate;

    return {
      "Manifest ID": manifest.reference || "",
      "Flight Number": manifest.flightNumber || "",
      "Flight Date": formattedFlightDate || "",
      Route:
        `${manifest.departure.airportCode} => ${manifest.arrival.airportCode}` ||
        manifest.route ||
        "",
      "Departure Time": getFormatTime(manifest.departureTime) || "",
      "Arrival Time": getFormatTime(manifest.arrivalTime) || "",
      Carrier: manifest.carrier || "",
      Status: manifest.status || "",
      "Time Until Flight":
        manifest.timeUntilFlight ||
        calculateTimeUntilFlight(formattedFlightDate) ||
        "",
      "Passenger Count": manifest.passengerCount || "",
    };
  });
};

export const exportManifestToCSV = (
  data: any[],
  filename: string = "export"
) => {
  try {
    if (!data || data.length === 0) {
      console.warn("No data to export");
      return false;
    }

    // Get headers from the first object's keys
    const headers = Object.keys(data[0]);

    // Create CSV content
    let csvContent = [
      headers.join(","), // Header row
      ...data.map((row) =>
        headers
          .map(
            (fieldName) =>
              `"${String(row[fieldName] || "").replace(/"/g, '""')}"`
          )
          .join(",")
      ),
    ].join("\r\n");

    // Create a Blob with the CSV data
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

    // Create a download link
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    // Set the download attributes
    link.setAttribute("href", url);
    link.setAttribute("download", `${filename}.csv`);
    link.style.visibility = "hidden";

    // Append to body, click and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    return true;
  } catch (error) {
    console.error("Error exporting data to CSV:", error);
    return false;
  }
};

// Helper function to calculate time until flight if not provided
const calculateTimeUntilFlight = (flightDate: string): string => {
  if (!flightDate) return "";

  try {
    // Check if flightDate is in DD/MM/YYYY format
    const dateParts = flightDate.split("/");
    if (dateParts.length !== 3) return "";

    const [day, month, year] = dateParts.map(Number);
    
    // Create date objects in local timezone for accurate day calculation
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const flightDay = new Date(year, month - 1, day);
    
    // Calculate the difference in days
    const timeDiff = flightDay.getTime() - today.getTime();
    const diffDays = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    
    // If flight is in the past, return empty string
    
    // Calculate remaining hours and minutes
    const nowTime = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds();
    const remainingSeconds = 24 * 3600 - nowTime;
    // For today's flight, calculate remaining hours and minutes
    const diffHours = Math.floor(remainingSeconds / 3600);
    const diffMins = Math.floor((remainingSeconds % 3600) / 60);
    
    if (diffDays < 0) return `${diffDays}d ${diffHours}h ${diffMins}m`;
    
    if (diffDays === 0) {
      
      // If less than a minute remains, return empty string
      if (diffHours <= 0 && diffMins <= 0) return "";
      
      if (diffHours > 0) return `${diffHours}h ${diffMins}m`;
      return `${diffMins}m`;
    } else {      
      // If it's more than 1 day away, show days and hours
      if (diffDays > 1) {
        return `${diffDays - 1}d ${diffHours}h`;
      }
      // If it's exactly 1 day away, just show hours
      return `${diffHours + 24}h`;
    }
  } catch (e) {
    console.error("Error calculating time until flight:", e);
    return "";
  }
};
