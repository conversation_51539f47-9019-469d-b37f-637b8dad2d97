"use client";
import React, { Suspense, useEffect, useState, useRef } from "react";
import ProgressLoading from "../../utils/ProgressLoading";
import MasterUsersTab from "../users/MasterUsersTab";
import useAuth from "@/components/hooks/useAuth";

import {
  AccountStatusEnum,
  TeamMemberDataType,
} from "@/utils/definitions/masterDefinitions";
import {
  fetchDeleteUsersForMaster,
  softDeleteUser,
  fetchUserRequestForMaster,
} from "@/lib/data/masterUsersData";
import { useInView } from "react-intersection-observer";
import SearchUsersMasterBar from "../users/SearchUsersMasterBar";
import { setLoading } from "@/redux/features/LoadingSlice";
import ListLoading from "@/components/flight-tickets/myTickets/ListLoading";
import { useDebouncedCallback } from "use-debounce";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { selectUser } from "@/redux/features/AuthSlice";
import Tabs from "@/components/extra-components/Tabs";
import FilterClassDropdown from "../tickets-overview/FilterDropdown";
import DateFilter from "@/components/master-control/users/DateFilter";
import { PlusCircle, Users } from "lucide-react";
import AddTeamMember from "./AddTeamMember";
import {
  fetchAllSearchTeamMembers,
  fetchAllTeamMembers,
  getTeamId,
} from "@/lib/data/teamData";
import TeamManagementTableList from "./TeamManagementList";
import { TeamMember } from "@/utils/definitions/teamDefinitions";

const accountTypeTypes = [
  { id: 0, value: "all" },
  { id: 1, value: "admin" },
  { id: 2, value: "moderator" },
  { id: 3, value: "accountant" },
];

const departmentType = [
  { id: 0, value: "all", enumValue: "all" },
  { id: 1, value: "customer support", enumValue: "customer_support" },
  { id: 2, value: "management", enumValue: "management" },
  { id: 3, value: "finance", enumValue: "finance" },
  { id: 4, value: "marketing", enumValue: "marketing" },
  { id: 5, value: "sales", enumValue: "sales" },
  { id: 6, value: "it", enumValue: "it" },
  { id: 7, value: "operations", enumValue: "operations" },
];

const tableHeaders = [
  "Employee ID",
  "Full Name",
  "Email",
  "Role",
  "Department",
  "Login Status",
  "Date Added",
  "Last Login",
  "Actions",
];

const getEnumValueFromDisplay = (displayValue: string): string | undefined => {
  const result = departmentType.find((opt) => opt.value === displayValue);
  return result?.enumValue;
};
const formatDepartmentName = (department: string): string => {
  return (
    departmentType.find((opt) => opt.enumValue === department)?.value ||
    department
  );
};

function TeamManagementList() {
  // ########## STATES #########
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const tabs = ["all", "admin", "moderator", "accountant"];
  const [selectedTab, setSelectedTab] = useState(() => {
    // Retrieve the selected tab from the URL hash or default to the first tab
    const hash = window.location.hash.replace("#", "");
    return tabs.includes(hash) ? hash : tabs[0];
  });

  // Listen for hash changes
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace("#", "");
      if (tabs.includes(hash)) {
        setSelectedTab(hash);
      }
    };

    window.addEventListener("hashchange", handleHashChange);
    return () => window.removeEventListener("hashchange", handleHashChange);
  }, []);

  // Update hash when tab changes
  useEffect(() => {
    window.location.hash = selectedTab;
  }, [selectedTab]);

  const [users, setUsers] = useState<TeamMember[]>([]);
  const [nextCursor, setNextCursor] = useState(null);
  const [searchInput, setSearchInput] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [teamId, setTeamId] = useState<string>("");
  const { ref, inView } = useInView();
  const [userStatus, setUserStatus] = useState<{
    userId: string;
    status: string;
    value: string;
  }>({ userId: "", status: "", value: "" });

  const [filterData, setFilterData] = useState<TeamMemberDataType>({
    accountType: "all",
    department: "all",
    registrationDateFilter: "all time",
    lastLoginFilter: "all time",
  });
  // clicked for reset
  const [resetForm, setResetForm] = useState(false);

  // ############ FUNCTIONS #################
  // Pass enumValue to Prisma instead of the display value
  const handleDepartmentChange = (departmentEnum: string) => {
    setFilterData({ ...filterData, department: departmentEnum }); // Use enum value for saving
  };

  const loadMoreUsers = async () => {
    if (isLoading || !nextCursor) return;

    setIsLoading(true);
    try {
      let data: any;
      if (
        searchInput ||
        filterData.accountType !== "all" ||
        // selectedTab !== "all" ||
        filterData.department !== "all"
      ) {
        const searchQuery = {
          searchQuery: searchInput,
          ...filterData,
          // accountType: selectedTab, // Override with selectedTab
        };
        const filterString = JSON.stringify(searchQuery);
        data = await fetchAllSearchTeamMembers(
          filterString,
          filterData.accountType,
          // filterData.accountType || selectedTab,
          nextCursor,
          20
        );
      } else {
        data = await fetchAllTeamMembers(nextCursor);
      }

      if (data?.results) {
        setUsers((prev: TeamMember[]) => [
          ...prev,
          ...(data.results.users || []),
        ]);
        setTotalUsers(data.results.usersTotal);
        setNextCursor(data.results.nextCursor);
      }
    } catch (error) {
      console.error("Error loading more users:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // ############### USE EFFECT ###########
  // Fetch team ID
  useEffect(() => {
    const fetchTeamId = async () => {
      try {
        const response = await getTeamId();
        setTeamId(response.teamId);
      } catch (error) {
        console.error("Error fetching team ID:", error);
      }
    };
    fetchTeamId();
  }, [teamId]);

  // call to load more users whenever scroll
  useEffect(() => {
    if (inView) {
      loadMoreUsers();
    }
  }, [inView]);

  // fetch search data with debounce
  const debouncedFetchSearchUsers = useDebouncedCallback(async (query) => {
    setIsLoading(true);
    setUsers([]);
    setTotalUsers(0);
    setNextCursor(null);
    const searchQuery = {
      searchQuery: searchInput,
      ...query,
      // accountType: selectedTab,
    };
    const filterString = JSON.stringify(searchQuery);
    const searchUsers = await fetchAllSearchTeamMembers(
      filterString,
      // selectedTab === "all" ? "all" : selectedTab,
      filterData.accountType,
      undefined,
      20
    );
    setUsers(searchUsers?.results?.users);
    setTotalUsers(searchUsers?.results?.totalUsers);
    setIsLoading(false);
  }, 700);

  // fetch data
  const fetchData = async () => {
    setIsLoading(true);
    setUsers([]);
    setTotalUsers(0);
    setNextCursor(null);

    try {
      let data: any;
      if (selectedTab === "all") {
        data = await fetchAllTeamMembers();
      } else {
        const searchQuery = {
          searchQuery: searchInput,
          ...filterData,
          accountType: selectedTab,
        };
        const filterString = JSON.stringify(searchQuery);
        data = await fetchAllSearchTeamMembers(
          filterString,
          selectedTab,
          undefined,
          20
        );
      }

      if (data?.results) {
        setUsers(data.results.users || []);
        setTotalUsers(data.results.usersTotal);
        setNextCursor(data.results.nextCursor);
      }
    } catch (error) {
      console.error("Error fetching team members:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTeamMemberUpdate = (updatedUser: TeamMember) => {
    setUsers((prevUsers) => {
      // If user is rejected/deleted, remove them from the list
      if (updatedUser.accountStatus === AccountStatusEnum.rejected) {
        return prevUsers.filter((user) => user.id !== updatedUser.id);
      }

      // Update existing user or add new one
      const userIndex = prevUsers.findIndex(
        (user) => user.id === updatedUser.id
      );
      if (userIndex !== -1) {
        // Create new array with updated user
        const newUsers = [...prevUsers];
        newUsers[userIndex] = updatedUser;
        return newUsers;
      } else {
        // Add new user to the beginning of the list
        return [updatedUser, ...prevUsers];
      }
    });

    // Update total count if needed
    setTotalUsers((prev) =>
      updatedUser.accountStatus === AccountStatusEnum.rejected ? prev - 1 : prev
    );
  };
  // call it when there a search input or filters change
  useEffect(() => {
    if (
      searchInput.length > 0 ||
      // selectedTab !== "all" || // Check selectedTab
      filterData.accountType !== "all" ||
      filterData.department !== "all" ||
      filterData.registrationDateFilter !== "all time" ||
      filterData.lastLoginFilter !== "all time"
    ) {
      debouncedFetchSearchUsers(filterData);
    } else {
      fetchData();
    }
  }, [searchInput, filterData, selectedTab, debouncedFetchSearchUsers]);

  // UPDATE & DELETE USER
  useEffect(() => {
    const updateUserStatus = async () => {
      dispatch(setLoading(true));
      const updateData = await fetchUserRequestForMaster(userStatus.userId, {
        accountStatus: userStatus.value,
      });

      // update user info with client if success
      if (updateData.success) {
        fetchData();
      }

      // display message
      dispatch(
        setMsg({ success: updateData.success, message: updateData.message })
      );

      dispatch(setLoading(false));
    };

    const deleteUser = async () => {
      dispatch(setLoading(true));

      // send req to delete the user
      const DeleteUser = await fetchDeleteUsersForMaster(userStatus.userId);
      // const DeleteUser = await softDeleteUser(userStatus.userId);

      if (DeleteUser.success) {
        setUsers(users.filter((user) => user.id !== userStatus.userId));
        setTotalUsers(totalUsers - 1);
      }

      // show the message
      dispatch(
        setMsg({
          success: DeleteUser.success,
          message: DeleteUser.message,
        })
      );

      dispatch(setLoading(false));
    };

    // if user update status
    if (userStatus.status === "update") {
      updateUserStatus();
    }

    // if delete user
    if (userStatus.status === "delete") {
      deleteUser();
    }
  }, [
    userStatus.userId,
    userStatus.status,
    userStatus.value,
    teamMembers,
    users,
  ]);

  // ############## RENDER #################

  useEffect(() => {
    // Reset state when filters change
    setUsers([]);
    setTotalUsers(0);
    setNextCursor(null);
    setIsLoading(false);

    // Fetch initial data
    fetchData();
  }, [filterData.accountType, filterData.department, selectedTab]);

  // // Clean Up Filter Data Sync Logic
  // useEffect(() => {
  //   // Sync filterData.accountType with selectedTab
  //   setFilterData((prev) => ({
  //     ...prev,
  //     accountType: selectedTab,
  //   }));
  // }, [selectedTab]);
  return (
    <>
      <div className="w-full max-w-7xl mx-auto">
        {/* Page header */}
        <div className="sm:flex sm:justify-between sm:items-center mb-5">
          {/* Left: Title */}
          <div className="mb-4 sm:mb-0">
            <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
              Master Users Management
            </h1>
          </div>
        </div>

        {/* Filters */}
        <div className="relative mb-5 bg-white dark:bg-gray-800 shadow-lg rounded-lg pb-7 pt-5 px-1 sm:px-3 ">
          <div className="flex flex-col lg:flex-row items-stretch lg:items-end space-y-4 lg:space-y-0 lg:space-x-4">
            {/*  Account Type*/}
            <div className="flex-1">
              <FilterClassDropdown
                fieldName="Account Type"
                options={accountTypeTypes}
                selectedOption={filterData.accountType}
                onChangeHandler={(accountType) =>
                  setFilterData({ ...filterData, accountType })
                }
              />
            </div>

            {/* Registration Date filter */}
            <div className="flex-1">
              <DateFilter
                filterFormData={filterData}
                setFilterFormData={(formData) => setFilterData(formData)}
                labelName="Registration Date"
                fieldName="registrationDateFilter"
              />
            </div>

            {/* Last Login filter */}
            <div className="flex-1">
              <DateFilter
                filterFormData={filterData}
                setFilterFormData={(formData) => setFilterData(formData)}
                labelName="Last Login"
                fieldName="lastLoginFilter"
              />
            </div>

            {/* Department filter */}
            <div className="flex-1">
              <FilterClassDropdown
                fieldName="Department Type"
                options={departmentType.map(({ id, value }) => ({ id, value }))}
                selectedOption={formatDepartmentName(filterData.department)}
                onChangeHandler={(selectedDisplayValue) => {
                  const selectedEnumValue =
                    getEnumValueFromDisplay(selectedDisplayValue);

                  if (selectedEnumValue) {
                    handleDepartmentChange(selectedEnumValue); // Set enum value in filterData
                  }
                }}
              />
            </div>

            {/* Reset Filters */}
            <button
              type="button"
              className="h-[45px] bg-blue-500 text-white text-sm hover:bg-blue-600 transition duration-300 py-2 px-4 rounded-lg"
              onClick={() => {
                setFilterData({
                  accountType: "all",
                  department: "all",
                  registrationDateFilter: "all time",
                  lastLoginFilter: "all time",
                });
                setResetForm(true);
              }}
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg relative overflow-hidden max-w-7xl mx-auto">
        {/* SMALL SCREENS */}
        <header className="xl:hidden bg-white dark:bg-gray-800 py-4 px-6 flex flex-col sm:flex-row sm:flex-wrap items-center justify-between border-b border-gray-300 dark:border-gray-700">
          <div className="flex flex-wrap items-center justify-between w-full space-y-4">
            <h2 className="font-semibold text-gray-800 dark:text-gray-100 mb-4 sm:mb-0">
              <span className="text-2xl font-bold text-red-500 mr-2">
                {totalUsers}
              </span>
              <span className="text-lg font-semibold">Total Team Members</span>
            </h2>
            {/* Search form */}
            <SearchUsersMasterBar
              searchInput={searchInput}
              setSearchInput={setSearchInput}
            />
          </div>
          <div className="w-full flex flex-wrap items-center justify-between overflow-x-auto mt-8 xl:mt-0 custom-scrollbar">
            {/* Tabs */}
            <Tabs
              tabs={tabs}
              selectedTab={selectedTab}
              onChangeHandler={setSelectedTab}
            />
            {/* Add Team Member button */}
            {"role" in user &&
              (user.roleType === "master_owner" ||
                (user.subRole &&
                  ["admin", "moderator"].includes(user.subRole))) && (
                <button
                  className="bg-red-500 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-600 transition duration-300 mt-4 sm:mt-0"
                  onClick={() => {
                    setModalOpen(true);
                  }}
                >
                  <PlusCircle size={20} className="mr-2" />
                  <span className=" ml-2">Add Team Member</span>
                </button>
              )}
          </div>
        </header>

        {/* LARGE SCREENS */}
        <header className="hidden bg-white dark:bg-gray-800 py-4 px-6 xl:flex flex-col sm:flex-row sm:flex-wrap items-center justify-between border-b border-gray-300 dark:border-gray-700">
          <div className="flex items-center flex-1 space-x-6 w-full sm:w-auto mt-4 sm:mt-0">
            <div className="flex items-center space-x-6 flex-1">
              <h2 className="font-semibold text-gray-800 dark:text-gray-100 flex justify-center items-center content-center">
                <span className="text-2xl font-bold text-red-500 mr-2 flex justify-center items-center content-center space-x-2">
                  <span>{totalUsers}</span>
                </span>
                <span className="text-lg font-semibold">
                  Total Team Members
                </span>
              </h2>
              <div className="h-8 w-px bg-gray-300 dark:bg-gray-700"></div>
              {/* Tabs */}
              <Tabs
                tabs={tabs}
                selectedTab={selectedTab}
                onChangeHandler={setSelectedTab}
              />
            </div>
            {/* Add Team Member button */}
            {"role" in user &&
              (user.roleType === "master_owner" ||
                (user.subRole &&
                  ["admin", "moderator"].includes(user.subRole))) && (
                <button
                  className="bg-red-500 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-600 transition duration-300"
                  onClick={() => {
                    setModalOpen(true);
                  }}
                >
                  <PlusCircle size={20} className="mr-2" />
                  <span className=" ml-2">Add Team Member</span>
                </button>
              )}
            {/* Search form */}
            <SearchUsersMasterBar
              searchInput={searchInput}
              setSearchInput={setSearchInput}
            />
          </div>
        </header>
        <div>
          {/* Table */}
          <div className="overflow-x-auto custom-scrollbar max-h-[calc(100vh-400px)]">
            <Suspense
              fallback={
                <div className="py-3 w-full">
                  <div className="flex justify-center">
                    <ListLoading />
                  </div>
                </div>
              }
            >
              <table className="table-auto w-full">
                {/* Table header */}
                <thead className="text-xs font-semibold capitalize text-gray-800 dark:text-gray-50 bg-gray-50 dark:bg-gray-900/20 border-t border-b border-gray-200 dark:border-gray-700 text-left sticky -top-0.5">
                  <tr>
                    {tableHeaders.map((header, index) => (
                      <th
                        key={index}
                        className="px-4 py-4 whitespace-nowrap font-semibold text-sm bg-gray-300 dark:bg-gray-700 z-10"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                {/* Table body */}

                <tbody className="text-sm divide-y divide-gray-200 dark:divide-gray-700">
                  {users &&
                    users.map((user) => (
                      <TeamManagementTableList
                        key={user.id}
                        user={user}
                        setUserStatus={setUserStatus}
                        onUpdate={handleTeamMemberUpdate}
                      />
                    ))}
                </tbody>
              </table>
              {users && users.length === 0 && isLoading === false && (
                <h1 className="text-center text-lg mt-10">No Users Found</h1>
              )}
              <div ref={ref} className="w-full py-4">
                {isLoading && (
                  <div className="py-3 w-full">
                    <div className="flex justify-center">
                      <ListLoading />
                    </div>
                  </div>
                )}
                {!isLoading && nextCursor && users.length < totalUsers && (
                  <div ref={ref} className="w-full h-10" />
                )}
              </div>
            </Suspense>
          </div>
        </div>
      </div>

      {/* Model */}
      <AddTeamMember
        dangerModalOpen={modalOpen}
        setDangerModalOpen={setModalOpen}
        teamId={teamId}
        onSuccess={handleTeamMemberUpdate}
      />
    </>
  );
}

const TeamManagement = () => {
  const { loading } = useAuth();

  if (loading) {
    return <ProgressLoading />;
  }

  return (
    <div className="px-4 sm:px-6 lg:px-0 py-8 w-full max-w-7xl mx-auto">
      <TeamManagementList />
    </div>
  );
};

export default TeamManagement;
