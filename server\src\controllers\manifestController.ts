import { Request, Response } from "express";
import { prisma } from "../prisma";
import { emailManifest } from "../utils/email/emailManifest";
import { generateManifestPdf } from "../utils/pdf/generateManifestPdf";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import "jspdf-autotable";
import {
  capitalize,
  getFormatDateTable,
  getFormatTime,
} from "../utils/functions";
export type ManifestStatus = "PENDING" | "SUBMITTED" | "NOT_SUBMITTED";

export interface ManifestTicket {
  id: string;
  refId: string;
  manifestId: string | null;
  flightDate: string;
  departure: {
    airportCode: string;
    city: string;
    country: string;
  };
  arrival: {
    airportCode: string;
    city: string;
    country: string;
  };
  departureTime: string;
  arrivalTime: string;
  pdfBase64?: string;
  agencyName?: string;
  ownerId?: string;
  owner?: {
    id: string;
    agencyName: string | null;
  };
  travelClass?: string;
  manifestStatus: ManifestStatus;
  submittedAt?: string | null;
  flightDuration: string;
  flightStops: number;
  flightNumber: string;
  carrier: string;
  passengerCount: number;
  status: string;
  Booking?: {
    id: string;
    status: string;
    tripType?: string; // Make this optional with ?
    travelers: Array<{
      id: string;
      bookingId: string;
      travelerId: string;
      infoStatus?: string;
      traveler: {
        id: string;
        title: string;
        firstName: string;
        lastName: string;
        dateOfBirth: string;
        nationality: string;
        issuingCountry?: string;
        documentNumber?: string;
        documentExpiryDate?: string;
      };
    }>;
  }[];
}

export const getManifestTickets = async (req: Request, res: Response) => {
  try {
    // Get user info from request
    // Prefer authenticated user/session info
    const user = (req as any).user;
    const teamMember = (req as any).teamMember;
    const agencyAgent = (req as any).agencyAgent;
    const accountType = (req as any).accountType || req.query.accountType;

    // Get user ID, agency ID, and team ID with fallbacks
    const userId = (user && user.id) || req.query.userId;
    const agencyId =
      (user && user.agencyId) ||
      (teamMember && teamMember.agencyId) ||
      req.query.agencyId;
    const teamId =
      (user && user.teamId) ||
      (teamMember && teamMember.teamId) ||
      req.query.teamId;
    const { pageSize = 20, cursor, bookingType } = req.query;
    const pageSizeNum = Math.min(Number(pageSize) || 20, 20);

    const account = user || accountType || userId || agencyId || teamId;

    if (!account) {
      return res.status(401).json({
        success: false,
        error: "Unauthorized",
        message: `Unauthorized ${account}`,
      });
    }

    // Helper function to get the creator ID (user ID) for a team member
    async function getTeamMemberCreatorId(
      teamMemberId: string
    ): Promise<string | null> {
      try {
        const member = await prisma.teamMember.findUnique({
          where: { id: teamMemberId },
          select: { createdById: true, createdByTeamMemberId: true },
        });

        if (!member) return null;

        // If created by a user, return the user ID
        if (member.createdById) {
          return member.createdById;
        }

        // If created by another team member, recursively find the user ID
        if (member.createdByTeamMemberId) {
          return getTeamMemberCreatorId(member.createdByTeamMemberId);
        }

        return null;
      } catch (error) {
        console.error("Error finding team member creator:", error);
        return null;
      }
    }

    // Determine the owner's agency ID based on the user's role
    let ownerAgencyId;
    if (userId && accountType) {
      // If the user is already an agency owner, use their ID directly
      if (accountType === "agencyOwner" || accountType === "masterOwner") {
        ownerAgencyId = userId;
      }
      // If the user is an agency agent, find their owner's agency ID
      else if (accountType === "agencyUser") {
        try {
          const agent = await prisma.agencyAgent.findUnique({
            where: { id: agencyAgent.id },
            select: { agencyId: true },
          });

          if (agent && agent.agencyId) {
            ownerAgencyId = agent.agencyId;
          }
        } catch (error) {
          console.error("Error finding agency agent's owner:", error);
        }
      }
      // If the user is a team member, find their owner's agency ID
      else if (accountType === "masterUser") {
        // For team members, first try to get the creator's user ID
        let creatorUserId: string | null = null;

        if (teamMember.createdById) {
          // Directly created by a user
          creatorUserId = teamMember.createdById;
        } else if (teamMember.createdByTeamMemberId) {
          // Created by another team member, find the root user
          creatorUserId = await getTeamMemberCreatorId(
            teamMember.createdByTeamMemberId
          );
        }

        // Use creator's user ID if found, otherwise fall back to team member's own IDs
        ownerAgencyId =
          creatorUserId ||
          teamMember.agencyId ||
          teamMember.teamId ||
          teamMember.id;
      }
      // If the user is an affiliate, they should only see their own bookings
      else if (accountType === "affiliate") {
        ownerAgencyId = userId;
      }
    } else if (agencyId) {
      ownerAgencyId = agencyId;
    }
    // Build the base where clause for the query
    const whereClause: any = {
      manifestId: { not: null }, // Only include tickets with manifest IDs
      Booking: {
        some: {
          status: "BOOKING_CONFIRMED",
        },
      },
    };

    /* ---------------------------------------------------------
     *  Additional filtering (status, flightDate)
     * --------------------------------------------------------*/
    // Helper to map UI manifest status labels ➜ DB enum values
    const mapManifestStatus = (val: string): string => {
      switch (val.trim().toLowerCase()) {
        case "submitted":
          return "SUBMITTED";
        case "pending":
          return "PENDING";
        case "not submitted":
          return "NOT_SUBMITTED";
        default:
          return val;
      }
    };

    // Convert relative date ranges ("Today", "Next 7 Days", …) ➜ { gte, lte }
    const mapRelativeRange = (
      range: string
    ): { gte: string; lte: string } | null => {
      const today = new Date();
      const format = (d: Date) => d.toISOString().split("T")[0];

      switch (range.trim().toLowerCase()) {
        case "today": {
          return { gte: format(today), lte: format(today) };
        }
        case "next 7 days": {
          const end = new Date(today);
          end.setDate(end.getDate() + 7);
          return { gte: format(today), lte: format(end) };
        }
        case "next 30 days": {
          const end = new Date(today);
          end.setDate(end.getDate() + 30);
          return { gte: format(today), lte: format(end) };
        }
        case "next 90 days": {
          const end = new Date(today);
          end.setDate(end.getDate() + 90);
          return { gte: format(today), lte: format(end) };
        }
        default:
          return null; // "All Time" or unknown
      }
    };

    // Apply status filter
    if (req.query.status && req.query.status !== "All") {
      const statusVal = Array.isArray(req.query.status)
        ? (req.query.status[0] as string)
        : (req.query.status as string);
      whereClause.manifest = {
        is: { manifestStatus: mapManifestStatus(statusVal) },
      };
    }

    // Apply flightDate relative range filter
    if (req.query.flightDate && req.query.flightDate !== "All Time") {
      const dateRange = mapRelativeRange(req.query.flightDate as string);
      if (dateRange) {
        whereClause.flightDate = dateRange;
      }
    }

    // Add access control based on user role
    let ownerFilter: any[] = [];

    // Only add the OR clause if we have valid owner IDs
    const validOwnerIds = new Set<string>();

    // Add the IDs from your initial filter
    [
      user?.id,
      teamMember?.teamId,
      teamMember?.createdById,
      teamMember?.createdByTeamMemberId,
    ]
      .filter((id): id is string => Boolean(id))
      .forEach((id) => validOwnerIds.add(id));

    switch (accountType) {
      case "masterOwner":
        if (user?.id) validOwnerIds.add(user.id);
        break;

      case "masterUser":
        // Team members can see manifests owned by their team
        const member = await prisma.teamMember.findUnique({
          where: { id: teamMember.id },
          select: {
            teamId: true,
            createdById: true,
            createdByTeamMemberId: true,
          },
        });
        // Created by another team member, find the root user
        const creatorUserId = await getTeamMemberCreatorId(
          teamMember.createdByTeamMemberId ||
            member?.createdById ||
            teamMember.createdById
        );

        if (!member?.teamId) {
          return res
            .status(403)
            .json({ success: false, error: "Access denied2" });
        }

        if (member?.teamId) validOwnerIds.add(member.teamId);
        if (teamMember?.teamId) validOwnerIds.add(teamMember.teamId);
        if (ownerAgencyId) validOwnerIds.add(ownerAgencyId);
        if (member?.createdById) validOwnerIds.add(member.createdById);
        if (member?.createdByTeamMemberId)
          validOwnerIds.add(member.createdByTeamMemberId);
        if (creatorUserId) validOwnerIds.add(creatorUserId);
        break;

      case "agencyOwner":
        if (user?.id) validOwnerIds.add(user.id);
        break;

      case "agencyUser":
        // Agency users can see manifests owned by their agency
        const agent = await prisma.agencyAgent.findUnique({
          where: { id: agencyAgent.id },
          select: { agencyId: true },
        });

        if (!agent?.agencyId) {
          return res
            .status(403)
            .json({ success: false, error: "Access denied3" });
        }

        if (agent?.agencyId) validOwnerIds.add(agent.agencyId);
        if (agencyAgent?.agencyId) validOwnerIds.add(agencyAgent.agencyId);
        break;

      default:
        return res
          .status(403)
          .json({ success: false, error: "Invalid account type" });
    }

    // Apply owner filter if any conditions were added
    if (ownerFilter.length > 0) {
      whereClause.OR = ownerFilter;
    }

    if (validOwnerIds.size > 0) {
      whereClause.OR = Array.from(validOwnerIds).map((id) => ({ ownerId: id }));
    }

    // Add cursor-based pagination if cursor is provided
    if (cursor) {
      whereClause.id = { lt: cursor };
    }

    // Get tickets with pagination
    const tickets = await prisma.flightTicket.findMany({
      where: whereClause,
      take: pageSizeNum + 1,
      cursor: cursor && typeof cursor === "string" ? { id: cursor } : undefined,
      select: {
        id: true,
        refId: true,
        manifestId: true,
        flightDate: true,
        departureTime: true,
        arrivalTime: true,
        duration: true,
        stops: true,
        ownerId: true,
        ticketStatus: true,
        manifest: true,
        departure: true,
        arrival: true,
        owner: {
          select: {
            id: true,
            agencyName: true,
            email: true,
            website: true,
            commercialOperationNo: true,
            iataNo: true,
            address: true,
            phoneNumber: true,
          },
        },
        flightClasses: true,
        Booking: {
          where: { status: "BOOKING_CONFIRMED" },
          include: {
            travelers: {
              include: {
                traveler: true,
              },
            },
          },
        },
        segments: {
          take: 1,
          orderBy: { departureTime: "asc" },
        },
        _count: {
          select: {
            Booking: {
              where: { status: "BOOKING_CONFIRMED" },
            },
          },
        },
      },
      orderBy: { flightDate: "asc" },
    });

    // Check if there are more items for pagination
    const hasMore = tickets.length > pageSizeNum;
    const paginatedTickets = hasMore ? tickets.slice(0, -1) : tickets;
    const nextCursor = hasMore ? tickets[tickets.length - 1].id : null;

    // Transform the data for the frontend
    const manifestTickets: ManifestTicket[] = paginatedTickets.map((ticket) => {
      const segment = ticket.segments[0];
      const travelClass = ticket?.flightClasses?.[0]?.type || "";
      const manifestStatus = ticket.manifest?.manifestStatus;
      const submittedAt = ticket.manifest?.submittedAt;
      return {
        id: ticket.id,
        refId: ticket.refId,
        manifestId: ticket.manifestId,
        flightDate: ticket.flightDate,
        departure: {
          airportCode: ticket.departure?.airportCode || "",
          city: ticket.departure?.city || "",
          country: ticket.departure?.country || "",
        },
        arrival: {
          airportCode: ticket.arrival?.airportCode || "",
          city: ticket.arrival?.city || "",
          country: ticket.arrival?.country || "",
        },
        departureTime: ticket?.departureTime || "",
        arrivalTime: ticket?.arrivalTime || "",
        flightDuration: ticket?.duration || "",
        flightStops: ticket?.stops || 0,
        travelClass,
        ownerId: ticket?.ownerId || "",
        owner: ticket.owner
          ? {
              id: ticket.owner.id,
              agencyName: ticket.owner.agencyName,
              email: ticket.owner.email,
              website: ticket.owner.website,
              commercialOperationNo: ticket.owner.commercialOperationNo,
              iataNo: ticket.owner.iataNo,
              address: ticket.owner.address,
              phoneNumber: ticket.owner.phoneNumber,
            }
          : undefined,
        manifestStatus: manifestStatus as ManifestStatus,
        submittedAt: submittedAt?.toISOString(),
        flightNumber: segment?.flightNumber || "",
        carrier: segment?.carrier || "",
        passengerCount: ticket._count?.Booking || 0,
        status: ticket.ticketStatus || "",
        Booking: ticket.Booking?.map((booking) => ({
          id: booking.id,
          status: booking.status,
          tripType: booking.tripType,
          travelers: booking.travelers.map((traveler) => ({
            id: traveler.id,
            bookingId: traveler.bookingId,
            travelerId: traveler.travelerId,
            infoStatus: traveler.infoStatus
              ? String(traveler.infoStatus)
              : undefined,
            traveler: {
              id: traveler.traveler.id,
              title: traveler.traveler.title || "", // Handle null title
              firstName: traveler.traveler.firstName || "", // Handle null firstName
              lastName: traveler.traveler.lastName || "", // Handle null lastName
              dateOfBirth: traveler.traveler.dateOfBirth
                ? new Date(traveler.traveler.dateOfBirth).toISOString()
                : "", // Handle null dateOfBirth
              nationality: traveler.traveler.nationality || "", // Handle null nationality
              issuingCountry: traveler.traveler.issuingCountry || undefined,
              documentNumber: traveler.traveler.documentNumber || undefined,
              documentExpiryDate: traveler.traveler.expirationDate
                ? new Date(traveler.traveler.expirationDate).toISOString()
                : undefined,
            },
          })),
        })),
      };
    });

    return res.status(200).json({
      success: true,
      results: {
        manifestTickets: manifestTickets,
        totalManifestTickets: hasMore,
        nextCursor,
      },
    });
  } catch (error) {
    console.error("Error fetching manifest tickets:", error);
    return res.status(500).json({
      success: false,
      error: "An error occurred while fetching manifest tickets",
    });
  }
};
// In manifestController.ts
export const updateManifestStatus = async (req: Request, res: Response) => {
  try {
    const { manifestId, status } = req.body;

    await prisma.manifest.update({
      where: { id: manifestId },
      data: {
        manifestStatus: status,
        ...(status === "SUBMITTED" && { submittedAt: new Date() }),
      },
    });

    res.json({ success: true });
  } catch (error) {
    console.error("Error updating manifest status:", error);
    res.status(500).json({ error: "Failed to update manifest status" });
  }
};

// Format the date to match your database format (e.g., 'YYYY-MM-DD')
const formatDateForDB = (date: Date) => {
  return date.toISOString().split("T")[0]; // Returns 'YYYY-MM-DD'
};

// In manifestController.ts
export const checkAndUpdateExpiredManifests = async () => {
  try {
    const now = new Date();
    const formattedDate = formatDateForDB(now);

    // Find the ones that need to be expired
    const pendingManifests = await prisma.manifest.findMany({
      where: {
        manifestStatus: "PENDING",
        // flightDate: {
        //   lt: formattedDate,
        // },
      },
      include: {
        flightTicket: {
          // Include flightTicket to access its flightDate
          select: {
            flightDate: true,
          },
        },
      },
    });

    // Filter manifests where flight ticket's date is in the past
    const manifestsToExpire = pendingManifests.filter((manifest) => {
      const flightDateStr = manifest.flightTicket?.flightDate;
      if (!flightDateStr) return false; // Skip if no flight date

      const flightDate = new Date(flightDateStr);
      return flightDate < now;
    });

    const updatePromises = manifestsToExpire
      .map((manifest) => {
        const flightDateStr = manifest.flightTicket?.flightDate;
        if (!flightDateStr) return null; // Skip if no flight date

        return prisma.manifest.update({
          where: { id: manifest.id },
          data: {
            manifestStatus: "NOT_SUBMITTED",
            flightDate: flightDateStr, // Use the string directly
            updatedAt: new Date(),
          },
        });
      })
      .filter(Boolean); // Remove any null entries

    if (updatePromises.length > 0) {
      await Promise.all(updatePromises);
      console.log(
        `Updated ${updatePromises.length} manifests to NOT_SUBMITTED`
      );
    } else {
      console.log("No manifests to expire");
    }
  } catch (error) {
    console.error("Error in checkAndUpdateExpiredManifests:", error);
  }
};

// Call this function periodically (e.g., every hour)
setInterval(checkAndUpdateExpiredManifests, 60 * 1000);

// export const sendManifestEmail = async (req: Request, res: Response) => {
//   if (req.method !== "POST") {
//     return res.status(405).json({ error: "Method not allowed" });
//   }

//   try {
//     const { manifestId, email, agencyName, pdfBase64 } = req.body;

//     // Get manifest data
//     const manifest = await prisma.flightTicket.findUnique({
//       where: { id: manifestId },
//       include: {
//         // Include necessary relations
//         departure: true,
//         arrival: true,
//         Booking: {
//           include: {
//             travelers: {
//               include: {
//                 traveler: true,
//               },
//             },
//           },
//         },
//       },
//     });

//     if (!manifest) {
//       return res.status(404).json({ error: "Manifest not found" });
//     }

//     // Send email with PDF attachment
//     await emailManifest(email, manifest, pdfBase64);

//     res.status(200).json({ success: true });
//   } catch (error) {
//     console.error("Error sending manifest email:", error);
//     res.status(500).json({ error: "Failed to send email" });
//   }
// };

export const downloadManifestPdf = async (req: Request, res: Response) => {
  try {
    const { manifestId } = req.params;

    // Get the manifest data
    const manifest = await prisma.flightTicket.findUnique({
      where: { id: manifestId },
      include: {
        departure: true,
        arrival: true,
        Booking: {
          include: {
            travelers: {
              include: {
                traveler: true,
              },
            },
          },
        },
      },
    });

    if (!manifest) {
      return res.status(404).send("Manifest not found");
    }
    // Generate the PDF
    const pdfBuffer = await generateManifestPdf(manifest);

    if (!pdfBuffer || pdfBuffer.byteLength === 0) {
      return res.status(404).send("PDF not found for this manifest");
    }
    // Set headers for PDF download
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=manifest-${manifestId}.pdf`
    );

    // Return the PDF data (you'll need to generate this)
    res.send(pdfBuffer);
  } catch (error) {
    console.error("Error downloading manifest PDF:", error);
    res.status(500).send("Error downloading manifest");
  }
};

export const handleDownloadPdf = (manifest: ManifestTicket) => {
  const doc = new jsPDF();

  // Set document properties
  doc.setProperties({
    title: `Manifest - ${manifest.manifestId}`,
    subject: "Flight Manifest",
    author: "AirVilla",
  });

  // Add logo
  const logoUrl = "/images/logo/airvilla_logo_symbol_red.png";
  doc.addImage(logoUrl, "PNG", 15, 10, 20, 20);

  // Add title and flight info
  doc.setFontSize(18);
  doc.setTextColor(40, 40, 40);
  doc.text(manifest.owner?.agencyName || "", 40, 20);

  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text(`Flight Number:`, 40, 30);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${manifest.flightNumber}`, 70, 30);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`Flight Date:`, 150, 30);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${getFormatDateTable(manifest.flightDate)}`, 175, 30);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`Carrier:`, 40, 36);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${manifest.carrier}`, 70, 36);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`Travel Class:`, 150, 36);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${manifest.travelClass}`, 175, 36);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`From:`, 40, 42);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${manifest.departure.city}`, 70, 42);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`To:`, 150, 42);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${manifest.arrival.city}`, 175, 42);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`Departure Time:`, 40, 48);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${getFormatTime(manifest.departureTime)}`, 70, 48);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);
  doc.text(`Arrival Time:`, 150, 48);
  doc.setFont("helvetica", "bold");
  doc.setFontSize(11);
  doc.text(`${getFormatTime(manifest.arrivalTime)}`, 175, 48);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(10);

  // Add current date and time
  const now = new Date();
  doc.text(`Generated: ${now.toLocaleString()}`, 198, 10, { align: "right" });

  // Add passenger table
  const passengerRows =
    manifest.Booking?.flatMap((booking, index) =>
      booking.travelers?.map((passenger) => [
        (index + 1).toString(),
        `${capitalize(passenger.traveler.title)} ${passenger.traveler.firstName} ${passenger.traveler.lastName}`,
        getFormatDateTable(passenger.traveler.dateOfBirth),
        `${passenger.traveler.nationality} / ${passenger.traveler.issuingCountry}`,
        passenger.traveler.documentNumber,
        passenger?.traveler?.documentExpiryDate
          ? getFormatDateTable(passenger.traveler.documentExpiryDate)
          : "N/A",
        passenger.bookingId,
      ])
    ) || [];

  autoTable(doc, {
    startY: 60,
    head: [
      [
        "#",
        "Passenger Name",
        "Date of Birth",
        "Nationality / Issuing Country",
        "Document #",
        "Expiry",
        "Booking ID",
      ],
    ],
    body: passengerRows as (string | number)[][],
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: "bold",
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245],
    },
    margin: { top: 10 },
    styles: {
      fontSize: 8,
      cellPadding: 3,
      overflow: "linebreak",
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    columnStyles: {
      0: { cellWidth: 10 }, // #
      1: { cellWidth: 40 }, // Name
      2: { cellWidth: 25 }, // DOB
      3: { cellWidth: 25 }, // Nationality
      4: { cellWidth: 30 }, // Document #
      5: { cellWidth: 25 }, // Expiry
      6: { cellWidth: 30 }, // Booking ID
    },
  });

  // Add page numbers
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.setTextColor(150);
    doc.text(
      `Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: "center" }
    );
  }

  doc.save(`manifest-${manifest.manifestId}.pdf`);
};

export const sendManifestEmail = async (req: Request, res: Response) => {
  try {
    const { manifestId, pdfBase64, email } = req.body;
    console.log({ manifestId, email, pdfBase64Length: pdfBase64?.length });

    // Get manifest data
    const ticket = await prisma.flightTicket.findUnique({
      where: { id: manifestId },
      include: {
        departure: true,
        arrival: true,
        owner: true,
        manifest: true,
        Booking: {
          include: {
            travelers: {
              include: {
                traveler: true,
              },
            },
          },
        },
      },
    });
    console.log({ ticket });

    if (!ticket) {
      return res.status(404).json({ error: "Manifest not found" });
    }

    // Send email with PDF attachment
    try {
      await emailManifest(email, ticket, pdfBase64);
      console.log("Email sent successfully for manifest:", ticket.manifest?.id);
    } catch (emailError: any) {
      console.error(
        "Failed to send email for manifest:",
        ticket.manifest?.id,
        emailError
      );
      return res.status(500).json({
        success: false,
        message: "Failed to send email",
        error: emailError.message,
      });
    }

    // Update manifest status to SUBMITTED
    if (!ticket.manifest?.id) {
      return res.status(404).json({ error: "Manifest not found" });
    }
    console.log("Updating manifest status to SUBMITTED:", ticket.manifest?.id);
    try {
      await prisma.manifest.update({
        where: { id: ticket.manifest?.id },
        data: {
          manifestStatus: "SUBMITTED",
          submittedAt: new Date(),
        },
      });
      console.log("Manifest status updated to SUBMITTED:", ticket.manifest?.id);
    } catch (updateError) {
      console.error("Failed to update manifest status:", updateError);
      // Don't fail the request if status update fails, just log it
      return res.status(200).json({
        success: true,
        message: "Email sent but failed to update manifest status",
        warning: "Manifest status could not be updated",
      });
    }

    res.status(200).json({
      success: true,
      message: "Email sent successfully",
    });
  } catch (error: any) {
    console.error("Error sending manifest email:", error);
    res.status(500).json({
      success: false,
      message: "Failed to send email",
      error: error.message,
    });
  }
};
