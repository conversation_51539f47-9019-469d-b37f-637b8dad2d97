"use client";
import { fetchResetPassword } from "@/lib/data/authData";
import { selectIsLoggedIn } from "@/redux/features/AuthSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { AuthResTypes } from "@/utils/definitions/authDefinitions";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import AuthImage from "@/app/(auth)/auth-image";
import ProgressLoading from "../../utils/ProgressLoading";
import { ArrowRight, Loader2 } from "lucide-react";
import useDarkMode from "@/components/hooks/useDarkMode";

const generateInputClassName = (hasError: boolean) => {
  const baseClasses =
    "bg-gray-300 dark:bg-gray-700 dark:text-white rounded-lg pl-10 pr-4 py-2.5 w-full outline-none transition-all duration-300 border-0 focus:ring-2 focus:ring-red-500 focus:border-red-500 placeholder:text-sm";
  const errorClasses = hasError ? "border-red-500" : "";
  return `${baseClasses} ${errorClasses}`;
};

function isValidJwt(token: string): boolean {
  // A simple check to see if the token has 2 periods, which implies it has 3 parts
  const parts = token.split(".");
  return parts.length === 3;
}

export default function ResetPasswordFormToken({ token }: { token: string }) {
  const [loading, setLoading] = useState(true);
  const dispatch = useAppDispatch();
  const isLoggedIn = useAppSelector(selectIsLoggedIn);
  const router = useRouter();

  // ############# USEEFFECT ############
  // Redirect to homepage if already logged in
  useEffect(() => {
    if (isLoggedIn) {
      router.push("/");
    } else if (!isValidJwt(token)) {
      router.push("/reset-password");
    } else {
      setLoading(false); // Set loading to false once the check is done
    }
  }, [isLoggedIn, router]);

  // ############# RETURNS #################
  if (loading) {
    return <ProgressLoading />;
  }
  return <NewPasswordPage token={token} />;
}

import { Lock, Eye, EyeOff } from "lucide-react";
import { checkPasswordStrength } from "@/utils/passwordStrength";
import PasswordField from "@/components/common/PasswordField";
import { PasswordStrength } from "@/utils/definitions/agentsDefinitions";

/**
 * NewPasswordPage Component
 * Renders a password creation form with the following features:
 * - Password visibility toggle
 * - Password confirmation
 * - Responsive layout with illustration
 * - Minimal password requirement indicator
 */
const NewPasswordPage = ({ token }: { token: string }) => {
  // State for password visibility toggles
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // form input data
  const [form, setForm] = useState<{
    password: string;
    confirmPassword: string;
  }>({
    password: "",
    confirmPassword: "",
  });

  const [validationError, setValidationError] = useState<any>(null);
  const [loadingBtn, setLoadingBtn] = useState<boolean>(false);
  const [reset, setReset] = useState<boolean>(false);
  const [results, setResults] = useState<AuthResTypes | null>(null);
  const darkMode = useDarkMode();
  const isDarkMode = typeof darkMode === "boolean" ? darkMode : false;

  const getInitialPasswordStrength = (): PasswordStrength => ({
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  });
  const [passwordStrength, setPasswordStrength] = useState<
    PasswordStrength | undefined
  >(getInitialPasswordStrength());
  const [confirmPasswordStrength, setConfirmPasswordStrength] = useState<
    PasswordStrength | undefined
  >(getInitialPasswordStrength());
  const [validationErrorPassword, setValidationErrorPassword] = useState<{
    password?: string;
    confirmNewPassword?: string;
  }>({});
  const [debouncedPassword, setDebouncedPassword] = useState<string>("");
  const [debouncedConfirmPassword, setDebouncedConfirmPassword] =
    useState<string>("");

  // Debounce password strength check
  useEffect(() => {
    const handler = setTimeout(() => {
      setPasswordStrength(checkPasswordStrength(debouncedPassword, isDarkMode));
      setConfirmPasswordStrength(
        checkPasswordStrength(debouncedConfirmPassword, isDarkMode)
      );
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [debouncedPassword, debouncedConfirmPassword, isDarkMode]);
  // Handles the form submission event for the reset password form.
  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    // Prevent the default form submission behavior
    e.preventDefault();

    // Set loading state to true
    setLoadingBtn(true);

    // Check password strength
    const passwordStrength = checkPasswordStrength(form.password, isDarkMode);

    if (passwordStrength.score < 3) {
      // Set minimum required strength
      setValidationError((prev: any) => ({
        ...prev,
        password: `Weak password: ${passwordStrength.feedback.join(", ")}`,
      }));
      setLoadingBtn(false);
      return;
    }

    // Check if passwords match
    if (form.password !== form.confirmPassword) {
      setValidationError((prev: any) => ({
        ...prev,
        confirmPassword:
          "The passwords you entered don’t match. Please try again",
      }));
      setLoadingBtn(false);
      return;
    }

    // Send reset password email
    const resetPassword = await fetchResetPassword(form, token);

    // If the request is successful, clear the form and set the 'reset' state to true
    if (resetPassword?.success) {
      setForm({ password: "", confirmPassword: "" });
      setReset(true);
      setLoadingBtn(false);
    }

    // If there are validation errors from the server, set the validation errors state
    if (resetPassword?.success === false && resetPassword.validationErrors) {
      setValidationError(resetPassword.validationErrors);
      setLoadingBtn(false);
    }

    // Set the results state to the response from the server
    setResults(resetPassword);

    // Set loading state to false
    setLoadingBtn(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  // if reset show success message
  if (reset) {
    return (
      <div className="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center p-4">
        {/* Content card with shadow and rounded corners */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl shadow-2xl p-8 w-full min-h-[515px] max-w-6xl flex py-20">
          {/* Left section - Illustration */}
          <div className="w-1/2 pr-8">
            <AuthImage />
          </div>

          {/* Right section - Content */}
          <div className="w-1/2 flex flex-col justify-center">
            {/* Header content */}
            <div className="mb-8">
              {/* Brand badge */}
              <div className="bg-red-500 text-white text-sm font-bold px-3 py-1 rounded inline-block mb-2">
                airvilla Charter
              </div>

              {/* Success message section */}
              <div className="flex flex-col">
                <div className="flex items-center mb-4">
                  <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                    Password Updated!
                  </h1>
                </div>

                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Your password has been successfully reset. You can now use
                  your new password to access your account.
                </p>
              </div>
            </div>

            {/* Action button */}
            <div className="w-full">
              <Link
                href="/signin"
                // onClick={handleLoginRedirect}
                className="
                bg-red-500 hover:bg-red-600
                text-white font-semibold
                py-3 px-6 rounded-lg
                transition-colors duration-300
                w-full flex items-center justify-center
              "
              >
                Continue to Login
                <ArrowRight size={18} className="ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    // Main container with dark theme
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center p-4">
      {/* Content card */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl shadow-2xl p-8 w-full min-h-[515px] max-w-6xl flex py-20">
        {/* Left section - Illustration */}
        <div className="w-1/2 pr-8">
          <AuthImage />
        </div>

        {/* Right section - Form */}
        <div className="w-1/2 flex flex-col justify-center">
          {/* Header section */}
          <div className="mb-8">
            <div className="bg-red-500 text-white text-sm font-bold px-3 py-1 rounded inline-block mb-2">
              airvilla Charter
            </div>
            <h1 className="text-4xl font-bold text-gray-600 dark:text-white mb-2">
              Create New Password
            </h1>
            <p className="text-gray-400 text-sm">
              Enter and confirm your new password below.
            </p>
          </div>

          {/* Password form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* New password input */}
            {/* <PasswordInput
              id="password"
              label="New Password"
              placeholder="Enter new password"
              showPassword={showPassword}
              onToggleVisibility={() => setShowPassword(!showPassword)}
              handleChange={handleChange}
              validationError={validationError?.password}
              form={form}
            /> */}

            <PasswordField
              label="Set Password"
              name="password"
              placeholder="Create a strong password"
              showPassword={showPassword}
              onToggleVisibility={() => setShowPassword(!showPassword)}
              required
              strength={passwordStrength}
              tooltip="Choose a strong, unique password to protect your Airvilla account."
              value={form.password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setForm({ ...form, password: e.target.value });
                setDebouncedPassword(e.target.value);
              }}
              formErrors={validationErrorPassword?.password}
              labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
              inputClassName={generateInputClassName(
                !!validationErrorPassword?.password
              )}
              strengthClassName="bg-gray-300 dark:bg-gray-700"
            />

            {/* Confirm password input */}
            {/* <PasswordInput
              id="confirmPassword"
              label="Confirm Password"
              placeholder="Confirm new password"
              showPassword={showConfirmPassword}
              onToggleVisibility={() =>
                setShowConfirmPassword(!showConfirmPassword)
              }
              handleChange={handleChange}
              validationError={validationError?.confirmPassword}
              form={form}
            /> */}
            <PasswordField
              label="Confirm Password"
              name="confirmPassword"
              placeholder="Confirm your password"
              showPassword={showConfirmPassword}
              onToggleVisibility={() =>
                setShowConfirmPassword(!showConfirmPassword)
              }
              required
              strength={confirmPasswordStrength}
              tooltip="Re-enter your password to ensure it's correct."
              value={form.confirmPassword}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setForm({
                  ...form,
                  confirmPassword: e.target.value,
                });
                setDebouncedConfirmPassword(e.target.value);
              }}
              formErrors={validationErrorPassword?.confirmNewPassword}
              labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
              inputClassName={generateInputClassName(
                !!validationErrorPassword?.confirmNewPassword
              )}
              strengthClassName="bg-gray-300 dark:bg-gray-700"
            />

            {results?.success === false && (
              <div className="text-slate-100">
                <div className="text-sm font-semibold mt-3 text-rose-500">
                  {results?.message}
                </div>
              </div>
            )}

            {/* Submit button */}
            <button
              type="submit"
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 w-full flex items-center justify-center"
              disabled={loadingBtn}
            >
              {loadingBtn ? (
                <Loader2 className="w-5 h-5 animate-spin shrink-0" />
              ) : (
                "Reset Password"
              )}
            </button>
          </form>

          {/* Password requirements hint */}
          <div className="mt-6 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Lock size={16} className="mr-2" />
            Make sure your password is at least 8 characters
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Renders a password input field with visibility toggle
 * @param {Object} props - Component properties
 * @param {string} props.id - Input field ID
 * @param {string} props.label - Input field label
 * @param {string} props.placeholder - Input placeholder text
 * @param {boolean} props.showPassword - Password visibility state
 * @param {Function} props.onToggleVisibility - Visibility toggle handler
 * @param {Function} props.handleChange - Input change handler
 * @param {string} props.validationError - Input validation error
 */
const PasswordInput = ({
  id,
  label,
  placeholder,
  showPassword,
  onToggleVisibility,
  handleChange,
  validationError,
  form,
}: {
  id: string;
  label: string;
  placeholder: string;
  showPassword: boolean;
  onToggleVisibility: () => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  validationError?: string;
  form: { [key: string]: string };
}) => (
  <div key={id}>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-400 mb-1"
    >
      {label}
    </label>
    <div className="relative">
      <input
        id={id}
        name={id}
        type={showPassword ? "text" : "password"}
        className="bg-gray-300 dark:bg-gray-700 dark:text-white rounded-lg pl-10 pr-4 py-2.5 w-full outline-none transition-all duration-300 border-0 focus:ring-2 focus:ring-red-500 focus:border-red-500 placeholder:text-sm"
        placeholder={placeholder}
        value={form[id as keyof typeof form]}
        required
        autoComplete="off"
        aria-label={label}
        onChange={handleChange}
      />
      {/* Left icon */}
      <Lock
        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        size={18}
      />
      {/* Visibility toggle button */}
      <button
        type="button"
        onClick={onToggleVisibility}
        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-300"
        aria-label={showPassword ? "Hide password" : "Show password"}
      >
        {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
      </button>
    </div>
    {validationError && (
      <div className="text-sm mt-1 text-rose-500 flex items-start gap-1">
        {validationError}
      </div>
    )}
  </div>
);
