"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  fetchUpdateUserEmail,
  fetchUpdateUserPassword,
} from "@/lib/data/userProfileData";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import ProgressLoading from "@/components/utils/ProgressLoading";
import { UserProfileResultType } from "@/utils/definitions/userProfileDefinitions";

import { setLoading } from "@/redux/features/LoadingSlice";
import { selectUser } from "@/redux/features/AuthSlice";
import { useRouter } from "next/navigation";
import DeleteUserAlert from "./DeleteUserAlert";
import { Lock, Mail, Eye, EyeOff } from "lucide-react";
import HardDeleteUserButton from "@/components/common/HardDeleteUser";
import {
  fetchSingleUserForMaster,
  softDeleteUser,
} from "@/lib/data/masterUsersData";
import Password<PERSON>ield from "@/components/common/PasswordField";
import { PasswordStrength } from "@/utils/definitions/agentsDefinitions";
import useDarkMode from "@/components/hooks/useDarkMode";
import { checkPasswordStrength } from "@/utils/passwordStrength";

const generateInputClassName = (hasError: boolean) => {
  const baseClasses =
    "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-1.5 md:py-2.5 bg-gray-100 dark:bg-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-2 w-full outline-none transition-all duration-300 focus:ring-2 focus:ring-red-500 focus:border-red-500 border-none";
  const errorClasses = hasError ? "border-red-500" : "";
  return `${baseClasses} ${errorClasses}`;
};

interface PrivacyAccountPanelProps {
  userId: string;
}

export default function PrivacyAccountPanel({
  userId,
}: PrivacyAccountPanelProps) {
  const [userInfo, setUserInfo] = useState<UserProfileResultType | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // email
  const [newEmail, setNewEmail] = useState<{ newEmail: string }>({
    newEmail: "",
  });
  const [validationErrorEmail, setValidationErrorEmail] = useState<string>("");

  // password
  const [updatePasswordForm, setUpdatePasswordForm] = useState<{
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
  }>({
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });
  const [validationErrorPassword, setValidationErrorPassword] = useState<{
    currentPassword?: string;
    newPassword?: string;
    confirmNewPassword?: string;
  }>({});
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  // delete user
  const [dangerModalOpen, setDangerModalOpen] = useState<boolean>(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState<string>("");

  // Individual password visibility states
  const [visibility, setVisibility] = useState({
    current: false,
    new: false,
    confirm: false,
    groupToggled: false,
  });

  const darkMode = useDarkMode();
  const isDarkMode = typeof darkMode === "boolean" ? darkMode : false;

  const getInitialPasswordStrength = (): PasswordStrength => ({
    score: 0,
    feedback: [],
    color: isDarkMode ? "#ff4444" : "#cc0000",
    label: "",
    message: "",
  });
  const [currentDebouncedPassword, setCurrentDebouncedPassword] =
    useState<string>("");
  const [newPasswordStrength, setNewPasswordStrength] =
    useState<PasswordStrength>(getInitialPasswordStrength());
  const [newDebouncedPassword, setNewDebouncedPassword] = useState<string>("");
  const [confirmNewPasswordStrength, setConfirmNewPasswordStrength] =
    useState<PasswordStrength>(getInitialPasswordStrength());
  const [confirmNewDebouncedPassword, setConfirmNewDebouncedPassword] =
    useState<string>("");

  // Debounce password strength check
  useEffect(() => {
    const handler = setTimeout(() => {
      setNewPasswordStrength(
        checkPasswordStrength(newDebouncedPassword, isDarkMode)
      );
      setConfirmNewPasswordStrength(
        checkPasswordStrength(confirmNewDebouncedPassword, isDarkMode)
      );
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [
    currentDebouncedPassword,
    newDebouncedPassword,
    confirmNewDebouncedPassword,
    isDarkMode,
  ]);

  // Handle individual password toggle
  const toggleSinglePassword = useCallback((field: keyof typeof visibility) => {
    setVisibility((prev) => {
      const newState = {
        ...prev,
        [field]: !prev[field],
        groupToggled: false,
      };
      return newState;
    });
  }, []);

  // hooks
  const dispatch = useAppDispatch();
  const router = useRouter();
  const currentUser = useAppSelector(selectUser);

  const showHardDelete =
    currentUser.isLogin &&
    "role" in currentUser &&
    "roleType" in currentUser &&
    currentUser.role === "master" &&
    currentUser.roleType === "master_admin" &&
    userInfo?.id;

  // ######## functions ########
  const fetchUserInfo = async () => {
    setIsLoading(true);
    // Fetch user profile data
    const data = await fetchSingleUserForMaster(userId);

    // Set user info
    if (data.success && data.results) {
      setUserInfo(data.results);
    }
    // Display message
    dispatch(
      setMsg({
        success: data.success,
        message: data.message,
      })
    );
    setIsLoading(false);
  };

  // handle password
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setUpdatePasswordForm({
      ...updatePasswordForm,
      newPassword: password,
    });
    const strength =
      password.length > 8
        ? password.match(/[A-Z]/) &&
          password.match(/[a-z]/) &&
          password.match(/[0-9]/)
          ? 3
          : 2
        : 1;
    setPasswordStrength(strength);
  };

  // Updates the user profile by sending a PUT request to the server.
  const updateEmail = async (): Promise<void> => {
    // Set loading state to true
    dispatch(setLoading(true));

    // Send PUT request to server to update user profile
    const data = await fetchUpdateUserEmail(newEmail);

    // If the request is successful
    if (data.success) {
      // Update the user information in the Redux store
      setValidationErrorEmail("");
      setNewEmail({ newEmail: "" });
      alert(
        `Email verification send successful, please check your email ${newEmail.newEmail}`
      );
    }

    // If there are validation errors
    if (data.validationError) {
      // Update the validation error state
      setValidationErrorEmail(data.validationError);
    }

    // Display the appropriate message
    dispatch(
      setMsg({
        success: data.success,
        message: data.message,
      })
    );

    // Set loading state to false
    dispatch(setLoading(false));
  };

  // update user password
  const updatePassword = async (): Promise<void> => {
    // Set loading state to true
    dispatch(setLoading(true));
    // Send PUT request to server to update user profile
    const data = await fetchUpdateUserPassword(updatePasswordForm);
    // If the request is successful
    if (data.success) {
      // reset the form
      setUpdatePasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmNewPassword: "",
      });
      setValidationErrorPassword({});
    }

    // If there are validation errors
    if (data.validationError) {
      // Update the validation error state
      setValidationErrorPassword(data.validationError);
    }

    // Display the appropriate message
    dispatch(
      setMsg({
        success: data.success,
        message: data.message,
      })
    );
    // Set loading state to false
    dispatch(setLoading(false));
  };

  const handleSoftDeleteUser = async () => {
    dispatch(setLoading(true));
    try {
      const result = await softDeleteUser(userId);
      dispatch(
        setMsg({
          success: result.success,
          message: result.message,
        })
      );
      if (result.success) {
        router.push("/master-control/users");
      }
    } catch (error) {
      console.error("Error soft deleting user:", error);
      dispatch(
        setMsg({
          success: false,
          message: "An error occurred while trying to delete the user",
        })
      );
    } finally {
      dispatch(setLoading(false));
    }
  };

  // ######## useEffect ########
  useEffect(() => {
    fetchUserInfo();
  }, []);

  // Loading
  if (isLoading) {
    return <ProgressLoading />;
  }

  // User not found
  if (!userInfo || userInfo === null) {
    return (
      <div className="w-full h-[80vh] flex justify-center items-center">
        <h1 className="text-red-700 text-xl font-bold">User not found</h1>
      </div>
    );
  }

  const user = userInfo as UserProfileResultType;

  return (
    <div className="space-y-8">
      {/* Account Security Section */}
      <h3 className="text-xl md:text-2xl font-semibold mb-4 text-gray-800 dark:text-white">
        Account Security
      </h3>
      <section className="bg-white dark:bg-gray-700 p-6 rounded-lg">
        {/* Password Update */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium dark:text-gray-300">
            Update Password
          </h4>
          {/* Current Password */}
          <PasswordField
            name="currentPassword"
            label="Current Password"
            placeholder="Enter your current password"
            value={updatePasswordForm.currentPassword}
            // showPassword={showCurrentPassword}
            showPassword={visibility.current}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = e.target.value;
              setUpdatePasswordForm((prev) => ({
                ...prev,
                currentPassword: newValue,
              }));
              // Immediately update password strength
              setCurrentDebouncedPassword(newValue);
            }}
            onToggleVisibility={() => {
              toggleSinglePassword("current");
            }}
            required
            tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
            formErrors={validationErrorPassword?.currentPassword}
            labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            inputClassName={generateInputClassName(
              !!validationErrorPassword?.currentPassword
            )}
            strengthClassName="bg-gray-200 dark:bg-gray-800"
          />

          {/* New Password */}
          <PasswordField
            name="newPassword"
            label="New Password"
            placeholder="Enter new password"
            value={updatePasswordForm.newPassword}
            // showPassword={showNewPassword}
            showPassword={visibility.new}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = e.target.value;
              setUpdatePasswordForm((prev) => ({
                ...prev,
                newPassword: newValue,
              }));
              // Immediately update password strength
              setNewDebouncedPassword(newValue);
            }}
            onToggleVisibility={() => {
              toggleSinglePassword("new");
            }}
            strength={newPasswordStrength}
            required
            tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
            formErrors={validationErrorPassword?.newPassword}
            labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            inputClassName={generateInputClassName(
              !!validationErrorPassword?.newPassword
            )}
            strengthClassName="bg-gray-200 dark:bg-gray-800"
          />

          {/* Confirm New Password */}
          <PasswordField
            name="confirmNewPassword"
            label="Confirm New Password"
            placeholder="Confirm new password"
            value={updatePasswordForm.confirmNewPassword}
            // showPassword={showConfirmNewPassword}
            showPassword={visibility.confirm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              const newValue = e.target.value;
              setUpdatePasswordForm((prev) => ({
                ...prev,
                confirmNewPassword: newValue,
              }));
              // Immediately update password strength
              setConfirmNewDebouncedPassword(newValue);
            }}
            onToggleVisibility={() => {
              toggleSinglePassword("confirm");
            }}
            strength={confirmNewPasswordStrength}
            required
            tooltip="Your password must be at least 8 characters long and contain a mix of uppercase, lowercase, numbers, and special characters."
            formErrors={validationErrorPassword?.confirmNewPassword}
            labelClassName="flex items-center space-x-2 block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
            inputClassName={generateInputClassName(
              !!validationErrorPassword?.confirmNewPassword
            )}
            strengthClassName="bg-gray-200 dark:bg-gray-800"
          />

          <section className="flex justify-start items-center space-x-8">
            <button
              onClick={updatePassword}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
            >
              Save Password Changes
            </button>
          </section>
        </div>
      </section>
      <section className="bg-white dark:bg-gray-700 p-6 rounded-lg">
        {/* Email Settings */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium dark:text-gray-300">
            Email Settings
          </h4>
          <div className="flex flex-wrap items-center md:space-x-4">
            <span className="dark:text-gray-400">Current Email:</span>
            <span className="text-gray-700 font-semibold dark:text-white overflow-hidden text-ellipsis break-all">
              {user.email}
            </span>
          </div>
          {/* New Email Address */}
          <InputField
            label="New Email Address"
            id="newEmail"
            type="email"
            icon={<Mail size={18} />}
            value={newEmail.newEmail}
            onChange={(e) => setNewEmail({ newEmail: e.target.value })}
            required
            validationError={validationErrorEmail}
            placeholder="Enter new email address"
          />
          {/* send email button*/}
          <section className="flex justify-start items-center">
            <button
              type="button"
              onClick={updateEmail}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-2 md:px-4 rounded-lg transition-colors duration-300"
            >
              Send Verification Code
            </button>
          </section>
        </div>
      </section>

      {/* Account Deletion Section */}
      <section className="bg-white dark:bg-gray-700 p-6 rounded-lg">
        <h3 className="text-xl font-semibold mb-4 text-red-500">
          Account Deletion
        </h3>
        <p className="dark:text-gray-300 mb-4">
          Deleting your account is permanent. All your data will be permanently
          removed and cannot be recovered.
        </p>
        <ul className="list-disc list-inside dark:text-gray-300 mb-4">
          <li>Your profile and all associated data will be deleted</li>
          <li>You will lose access to all services and subscriptions</li>
          <li>Any outstanding balances or credits will be forfeited</li>
        </ul>
        <section className="flex justify-start items-center space-x-8">
          {showHardDelete && userId && (
            <div className="mt-6">
              <HardDeleteUserButton userId={userId} />
            </div>
          )}
        </section>

        <DeleteUserAlert
          showDeleteConfirmation={dangerModalOpen}
          setShowDeleteConfirmation={setDangerModalOpen}
          deleteConfirmText={deleteConfirmText}
          setDeleteConfirmText={setDeleteConfirmText}
          handleDelete={handleSoftDeleteUser}
          title="Soft Delete Account"
          description="This will remove only the user's email while keeping other data. This action cannot be undone."
          confirmButtonText="Soft Delete"
          confirmationText="delete"
        />
      </section>
    </div>
  );
}

const InputField = ({
  label,
  id,
  icon,
  value,
  onChange,
  required,
  type = "text",
  validationError,
  placeholder,
}: {
  label: string;
  id: string;
  icon: React.ReactNode;
  value: string | undefined;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  type?: string;
  validationError?: string;
  placeholder?: string;
}) => (
  <div>
    <label
      className="block text-sm font-medium dark:text-gray-400 mb-1"
      htmlFor={id}
    >
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <div className="relative">
      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
        {icon}
      </span>
      <input
        id={id}
        type={type}
        className="bg-gray-100 dark:bg-gray-800 dark:text-white rounded-lg pl-10 pr-4 py-2 w-full outline-none transition-all duration-300 focus:ring-2 focus:ring-red-500 focus:border-red-500 border-none"
        value={value || ""}
        onChange={onChange}
        required={required}
        placeholder={placeholder}
      />
    </div>
    {validationError && (
      <div className="text-sm mt-1 text-red-500">{validationError}</div>
    )}
  </div>
);
