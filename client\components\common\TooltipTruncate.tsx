// components/TooltipTruncate.tsx
/**
 * Extracted field truncation with tooltip logic
 * Reusable component for truncated text with hover tooltip
 */
export const TooltipTruncate = ({
  text,
  maxLength,
}: {
  text: string;
  maxLength: number;
}) => (
  <div className="group">
    {/* Truncated Field */}
    <div className="font-medium text-gray-800 dark:text-gray-100 truncate max-w-[150px]">
      {text?.length > maxLength ? `${text.substring(0, maxLength)}...` : text}
    </div>
    {/* Tooltip with Full Field */}
    {text?.length > maxLength && (
      <div className="relative">
        <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white text-sm rounded px-2 py-1 group-hover:block">
          {text}
        </div>
      </div>
    )}
  </div>
);
