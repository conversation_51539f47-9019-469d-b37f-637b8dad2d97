import { Request, Response, NextFunction } from "express";
import { UserRole, hasPermission, AgencyTier } from "../utils/types/auth";

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: UserRole;
    agencyTier?: AgencyTier;
  };
}

export const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const user = req.user;

    if (!user) {
      return res
        .status(401)
        .json({ message: "Unauthorized - User not authenticated" });
    }

    if (!hasPermission(user.role, permission, user.agencyTier)) {
      return res
        .status(403)
        .json({ message: "Forbidden - Insufficient permissions" });
    }

    next();
  };
};

export const requireRole = (roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const user = req.user;

    if (!user) {
      return res
        .status(401)
        .json({ message: "Unauthorized - User not authenticated" });
    }

    if (!roles.includes(user.role)) {
      return res.status(403).json({ message: "Forbidden - Insufficient role" });
    }

    next();
  };
};
