"use client";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectFullTicket,
  selectPassengerCounts,
  setBookingConfirmationData,
} from "@/redux/features/BookingConfirmationSlice";
import TicketDetailsCard from "./TicketDetailsCard";
import InfoCard from "./InfoCard";
import TravelerDetailsForm from "./TravelerDetailsForm";
import FareSummary from "./FareSummary";
import { fetchTicketById } from "@/lib/data/ticketData";
import ListLoading from "@/components/flight-tickets/myTickets/ListLoading";
import {
  FlightTicketRes,
  Traveler,
} from "@/utils/definitions/blockSeatsDefinitions";
import { selectUser } from "@/redux/features/AuthSlice";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppSelector } from "@/redux/hooks";
import { StoredUser } from "@/utils/definitions/authDefinitions";
import imgBanner from "@/public/images/support-banner.png";
import Image from "next/image";
import { useBookingTimer } from "@/hooks/useBookingTimer";

// Define agent operations interface
export interface AgentOperations {
  bookingReference: string;
  ticketId: string;
  issuingBookingSource: string;
  agent: {
    name: string;
    role: string;
    agency: string;
  };
  formFiller?: {
    id: string;
    name: string;
    email: string;
    role: string;
    agency: string;
  };
}

export default function SingleTicketDetails({
  departureId,
  returnId,
}: {
  departureId: string;
  returnId?: string;
}) {
  // Get the original full ticket from Redux
  const fullTicket = useSelector(
    (state: any) => state.bookingConfirmation.fullTicket
  );
  const passengerCounts = useSelector(
    (state: any) => state.bookingConfirmation.passengerCounts
  );
  const dispatch = useDispatch();
  const user = useAppSelector(selectUser);
  // Type guard for user fields
  const userInfo =
    user &&
    typeof user === "object" &&
    "id" in user &&
    "firstName" in user &&
    "lastName" in user &&
    "agencyName" in user
      ? {
          id: (user as any).id || "",
          firstName: (user as any).firstName || "",
          lastName: (user as any).lastName || "",
          userName: (user as any).userName || "",
          agencyName: (user as any).agencyName || "",
          email: (user as any).email || "",
          role: (user as any).role || "",
          roleType: (user as any).roleType || "",
        }
      : { id: "", firstName: "", lastName: "", agencyName: "" };
  // State to track form validity
  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [departureTicket, setDepartureTicket] = useState<FlightTicketRes>();
  const [returnTicket, setReturnTicket] = useState<FlightTicketRes>();
  const [loading, setLoading] = useState<boolean>(true);
  const [travelerData, setTravelerData] = useState<Traveler[]>([]);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get the clearBookingSession function from the useBookingTimer hook
  // const { clearBookingSession } = useBookingTimer();

  useEffect(() => {
    const fetchTickets = async () => {
      try {
        // Reset booking timer only if not in a booking flow
        // This preserves the timer when navigating between booking pages
        // clearBookingSession(false); // false means don't force reset

        const departureResponse = await fetchTicketById(departureId);
        if (!departureResponse) {
          throw new Error("Failed to fetch departure ticket data");
        }
        const departureData = await departureResponse.results;

        setDepartureTicket(departureData);

        if (returnId) {
          const returnResponse = await fetchTicketById(returnId);
          if (!returnResponse) {
            throw new Error("Failed to fetch return ticket data");
          }
          const returnData = await returnResponse.results;
          setReturnTicket(returnData);
        }
      } catch (error) {
        // Handle error state or display error message
        console.error("Error fetching ticket:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchTickets();
  // }, [departureId, returnId, clearBookingSession]);
  }, [departureId, returnId]);

  useEffect(() => {
    // Redirect to login page if not logged in
    if (!user.isLogin) {
      router.push("/signin");
      return;
    } else if (!(user as StoredUser).verified) {
      router.push("/not-verified");
    } else {
      setLoading(false);
    }
  }, [user, router]);

  const constructPaymentURLParams = (
    travelers: Traveler[],
    departureTicket: FlightTicketRes | undefined,
    returnTicket: FlightTicketRes | undefined,
    searchParams: ReturnType<typeof useSearchParams> | URLSearchParams
  ) => {
    // Get all current URL parameters
    const urlParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      // Skip any existing traveler data parameters
      if (!key.startsWith("traveler_")) {
        urlParams.append(key, value);
      }
    });

    // Add carrier and flight number with explicit console logging
    const departureCarrier = departureTicket?.segments?.[0]?.carrier || "";
    const departureFlightNumber =
      departureTicket?.segments?.[0]?.flightNumber || "";

    // Safely access return ticket data with proper null checks
    const returnCarrier = returnTicket?.segments?.[0]?.carrier || "";
    const returnFlightNumber = returnTicket?.segments?.[0]?.flightNumber || "";

    const stops = departureTicket?.stops || 0;
    const departureTime = departureTicket?.departureTime || "";
    const arrivalTime = departureTicket?.arrivalTime || "";

    urlParams.append("departureCarrier", departureCarrier);
    urlParams.append("departureFlightNumber", departureFlightNumber);
    urlParams.append("returnCarrier", returnCarrier);
    urlParams.append("returnFlightNumber", returnFlightNumber);
    urlParams.append("departureTime", departureTime);
    urlParams.append("arrivalTime", arrivalTime);
    urlParams.append("stops", stops.toString());

    const price = {
      departureAdultPrice:
        departureTicket?.flightClasses?.[0]?.price?.adult || 0,
      departureChildPrice:
        departureTicket?.flightClasses?.[0]?.price?.child || 0,
      departureInfantPrice:
        departureTicket?.flightClasses?.[0]?.price?.infant || 0,
      departureTaxPrice: departureTicket?.flightClasses?.[0]?.price?.tax || 0,
      departureCurrency:
        departureTicket?.flightClasses?.[0]?.price?.currency || "",

      returnAdultPrice: returnTicket?.flightClasses?.[0]?.price?.adult || 0,
      returnChildPrice: returnTicket?.flightClasses?.[0]?.price?.child || 0,
      returnInfantPrice: returnTicket?.flightClasses?.[0]?.price?.infant || 0,
      returnTaxPrice: returnTicket?.flightClasses?.[0]?.price?.tax || 0,
      returnCurrency: returnTicket?.flightClasses?.[0]?.price?.currency || "",
    };

    urlParams.append("price", JSON.stringify(price));

    // Calculate agentOperationsData directly here
    let agentOperationsData: AgentOperations | undefined = undefined;
    if (departureTicket) {
      try {
        const ownerName = departureTicket.owner
          ? `${departureTicket.owner.firstName || ""} ${
              departureTicket.owner.lastName || ""
            }`.trim()
          : "";
        const agentName = departureTicket.agencyAgent
          ? `${departureTicket.agencyAgent.firstName || ""} ${
              departureTicket.agencyAgent.lastName || ""
            }`.trim()
          : "";

        // Create agent operations data using real user information
        agentOperationsData = {
          bookingReference: departureTicket.id,
          ticketId: departureTicket.id || "",
          ...(returnTicket?.id && { returnTicketId: returnTicket.id }),
          issuingBookingSource: "Internal", // Set to Internal for consistency
          agent: {
            name: userInfo.firstName + " " + userInfo.lastName || agentName || ownerName || "",
            role: userInfo.role || departureTicket.agencyAgent?.role || "",
            agency: userInfo.agencyName ||
                   departureTicket.agencyAgent?.agencyName ||
                   departureTicket.owner?.agencyName || "",
          },
          formFiller: {
            id: userInfo.id || "",
            name: userInfo.firstName + " " + userInfo.lastName || "",
            email: userInfo.email || "",
            role: userInfo.role || "",
            agency: userInfo.agencyName || "",
          },
        };
      } catch (error) {
        console.error("Error constructing agent operations data:", error);
      }
    }

    // Stringify the calculated agentOperations data
    const agentOperationsJson = agentOperationsData || {};

    // Append the stringified JSON directly; URLSearchParams will handle encoding
    urlParams.append("agentOperations", JSON.stringify(agentOperationsJson));

    // Add simplified individual parameters for agent operations
    urlParams.set("bookRef", agentOperationsData?.bookingReference || "");
    urlParams.set("tktId", agentOperationsData?.ticketId || "");
    urlParams.set("source", agentOperationsData?.issuingBookingSource || "");

    // Agent details
    urlParams.set("agentName", agentOperationsData?.agent?.name || "");
    urlParams.set("agentRole", agentOperationsData?.agent?.role || "");
    urlParams.set("agentAgency", agentOperationsData?.agent?.agency || "");

    // Form filler details
    if (agentOperationsData?.formFiller) {
      urlParams.set("fillerName", agentOperationsData.formFiller.name || "");
      urlParams.set("fillerEmail", agentOperationsData.formFiller.email || "");
      urlParams.set("fillerRole", agentOperationsData.formFiller.role || "");

      // Add the form filler's agency if it exists
      if ("agency" in agentOperationsData.formFiller) {
        urlParams.set(
          "fillerAgency",
          agentOperationsData.formFiller.agency || ""
        );
      }
    }

    // Add each traveler's data as individual parameters
    travelers.forEach((traveler, index) => {
      // Skip the errors field
      Object.entries(traveler).forEach(([field, value]) => {
        if (field !== "errors" && value) {
          urlParams.append(`traveler_${index}_${field}`, value.toString());
        }
      });
    });

    // Add returnId and itinerary to URL parameters if this is a round-trip booking
    if (returnTicket?.id) {
      urlParams.append('returnId', returnTicket.id);
      // Explicitly set the itinerary to 'round trip' for round-trip bookings
      urlParams.set('itinerary', 'round trip');
    } else {
      // Explicitly set the itinerary to 'one way' for one-way bookings
      urlParams.set('itinerary', 'one way');
    }

    // Construct the full URL with both ticket IDs if it's a round trip
    let fullUrl;
    if (returnTicket?.id) {
      // For round trips, include both ticket IDs in the path
      fullUrl = `/blockseats/list/${departureTicket?.id}_${returnTicket.id}/checkout?${urlParams.toString()}`;
    } else {
      // For one-way trips, just include the departure ticket ID
      fullUrl = `/blockseats/list/${departureTicket?.id}/checkout?${urlParams.toString()}`;
    }
    
    return fullUrl;
  };

  const handleProceedToPayment = (travelers: Traveler[]) => {
    if (departureTicket) {
      // Save traveler data to state
      setTravelerData(travelers);

      // Determine trip type based on return ticket presence
      const isRoundTrip = !!returnTicket?.id;
      const tripType = isRoundTrip ? 'ROUND_TRIP' : 'ONE_WAY';

      // Update Redux store with booking data
      if (typeof window !== "undefined" && dispatch) {
        const ticketObj = {
          departureTicket,
          returnTicket: returnTicket || null,
          tripType, // Include trip type in the ticket object
        };
        
        // Create a copy of search params to avoid mutating the original
        const searchParamsObj = Object.fromEntries(searchParams.entries());
        
        // Ensure returnId is included in search params for round trips
        if (isRoundTrip && returnTicket?.id) {
          searchParamsObj.returnId = returnTicket.id;
          searchParamsObj.itinerary = 'round trip';
        } else {
          searchParamsObj.itinerary = 'one way';
        }

        dispatch(
          setBookingConfirmationData({
            bookingResult: {
              ...departureTicket,
              returnTicket: returnTicket || null,
              tripType, // Include trip type in booking result
              searchParams: searchParamsObj,
              userInfo: userInfo,
            },
            travelerData: travelers,
            ticket: ticketObj,
            fullTicket: fullTicket || ticketObj,
            passengerCounts: passengerCounts,
          })
        );
      }

      // Create a new URLSearchParams object to ensure we have all the necessary parameters
      const urlParams = new URLSearchParams();
      
      // Copy all existing search params except traveler_* and returnId
      searchParams.forEach((value, key) => {
        if (!key.startsWith('traveler_') && key !== 'returnId') {
          urlParams.append(key, value);
        }
      });
      
      // Ensure returnId is included in URL params for round trips
      if (returnTicket?.id) {
        // Set the returnId in the URL params and ensure itinerary is set to round trip
        urlParams.set('returnId', returnTicket.id);
        urlParams.set('itinerary', 'round trip');
      } else {
        // Remove returnId if it exists (for one-way trips)
        urlParams.delete('returnId');
        urlParams.set('itinerary', 'one way');
      }
      
      // Construct the payment URL with all necessary parameters
      const fullUrl = constructPaymentURLParams(
        travelers,
        departureTicket,
        returnTicket,
        urlParams
      );
      
      // Navigate to the checkout page with the updated URL
      router.push(fullUrl);
    }
  };

  if (loading) {
    return <ListLoading />;
  }

  return (
    <div className="max-w-7xl mx-auto pt-8 font-sans">
      <h1 className="font-bold text-3xl leading-10 text-gray-700 dark:text-white">
        Flight Booking
      </h1>
      <Image
        src={imgBanner}
        alt="img"
        width={800}
        height={120}
        className="w-full rounded-lg shadow-lg my-6"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:gap-8 space-y-4 lg:space-y-0">
        <div className="flex-grow bg-gray-200 dark:bg-gray-800 rounded-lg shadow-xl p-4 md:p-8 col-span-2">
          {departureTicket && (
            <TicketDetailsCard ticket={departureTicket} itinerary="one way" />
          )}
          {departureTicket && departureTicket.description && (
            <InfoCard ticket={departureTicket} />
          )}

          {returnTicket && (
            <TicketDetailsCard ticket={returnTicket} itinerary="round trip" />
          )}
          {returnTicket && returnTicket.description && (
            <InfoCard ticket={returnTicket} />
          )}
          {departureTicket && (
            <TravelerDetailsForm
              ticket={departureTicket}
              onFormValidChange={setIsFormValid}
              onTravelerDataChange={(travelers) => {
                // Update traveler data in state whenever it changes
                setTravelerData(travelers);
              }}
              onProceedToPayment={handleProceedToPayment}
            />
          )}
          {/* Mobile screens */}
          <div className="xl:hidden mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {departureTicket && (
              <FareSummary
                departureTicket={departureTicket}
                returnTicket={returnTicket}
                isFormValid={isFormValid}
                onProceedToPayment={handleProceedToPayment}
                travelers={travelerData}
              />
            )}
            {/* {departureTicket && <CancelCard ticket={departureTicket} />} */}
          </div>
        </div>
        {/* Right side for large screens */}
        <div className="hidden xl:block xl:sticky xl:top-4 self-start w-full mt-[140px] col-span-1">
          {departureTicket && (
            <FareSummary
              departureTicket={departureTicket}
              returnTicket={returnTicket}
              isFormValid={isFormValid}
              onProceedToPayment={handleProceedToPayment}
              travelers={travelerData}
            />
          )}
          {/* {departureTicket && <CancelCard ticket={departureTicket} />} */}
        </div>
      </div>
    </div>
  );
}
