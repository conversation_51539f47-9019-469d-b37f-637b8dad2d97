// components/TeamMemberStatus.tsx

import { getColorClasses, getDotColorClass } from "@/utils/functions/functions";

/**
 * Extracted status display logic from TeamManagementTableList
 * Reusable component for displaying Team Members status with consistent styling
 */
type StatusColor =
  | "yellow"
  | "red"
  | "green"
  | "blue"
  | "purple"
  | "gray"
  | "orange"
  | "indigo"
  | "pink"
  | "teal"
  | "cyan"
  | "lime"
  | "amber"
  | "fuchsia"
  | "violet"
  | "emerald"
  | "zinc";

const STATUS_COLORS: Record<string, StatusColor> = {
  suspended: "red", // Strong red for warning/danger
  accepted: "emerald", // Rich green for success/active
  pending: "blue", // Pure blue for in-progress
  rejected: "gray", // Neutral gray for inactive
  moderator: "violet", // Deep purple for authority
  accountant: "amber", // Golden yellow for finance-related
  management: "lime", // Deep blue for leadership
  finance: "yellow", // Bright yellow for financial
  marketing: "fuchsia", // Vibrant pink for creativity
  sales: "purple", // Bright blue for commerce
  it: "teal", // Blue-green for technology
  operations: "orange", // Warm orange for operations
  agency: "green", // Rich indigo for agency partners
  affiliate: "zinc", // Cyan for affiliate partners
};

const STATUS_ALIASES: Record<string, string[]> = {
  suspended: ["inactive", "admin"],
  accepted: ["active"],
  pending: ["customer support"],
  sales: ["elite"],
};

export const AccountStatus = ({ status }: { status: string }) => {
  const getStatusColor = () => {
    const normalizedStatus = status?.toLowerCase();

    // Check direct status
    if (STATUS_COLORS[normalizedStatus]) return STATUS_COLORS[normalizedStatus];

    // Check aliases
    for (const [mainStatus, aliases] of Object.entries(STATUS_ALIASES)) {
      if (aliases.includes(normalizedStatus)) return STATUS_COLORS[mainStatus];
    }

    return STATUS_COLORS.rejected;
  };

  const color = getStatusColor();
  const colorClasses = getColorClasses(color);
  const dotColorClass = getDotColorClass(color);

  // Add null check for status
  const statusText = status || "inactive";

  return (
    <span
      className={`capitalize inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${colorClasses}`}
    >
      {["inactive", "active"].includes(statusText.toLowerCase()) ? (
        <span
          className={`inline-block w-1.5 h-1.5 rounded-full mr-1.5 ${dotColorClass}`}
        ></span>
      ) : null}
      {status}
    </span>
  );
};
