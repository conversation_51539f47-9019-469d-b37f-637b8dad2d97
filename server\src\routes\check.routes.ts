import { Router, Request, Response } from "express";
import { prisma } from "../prisma";
import toobusy from "toobusy-js";
import os from "os";

const router = Router();

// --- Shared Formatting Helpers ---
function formatTimestamp(epoch: number) {
  return new Date(epoch).toLocaleString();
}
function getNow() {
  const now = Date.now();
  return { timestamp: formatTimestamp(now), timestampRaw: now };
}
function formatUptime(sec: number) {
  const h = Math.floor(sec / 3600);
  const m = Math.floor((sec % 3600) / 60);
  const s = Math.floor(sec % 60);
  const parts = [];
  if (h) parts.push(`${h} hour${h > 1 ? 's' : ''}`);
  if (m) parts.push(`${m} minute${m > 1 ? 's' : ''}`);
  if (s || (!h && !m)) parts.push(`${s} second${s > 1 ? 's' : ''}`);
  return parts.join(', ');
}
function formatBytes(bytes: number): string {
  if (bytes < 1024) return `${bytes} B`;
  const units = ['KB', 'MB', 'GB', 'TB'];
  let i = -1;
  do {
    bytes /= 1024;
    i++;
  } while (bytes >= 1024 && i < units.length - 1);
  return `${bytes.toFixed(1)} ${units[i]}`;
}

// --- Monitoring Endpoints ---

// Health check
/**
 * @openapi
 * /check/health:
 *   get:
 *     tags:
 *       - Check
 *     summary: Health check
 *     responses:
 *       200:
 *         description: Health status
 */
router.get("/health", (req: Request, res: Response) => {
  const now = getNow();
  const uptimeSeconds = process.uptime();
  res.json({
    status: "ok",
    uptime: formatUptime(uptimeSeconds),
    uptimeSeconds,
    ...now,
  });
});

// Readiness check (with DB check example)
/**
 * @openapi
 * /check/ready:
 *   get:
 *     tags:
 *       - Check
 *     summary: Readiness check
 *     responses:
 *       200:
 *         description: Readiness status
 */
router.get("/ready", async (req: Request, res: Response) => {
  let dbStatus = "unknown";
  try {
    // Real DB check using Prisma
    await prisma.$queryRaw`SELECT 1`;
    dbStatus = "ok";
  } catch (e) {
    dbStatus = "fail";
  }
  const now = getNow();
  res.json({
    status: dbStatus === "ok" ? "ready" : "not ready",
    db: dbStatus,
    ...now,
  });
});

// Info endpoint
const buildTime = process.env.BUILD_TIME || null;
router.get("/info", (req: Request, res: Response) => {
  const now = getNow();
  res.json({
    name: process.env.npm_package_name || "airvilla-charter-app",
    version: process.env.npm_package_version || "1.0.0",
    environment: process.env.NODE_ENV || "development",
    buildTime,
    hostname: os.hostname(),
    ...now,
  });
});

// Status endpoint (system resource info)

router.get("/status", (req: Request, res: Response) => {
  function formatBytes(bytes: number): string {
    if (bytes < 1024) return `${bytes} B`;
    const units = ['KB', 'MB', 'GB', 'TB'];
    let i = -1;
    do {
      bytes /= 1024;
      i++;
    } while (bytes >= 1024 && i < units.length - 1);
    return `${bytes.toFixed(1)} ${units[i]}`;
  }
  function formatUptime(sec: number): string {
    const h = Math.floor(sec / 3600);
    const m = Math.floor((sec % 3600) / 60);
    const s = Math.floor(sec % 60);
    const parts = [];
    if (h) parts.push(`${h} hour${h > 1 ? 's' : ''}`);
    if (m) parts.push(`${m} minute${m > 1 ? 's' : ''}`);
    if (s || (!h && !m)) parts.push(`${s} second${s > 1 ? 's' : ''}`);
    return parts.join(', ');
  }
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  let eventLoopLag = null;
  try {
    eventLoopLag = toobusy.lag();
  } catch (e) {}
  const uptimeSeconds = process.uptime();
  const now = getNow();
  res.json({
    uptime: formatUptime(uptimeSeconds),
    uptimeSeconds,
    memory: {
      RSS: formatBytes(memoryUsage.rss),
      HeapTotal: formatBytes(memoryUsage.heapTotal),
      HeapUsed: formatBytes(memoryUsage.heapUsed),
      External: formatBytes(memoryUsage.external),
      ArrayBuffers: formatBytes(memoryUsage.arrayBuffers),
    },
    cpu: {
      User: (cpuUsage.user / 1e6).toFixed(1) + " sec",
      System: (cpuUsage.system / 1e6).toFixed(1) + " sec",
    },
    pid: process.pid,
    platform: process.platform === "win32" ? "Windows" : process.platform,
    nodeVersion: process.version,
    eventLoopLag: eventLoopLag !== null ? eventLoopLag + " ms" : null,
    hostname: os.hostname(),
    ...now,
    details: {
      rawMemory: memoryUsage,
      rawCPU: cpuUsage,
      rawUptimeSeconds: uptimeSeconds,
    }
  });
});

// Business metrics endpoint (real DB data)
router.get("/business-metrics", async (req: Request, res: Response) => {
  try {
    const [activeUsers, totalBookings, failedPayments] = await Promise.all([
      prisma.user.count({ where: { accountStatus: "accepted", verified: true } }),
      prisma.booking.count(),
      prisma.booking.count({ where: { status: "TIMED_OUT" } })
    ]);
    const now = getNow();
    res.json({
      activeUsers,
      totalBookings,
      failedPayments,
      ...now,
    });
  } catch (err) {
    res.status(500).json({
      error: "Failed to fetch business metrics",
      details: err instanceof Error ? err.message : err,
    });
  }
});

// Debug Sentry error simulation
router.get("/debug-sentry", function mainHandler(req: Request, res: Response) {
  throw new Error("Sentry debug error!");
});

export default router;