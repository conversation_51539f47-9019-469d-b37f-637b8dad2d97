import { createAsyncThunk } from "@reduxjs/toolkit";
import { fetchTicketById } from "@/lib/data/ticketData";
import {
  setDepartureTicket,
  setReturnTicket,
  setTicketsLoading,
} from "./BookingConfirmationSlice";
import { AppDispatch } from "../store";

interface FetchTicketsParams {
  departureTicketId?: string;
  returnTicketId?: string;
}

export const fetchTickets = createAsyncThunk(
  "booking/fetchTickets",
  async (
    { departureTicketId, returnTicketId }: FetchTicketsParams,
    { dispatch }
  ) => {
    dispatch(setTicketsLoading(true));

    try {
      let departureTicket = null;
      let returnTicket = null;

      // Fetch departure ticket if ID is provided
      if (departureTicketId) {
        const departureResponse = await fetchTicketById(departureTicketId);
        if (departureResponse) {
          departureTicket = departureResponse.results;
          // Ensure isReturn is set correctly
          departureTicket.isReturn = false;
          dispatch(setDepartureTicket(departureTicket));
        }
      }

      // Fetch return ticket if ID is provided
      if (returnTicketId) {
        const returnResponse = await fetchTicketById(returnTicketId);
        if (returnResponse) {
          returnTicket = returnResponse.results;
          // Ensure isReturn is set correctly
          returnTicket.isReturn = true;
          dispatch(setReturnTicket(returnTicket));
        }
      }
      console.log("fetchTickets", { departureTicket, returnTicket });
      return { departureTicket, returnTicket };
    } catch (error) {
      console.error("Error fetching tickets:", error);
      // You might want to dispatch an error action here
      throw error;
    } finally {
      dispatch(setTicketsLoading(false));
    }
  }
);

// Thunk to clear tickets when component unmounts or when needed
export const clearTickets = createAsyncThunk(
  "booking/clearTickets",
  (_, { dispatch }) => {
    dispatch(setDepartureTicket(null));
    dispatch(setReturnTicket(null));
    dispatch(setTicketsLoading(false));
  }
);
