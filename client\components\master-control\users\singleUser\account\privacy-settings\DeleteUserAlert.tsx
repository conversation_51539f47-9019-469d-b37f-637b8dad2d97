"use client";

import React, { useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import { useAppDispatch } from "@/redux/hooks";
import { fetchDeleteUserProfile } from "@/lib/data/userProfileData";
import { setMsg } from "@/redux/features/ActionMsgSlice";
import { logoutUser } from "@/redux/features/AuthSlice";
import { setLoading } from "@/redux/features/LoadingSlice";
import { Eye, EyeOff, Lock } from "lucide-react";

interface DeleteUserAlertProps {
  showDeleteConfirmation: boolean;
  setShowDeleteConfirmation: React.Dispatch<React.SetStateAction<boolean>>;
  deleteConfirmText: string;
  setDeleteConfirmText: React.Dispatch<React.SetStateAction<string>>;
  handleDelete: () => Promise<void>;
  title: string;
  description: string;
  confirmButtonText: string;
  confirmationText: string;
}

export default function DeleteUserAlert({
  showDeleteConfirmation,
  setShowDeleteConfirmation,
  deleteConfirmText,
  setDeleteConfirmText,
  handleDelete,
  title,
  description,
  confirmButtonText,
  confirmationText,
}: DeleteUserAlertProps) {
  const [validationError, setValidationError] = useState("");

  const handleConfirmDelete = useCallback(async () => {
    if (deleteConfirmText.toLowerCase() !== confirmationText.toLowerCase()) {
      setValidationError(`Please type "${confirmationText}" to confirm.`);
      return;
    }
    await handleDelete();
    setShowDeleteConfirmation(false);
    setDeleteConfirmText("");
    setValidationError("");
  }, [
    deleteConfirmText,
    confirmationText,
    handleDelete,
    setShowDeleteConfirmation,
    setDeleteConfirmText,
  ]);

  const handleCancel = useCallback(() => {
    setShowDeleteConfirmation(false);
    setDeleteConfirmText("");
    setValidationError("");
  }, [setShowDeleteConfirmation, setDeleteConfirmText]);

  if (!showDeleteConfirmation) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl p-6">
        <h3 className="text-2xl font-bold text-red-500 mb-2">{title}</h3>
        <div className="text-gray-700 dark:text-gray-300 mb-6">
          <p className="mb-4">{description}</p>
          <p className="text-sm text-gray-700 dark:text-gray-400">
            Please type "{confirmationText}" in the box below to confirm:
          </p>
          <div className="mt-4">
            <input
              type="text"
              value={deleteConfirmText}
              onChange={(e) => {
                setDeleteConfirmText(e.target.value);
                setValidationError("");
              }}
              className="w-full bg-gray-300 dark:bg-gray-700 text-white border dark:border-gray-600 rounded-md px-3 py-2 mt-2 focus:outline-none focus:ring-1 focus:ring-gray-400"
              placeholder={`Type "${confirmationText}" to confirm`}
            />
            {validationError && (
              <p className="text-red-500 text-sm mt-1">{validationError}</p>
            )}
          </div>
        </div>
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            // className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium rounded-lg"
            className="bg-gray-300 text-gray-800 hover:bg-gray-400 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600 font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirmDelete}
            className="px-4 py-2 bg-red-500 text-white font-medium rounded-lg hover:bg-red-600"
          >
            {confirmButtonText}
          </button>
        </div>
      </div>
    </div>
  );
}
