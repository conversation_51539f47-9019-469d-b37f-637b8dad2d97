"use client";
import React from "react";
import { useSelector } from "react-redux";
import {
  selectBookingResult,
  selectTravelerData,
  selectFullTicket,
  selectPassengerCounts,
} from "@/redux/features/BookingConfirmationSlice";
import { getFormatDateTable, getFormatTime } from "@/utils/functions/functions";

const PrintTemplate = () => {
  // Fetch data from Redux store
  const bookingResult = useSelector(selectBookingResult);
  const travelerData = useSelector(selectTravelerData);
  const fullTicket = useSelector(selectFullTicket);
  const passengerCounts = useSelector(selectPassengerCounts);

  // Extract relevant data
  const ticket = fullTicket || {};
  const flightClasses = ticket?.flightClasses || [];
  const returnFlightClasses = ticket?.returnFlightClasses || [];
  const flightClass = flightClasses[0] || {};
  const returnFlightClass = returnFlightClasses[0] || {};
  const price = flightClass?.price || {};
  const returnPrice = returnFlightClass?.price || {};
  const hasReturnTicket = returnFlightClasses.length > 0;

  // Calculate prices
  const currency = price?.currency || "JOD";
  const adultFare =
    (price?.adult || 0) * (passengerCounts?.adults || 0) +
    (returnPrice?.adult || 0) * (passengerCounts?.adults || 0);
  const childFare =
    (price?.child || 0) * (passengerCounts?.children || 0) +
    (returnPrice?.child || 0) * (passengerCounts?.children || 0);
  const infantFare =
    (price?.infant || 0) * (passengerCounts?.infants || 0) +
    (returnPrice?.infant || 0) * (passengerCounts?.infants || 0);
  const totalBaseFare = adultFare + childFare + infantFare;
  const taxes = totalBaseFare * ((price?.tax || 0) / 100);
  const total = totalBaseFare + taxes;

  return (
    <div className="print-container">
      {/* Header Section */}
      <header className="print-header">
        <h1>Booking Summary</h1>
        <p>Request ID: {bookingResult?.requestId || "N/A"}</p>
        <p>Booking Status: {bookingResult?.status || "N/A"}</p>
      </header>

      {/* Request Information */}
      <section className="print-section">
        <h2>Request Information</h2>
        <table>
          <tbody>
            <tr>
              <td>Agent Name</td>
              <td>{bookingResult?.meta?.agentName || "N/A"}</td>
            </tr>
            <tr>
              <td>Request Date</td>
              <td>{getFormatDateTable(bookingResult?.createdAt) || "N/A"}</td>
            </tr>
          </tbody>
        </table>
      </section>

      {/* Traveler Details */}
      <section className="print-section">
        <h2>Traveler Details</h2>
        {travelerData?.map((traveler: any, index: number) => (
          <div key={index}>
            <h3>Traveler {index + 1}</h3>
            <table>
              <tbody>
                <tr>
                  <td>Name</td>
                  <td>
                    {traveler.firstName} {traveler.lastName}
                  </td>
                </tr>
                <tr>
                  <td>Passport Number</td>
                  <td>{traveler.documentNumber || "N/A"}</td>
                </tr>
                <tr>
                  <td>Passport Expiry</td>
                  <td>{traveler.expirationDate || "N/A"}</td>
                </tr>
                <tr>
                  <td>Email</td>
                  <td>{traveler.contactEmail || "N/A"}</td>
                </tr>
                <tr>
                  <td>Phone</td>
                  <td>{traveler.contactPhone || "N/A"}</td>
                </tr>
              </tbody>
            </table>
          </div>
        ))}
      </section>

      {/* Itinerary Details */}
      <section className="print-section">
        <h2>Itinerary Details</h2>
        <table>
          <tbody>
            <tr>
              <td>Flight Date</td>
              <td>{getFormatDateTable(ticket?.flightDate) || "N/A"}</td>
            </tr>
            <tr>
              <td>Origin</td>
              <td>{ticket?.departure?.airportCode || "N/A"}</td>
            </tr>
            <tr>
              <td>Destination</td>
              <td>{ticket?.arrival?.airportCode || "N/A"}</td>
            </tr>
            <tr>
              <td>Airline</td>
              <td>{ticket?.segments?.[0]?.carrier || "N/A"}</td>
            </tr>
            <tr>
              <td>Duration</td>
              <td>{ticket?.duration || "N/A"}</td>
            </tr>
          </tbody>
        </table>
      </section>

      {/* Payment Summary */}
      <section className="print-section">
        <h2>Payment Summary</h2>
        <table>
          <tbody>
            <tr>
              <td>Total Base Fare</td>
              <td>
                {totalBaseFare.toFixed(2)} {currency}
              </td>
            </tr>
            <tr>
              <td>Taxes</td>
              <td>
                {taxes.toFixed(2)} {currency}
              </td>
            </tr>
            <tr>
              <td>Total</td>
              <td>
                {total.toFixed(2)} {currency}
              </td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  );
};

export default PrintTemplate;
